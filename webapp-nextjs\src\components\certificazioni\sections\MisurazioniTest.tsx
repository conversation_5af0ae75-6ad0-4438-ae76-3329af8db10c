'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Settings } from 'lucide-react'
import { CertificazioneCavoCreate } from '@/types/certificazioni'

interface MisurazioniTestProps {
  formData: Partial<CertificazioneCavoCreate>
  validationErrors: Record<string, string>
  onInputChange: (field: string, value: any) => void
}

export function MisurazioniTest({
  formData,
  validationErrors,
  onInputChange
}: MisurazioniTestProps) {
  return (
    <Card className="h-fit shadow-sm border-gray-200 bg-white">
      <CardHeader className="pb-4 px-6 pt-6 border-b border-gray-100">
        <CardTitle className="flex items-center gap-2 text-lg font-semibold text-gray-900">
          <Settings className="h-5 w-5" />
          ⚡ Misurazioni e Test CEI 64-8
        </CardTitle>
      </CardHeader>
      <CardContent className="px-6 py-6 space-y-5">
        {/* Misurazioni Principali */}
        <div className="space-y-5">
          <div className="space-y-2">
            <Label htmlFor="valore_isolamento" className="text-sm font-medium text-gray-700">
              Isolamento (MΩ) *
            </Label>
            <Input
              id="valore_isolamento"
              type="number"
              step="0.01"
              value={formData.valore_isolamento || ''}
              onChange={(e) => onInputChange('valore_isolamento', parseFloat(e.target.value))}
              placeholder="≥ 1000"
              className={`h-11 text-sm border-gray-300 focus:border-blue-500 focus:ring-blue-500 ${validationErrors.valore_isolamento ? 'border-red-500' : ''}`}
            />
            {validationErrors.valore_isolamento && (
              <p className="text-sm text-red-600">{validationErrors.valore_isolamento}</p>
            )}
            <p className="text-sm text-slate-500">Valore minimo: 1000 MΩ</p>
          </div>

          <div className="space-y-3">
            <Label htmlFor="valore_continuita" className="text-base font-medium">
              Test Continuità *
            </Label>
            <Select
              value={formData.valore_continuita}
              onValueChange={(value) => onInputChange('valore_continuita', value)}
            >
              <SelectTrigger className={`h-12 text-base ${validationErrors.valore_continuita ? 'border-red-500' : ''}`}>
                <SelectValue placeholder="Seleziona esito..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="CONFORME">
                  <span className="text-base">✅ CONFORME</span>
                </SelectItem>
                <SelectItem value="NON_CONFORME">
                  <span className="text-base">❌ NON CONFORME</span>
                </SelectItem>
              </SelectContent>
            </Select>
            {validationErrors.valore_continuita && (
              <p className="text-sm text-red-600">{validationErrors.valore_continuita}</p>
            )}
          </div>

          <div className="space-y-3">
            <Label htmlFor="valore_resistenza" className="text-base font-medium">
              Resistenza (Ω) *
            </Label>
            <Input
              id="valore_resistenza"
              type="number"
              step="0.01"
              value={formData.valore_resistenza || ''}
              onChange={(e) => onInputChange('valore_resistenza', parseFloat(e.target.value))}
              placeholder="< 1.0"
              className={`h-12 text-base ${validationErrors.valore_resistenza ? 'border-red-500' : ''}`}
            />
            {validationErrors.valore_resistenza && (
              <p className="text-sm text-red-600">{validationErrors.valore_resistenza}</p>
            )}
            <p className="text-sm text-slate-500">Valore massimo: 1.0 Ω</p>
          </div>
        </div>

        {/* Parametri di Prova */}
        <div className="border-t pt-6">
          <h4 className="text-base font-medium text-slate-700 mb-4">Parametri di Prova</h4>
          <div className="space-y-6">
            <div className="space-y-3">
              <Label htmlFor="tensione_prova_isolamento" className="text-base font-medium">
                Tensione di Prova (V)
              </Label>
              <Input
                id="tensione_prova_isolamento"
                type="number"
                value={formData.tensione_prova_isolamento || 500}
                onChange={(e) => onInputChange('tensione_prova_isolamento', parseInt(e.target.value))}
                className="h-12 text-base"
              />
              <p className="text-sm text-slate-500">Standard: 500V DC</p>
            </div>

            <div className="space-y-3">
              <Label htmlFor="durata_prova" className="text-base font-medium">
                Durata Prova (min)
              </Label>
              <Input
                id="durata_prova"
                type="number"
                value={formData.durata_prova || 1}
                onChange={(e) => onInputChange('durata_prova', parseInt(e.target.value))}
                placeholder="1"
                className="h-12 text-base"
              />
              <p className="text-sm text-slate-500">Standard: 1 minuto</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
