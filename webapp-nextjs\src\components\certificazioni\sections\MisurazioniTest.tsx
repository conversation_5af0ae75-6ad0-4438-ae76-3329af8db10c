'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Settings } from 'lucide-react'
import { CertificazioneCavoCreate } from '@/types/certificazioni'

interface MisurazioniTestProps {
  formData: Partial<CertificazioneCavoCreate>
  validationErrors: Record<string, string>
  onInputChange: (field: string, value: any) => void
}

export function MisurazioniTest({
  formData,
  validationErrors,
  onInputChange
}: MisurazioniTestProps) {
  return (
    <Card className="h-fit">
      <CardHeader className="pb-3 px-4 pt-4">
        <CardTitle className="flex items-center gap-2 text-base font-semibold text-slate-900">
          <Settings className="h-4 w-4" />
          ⚡ Misurazioni e Test CEI 64-8
        </CardTitle>
      </CardHeader>
      <CardContent className="px-4 pb-4 space-y-4">
        {/* Misurazioni Principali */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="valore_isolamento" className="text-sm font-medium">
              Isolamento (MΩ) *
            </Label>
            <Input
              id="valore_isolamento"
              type="number"
              step="0.01"
              value={formData.valore_isolamento || ''}
              onChange={(e) => onInputChange('valore_isolamento', parseFloat(e.target.value))}
              placeholder="≥ 1000"
              className={`h-10 ${validationErrors.valore_isolamento ? 'border-red-500' : ''}`}
            />
            {validationErrors.valore_isolamento && (
              <p className="text-xs text-red-600">{validationErrors.valore_isolamento}</p>
            )}
            <p className="text-xs text-slate-500">Valore minimo: 1000 MΩ</p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="valore_continuita" className="text-sm font-medium">
              Test Continuità *
            </Label>
            <Select
              value={formData.valore_continuita}
              onValueChange={(value) => onInputChange('valore_continuita', value)}
            >
              <SelectTrigger className={`h-10 ${validationErrors.valore_continuita ? 'border-red-500' : ''}`}>
                <SelectValue placeholder="Seleziona esito..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="CONFORME">✅ CONFORME</SelectItem>
                <SelectItem value="NON_CONFORME">❌ NON CONFORME</SelectItem>
              </SelectContent>
            </Select>
            {validationErrors.valore_continuita && (
              <p className="text-xs text-red-600">{validationErrors.valore_continuita}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="valore_resistenza" className="text-sm font-medium">
              Resistenza (Ω) *
            </Label>
            <Input
              id="valore_resistenza"
              type="number"
              step="0.01"
              value={formData.valore_resistenza || ''}
              onChange={(e) => onInputChange('valore_resistenza', parseFloat(e.target.value))}
              placeholder="< 1.0"
              className={`h-10 ${validationErrors.valore_resistenza ? 'border-red-500' : ''}`}
            />
            {validationErrors.valore_resistenza && (
              <p className="text-xs text-red-600">{validationErrors.valore_resistenza}</p>
            )}
            <p className="text-xs text-slate-500">Valore massimo: 1.0 Ω</p>
          </div>
        </div>

        {/* Parametri di Prova */}
        <div className="border-t pt-4">
          <h4 className="text-sm font-medium text-slate-700 mb-3">Parametri di Prova</h4>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="tensione_prova_isolamento" className="text-sm font-medium">
                Tensione di Prova (V)
              </Label>
              <Input
                id="tensione_prova_isolamento"
                type="number"
                value={formData.tensione_prova_isolamento || 500}
                onChange={(e) => onInputChange('tensione_prova_isolamento', parseInt(e.target.value))}
                className="h-10"
              />
              <p className="text-xs text-slate-500">Standard: 500V DC</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="durata_prova" className="text-sm font-medium">
                Durata Prova (min)
              </Label>
              <Input
                id="durata_prova"
                type="number"
                value={formData.durata_prova || 1}
                onChange={(e) => onInputChange('durata_prova', parseInt(e.target.value))}
                placeholder="1"
                className="h-10"
              />
              <p className="text-xs text-slate-500">Standard: 1 minuto</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
