'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Settings } from 'lucide-react'
import { CertificazioneCavoCreate } from '@/types/certificazioni'

interface MisurazioniTestProps {
  formData: Partial<CertificazioneCavoCreate>
  validationErrors: Record<string, string>
  onInputChange: (field: string, value: any) => void
}

export function MisurazioniTest({
  formData,
  validationErrors,
  onInputChange
}: MisurazioniTestProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Isolamento */}
      <div className="space-y-2">
        <Label htmlFor="valore_isolamento" className="text-sm font-medium text-gray-700">
          Isolamento (MΩ) *
        </Label>
        <Input
          id="valore_isolamento"
          type="number"
          step="0.01"
          value={formData.valore_isolamento || ''}
          onChange={(e) => onInputChange('valore_isolamento', parseFloat(e.target.value))}
          placeholder="≥ 1000"
          className={`h-11 text-sm ${validationErrors.valore_isolamento ? 'border-red-500' : 'border-gray-300'}`}
        />
        {validationErrors.valore_isolamento && (
          <p className="text-sm text-red-600">{validationErrors.valore_isolamento}</p>
        )}
        <p className="text-xs text-gray-500">Valore minimo: 1000 MΩ</p>
      </div>

      {/* Test Continuità */}
      <div className="space-y-2">
        <Label htmlFor="valore_continuita" className="text-sm font-medium text-gray-700">
          Test Continuità *
        </Label>
        <Select
          value={formData.valore_continuita}
          onValueChange={(value) => onInputChange('valore_continuita', value)}
        >
          <SelectTrigger className={`h-11 text-sm ${validationErrors.valore_continuita ? 'border-red-500' : 'border-gray-300'}`}>
            <SelectValue placeholder="Seleziona esito..." />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="CONFORME">
              <span className="text-sm">✅ CONFORME</span>
            </SelectItem>
            <SelectItem value="NON_CONFORME">
              <span className="text-sm">❌ NON CONFORME</span>
            </SelectItem>
          </SelectContent>
        </Select>
        {validationErrors.valore_continuita && (
          <p className="text-sm text-red-600">{validationErrors.valore_continuita}</p>
        )}
      </div>

      {/* Resistenza */}
      <div className="space-y-2">
        <Label htmlFor="valore_resistenza" className="text-sm font-medium text-gray-700">
          Resistenza (Ω) *
        </Label>
        <Input
          id="valore_resistenza"
          type="number"
          step="0.01"
          value={formData.valore_resistenza || ''}
          onChange={(e) => onInputChange('valore_resistenza', parseFloat(e.target.value))}
          placeholder="< 1.0"
          className={`h-11 text-sm ${validationErrors.valore_resistenza ? 'border-red-500' : 'border-gray-300'}`}
        />
        {validationErrors.valore_resistenza && (
          <p className="text-sm text-red-600">{validationErrors.valore_resistenza}</p>
        )}
        <p className="text-xs text-gray-500">Valore massimo: 1.0 Ω</p>
      </div>

      {/* Tensione Prova Isolamento */}
      <div className="space-y-2">
        Tensione di Prova (V)
        </Label>
        <Input
          id="tensione_prova_isolamento"
          type="number"
          value={formData.tensione_prova_isolamento || 500}
          onChange={(e) => onInputChange('tensione_prova_isolamento', parseInt(e.target.value))}
          className="h-11 text-sm border-gray-300"
        />
        <p className="text-xs text-gray-500">Standard: 500V DC</p>
      </div>

      {/* Durata Prova */}
      <div className="space-y-2">
        <Label htmlFor="durata_prova" className="text-sm font-medium text-gray-700">
          Durata Prova (min)
        </Label>
        <Input
          id="durata_prova"
          type="number"
          value={formData.durata_prova || 1}
          onChange={(e) => onInputChange('durata_prova', parseInt(e.target.value))}
          placeholder="1"
          className="h-11 text-sm border-gray-300"
        />
        <p className="text-xs text-gray-500">Standard: 1 minuto</p>
      </div>
    </div>
  )
}
