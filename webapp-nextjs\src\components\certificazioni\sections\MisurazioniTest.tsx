'use client'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Settings } from 'lucide-react'
import { CertificazioneCavoCreate } from '@/types/certificazioni'

interface MisurazioniTestProps {
  formData: Partial<CertificazioneCavoCreate>
  validationErrors: Record<string, string>
  onInputChange: (field: string, value: any) => void
}

export function MisurazioniTest({
  formData,
  validationErrors,
  onInputChange
}: MisurazioniTestProps) {
  return (
    <Card>
      <CardHeader className="pb-1 px-3 pt-2">
        <CardTitle className="flex items-center gap-2 text-sm font-semibold">
          <Settings className="h-3 w-3" />
          Misurazioni e Test
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-2 pt-0 px-3 pb-2">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          {/* Prima riga: Misurazioni principali */}
          <div className="space-y-1">
            <Label htmlFor="valore_isolamento" className="text-sm">Isolamento (MΩ) *</Label>
            <Input
              id="valore_isolamento"
              type="number"
              step="0.01"
              value={formData.valore_isolamento || ''}
              onChange={(e) => onInputChange('valore_isolamento', parseFloat(e.target.value))}
              placeholder="1000"
              className="text-sm"
            />
            {validationErrors.valore_isolamento && (
              <p className="text-xs text-red-600">{validationErrors.valore_isolamento}</p>
            )}
          </div>

          <div className="space-y-1">
            <Label htmlFor="valore_continuita" className="text-sm">Continuità *</Label>
            <Select
              value={formData.valore_continuita}
              onValueChange={(value) => onInputChange('valore_continuita', value)}
            >
              <SelectTrigger className="text-sm">
                <SelectValue placeholder="Seleziona..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="CONFORME">✅ CONFORME</SelectItem>
                <SelectItem value="NON_CONFORME">❌ NON CONFORME</SelectItem>
              </SelectContent>
            </Select>
            {validationErrors.valore_continuita && (
              <p className="text-xs text-red-600">{validationErrors.valore_continuita}</p>
            )}
          </div>

          <div className="space-y-1">
            <Label htmlFor="valore_resistenza" className="text-sm">Resistenza (Ω) *</Label>
            <Input
              id="valore_resistenza"
              type="number"
              step="0.01"
              value={formData.valore_resistenza || ''}
              onChange={(e) => onInputChange('valore_resistenza', parseFloat(e.target.value))}
              placeholder="0.5"
              className="text-sm"
            />
            {validationErrors.valore_resistenza && (
              <p className="text-xs text-red-600">{validationErrors.valore_resistenza}</p>
            )}
          </div>
        </div>

        {/* Seconda riga: Parametri di prova */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <div className="space-y-1">
            <Label htmlFor="tensione_prova_isolamento" className="text-sm">Tensione Prova (V)</Label>
            <Input
              id="tensione_prova_isolamento"
              type="number"
              value={formData.tensione_prova_isolamento || 500}
              onChange={(e) => onInputChange('tensione_prova_isolamento', parseInt(e.target.value))}
              className="text-sm"
            />
          </div>

          <div className="space-y-1">
            <Label htmlFor="durata_prova" className="text-sm">Durata Prova (min)</Label>
            <Input
              id="durata_prova"
              type="number"
              value={formData.durata_prova || ''}
              onChange={(e) => onInputChange('durata_prova', parseInt(e.target.value))}
              placeholder="1"
              className="text-sm"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
