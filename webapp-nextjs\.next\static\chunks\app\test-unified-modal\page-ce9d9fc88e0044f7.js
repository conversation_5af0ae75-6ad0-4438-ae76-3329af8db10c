(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2804],{381:(e,i,a)=>{"use strict";a.d(i,{A:()=>t});let t=(0,a(19946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1243:(e,i,a)=>{"use strict";a.d(i,{A:()=>t});let t=(0,a(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},13735:(e,i,a)=>{"use strict";a.r(i),a.d(i,{default:()=>c});var t=a(95155),r=a(12115),s=a(30285),l=a(84333);let n={id_cavo:"C001",id_cantiere:1,revisione_ufficiale:"Rev 1.0",tipologia:"LIYCY",sezione:"3X2.5MM",formazione:"3X2.5MM",da:"Quadro A",a:"Quadro B",ubicazione_partenza:"Quadro A",ubicazione_arrivo:"Quadro B",metri_teorici:150,metri_posati:75,metratura_reale:75,stato_installazione:"installato",id_bobina:"BOB001"},d={id_cantiere:1,commessa:"TEST-001"},o=()=>{let[e,i]=(0,r.useState)(!1),[a,o]=(0,r.useState)(!1),c=async e=>{console.log("\uD83D\uDCBE Unified Modal Save:",e),await new Promise(e=>setTimeout(e,1e3)),alert("Operazione completata con successo!\nModalit\xe0: ".concat(e.mode,"\nCavo: ").concat(e.cableId))};return(0,t.jsxs)("div",{className:"p-6 space-y-4",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Test Interfaccia Unificata"}),(0,t.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,t.jsxs)("h3",{className:"font-semibold mb-2",children:["Cavo di Test: ",n.id_cavo]}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[n.tipologia," ",n.sezione," - Da: ",n.da," A: ",n.a]}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Metri posati: ",n.metri_posati,"m / ",n.metri_teorici,"m"]})]}),(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)(s.$,{onClick:()=>i(!0),className:"bg-green-600 hover:bg-green-700",children:"Test Modalit\xe0: Aggiungi Metri"}),(0,t.jsx)(s.$,{onClick:()=>o(!0),className:"bg-blue-600 hover:bg-blue-700",children:"Test Modalit\xe0: Modifica Bobina"})]}),(0,t.jsx)(l.B,{mode:"aggiungi_metri",open:e,onClose:()=>i(!1),cavo:n,cantiere:d,onSave:c}),(0,t.jsx)(l.B,{mode:"modifica_bobina",open:a,onClose:()=>o(!1),cavo:n,cantiere:d,onSave:c})]})};function c(){return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,t.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-center mb-8 text-gray-800",children:"Test Interfaccia Unificata Cable/Bobbin"}),(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6",children:[(0,t.jsx)("h2",{className:"font-semibold text-blue-800 mb-2",children:"Istruzioni per il Test"}),(0,t.jsxs)("ul",{className:"text-blue-700 text-sm space-y-1",children:[(0,t.jsx)("li",{children:"• Clicca sui pulsanti per aprire la modale unificata in diverse modalit\xe0"}),(0,t.jsx)("li",{children:"• Testa la validazione dei campi e la selezione delle bobine"}),(0,t.jsx)("li",{children:"• Verifica che le sezioni dinamiche si mostrino correttamente"}),(0,t.jsx)("li",{children:"• Controlla l'accessibilit\xe0 con Tab e Escape"}),(0,t.jsx)("li",{children:"• Verifica i messaggi di errore e successo"})]})]}),(0,t.jsx)(o,{}),(0,t.jsxs)("div",{className:"mt-8 bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,t.jsx)("h2",{className:"font-semibold text-green-800 mb-2",children:"Funzionalit\xe0 Implementate"}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4 text-sm text-green-700",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium mb-1",children:"Modalit\xe0 Aggiungi Metri:"}),(0,t.jsxs)("ul",{className:"space-y-1",children:[(0,t.jsx)("li",{children:"✅ Validazione input metri"}),(0,t.jsx)("li",{children:"✅ Selezione bobina compatibile/incompatibile"}),(0,t.jsx)("li",{children:"✅ Opzione BOBINA VUOTA"}),(0,t.jsx)("li",{children:"✅ Ricerca bobine"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium mb-1",children:"Modalit\xe0 Modifica Bobina:"}),(0,t.jsxs)("ul",{className:"space-y-1",children:[(0,t.jsx)("li",{children:"✅ Radio button per opzioni"}),(0,t.jsx)("li",{children:"✅ Selezione condizionale bobina"}),(0,t.jsx)("li",{children:"✅ Modifica metri posati"}),(0,t.jsx)("li",{children:"✅ Annulla posa"})]})]})]})]}),(0,t.jsxs)("div",{className:"mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,t.jsx)("h2",{className:"font-semibold text-yellow-800 mb-2",children:"Accessibilit\xe0"}),(0,t.jsxs)("div",{className:"text-sm text-yellow-700 space-y-1",children:[(0,t.jsx)("li",{children:"✅ Attributi ARIA per screen reader"}),(0,t.jsx)("li",{children:"✅ Navigazione da tastiera (Tab, Enter, Escape)"}),(0,t.jsx)("li",{children:"✅ Focus management e indicatori visivi"}),(0,t.jsx)("li",{children:'✅ Messaggi di errore con role="alert"'}),(0,t.jsx)("li",{children:"✅ Etichette descrittive per tutti i controlli"})]})]})]})})})}},30285:(e,i,a)=>{"use strict";a.d(i,{$:()=>d});var t=a(95155);a(12115);var r=a(99708),s=a(74466),l=a(59434);let n=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:i,variant:a,size:s,asChild:d=!1,...o}=e,c=d?r.DX:"button";return(0,t.jsx)(c,{"data-slot":"button",className:(0,l.cn)(n({variant:a,size:s,className:i})),...o})}},37108:(e,i,a)=>{"use strict";a.d(i,{A:()=>t});let t=(0,a(19946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},39647:(e,i,a)=>{Promise.resolve().then(a.bind(a,13735))},55365:(e,i,a)=>{"use strict";a.d(i,{Fc:()=>d,TN:()=>o});var t=a(95155),r=a(12115),s=a(74466),l=a(59434);let n=(0,s.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),d=r.forwardRef((e,i)=>{let{className:a,variant:r,...s}=e;return(0,t.jsx)("div",{ref:i,role:"alert",className:(0,l.cn)(n({variant:r}),a),...s})});d.displayName="Alert",r.forwardRef((e,i)=>{let{className:a,...r}=e;return(0,t.jsx)("h5",{ref:i,className:(0,l.cn)("mb-1 font-medium leading-none tracking-tight",a),...r})}).displayName="AlertTitle";let o=r.forwardRef((e,i)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:i,className:(0,l.cn)("text-sm [&_p]:leading-relaxed",a),...r})});o.displayName="AlertDescription"},59434:(e,i,a)=>{"use strict";a.d(i,{cn:()=>s});var t=a(52596),r=a(39688);function s(){for(var e=arguments.length,i=Array(e),a=0;a<e;a++)i[a]=arguments[a];return(0,r.QP)((0,t.$)(i))}},62523:(e,i,a)=>{"use strict";a.d(i,{p:()=>l});var t=a(95155),r=a(12115),s=a(59434);let l=r.forwardRef((e,i)=>{let{className:a,type:r,...l}=e;return(0,t.jsx)("input",{type:r,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),ref:i,...l})});l.displayName="Input"},66140:(e,i,a)=>{"use strict";a.d(i,{A:()=>t});let t=(0,a(19946).A)("ruler",[["path",{d:"M21.3 15.3a2.4 2.4 0 0 1 0 3.4l-2.6 2.6a2.4 2.4 0 0 1-3.4 0L2.7 8.7a2.41 2.41 0 0 1 0-3.4l2.6-2.6a2.41 2.41 0 0 1 3.4 0Z",key:"icamh8"}],["path",{d:"m14.5 12.5 2-2",key:"inckbg"}],["path",{d:"m11.5 9.5 2-2",key:"fmmyf7"}],["path",{d:"m8.5 6.5 2-2",key:"vc6u1g"}],["path",{d:"m17.5 15.5 2-2",key:"wo5hmg"}]])},85057:(e,i,a)=>{"use strict";a.d(i,{J:()=>l});var t=a(95155);a(12115);var r=a(40968),s=a(59434);function l(e){let{className:i,...a}=e;return(0,t.jsx)(r.b,{"data-slot":"label",className:(0,s.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",i),...a})}}},e=>{var i=i=>e(e.s=i);e.O(0,[3455,3464,9816,6987,283,4333,8441,1684,7358],()=>i(39647)),_N_E=e.O()}]);