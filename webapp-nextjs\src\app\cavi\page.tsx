'use client'

import { useState, useEffect } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useAuth } from '@/contexts/AuthContext'
import { useCantiere } from '@/hooks/useCantiere'
import { CantiereErrorBoundary } from '@/components/cantiere/CantiereErrorBoundary'
import { caviApi } from '@/lib/api'
import { Cavo } from '@/types'
import CaviTable from '@/components/cavi/CaviTable'
import CaviStatistics from '@/components/cavi/CaviStatistics'
import InserisciMetriDialog from '@/components/cavi/InserisciMetriDialog'
import CollegamentiDialogSimple from '@/components/cavi/CollegamentiDialogSimple'
// Import dei nuovi componenti modali migliorati
import {
  DisconnectCableModal,
  GeneratePdfModal,
  CertificationModal,
  BulkCertificationModal,
  CertificationErrorModal,
  SuccessToast
} from '@/components/cavi/modals/CableActionModals'
import {
  UnifiedCableBobbinModal
} from '@/components/cavi/modals/BobinaManagementModals'
// Import CSS per i modali migliorati
import '@/components/cavi/modals/enhanced-modals.css'
// Import del vecchio dialog per InserisciMetri (temporaneo)
import InserisciMetriDialogOld from '@/components/cavi/InserisciMetriDialog'
import CreaComandaDialog from '@/components/cavi/CreaComandaDialog'
import ImportExcelDialog from '@/components/cavi/ImportExcelDialog'
// Import dei dialog per gestione cavi
import AggiungiCavoDialog from '@/components/cavi/AggiungiCavoDialog'
import ModificaCavoDialog from '@/components/cavi/ModificaCavoDialog'
import EliminaCavoDialog from '@/components/cavi/EliminaCavoDialog'
import ExportDataDialog from '@/components/cavi/ExportDataDialog'
// import { useToast } from '@/hooks/use-toast'
import {
  Package,
  AlertCircle,
  Loader2
} from 'lucide-react'

interface DashboardStats {
  totali: number
  installati: number
  collegati: number
  certificati: number
  percentualeInstallazione: number
  percentualeCollegamento: number
  percentualeCertificazione: number
  metriTotali: number
  metriInstallati: number
  metriCollegati: number
  metriCertificati: number
}

export default function CaviPage() {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth()
  const { cantiereId, cantiere, isValidCantiere, isLoading: cantiereLoading, error: cantiereError } = useCantiere()
  const router = useRouter()



  // Sistema toast semplice
  const toast = ({ title, description, variant }: { title: string, description: string, variant?: string }) => {
    // TODO: Implementare sistema toast visuale
  }
  const [cavi, setCavi] = useState<Cavo[]>([])
  const [caviSpare, setCaviSpare] = useState<Cavo[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [selectedCavi, setSelectedCavi] = useState<string[]>([])
  const [selectionEnabled, setSelectionEnabled] = useState(false)
  const [filteredCavi, setFilteredCavi] = useState<Cavo[]>([])
  const [revisioneCorrente, setRevisioneCorrente] = useState<string>('')

  // Update filtered cavi when main cavi change
  useEffect(() => {
    setFilteredCavi(cavi)
  }, [cavi])

  // Stati per i dialoghi
  const [inserisciMetriDialog, setInserisciMetriDialog] = useState<{
    open: boolean
    cavo: Cavo | null
  }>({ open: false, cavo: null })

  const [dettagliCavoDialog, setDettagliCavoDialog] = useState<{
    open: boolean
    cavo: Cavo | null
  }>({ open: false, cavo: null })

  // Stati per i dialog di gestione cavi
  const [aggiungiCavoDialog, setAggiungiCavoDialog] = useState(false)
  const [modificaCavoDialog, setModificaCavoDialog] = useState<{
    open: boolean
    cavo: Cavo | null
  }>({ open: false, cavo: null })
  const [eliminaCavoDialog, setEliminaCavoDialog] = useState<{
    open: boolean
    cavo: Cavo | null
  }>({ open: false, cavo: null })

  const [modificaBobinaDialog, setModificaBobinaDialog] = useState<{
    open: boolean
    cavo: Cavo | null
  }>({ open: false, cavo: null })

  // Stato per la modale unificata
  const [unifiedModal, setUnifiedModal] = useState<{
    open: boolean
    mode: 'aggiungi_metri' | 'modifica_bobina' | null
    cavo: Cavo | null
  }>({ open: false, mode: null, cavo: null })

  const [collegamentiDialog, setCollegamentiDialog] = useState<{
    open: boolean
    cavo: Cavo | null
  }>({ open: false, cavo: null })

  // Stati per i nuovi modali migliorati
  const [disconnectModal, setDisconnectModal] = useState<{
    open: boolean
    cavo: Cavo | null
  }>({ open: false, cavo: null })

  const [generatePdfModal, setGeneratePdfModal] = useState<{
    open: boolean
    cavo: Cavo | null
  }>({ open: false, cavo: null })

  const [certificationModal, setCertificationModal] = useState<{
    open: boolean
    cavo: Cavo | null
  }>({ open: false, cavo: null })

  const [bulkCertificationModal, setBulkCertificationModal] = useState<{
    open: boolean
    cavi: Cavo[]
  }>({ open: false, cavi: [] })

  const [certificationErrorModal, setCertificationErrorModal] = useState<{
    open: boolean
    cavo: Cavo | null
    error: string
  }>({ open: false, cavo: null, error: '' })

  const [successToast, setSuccessToast] = useState<{
    visible: boolean
    message: string
  }>({ visible: false, message: '' })

  const [creaComandaDialog, setCreaComandaDialog] = useState<{
    open: boolean
    tipoComanda?: 'POSA' | 'COLLEGAMENTO_PARTENZA' | 'COLLEGAMENTO_ARRIVO' | 'CERTIFICAZIONE'
  }>({ open: false })

  const [importExcelDialog, setImportExcelDialog] = useState<{
    open: boolean
    tipo?: 'cavi' | 'bobine'
  }>({ open: false })

  const [exportDataDialog, setExportDataDialog] = useState(false)
  const [stats, setStats] = useState<DashboardStats>({
    totali: 0,
    installati: 0,
    collegati: 0,
    certificati: 0,
    percentualeInstallazione: 0,
    percentualeCollegamento: 0,
    percentualeCertificazione: 0,
    metriTotali: 0,
    metriInstallati: 0,
    metriCollegati: 0,
    metriCertificati: 0
  })

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/login')
    }
  }, [isAuthenticated, authLoading, router])

  // Crea oggetto cantiere per il dialog
  const cantiereForDialog = cantiere || (cantiereId && cantiereId > 0 ? {
    id_cantiere: cantiereId,
    commessa: `Cantiere ${cantiereId}`
  } : null)

  // Carica i cavi dal backend - MIGLIORATO con nuovo hook
  useEffect(() => {
    if (isValidCantiere && cantiereId && cantiereId > 0 && !cantiereLoading) {

      loadCavi()
      loadRevisioneCorrente()
    } else if (!cantiereLoading && !isValidCantiere) {

      setCavi([])
      setCaviSpare([])
      setError(cantiereError || 'Nessun cantiere selezionato')
    }
  }, [cantiereId, isValidCantiere, cantiereLoading, cantiereError])

  const loadRevisioneCorrente = async () => {
    try {
      const response = await fetch(`http://localhost:8001/api/cavi/${cantiereId}/revisione-corrente`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()
        setRevisioneCorrente(data.revisione_corrente || '00')
      } else {
        setRevisioneCorrente('00')
      }
    } catch (error) {
      setRevisioneCorrente('00')
    }
  }

  const loadCavi = async () => {
    try {
      setLoading(true)
      setError('')

      // Prima prova con l'API normale
      try {
        const data = await caviApi.getCavi(cantiereId!)

        // Separa cavi attivi e spare usando la logica corretta
        // SPARE: modificato_manualmente = 3
        // ATTIVI: modificato_manualmente ≠ 3
        const caviAttivi = data.filter((cavo: Cavo) => cavo.modificato_manualmente !== 3)
        const caviSpareFiltered = data.filter((cavo: Cavo) => cavo.modificato_manualmente === 3)

        setCavi(caviAttivi)
        setCaviSpare(caviSpareFiltered)

        // Calcola statistiche
        calculateStats(caviAttivi)

      } catch (apiError: any) {
        throw apiError
      }

    } catch (error: any) {
      setError(`Errore nel caricamento dei cavi: ${error.response?.data?.detail || error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const calculateStats = (caviData: Cavo[]) => {
    const totali = caviData.length
    const installati = caviData.filter(c => (c.metri_posati || c.metratura_reale || 0) > 0).length
    const collegati = caviData.filter(c => (c.collegamento || c.collegamenti) === 3).length // 3 = collegato
    const certificati = caviData.filter(c => c.certificato).length

    const metriTotali = caviData.reduce((sum, c) => sum + (c.metri_teorici || 0), 0)
    const metriInstallati = caviData.reduce((sum, c) => sum + (c.metri_posati || 0), 0)
    const metriCollegati = caviData.filter(c => c.collegamento === 3).reduce((sum, c) => sum + (c.metri_posati || 0), 0)
    const metriCertificati = caviData.filter(c => c.certificato).reduce((sum, c) => sum + (c.metri_posati || 0), 0)

    setStats({
      totali,
      installati,
      collegati,
      certificati,
      percentualeInstallazione: totali > 0 ? Math.round((installati / totali) * 100) : 0,
      percentualeCollegamento: totali > 0 ? Math.round((collegati / totali) * 100) : 0,
      percentualeCertificazione: totali > 0 ? Math.round((certificati / totali) * 100) : 0,
      metriTotali,
      metriInstallati,
      metriCollegati,
      metriCertificati
    })
  }

  // Gestione azioni sui cavi con i nuovi modali migliorati
  const handleStatusAction = (cavo: Cavo, action: string, bulkCavi?: Cavo[]) => {

    switch (action) {
      case 'insert_meters':
        // Usa la modale unificata in modalità aggiungi_metri
        setUnifiedModal({ open: true, mode: 'aggiungi_metri', cavo })
        break
      case 'modify_reel':
        // Usa la modale unificata in modalità modifica_bobina
        setUnifiedModal({ open: true, mode: 'modifica_bobina', cavo })
        break
      case 'view_command':
        toast({
          title: "Visualizza Comanda",
          description: `Apertura comanda ${label} per cavo ${cavo.id_cavo}`,
        })
        break
      case 'connect_cable':
      case 'connect_arrival':
      case 'connect_departure':
      case 'manage_connections':
        // Usa il modal specifico per gestione collegamenti
        setCollegamentiDialog({ open: true, cavo })
        break
      case 'disconnect_cable':
        // Usa il modal specifico per gestione collegamenti (scollegamento)
        setCollegamentiDialog({ open: true, cavo })
        break
      case 'create_certificate':
        // Usa il nuovo modal di certificazione
        console.log('🚀 Opening certification modal for cavo:', cavo.id_cavo)
        setCertificationModal({ open: true, cavo })
        break
      case 'bulk_certify':
        // Usa il nuovo modal di certificazione multipla
        if (bulkCavi && bulkCavi.length > 0) {
          setBulkCertificationModal({ open: true, cavi: bulkCavi })
        }
        break
      case 'generate_pdf':
        // Usa il nuovo modal di generazione PDF
        setGeneratePdfModal({ open: true, cavo })
        break
    }
  }

  const handleContextMenuAction = (cavo: Cavo, action: string) => {

    switch (action) {
      case 'view_details':
        setDettagliCavoDialog({ open: true, cavo })
        break
      case 'edit':
        setModificaCavoDialog({ open: true, cavo })
        break
      case 'delete':
        setEliminaCavoDialog({ open: true, cavo })
        break
      case 'add_new':
        setAggiungiCavoDialog(true)
        break
      case 'select':
        const isSelected = selectedCavi.includes(cavo.id_cavo)
        if (isSelected) {
          setSelectedCavi(selectedCavi.filter(id => id !== cavo.id_cavo))
          toast({
            title: "Cavo Deselezionato",
            description: `Cavo ${cavo.id_cavo} deselezionato`,
          })
        } else {
          setSelectedCavi([...selectedCavi, cavo.id_cavo])
          toast({
            title: "Cavo Selezionato",
            description: `Cavo ${cavo.id_cavo} selezionato`,
          })
        }
        break
      case 'copy_id':
        navigator.clipboard.writeText(cavo.id_cavo)
        toast({
          title: "ID Copiato",
          description: `ID cavo ${cavo.id_cavo} copiato negli appunti`,
        })
        break
      case 'copy_details':
        const details = `ID: ${cavo.id_cavo}, Tipologia: ${cavo.tipologia}, Formazione: ${cavo.formazione || cavo.sezione}, Metri: ${cavo.metri_teorici}`
        navigator.clipboard.writeText(details)
        toast({
          title: "Dettagli Copiati",
          description: "Dettagli cavo copiati negli appunti",
        })
        break
      case 'add_to_command':
        toast({
          title: "Aggiungi a Comanda",
          description: "Funzione aggiunta a comanda in sviluppo",
        })
        break
      case 'remove_from_command':
        toast({
          title: "Rimuovi da Comanda",
          description: "Funzione rimozione da comanda in sviluppo",
        })
        break
      case 'create_command_posa':
        setCreaComandaDialog({ open: true, tipoComanda: 'POSA' })
        break
      case 'create_command_collegamento_partenza':
        setCreaComandaDialog({ open: true, tipoComanda: 'COLLEGAMENTO_PARTENZA' })
        break
      case 'create_command_collegamento_arrivo':
        setCreaComandaDialog({ open: true, tipoComanda: 'COLLEGAMENTO_ARRIVO' })
        break
      case 'create_command_certificazione':
        setCreaComandaDialog({ open: true, tipoComanda: 'CERTIFICAZIONE' })
        break
      case 'add_multiple_to_command':
        toast({
          title: "Aggiungi Tutti a Comanda",
          description: "Funzione aggiunta multipla a comanda in sviluppo",
        })
        break
      case 'remove_multiple_from_commands':
        toast({
          title: "Rimuovi Tutti dalle Comande",
          description: "Funzione rimozione multipla dalle comande in sviluppo",
        })
        break
      default:
        toast({
          title: "Azione non implementata",
          description: `Azione ${action} non ancora implementata`,
        })
        break
    }
  }

  // Gestione successo/errore dialoghi
  const handleDialogSuccess = (message: string) => {
    toast({
      title: "Operazione completata",
      description: message,
    })
    // Ricarica i dati
    loadCavi()
  }

  const handleDialogError = (message: string) => {
    toast({
      title: "Errore",
      description: message,
      variant: "destructive"
    })
  }

  // Handler unificato per la nuova modale
  const handleUnifiedModalSave = async (data: any) => {
    try {
      console.log('🔍 DEBUG handleUnifiedModalSave:', {
        cantiere,
        cantiereId,
        cantiereForDialog,
        isValidCantiere,
        data
      })

      if (!cantiereId || !isValidCantiere) {
        throw new Error('Cantiere non selezionato o non valido')
      }

      let message = ''

      if (data.mode === 'aggiungi_metri') {
        // Gestione inserimento metri
        console.log('🚀 UnifiedModal: Inserimento metri:', data)

        await caviApi.updateMetriPosati(
          cantiereId,
          data.cableId,
          data.metersToInstall,
          data.bobbinId === 'BOBINA_VUOTA' ? 'BOBINA_VUOTA' : data.bobbinId,
          true
        )

        message = `Metri posati aggiornati con successo per il cavo ${data.cableId}`

      } else if (data.mode === 'modifica_bobina') {
        // Gestione modifica bobina
        console.log('🚀 UnifiedModal: Modifica bobina:', data)

        if (data.editOption === 'cambia_bobina') {
          await caviApi.updateMetriPosati(
            cantiereId,
            data.cableId,
            data.newLaidMeters,
            data.newBobbinId,
            true
          )

          message = `Bobina aggiornata con successo per il cavo ${data.cableId}`

        } else if (data.editOption === 'bobina_vuota') {
          await caviApi.updateMetriPosati(
            cantiereId,
            data.cableId,
            data.newLaidMeters,
            'BOBINA_VUOTA',
            false
          )

          message = `Bobina rimossa con successo per il cavo ${data.cableId}`

        } else if (data.editOption === 'annulla_posa') {
          // Implementa logica per annullare la posa
          await caviApi.updateMetriPosati(
            cantiereId,
            data.cableId,
            0,
            'BOBINA_VUOTA',
            false
          )

          message = `Posa annullata con successo per il cavo ${data.cableId}`
        }
      }

      // Ricarica i dati
      await loadCavi()

      // Mostra messaggio di successo
      toast({
        title: "Operazione completata",
        description: message,
        variant: "default"
      })

    } catch (error: any) {
      console.error('Errore unified modal save:', error)
      toast({
        title: "Errore",
        description: error.message || 'Errore durante l\'operazione',
        variant: "destructive"
      })
      throw error // Re-throw per permettere alla modale di gestire l'errore
    }
  }



  // Gestione dei nuovi modali migliorati
  const handleDisconnectSuccess = () => {
    setSuccessToast({ visible: true, message: 'Cavo scollegato con successo' })
    setDisconnectModal({ open: false, cavo: null })
    loadCavi() // Ricarica i dati
  }

  const handleDisconnectError = (error: string) => {
    setCertificationErrorModal({
      open: true,
      cavo: disconnectModal.cavo,
      error
    })
    setDisconnectModal({ open: false, cavo: null })
  }

  const handlePdfSuccess = () => {
    setSuccessToast({ visible: true, message: 'PDF generato con successo' })
    setGeneratePdfModal({ open: false, cavo: null })
  }

  const handlePdfError = (error: string) => {
    setCertificationErrorModal({
      open: true,
      cavo: generatePdfModal.cavo,
      error
    })
    setGeneratePdfModal({ open: false, cavo: null })
  }

  const handleCertificationSuccess = () => {
    setSuccessToast({ visible: true, message: 'Certificazione completata con successo' })
    setCertificationModal({ open: false, cavo: null })
    loadCavi() // Ricarica i dati
  }

  // Gestione certificazione singola
  const handleCertify = async (cavoId: string, formData: any) => {
    try {
      if (!cantiere?.id_cantiere) {
        throw new Error('Cantiere non selezionato')
      }

      // Simula chiamata API per certificazione
      console.log('Certificando cavo:', cavoId, formData)

      // TODO: Implementare chiamata API reale
      await new Promise(resolve => setTimeout(resolve, 1000))

      handleCertificationSuccess()
    } catch (error: any) {
      console.error('Errore certificazione:', error)
      setCertificationErrorModal({
        open: true,
        cavo: certificationModal.cavo,
        error: error.message || 'Errore durante la certificazione'
      })
      setCertificationModal({ open: false, cavo: null })
    }
  }

  // Gestione certificazione multipla
  const handleBulkCertify = async (caviIds: string[], formData: any) => {
    try {
      if (!cantiere?.id_cantiere) {
        throw new Error('Cantiere non selezionato')
      }

      console.log('Certificando cavi multipli:', caviIds, formData)

      // TODO: Implementare chiamata API reale per certificazione multipla
      await new Promise(resolve => setTimeout(resolve, 2000))

      setSuccessToast({
        visible: true,
        message: `${caviIds.length} cavi certificati con successo`
      })
      setBulkCertificationModal({ open: false, cavi: [] })
      loadCavi() // Ricarica i dati
    } catch (error: any) {
      console.error('Errore certificazione multipla:', error)
      setCertificationErrorModal({
        open: true,
        cavo: null,
        error: error.message || 'Errore durante la certificazione multipla'
      })
      setBulkCertificationModal({ open: false, cavi: [] })
    }
  }

  const handleCertificationError = (error: string) => {
    setCertificationErrorModal({
      open: true,
      cavo: certificationModal.cavo,
      error
    })
    setCertificationModal({ open: false, cavo: null })
  }

  // Mostra loader se stiamo caricando i dati dei cavi
  if (loading && isValidCantiere) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Caricamento cavi...</span>
      </div>
    )
  }

  return (
    <CantiereErrorBoundary>
      <div className="max-w-[90%] mx-auto p-6">

        {/* Mostra errore specifico dei cavi se presente */}
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Statistics */}
        <CaviStatistics
        cavi={cavi}
        filteredCavi={filteredCavi}
        revisioneCorrente={revisioneCorrente}
        className="mb-2"
      />

      {/* Tabella Cavi Attivi */}
      <div className="mb-8">
        <CaviTable
          cavi={cavi}
          loading={loading}
          selectionEnabled={selectionEnabled}
          selectedCavi={selectedCavi}
          onSelectionChange={setSelectedCavi}
          onStatusAction={handleStatusAction}
          onContextMenuAction={handleContextMenuAction}
        />
      </div>

      {/* Tabella Cavi Spare */}
      {caviSpare.length > 0 && (
        <div className="mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Package className="h-5 w-5" />
                <span>Cavi Spare ({caviSpare.length})</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <CaviTable
                cavi={caviSpare}
                loading={loading}
                selectionEnabled={false}
                onStatusAction={handleStatusAction}
                onContextMenuAction={handleContextMenuAction}
              />
            </CardContent>
          </Card>
        </div>
      )}



      {/* Nuovi Modali Migliorati */}

      {/* Modale Unificata per Gestione Cavi e Bobine */}
      <UnifiedCableBobbinModal
        mode={unifiedModal.mode || 'aggiungi_metri'}
        open={unifiedModal.open}
        onClose={() => setUnifiedModal({ open: false, mode: null, cavo: null })}
        cavo={unifiedModal.cavo}
        cantiere={cantiereForDialog}
        onSave={handleUnifiedModalSave}
      />

      {/* Modali Legacy (mantenute per compatibilità temporanea) */}
      <InserisciMetriDialogOld
        open={inserisciMetriDialog.open}
        onClose={() => setInserisciMetriDialog({ open: false, cavo: null })}
        cavo={inserisciMetriDialog.cavo}
        cantiere={cantiereForDialog}
        onSuccess={handleDialogSuccess}
        onError={handleDialogError}
      />



      {collegamentiDialog.cavo && (
        <CollegamentiDialogSimple
          open={collegamentiDialog.open}
          onClose={() => setCollegamentiDialog({ open: false, cavo: null })}
          cavo={collegamentiDialog.cavo}
          cantiere={cantiereForDialog}
          onSuccess={() => {
            toast({
              title: "Successo",
              description: "Operazione completata con successo",
            })
            loadCavi() // Ricarica i dati
          }}
        />
      )}

      <DisconnectCableModal
        open={disconnectModal.open}
        onClose={() => setDisconnectModal({ open: false, cavo: null })}
        cavo={disconnectModal.cavo}
        onConfirm={handleDisconnectSuccess}
        onError={handleDisconnectError}
      />

      <GeneratePdfModal
        open={generatePdfModal.open}
        onClose={() => setGeneratePdfModal({ open: false, cavo: null })}
        cavo={generatePdfModal.cavo}
        onSuccess={handlePdfSuccess}
        onError={handlePdfError}
      />

      <CertificationModal
        open={certificationModal.open}
        onClose={() => setCertificationModal({ open: false, cavo: null })}
        cavo={certificationModal.cavo}
        cantiereId={cantiere?.id_cantiere}
        onCertify={handleCertify}
      />

      <BulkCertificationModal
        open={bulkCertificationModal.open}
        onClose={() => setBulkCertificationModal({ open: false, cavi: [] })}
        cavi={bulkCertificationModal.cavi}
        onCertify={handleBulkCertify}
      />

      <CertificationErrorModal
        open={certificationErrorModal.open}
        onClose={() => setCertificationErrorModal({ open: false, cavo: null, error: '' })}
        cavo={certificationErrorModal.cavo}
        error={certificationErrorModal.error}
        onRetry={() => {
          setCertificationErrorModal({ open: false, cavo: null, error: '' })
          // Riapri il modal appropriato basato sul contesto
          if (certificationErrorModal.cavo) {
            setCertificationModal({ open: true, cavo: certificationErrorModal.cavo })
          }
        }}
      />

      <SuccessToast
        visible={successToast.visible}
        message={successToast.message}
        onClose={() => setSuccessToast({ visible: false, message: '' })}
      />

      <CreaComandaDialog
        open={creaComandaDialog.open}
        onClose={() => setCreaComandaDialog({ open: false })}
        caviSelezionati={selectedCavi}
        tipoComanda={creaComandaDialog.tipoComanda}
        onSuccess={handleDialogSuccess}
        onError={handleDialogError}
      />

      <ImportExcelDialog
        open={importExcelDialog.open}
        onClose={() => setImportExcelDialog({ open: false })}
        tipo={importExcelDialog.tipo || 'cavi'}
        onSuccess={handleDialogSuccess}
        onError={handleDialogError}
      />

      <ExportDataDialog
        open={exportDataDialog}
        onClose={() => setExportDataDialog(false)}
        onSuccess={handleDialogSuccess}
        onError={handleDialogError}
      />

      {/* Dialog per gestione cavi */}
      <AggiungiCavoDialog
        open={aggiungiCavoDialog}
        onClose={() => setAggiungiCavoDialog(false)}
        cantiere={cantiere}
        onSuccess={(message) => {
          handleDialogSuccess(message)
          loadCavi() // Ricarica la lista cavi
        }}
        onError={handleDialogError}
      />

      <ModificaCavoDialog
        open={modificaCavoDialog.open}
        onClose={() => setModificaCavoDialog({ open: false, cavo: null })}
        cavo={modificaCavoDialog.cavo}
        cantiere={cantiere}
        onSuccess={(message) => {
          handleDialogSuccess(message)
          loadCavi() // Ricarica la lista cavi
        }}
        onError={handleDialogError}
      />

      <EliminaCavoDialog
        open={eliminaCavoDialog.open}
        onClose={() => setEliminaCavoDialog({ open: false, cavo: null })}
        cavo={eliminaCavoDialog.cavo}
        cantiere={cantiere}
        onSuccess={(message) => {
          handleDialogSuccess(message)
          loadCavi() // Ricarica la lista cavi
        }}
        onError={handleDialogError}
      />

      {/* Dialog per visualizzare dettagli cavo */}
      {dettagliCavoDialog.open && dettagliCavoDialog.cavo && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Dettagli Cavo {dettagliCavoDialog.cavo.id_cavo}</h2>
              <button
                onClick={() => setDettagliCavoDialog({ open: false, cavo: null })}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>

            <div className="grid grid-cols-2 gap-4 text-sm">
              <div><span className="font-medium text-gray-600">ID Cavo:</span> <span className="font-bold">{dettagliCavoDialog.cavo.id_cavo}</span></div>
              <div><span className="font-medium text-gray-600">Tipologia:</span> <span className="font-bold">{dettagliCavoDialog.cavo.tipologia || 'N/A'}</span></div>
              <div><span className="font-medium text-gray-600">Sezione:</span> <span className="font-bold">{dettagliCavoDialog.cavo.sezione || dettagliCavoDialog.cavo.formazione || 'N/A'}</span></div>
              <div><span className="font-medium text-gray-600">Sistema:</span> <span className="font-bold">{dettagliCavoDialog.cavo.sistema || 'N/A'}</span></div>
              <div><span className="font-medium text-gray-600">Utility:</span> <span className="font-bold">{dettagliCavoDialog.cavo.utility || 'N/A'}</span></div>
              <div><span className="font-medium text-gray-600">Colore:</span> <span className="font-bold">{dettagliCavoDialog.cavo.colore_cavo || 'N/A'}</span></div>
              <div><span className="font-medium text-gray-600">Da:</span> <span className="font-bold">{dettagliCavoDialog.cavo.ubicazione_partenza || dettagliCavoDialog.cavo.da || 'N/A'}</span></div>
              <div><span className="font-medium text-gray-600">A:</span> <span className="font-bold">{dettagliCavoDialog.cavo.ubicazione_arrivo || dettagliCavoDialog.cavo.a || 'N/A'}</span></div>
              <div><span className="font-medium text-gray-600">Metri Teorici:</span> <span className="font-bold">{dettagliCavoDialog.cavo.metri_teorici || 0}</span></div>
              <div><span className="font-medium text-gray-600">Metri Posati:</span> <span className="font-bold">{dettagliCavoDialog.cavo.metri_posati || dettagliCavoDialog.cavo.metratura_reale || 0}</span></div>
              <div><span className="font-medium text-gray-600">Stato:</span> <span className="font-bold">{dettagliCavoDialog.cavo.stato_installazione || 'N/A'}</span></div>
              <div><span className="font-medium text-gray-600">Bobina:</span> <span className="font-bold">{dettagliCavoDialog.cavo.id_bobina || 'N/A'}</span></div>
              <div><span className="font-medium text-gray-600">Collegamenti:</span> <span className="font-bold">{dettagliCavoDialog.cavo.collegamento || dettagliCavoDialog.cavo.collegamenti || 0}</span></div>
              <div><span className="font-medium text-gray-600">Modificato Manualmente:</span> <span className="font-bold">{dettagliCavoDialog.cavo.modificato_manualmente || 0}</span></div>
              <div><span className="font-medium text-gray-600">Responsabile Posa:</span> <span className="font-bold">{dettagliCavoDialog.cavo.responsabile_posa || 'N/A'}</span></div>
              <div><span className="font-medium text-gray-600">Timestamp:</span> <span className="font-bold">{dettagliCavoDialog.cavo.timestamp || 'N/A'}</span></div>
            </div>

            <div className="mt-6 flex justify-end">
              <button
                onClick={() => setDettagliCavoDialog({ open: false, cavo: null })}
                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                Chiudi
              </button>
            </div>
          </div>
        </div>
      )}
      </div>
    </CantiereErrorBoundary>
  )
}
