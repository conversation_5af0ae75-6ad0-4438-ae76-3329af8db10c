"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6918],{1243:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},3493:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("cable",[["path",{d:"M17 21v-2a1 1 0 0 1-1-1v-1a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v1a1 1 0 0 1-1 1",key:"10bnsj"}],["path",{d:"M19 15V6.5a1 1 0 0 0-7 0v11a1 1 0 0 1-7 0V9",key:"1eqmu1"}],["path",{d:"M21 21v-2h-4",key:"14zm7j"}],["path",{d:"M3 5h4V3",key:"z442eg"}],["path",{d:"M7 5a1 1 0 0 1 1 1v1a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a1 1 0 0 1 1-1V3",key:"ebdjd7"}]])},4229:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},13717:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},14186:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},17580:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},19420:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},25273:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("clipboard-list",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]])},28883:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},48313:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]])},55863:(e,a,t)=>{t.d(a,{C1:()=>M,bL:()=>A});var r=t(12115),n=t(46081),l=t(63655),d=t(95155),o="Progress",[c,s]=(0,n.A)(o),[i,u]=c(o),h=r.forwardRef((e,a)=>{var t,r,n,o;let{__scopeProgress:c,value:s=null,max:u,getValueLabel:h=v,...p}=e;(u||0===u)&&!m(u)&&console.error((t="".concat(u),r="Progress","Invalid prop `max` of value `".concat(t,"` supplied to `").concat(r,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let y=m(u)?u:100;null===s||x(s,y)||console.error((n="".concat(s),o="Progress","Invalid prop `value` of value `".concat(n,"` supplied to `").concat(o,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let A=x(s,y)?s:null,M=f(A)?h(A,y):void 0;return(0,d.jsx)(i,{scope:c,value:A,max:y,children:(0,d.jsx)(l.sG.div,{"aria-valuemax":y,"aria-valuemin":0,"aria-valuenow":f(A)?A:void 0,"aria-valuetext":M,role:"progressbar","data-state":k(A,y),"data-value":null!=A?A:void 0,"data-max":y,...p,ref:a})})});h.displayName=o;var p="ProgressIndicator",y=r.forwardRef((e,a)=>{var t;let{__scopeProgress:r,...n}=e,o=u(p,r);return(0,d.jsx)(l.sG.div,{"data-state":k(o.value,o.max),"data-value":null!=(t=o.value)?t:void 0,"data-max":o.max,...n,ref:a})});function v(e,a){return"".concat(Math.round(e/a*100),"%")}function k(e,a){return null==e?"indeterminate":e===a?"complete":"loading"}function f(e){return"number"==typeof e}function m(e){return f(e)&&!isNaN(e)&&e>0}function x(e,a){return f(e)&&!isNaN(e)&&e<=a&&e>=0}y.displayName=p;var A=h,M=y},62525:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},69074:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},71007:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},76981:(e,a,t)=>{t.d(a,{C1:()=>g,bL:()=>M});var r=t(12115),n=t(6101),l=t(46081),d=t(85185),o=t(5845),c=t(45503),s=t(11275),i=t(28905),u=t(63655),h=t(95155),p="Checkbox",[y,v]=(0,l.A)(p),[k,f]=y(p);function m(e){let{__scopeCheckbox:a,checked:t,children:n,defaultChecked:l,disabled:d,form:c,name:s,onCheckedChange:i,required:u,value:y="on",internal_do_not_use_render:v}=e,[f,m]=(0,o.i)({prop:t,defaultProp:null!=l&&l,onChange:i,caller:p}),[x,A]=r.useState(null),[M,b]=r.useState(null),g=r.useRef(!1),j=!x||!!c||!!x.closest("form"),w={checked:f,disabled:d,setChecked:m,control:x,setControl:A,name:s,form:c,value:y,hasConsumerStoppedPropagationRef:g,required:u,defaultChecked:!C(l)&&l,isFormControl:j,bubbleInput:M,setBubbleInput:b};return(0,h.jsx)(k,{scope:a,...w,children:"function"==typeof v?v(w):n})}var x="CheckboxTrigger",A=r.forwardRef((e,a)=>{let{__scopeCheckbox:t,onKeyDown:l,onClick:o,...c}=e,{control:s,value:i,disabled:p,checked:y,required:v,setControl:k,setChecked:m,hasConsumerStoppedPropagationRef:A,isFormControl:M,bubbleInput:b}=f(x,t),g=(0,n.s)(a,k),j=r.useRef(y);return r.useEffect(()=>{let e=null==s?void 0:s.form;if(e){let a=()=>m(j.current);return e.addEventListener("reset",a),()=>e.removeEventListener("reset",a)}},[s,m]),(0,h.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":C(y)?"mixed":y,"aria-required":v,"data-state":E(y),"data-disabled":p?"":void 0,disabled:p,value:i,...c,ref:g,onKeyDown:(0,d.m)(l,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,d.m)(o,e=>{m(e=>!!C(e)||!e),b&&M&&(A.current=e.isPropagationStopped(),A.current||e.stopPropagation())})})});A.displayName=x;var M=r.forwardRef((e,a)=>{let{__scopeCheckbox:t,name:r,checked:n,defaultChecked:l,required:d,disabled:o,value:c,onCheckedChange:s,form:i,...u}=e;return(0,h.jsx)(m,{__scopeCheckbox:t,checked:n,defaultChecked:l,disabled:o,required:d,onCheckedChange:s,name:r,form:i,value:c,internal_do_not_use_render:e=>{let{isFormControl:r}=e;return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(A,{...u,ref:a,__scopeCheckbox:t}),r&&(0,h.jsx)(w,{__scopeCheckbox:t})]})}})});M.displayName=p;var b="CheckboxIndicator",g=r.forwardRef((e,a)=>{let{__scopeCheckbox:t,forceMount:r,...n}=e,l=f(b,t);return(0,h.jsx)(i.C,{present:r||C(l.checked)||!0===l.checked,children:(0,h.jsx)(u.sG.span,{"data-state":E(l.checked),"data-disabled":l.disabled?"":void 0,...n,ref:a,style:{pointerEvents:"none",...e.style}})})});g.displayName=b;var j="CheckboxBubbleInput",w=r.forwardRef((e,a)=>{let{__scopeCheckbox:t,...l}=e,{control:d,hasConsumerStoppedPropagationRef:o,checked:i,defaultChecked:p,required:y,disabled:v,name:k,value:m,form:x,bubbleInput:A,setBubbleInput:M}=f(j,t),b=(0,n.s)(a,M),g=(0,c.Z)(i),w=(0,s.X)(d);r.useEffect(()=>{if(!A)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,a=!o.current;if(g!==i&&e){let t=new Event("click",{bubbles:a});A.indeterminate=C(i),e.call(A,!C(i)&&i),A.dispatchEvent(t)}},[A,g,i,o]);let E=r.useRef(!C(i)&&i);return(0,h.jsx)(u.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=p?p:E.current,required:y,disabled:v,name:k,value:m,form:x,...l,tabIndex:-1,ref:b,style:{...l.style,...w,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function C(e){return"indeterminate"===e}function E(e){return C(e)?"indeterminate":e?"checked":"unchecked"}w.displayName=j},79397:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},84616:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},92657:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}}]);