'use client'

import { useAuth } from '@/contexts/AuthContext'
import CertificazioniManager from '@/components/certificazioni/CertificazioniManager'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { AlertCircle } from 'lucide-react'

export default function CertificazioniPage() {
  const { user, cantiere } = useAuth()

  const cantiereId = cantiere?.id_cantiere || user?.id_utente

  if (!cantiereId) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
        <div className="max-w-[90%] mx-auto">
          <Alert className="border-red-200 bg-red-50">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              Cantiere non selezionato. Seleziona un cantiere per accedere alle certificazioni.
            </AlertDescription>
          </Alert>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
      <div className="max-w-[95%] mx-auto">
        <CertificazioniManager cantiereId={cantiereId} />
      </div>
    </div>
  )
}
