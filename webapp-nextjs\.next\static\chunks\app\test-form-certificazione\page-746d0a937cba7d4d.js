(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5408],{4229:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},7946:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>g});var t=a(95155),i=a(12115),l=a(66695),c=a(30285),r=a(26126),n=a(75021),m=a(57434),o=a(50589),d=a(40646),x=a(38164),h=a(381),j=a(71539),p=a(6803);function g(){let[e,s]=(0,i.useState)(!1),[a]=(0,i.useState)(1);return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:[(0,t.jsxs)("div",{className:"max-w-6xl mx-auto space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"text-3xl font-bold text-slate-900 flex items-center gap-3",children:[(0,t.jsx)(n.A,{className:"h-8 w-8 text-blue-600"}),"Test Form Certificazione CEI 64-8"]}),(0,t.jsx)("p",{className:"text-slate-600 mt-1",children:"Test delle automazioni e dell'interfaccia utente"})]}),(0,t.jsxs)(c.$,{onClick:()=>s(!0),disabled:e,children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Apri Form Certificazione"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(o.A,{className:"h-5 w-5 text-blue-500"}),"Dati Meteorologici Automatici"]}),(0,t.jsx)(l.BT,{children:"Test del caricamento automatico"})]}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 text-green-500"}),(0,t.jsx)("span",{className:"text-sm",children:"Caricamento automatico all'apertura del form"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 text-green-500"}),(0,t.jsx)("span",{className:"text-sm",children:"Visualizzazione temperatura e umidit\xe0"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 text-green-500"}),(0,t.jsx)("span",{className:"text-sm",children:"Precompilazione campi temperatura/umidit\xe0"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 text-green-500"}),(0,t.jsx)("span",{className:"text-sm",children:"Indicatore fonte dati (demo/live)"})]})]})})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(x.A,{className:"h-5 w-5 text-green-500"}),"Collegamento Automatico Cavi"]}),(0,t.jsx)(l.BT,{children:"Test della logica CEI 64-8"})]}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 text-green-500"}),(0,t.jsx)("span",{className:"text-sm",children:"Verifica stato collegamento cavo"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 text-green-500"}),(0,t.jsx)("span",{className:"text-sm",children:"Dialog di conferma se non collegato"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 text-green-500"}),(0,t.jsx)("span",{className:"text-sm",children:'Collegamento automatico a "cantiere"'})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 text-green-500"}),(0,t.jsx)("span",{className:"text-sm",children:"Aggiornamento stato cavi"})]})]})})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(h.A,{className:"h-5 w-5 text-purple-500"}),"Validazione Automatica"]}),(0,t.jsx)(l.BT,{children:"Test della conformit\xe0 automatica"})]}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 text-green-500"}),(0,t.jsx)("span",{className:"text-sm",children:"Calcolo automatico conformit\xe0"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 text-green-500"}),(0,t.jsx)("span",{className:"text-sm",children:"Confronto con valore minimo isolamento"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 text-green-500"}),(0,t.jsx)("span",{className:"text-sm",children:"Impostazione stato certificato"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 text-green-500"}),(0,t.jsx)("span",{className:"text-sm",children:"Esito complessivo automatico"})]})]})})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(j.A,{className:"h-5 w-5 text-orange-500"}),"Interfaccia Utente"]}),(0,t.jsx)(l.BT,{children:"Test dell'esperienza utente"})]}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 text-green-500"}),(0,t.jsx)("span",{className:"text-sm",children:"Layout responsive e moderno"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 text-green-500"}),(0,t.jsx)("span",{className:"text-sm",children:"Feedback visivo per operazioni"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 text-green-500"}),(0,t.jsx)("span",{className:"text-sm",children:"Validazione campi in tempo reale"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 text-green-500"}),(0,t.jsx)("span",{className:"text-sm",children:"Gestione errori e stati di caricamento"})]})]})})]})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(n.A,{className:"h-5 w-5"}),"Istruzioni per il Test"]}),(0,t.jsx)(l.BT,{children:"Come testare le funzionalit\xe0 implementate"})]}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-4 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,t.jsx)("h4",{className:"font-semibold text-blue-900 mb-2",children:"1. Test Dati Meteorologici"}),(0,t.jsx)("p",{className:"text-blue-800 text-sm",children:'Apri il form e verifica che nella sezione "Condizioni Ambientali" vengano mostrati automaticamente temperatura e umidit\xe0. I campi dovrebbero essere precompilati.'})]}),(0,t.jsxs)("div",{className:"p-4 bg-green-50 border border-green-200 rounded-lg",children:[(0,t.jsx)("h4",{className:"font-semibold text-green-900 mb-2",children:"2. Test Collegamento Automatico"}),(0,t.jsx)("p",{className:"text-green-800 text-sm",children:"Seleziona un cavo non completamente collegato e prova a salvare. Dovrebbe apparire un dialog di conferma per il collegamento automatico."})]}),(0,t.jsxs)("div",{className:"p-4 bg-purple-50 border border-purple-200 rounded-lg",children:[(0,t.jsx)("h4",{className:"font-semibold text-purple-900 mb-2",children:"3. Test Validazione Automatica"}),(0,t.jsx)("p",{className:"text-purple-800 text-sm",children:'Inserisci un valore di isolamento (es. 600 MΩ) e verifica che lo stato venga automaticamente impostato su "CONFORME" se superiore al minimo.'})]}),(0,t.jsxs)("div",{className:"p-4 bg-orange-50 border border-orange-200 rounded-lg",children:[(0,t.jsx)("h4",{className:"font-semibold text-orange-900 mb-2",children:"4. Test Interfaccia"}),(0,t.jsx)("p",{className:"text-orange-800 text-sm",children:"Verifica la responsivit\xe0, i feedback visivi, la validazione dei campi e la gestione degli stati di caricamento durante le operazioni."})]})]})})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsx)(l.ZB,{children:"Risultati Attesi"}),(0,t.jsx)(l.BT,{children:"Cosa dovrebbe succedere durante il test"})]}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(r.E,{className:"bg-green-100 text-green-800 border-green-200",children:"✅ Dati meteorologici caricati automaticamente"}),(0,t.jsx)(r.E,{className:"bg-green-100 text-green-800 border-green-200",children:"✅ Campi temperatura/umidit\xe0 precompilati"}),(0,t.jsx)(r.E,{className:"bg-green-100 text-green-800 border-green-200",children:"✅ Dialog conferma collegamento automatico"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(r.E,{className:"bg-green-100 text-green-800 border-green-200",children:"✅ Stato conformit\xe0 calcolato automaticamente"}),(0,t.jsx)(r.E,{className:"bg-green-100 text-green-800 border-green-200",children:"✅ Interfaccia responsive e moderna"}),(0,t.jsx)(r.E,{className:"bg-green-100 text-green-800 border-green-200",children:"✅ Feedback visivo per tutte le operazioni"})]})]})})]})]}),e&&(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,t.jsx)("div",{className:"bg-white rounded-lg max-w-6xl w-full max-h-[95vh] overflow-y-auto",children:(0,t.jsx)("div",{className:"p-6",children:(0,t.jsx)(p.A,{cantiereId:a,certificazione:null,strumenti:[],onSuccess:()=>{s(!1),alert("✅ Certificazione creata con successo!\n\nVerifica che:\n- I dati meteorologici siano stati caricati automaticamente\n- Il collegamento automatico sia stato eseguito se necessario\n- La conformit\xe0 sia stata determinata automaticamente")},onCancel:()=>{s(!1)}})})})})]})}},25811:(e,s,a)=>{Promise.resolve().then(a.bind(a,7946))},71539:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[3455,3464,9816,9384,5171,9972,7697,8441,1684,7358],()=>s(25811)),_N_E=e.O()}]);