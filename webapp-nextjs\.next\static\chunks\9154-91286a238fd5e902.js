"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9154],{6803:(e,a,t)=>{t.d(a,{A:()=>y});var s=t(95155),r=t(30285),i=t(55365),n=t(51154),l=t(57434),o=t(54416),d=t(85339),c=t(4229),m=t(12115),x=t(25731),u=t(85057),v=t(59409);function p(e){var a,t;let{formData:r,cavi:i,responsabili:n,strumenti:l,validationErrors:o,isCavoLocked:d,onInputChange:c}=e;return(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(u.J,{htmlFor:"id_cavo",className:"text-sm font-medium text-gray-700",children:"Cavo *"}),(0,s.jsxs)(v.l6,{value:r.id_cavo,onValueChange:e=>c("id_cavo",e),disabled:d,children:[(0,s.jsx)(v.bq,{className:"h-11 text-sm ".concat(o.id_cavo?"border-red-500":"border-gray-300"),children:(0,s.jsx)(v.yv,{placeholder:"Seleziona cavo..."})}),(0,s.jsx)(v.gC,{children:i.map(e=>(0,s.jsx)(v.eb,{value:e.id_cavo,children:(0,s.jsxs)("div",{className:"flex flex-col py-1",children:[(0,s.jsx)("span",{className:"font-medium text-sm",children:e.id_cavo}),(0,s.jsxs)("span",{className:"text-xs text-gray-500",children:[e.tipologia," ",e.sezione]})]})},e.id_cavo))})]}),o.id_cavo&&(0,s.jsx)("p",{className:"text-sm text-red-600",children:o.id_cavo})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(u.J,{htmlFor:"id_operatore",className:"text-sm font-medium text-gray-700",children:"Operatore"}),(0,s.jsxs)(v.l6,{value:(null==(a=r.id_operatore)?void 0:a.toString())||"",onValueChange:e=>c("id_operatore",parseInt(e)),children:[(0,s.jsx)(v.bq,{className:"h-11 text-sm border-gray-300",children:(0,s.jsx)(v.yv,{placeholder:"Seleziona operatore..."})}),(0,s.jsx)(v.gC,{children:n.map(e=>(0,s.jsx)(v.eb,{value:e.id_responsabile.toString(),children:(0,s.jsx)("span",{className:"text-sm",children:e.nome_responsabile})},e.id_responsabile))})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(u.J,{htmlFor:"id_strumento",className:"text-sm font-medium text-gray-700",children:"Strumento di Misura"}),(0,s.jsxs)(v.l6,{value:(null==(t=r.id_strumento)?void 0:t.toString())||"",onValueChange:e=>{let a=l.find(a=>a.id_strumento===parseInt(e));c("id_strumento",parseInt(e)),a&&c("strumento_utilizzato","".concat(a.marca," ").concat(a.modello))},children:[(0,s.jsx)(v.bq,{className:"h-11 text-sm border-gray-300",children:(0,s.jsx)(v.yv,{placeholder:"Seleziona strumento..."})}),(0,s.jsx)(v.gC,{children:l.map(e=>(0,s.jsx)(v.eb,{value:e.id_strumento.toString(),children:(0,s.jsxs)("div",{className:"flex flex-col py-1",children:[(0,s.jsx)("span",{className:"font-medium text-sm",children:e.nome}),(0,s.jsxs)("span",{className:"text-xs text-gray-500",children:[e.marca," ",e.modello]})]})},e.id_strumento))})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(u.J,{htmlFor:"tipo_certificato",className:"text-sm font-medium text-gray-700",children:"Tipo Certificato"}),(0,s.jsxs)(v.l6,{value:r.tipo_certificato||"SINGOLO",onValueChange:e=>c("tipo_certificato",e),children:[(0,s.jsx)(v.bq,{className:"h-11 text-sm border-gray-300",children:(0,s.jsx)(v.yv,{})}),(0,s.jsxs)(v.gC,{children:[(0,s.jsx)(v.eb,{value:"SINGOLO",children:(0,s.jsx)("span",{className:"text-sm",children:"\uD83D\uDD0D Singolo"})}),(0,s.jsx)(v.eb,{value:"GRUPPO",children:(0,s.jsx)("span",{className:"text-sm",children:"\uD83D\uDCCA Gruppo"})})]})]})]})]})}var g=t(62523),h=t(381);function f(e){let{formData:a,weatherData:t,isLoadingWeather:i,isWeatherOverride:l,onInputChange:d,onToggleWeatherOverride:c}=e;return t?(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsx)("div",{className:"p-4 rounded-lg border-2 col-span-full ".concat(t.isDemo?"bg-amber-50 border-amber-200":"bg-emerald-50 border-emerald-200"),children:(0,s.jsxs)("div",{className:"flex items-start gap-4",children:[i?(0,s.jsx)(n.A,{className:"h-5 w-5 animate-spin text-blue-600 mt-1"}):(0,s.jsx)("div",{className:"text-2xl",children:t.isDemo?"\uD83D\uDD27":"\uD83C\uDF24️"}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"text-lg font-semibold text-gray-900",children:[t.temperature,"\xb0C • ",t.humidity,"% UR"]}),t.city&&(0,s.jsx)("div",{className:"text-sm text-gray-600",children:t.city})]}),(0,s.jsx)(r.$,{type:"button",variant:l?"default":"outline",size:"sm",onClick:c,className:"h-8",children:l?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o.A,{className:"h-3 w-3 mr-1"}),"Automatico"]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(h.A,{className:"h-3 w-3 mr-1"}),"Manuale"]})})]}),(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:["\uD83D\uDCE1 ",t.source,t.isDemo&&" • Dati dimostrativi"]})]})]})}),l&&(0,s.jsxs)("div",{className:"col-span-full p-4 bg-blue-50 border-2 border-blue-200 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,s.jsx)(h.A,{className:"h-4 w-4 text-blue-600"}),(0,s.jsx)("span",{className:"text-sm font-medium text-blue-800",children:"Inserimento Manuale Parametri"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(u.J,{htmlFor:"temperatura_prova",className:"text-sm font-medium text-gray-700",children:"Temperatura (\xb0C)"}),(0,s.jsx)(g.p,{id:"temperatura_prova",type:"number",step:"0.1",value:a.temperatura_prova||"",onChange:e=>d("temperatura_prova",parseFloat(e.target.value)),placeholder:"20.0",className:"h-11 text-sm"}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Range tipico: 15-30\xb0C"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(u.J,{htmlFor:"umidita_prova",className:"text-sm font-medium text-gray-700",children:"Umidit\xe0 Relativa (%)"}),(0,s.jsx)(g.p,{id:"umidita_prova",type:"number",min:"0",max:"100",value:a.umidita_prova||"",onChange:e=>d("umidita_prova",parseFloat(e.target.value)),placeholder:"50",className:"h-11 text-sm"}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Range tipico: 30-70%"})]})]})]})]}):null}function b(e){let{formData:a,validationErrors:t,onInputChange:r}=e;return(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(u.J,{htmlFor:"valore_isolamento",className:"text-sm font-medium text-gray-700",children:"Isolamento (MΩ) *"}),(0,s.jsx)(g.p,{id:"valore_isolamento",type:"number",step:"0.01",value:a.valore_isolamento||"",onChange:e=>r("valore_isolamento",parseFloat(e.target.value)),placeholder:"≥ 1000",className:"h-11 text-sm ".concat(t.valore_isolamento?"border-red-500":"border-gray-300")}),t.valore_isolamento&&(0,s.jsx)("p",{className:"text-sm text-red-600",children:t.valore_isolamento}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Valore minimo: 1000 MΩ"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(u.J,{htmlFor:"valore_continuita",className:"text-sm font-medium text-gray-700",children:"Test Continuit\xe0 *"}),(0,s.jsxs)(v.l6,{value:a.valore_continuita,onValueChange:e=>r("valore_continuita",e),children:[(0,s.jsx)(v.bq,{className:"h-11 text-sm ".concat(t.valore_continuita?"border-red-500":"border-gray-300"),children:(0,s.jsx)(v.yv,{placeholder:"Seleziona esito..."})}),(0,s.jsxs)(v.gC,{children:[(0,s.jsx)(v.eb,{value:"CONFORME",children:(0,s.jsx)("span",{className:"text-sm",children:"✅ CONFORME"})}),(0,s.jsx)(v.eb,{value:"NON_CONFORME",children:(0,s.jsx)("span",{className:"text-sm",children:"❌ NON CONFORME"})})]})]}),t.valore_continuita&&(0,s.jsx)("p",{className:"text-sm text-red-600",children:t.valore_continuita})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(u.J,{htmlFor:"valore_resistenza",className:"text-sm font-medium text-gray-700",children:"Resistenza (Ω) *"}),(0,s.jsx)(g.p,{id:"valore_resistenza",type:"number",step:"0.01",value:a.valore_resistenza||"",onChange:e=>r("valore_resistenza",parseFloat(e.target.value)),placeholder:"< 1.0",className:"h-11 text-sm ".concat(t.valore_resistenza?"border-red-500":"border-gray-300")}),t.valore_resistenza&&(0,s.jsx)("p",{className:"text-sm text-red-600",children:t.valore_resistenza}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Valore massimo: 1.0 Ω"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(u.J,{htmlFor:"tensione_prova_isolamento",className:"text-sm font-medium text-gray-700",children:"Tensione di Prova (V)"}),(0,s.jsx)(g.p,{id:"tensione_prova_isolamento",type:"number",value:a.tensione_prova_isolamento||500,onChange:e=>r("tensione_prova_isolamento",parseInt(e.target.value)),className:"h-11 text-sm border-gray-300"}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Standard: 500V DC"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(u.J,{htmlFor:"durata_prova",className:"text-sm font-medium text-gray-700",children:"Durata Prova (min)"}),(0,s.jsx)(g.p,{id:"durata_prova",type:"number",value:a.durata_prova||1,onChange:e=>r("durata_prova",parseInt(e.target.value)),placeholder:"1",className:"h-11 text-sm border-gray-300"}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Standard: 1 minuto"})]})]})}var j=t(88539);function N(e){let{formData:a,onInputChange:t}=e;return(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-2 col-span-full",children:[(0,s.jsx)(u.J,{htmlFor:"note",className:"text-sm font-medium text-gray-700",children:"Note Aggiuntive"}),(0,s.jsx)(j.T,{id:"note",value:a.note||"",onChange:e=>t("note",e.target.value),placeholder:"Inserisci eventuali note, osservazioni o anomalie riscontrate durante la certificazione...",rows:4,className:"resize-none text-sm border-gray-300"}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Descrivi eventuali condizioni particolari o osservazioni rilevanti"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(u.J,{htmlFor:"stato_certificato",className:"text-sm font-medium text-gray-700",children:"Stato Certificazione"}),(0,s.jsxs)(v.l6,{value:a.stato_certificato||"BOZZA",onValueChange:e=>t("stato_certificato",e),children:[(0,s.jsx)(v.bq,{className:"h-11 text-sm border-gray-300",children:(0,s.jsx)(v.yv,{})}),(0,s.jsxs)(v.gC,{children:[(0,s.jsx)(v.eb,{value:"BOZZA",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-3 h-3 rounded-full bg-gray-400"}),(0,s.jsx)("span",{className:"text-sm",children:"Bozza"})]})}),(0,s.jsx)(v.eb,{value:"CONFORME",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-3 h-3 rounded-full bg-green-500"}),(0,s.jsx)("span",{className:"text-sm",children:"✅ Conforme"})]})}),(0,s.jsx)(v.eb,{value:"NON_CONFORME",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-3 h-3 rounded-full bg-red-500"}),(0,s.jsx)("span",{className:"text-sm",children:"❌ Non Conforme"})]})}),(0,s.jsx)(v.eb,{value:"CONFORME_CON_RISERVA",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-3 h-3 rounded-full bg-yellow-500"}),(0,s.jsx)("span",{className:"text-sm",children:"⚠️ Conforme con Riserva"})]})})]})]}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Lo stato verr\xe0 determinato automaticamente in base ai valori misurati"})]})]})}function y(e){let{cantiereId:a,certificazione:t,strumenti:u,preselectedCavoId:v,onSuccess:g,onCancel:h}=e,{formData:j,cavi:y,responsabili:_,weatherData:w,isLoading:C,isSaving:z,isLoadingWeather:D,error:k,validationErrors:F,isWeatherOverride:O,isEdit:S,isCavoLocked:A,handleInputChange:E,handleSubmit:I,setIsWeatherOverride:R,onCancel:M}=function(e){let{cantiereId:a,certificazione:t,strumenti:s,preselectedCavoId:r,onSuccess:i,onCancel:n}=e,[l,o]=(0,m.useState)({id_cantiere:a,tipo_certificato:"SINGOLO",stato_certificato:"BOZZA",valore_continuita:"CONFORME",tensione_prova_isolamento:500,temperatura_prova:20,umidita_prova:50}),[d,c]=(0,m.useState)(!0),[u,v]=(0,m.useState)(!1),[p,g]=(0,m.useState)(!1),[h,f]=(0,m.useState)(""),[b,j]=(0,m.useState)({}),[N,y]=(0,m.useState)(!1),[_,w]=(0,m.useState)([]),[C,z]=(0,m.useState)([]),[D,k]=(0,m.useState)(null),F=!!t,O=!!r;async function S(){try{c(!0);let[e,t]=await Promise.all([x.At.getCavi(a),x.AR.getResponsabili(a)]);w(e.data||[]),z(t.data||[])}catch(e){console.error("Errore caricamento dati:",e),f("Errore nel caricamento dei dati iniziali")}finally{c(!1)}}async function A(){try{g(!0);let e=await x._I.getWeatherData(a);e.data&&k(e.data)}catch(e){console.error("Errore caricamento meteo:",e)}finally{g(!1)}}async function E(){if(!function(){let e={};return l.id_cavo||(e.id_cavo="Seleziona un cavo"),(!l.valore_isolamento||l.valore_isolamento<=0)&&(e.valore_isolamento="Inserisci un valore di isolamento valido"),l.valore_continuita||(e.valore_continuita="Seleziona il risultato della continuit\xe0"),l.valore_resistenza||(e.valore_resistenza="Inserisci un valore di resistenza"),j(e),0===Object.keys(e).length}())return void f("Correggi gli errori nel form prima di continuare");try{let e;v(!0),f("");let s=function(){let e={...l};return D&&!N&&(e.temperatura_prova=D.temperature,e.umidita_prova=D.humidity),e.id_cantiere=a,e.data_certificazione=e.data_certificazione||new Date().toISOString().split("T")[0],e}();(e=F&&t?await x.km.updateCertificazione(t.id_certificazione,s):await x.km.createCertificazione(s)).data&&i(e.data)}catch(a){var e,s;console.error("Errore salvataggio:",a),f((null==(s=a.response)||null==(e=s.data)?void 0:e.detail)||"Errore durante il salvataggio")}finally{v(!1)}}return(0,m.useEffect)(()=>{S(),A()},[a]),(0,m.useEffect)(()=>{t&&o({...t,id_cantiere:a})},[t,a]),(0,m.useEffect)(()=>{r&&!t&&o(e=>({...e,id_cavo:r}))},[r,t]),(0,m.useEffect)(()=>{!D||t||N||o(e=>({...e,temperatura_prova:D.temperature,umidita_prova:D.humidity}))},[D,t,N]),{formData:l,cavi:_,responsabili:C,strumenti:s,weatherData:D,isLoading:d,isSaving:u,isLoadingWeather:p,error:h,validationErrors:b,isWeatherOverride:N,isEdit:F,isCavoLocked:O,handleInputChange:function(e,a){o(t=>({...t,[e]:a})),b[e]&&j(a=>{let t={...a};return delete t[e],t})},handleSubmit:E,setIsWeatherOverride:y,onCancel:n}}({cantiereId:a,certificazione:t,strumenti:u,preselectedCavoId:v,onSuccess:g,onCancel:h});return C?(0,s.jsx)("div",{className:"flex items-center justify-center h-96",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(n.A,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Caricamento dati certificazione..."})]})}):(0,s.jsxs)("div",{className:"h-full w-full bg-gray-50",children:[(0,s.jsx)("div",{className:"bg-white border-b border-gray-200 sticky top-0 z-10",children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto px-6 py-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,s.jsx)(l.A,{className:"h-6 w-6 text-blue-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:S?"Modifica Certificazione":"Nuova Certificazione CEI 64-8"}),v&&(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["Cavo: ",v]})]})]}),(0,s.jsx)(r.$,{variant:"ghost",size:"sm",onClick:h,className:"text-gray-400 hover:text-gray-600",children:(0,s.jsx)(o.A,{className:"h-5 w-5"})})]})})}),k&&(0,s.jsx)("div",{className:"bg-red-50 border-b border-red-200",children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto px-6 py-4",children:(0,s.jsxs)(i.Fc,{variant:"destructive",className:"border-red-200 bg-red-50",children:[(0,s.jsx)(d.A,{className:"h-4 w-4"}),(0,s.jsx)(i.TN,{children:k})]})})}),(0,s.jsx)("div",{className:"max-w-4xl mx-auto px-6 py-8",children:(0,s.jsxs)("form",{onSubmit:e=>{e.preventDefault(),I()},className:"space-y-8",children:[(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden",children:[(0,s.jsxs)("div",{className:"px-6 py-4 border-b border-gray-100 bg-gray-50",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"\uD83D\uDCCB Informazioni Base"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Dati principali del cavo e operatore"})]}),(0,s.jsx)("div",{className:"p-6",children:(0,s.jsx)(p,{formData:j,cavi:y,responsabili:_,strumenti:u,validationErrors:F,isCavoLocked:A,onInputChange:E})})]}),(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden",children:[(0,s.jsxs)("div",{className:"px-6 py-4 border-b border-gray-100 bg-gray-50",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"\uD83C\uDF24️ Condizioni Ambientali"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Temperatura e umidit\xe0 durante la certificazione"})]}),(0,s.jsx)("div",{className:"p-6",children:(0,s.jsx)(f,{formData:j,weatherData:w,isLoadingWeather:D,isWeatherOverride:O,onInputChange:E,onToggleWeatherOverride:()=>R(!O)})})]}),(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden",children:[(0,s.jsxs)("div",{className:"px-6 py-4 border-b border-gray-100 bg-gray-50",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"⚡ Misurazioni e Test CEI 64-8"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Valori di isolamento, continuit\xe0 e parametri di prova"})]}),(0,s.jsx)("div",{className:"p-6",children:(0,s.jsx)(b,{formData:j,validationErrors:F,onInputChange:E})})]}),(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden",children:[(0,s.jsxs)("div",{className:"px-6 py-4 border-b border-gray-100 bg-gray-50",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"\uD83D\uDCDD Note e Stato Certificazione"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Osservazioni aggiuntive e stato finale"})]}),(0,s.jsx)("div",{className:"p-6",children:(0,s.jsx)(N,{formData:j,onInputChange:E})})]}),(0,s.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:(0,s.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,s.jsx)(r.$,{type:"button",variant:"outline",onClick:h,disabled:z,className:"px-8 py-2",children:"Annulla"}),(0,s.jsx)(r.$,{type:"submit",disabled:z,className:"px-8 py-2 bg-blue-600 hover:bg-blue-700",children:z?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.A,{className:"h-4 w-4 animate-spin mr-2"}),"Salvando..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(c.A,{className:"h-4 w-4 mr-2"}),S?"Aggiorna Certificazione":"Salva Certificazione"]})})]})})]})})]})}},26126:(e,a,t)=>{t.d(a,{E:()=>o});var s=t(95155);t(12115);var r=t(99708),i=t(74466),n=t(59434);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:a,variant:t,asChild:i=!1,...o}=e,d=i?r.DX:"span";return(0,s.jsx)(d,{"data-slot":"badge",className:(0,n.cn)(l({variant:t}),a),...o})}},30285:(e,a,t)=>{t.d(a,{$:()=>o});var s=t(95155);t(12115);var r=t(99708),i=t(74466),n=t(59434);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:a,variant:t,size:i,asChild:o=!1,...d}=e,c=o?r.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,n.cn)(l({variant:t,size:i,className:a})),...d})}},55365:(e,a,t)=>{t.d(a,{Fc:()=>o,TN:()=>d});var s=t(95155),r=t(12115),i=t(74466),n=t(59434);let l=(0,i.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=r.forwardRef((e,a)=>{let{className:t,variant:r,...i}=e;return(0,s.jsx)("div",{ref:a,role:"alert",className:(0,n.cn)(l({variant:r}),t),...i})});o.displayName="Alert",r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("h5",{ref:a,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",t),...r})}).displayName="AlertTitle";let d=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("div",{ref:a,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",t),...r})});d.displayName="AlertDescription"},59409:(e,a,t)=>{t.d(a,{bq:()=>m,eb:()=>u,gC:()=>x,l6:()=>d,yv:()=>c});var s=t(95155);t(12115);var r=t(38715),i=t(66474),n=t(5196),l=t(47863),o=t(59434);function d(e){let{...a}=e;return(0,s.jsx)(r.bL,{"data-slot":"select",...a})}function c(e){let{...a}=e;return(0,s.jsx)(r.WT,{"data-slot":"select-value",...a})}function m(e){let{className:a,size:t="default",children:n,...l}=e;return(0,s.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":t,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...l,children:[n,(0,s.jsx)(r.In,{asChild:!0,children:(0,s.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function x(e){let{className:a,children:t,position:i="popper",...n}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:i,...n,children:[(0,s.jsx)(v,{}),(0,s.jsx)(r.LM,{className:(0,o.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,s.jsx)(p,{})]})})}function u(e){let{className:a,children:t,...i}=e;return(0,s.jsxs)(r.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",a),...i,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(n.A,{className:"size-4"})})}),(0,s.jsx)(r.p4,{children:t})]})}function v(e){let{className:a,...t}=e;return(0,s.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",a),...t,children:(0,s.jsx)(l.A,{className:"size-4"})})}function p(e){let{className:a,...t}=e;return(0,s.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",a),...t,children:(0,s.jsx)(i.A,{className:"size-4"})})}},59434:(e,a,t)=>{t.d(a,{cn:()=>i});var s=t(52596),r=t(39688);function i(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,r.QP)((0,s.$)(a))}},62523:(e,a,t)=>{t.d(a,{p:()=>n});var s=t(95155),r=t(12115),i=t(59434);let n=r.forwardRef((e,a)=>{let{className:t,type:r,...n}=e;return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),ref:a,...n})});n.displayName="Input"},66695:(e,a,t)=>{t.d(a,{BT:()=>o,Wu:()=>d,ZB:()=>l,Zp:()=>i,aR:()=>n});var s=t(95155);t(12115);var r=t(59434);function i(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...t})}function n(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...t})}function l(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",a),...t})}function o(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",a),...t})}function d(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",a),...t})}},85057:(e,a,t)=>{t.d(a,{J:()=>n});var s=t(95155);t(12115);var r=t(40968),i=t(59434);function n(e){let{className:a,...t}=e;return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...t})}},88539:(e,a,t)=>{t.d(a,{T:()=>n});var s=t(95155),r=t(12115),i=t(59434);let n=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:a,...r})});n.displayName="Textarea"}}]);