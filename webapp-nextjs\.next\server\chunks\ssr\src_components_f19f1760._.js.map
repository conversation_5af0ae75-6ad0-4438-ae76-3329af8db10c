{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { CheckIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Checkbox({\n  className,\n  ...props\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\n  return (\n    <CheckboxPrimitive.Root\n      data-slot=\"checkbox\"\n      className={cn(\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <CheckboxPrimitive.Indicator\n        data-slot=\"checkbox-indicator\"\n        className=\"flex items-center justify-center text-current transition-none\"\n      >\n        <CheckIcon className=\"size-3.5\" />\n      </CheckboxPrimitive.Indicator>\n    </CheckboxPrimitive.Root>\n  )\n}\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,oKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,8OAAC,wMAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B", "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm border-collapse\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"data-[state=selected]:bg-muted border-b\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;YAC9D,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2CACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 313, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        data-slot=\"input\"\n        className={cn(\n          \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n          \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n          \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\n\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 343, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 568, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Popover = PopoverPrimitive.Root\n\nconst PopoverTrigger = PopoverPrimitive.Trigger\n\nconst PopoverContent = React.forwardRef<\n  React.ElementRef<typeof PopoverPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>\n>(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => (\n  <PopoverPrimitive.Portal>\n    <PopoverPrimitive.Content\n      ref={ref}\n      align={align}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        className\n      )}\n      {...props}\n    />\n  </PopoverPrimitive.Portal>\n))\nPopoverContent.displayName = PopoverPrimitive.Content.displayName\n\nexport { Popover, PopoverTrigger, PopoverContent }\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,UAAU,mKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,mKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,QAAQ,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC5D,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,KAAK;YACL,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8aACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,eAAe,WAAW,GAAG,mKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 609, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 783, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 811, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 839, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/radio-group.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as RadioGroupPrimitive from \"@radix-ui/react-radio-group\"\nimport { Circle } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst RadioGroup = React.forwardRef<\n  React.ElementRef<typeof RadioGroupPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>\n>(({ className, ...props }, ref) => {\n  return (\n    <RadioGroupPrimitive.Root\n      className={cn(\"grid gap-2\", className)}\n      {...props}\n      ref={ref}\n    />\n  )\n})\nRadioGroup.displayName = RadioGroupPrimitive.Root.displayName\n\nconst RadioGroupItem = React.forwardRef<\n  React.ElementRef<typeof RadioGroupPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>\n>(({ className, ...props }, ref) => {\n  return (\n    <RadioGroupPrimitive.Item\n      ref={ref}\n      className={cn(\n        \"aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <RadioGroupPrimitive.Indicator className=\"flex items-center justify-center\">\n        <Circle className=\"h-2.5 w-2.5 fill-current text-current\" />\n      </RadioGroupPrimitive.Indicator>\n    </RadioGroupPrimitive.Item>\n  )\n})\nRadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName\n\nexport { RadioGroup, RadioGroupItem }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC,0KAAA,CAAA,OAAwB;QACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;QAC3B,GAAG,KAAK;QACT,KAAK;;;;;;AAGX;AACA,WAAW,WAAW,GAAG,0KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC,0KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4OACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,0KAAA,CAAA,YAA6B;YAAC,WAAU;sBACvC,cAAA,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI1B;AACA,eAAe,WAAW,GAAG,0KAAA,CAAA,OAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 899, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cantiere/CantiereErrorBoundary.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { AlertCircle, Construction, RefreshCw, ArrowLeft } from 'lucide-react'\nimport { useRouter } from 'next/navigation'\nimport { useCantiere } from '@/hooks/useCantiere'\n\ninterface CantiereErrorBoundaryProps {\n  children: React.ReactNode\n  fallback?: React.ReactNode\n  showBackButton?: boolean\n  backUrl?: string\n}\n\n/**\n * Componente per gestire errori relativi alla selezione del cantiere\n * Mostra messaggi di errore appropriati e opzioni di recupero\n */\nexport function CantiereErrorBoundary({ \n  children, \n  fallback, \n  showBackButton = true, \n  backUrl = '/cantieri' \n}: CantiereErrorBoundaryProps) {\n  const router = useRouter()\n  const { cantiereId, cantiere, isValidCantiere, isLoading, error, clearError } = useCantiere()\n\n  // Se stiamo caricando, mostra un loader\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6\">\n        <div className=\"max-w-4xl mx-auto\">\n          <Card>\n            <CardContent className=\"flex items-center justify-center p-8\">\n              <div className=\"text-center\">\n                <RefreshCw className=\"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600\" />\n                <p className=\"text-lg font-medium text-gray-700\">Caricamento cantiere...</p>\n                <p className=\"text-sm text-gray-500 mt-2\">Verifica della selezione cantiere in corso</p>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    )\n  }\n\n  // Se c'è un errore o il cantiere non è valido, mostra l'errore\n  if (error || !isValidCantiere) {\n    const errorMessage = error || 'Nessun cantiere selezionato'\n    \n    if (fallback) {\n      return <>{fallback}</>\n    }\n\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6\">\n        <div className=\"max-w-4xl mx-auto space-y-6\">\n          \n          {/* Header con errore */}\n          <Card className=\"border-red-200 bg-red-50\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center text-red-800\">\n                <AlertCircle className=\"h-6 w-6 mr-2\" />\n                Problema con la selezione del cantiere\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <Alert variant=\"destructive\">\n                <AlertCircle className=\"h-4 w-4\" />\n                <AlertDescription>\n                  <strong>Errore:</strong> {errorMessage}\n                </AlertDescription>\n              </Alert>\n            </CardContent>\n          </Card>\n\n          {/* Informazioni di debug */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center text-gray-700\">\n                <Construction className=\"h-5 w-5 mr-2\" />\n                Informazioni cantiere\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-3\">\n              <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                <div>\n                  <span className=\"font-medium text-gray-600\">ID Cantiere:</span>\n                  <span className=\"ml-2 text-gray-800\">{cantiereId || 'Non disponibile'}</span>\n                </div>\n                <div>\n                  <span className=\"font-medium text-gray-600\">Nome Cantiere:</span>\n                  <span className=\"ml-2 text-gray-800\">{cantiere?.commessa || 'Non disponibile'}</span>\n                </div>\n                <div>\n                  <span className=\"font-medium text-gray-600\">Cantiere Valido:</span>\n                  <span className={`ml-2 ${isValidCantiere ? 'text-green-600' : 'text-red-600'}`}>\n                    {isValidCantiere ? 'Sì' : 'No'}\n                  </span>\n                </div>\n                <div>\n                  <span className=\"font-medium text-gray-600\">localStorage ID:</span>\n                  <span className=\"ml-2 text-gray-800\">\n                    {typeof window !== 'undefined' ? localStorage.getItem('selectedCantiereId') || 'Non presente' : 'N/A'}\n                  </span>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Azioni di recupero */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-gray-700\">Azioni disponibili</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-3\">\n              <div className=\"flex flex-wrap gap-3\">\n                \n                {/* Pulsante per tornare ai cantieri */}\n                {showBackButton && (\n                  <Button \n                    onClick={() => router.push(backUrl)}\n                    variant=\"default\"\n                    className=\"flex items-center\"\n                  >\n                    <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                    Seleziona Cantiere\n                  </Button>\n                )}\n\n                {/* Pulsante per pulire errore */}\n                {error && (\n                  <Button \n                    onClick={clearError}\n                    variant=\"outline\"\n                    className=\"flex items-center\"\n                  >\n                    <RefreshCw className=\"h-4 w-4 mr-2\" />\n                    Riprova\n                  </Button>\n                )}\n\n                {/* Pulsante per pulire localStorage */}\n                <Button \n                  onClick={() => {\n                    localStorage.removeItem('selectedCantiereId')\n                    localStorage.removeItem('selectedCantiereName')\n                    localStorage.removeItem('cantiere_data')\n                    window.location.reload()\n                  }}\n                  variant=\"outline\"\n                  className=\"flex items-center text-orange-600 border-orange-300 hover:bg-orange-50\"\n                >\n                  <AlertCircle className=\"h-4 w-4 mr-2\" />\n                  Reset Dati Cantiere\n                </Button>\n              </div>\n\n              <div className=\"mt-4 p-3 bg-blue-50 rounded-lg\">\n                <p className=\"text-sm text-blue-800\">\n                  <strong>Suggerimento:</strong> Se il problema persiste, prova a selezionare nuovamente un cantiere \n                  dalla pagina principale o contatta l'amministratore del sistema.\n                </p>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    )\n  }\n\n  // Se tutto è ok, renderizza i children\n  return <>{children}</>\n}\n\n/**\n * Hook per utilizzare il CantiereErrorBoundary in modo condizionale\n */\nexport function useCantiereErrorBoundary() {\n  const { isValidCantiere, isLoading, error } = useCantiere()\n  \n  return {\n    shouldShowError: !isLoading && (!isValidCantiere || error),\n    isLoading,\n    error\n  }\n}\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AARA;;;;;;;;AAqBO,SAAS,sBAAsB,EACpC,QAAQ,EACR,QAAQ,EACR,iBAAiB,IAAI,EACrB,UAAU,WAAW,EACM;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,eAAe,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAE1F,wCAAwC;IACxC,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;8BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;oCAAE,WAAU;8CAAoC;;;;;;8CACjD,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOxD;IAEA,+DAA+D;IAC/D,IAAI,SAAS,CAAC,iBAAiB;QAC7B,MAAM,eAAe,SAAS;QAE9B,IAAI,UAAU;YACZ,qBAAO;0BAAG;;QACZ;QAEA,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAGb,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAI5C,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;;sDACb,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC,iIAAA,CAAA,mBAAgB;;8DACf,8OAAC;8DAAO;;;;;;gDAAgB;gDAAE;;;;;;;;;;;;;;;;;;;;;;;;kCAOlC,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,kNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAI7C,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAA4B;;;;;;8DAC5C,8OAAC;oDAAK,WAAU;8DAAsB,cAAc;;;;;;;;;;;;sDAEtD,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAA4B;;;;;;8DAC5C,8OAAC;oDAAK,WAAU;8DAAsB,UAAU,YAAY;;;;;;;;;;;;sDAE9D,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAA4B;;;;;;8DAC5C,8OAAC;oDAAK,WAAW,CAAC,KAAK,EAAE,kBAAkB,mBAAmB,gBAAgB;8DAC3E,kBAAkB,OAAO;;;;;;;;;;;;sDAG9B,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAA4B;;;;;;8DAC5C,8OAAC;oDAAK,WAAU;8DACb,6EAA+F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ1G,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAgB;;;;;;;;;;;0CAEvC,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;4CAGZ,gCACC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,SAAQ;gDACR,WAAU;;kEAEV,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;4CAMzC,uBACC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS;gDACT,SAAQ;gDACR,WAAU;;kEAEV,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAM1C,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS;oDACP,aAAa,UAAU,CAAC;oDACxB,aAAa,UAAU,CAAC;oDACxB,aAAa,UAAU,CAAC;oDACxB,OAAO,QAAQ,CAAC,MAAM;gDACxB;gDACA,SAAQ;gDACR,WAAU;;kEAEV,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAK5C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;;8DACX,8OAAC;8DAAO;;;;;;gDAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAS9C;IAEA,uCAAuC;IACvC,qBAAO;kBAAG;;AACZ;AAKO,SAAS;IACd,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAExD,OAAO;QACL,iBAAiB,CAAC,aAAa,CAAC,CAAC,mBAAmB,KAAK;QACzD;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1370, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/common/FilterableTable.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useMemo } from 'react'\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow,\n} from '@/components/ui/table'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport {\n  Popover,\n  PopoverContent,\n  PopoverTrigger,\n} from '@/components/ui/popover'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport {\n  Filter,\n  ChevronDown,\n  ChevronUp,\n  ArrowUpDown,\n  ArrowUp,\n  ArrowDown,\n  X,\n  ChevronLeft,\n  ChevronRight,\n  ChevronsLeft,\n  ChevronsRight\n} from 'lucide-react'\nimport { cn } from '@/lib/utils'\n\nexport interface ColumnDef {\n  field: string\n  headerName: string\n  dataType?: 'text' | 'number' | 'date'\n  align?: 'left' | 'center' | 'right'\n  width?: number\n  disableFilter?: boolean\n  disableSort?: boolean\n  headerStyle?: React.CSSProperties\n  cellStyle?: React.CSSProperties\n  renderHeader?: () => React.ReactNode\n  renderCell?: (row: any) => React.ReactNode\n  getFilterValue?: (row: any) => string\n}\n\ninterface FilterableTableProps {\n  data: any[]\n  columns: ColumnDef[]\n  loading?: boolean\n  emptyMessage?: string\n  onFilteredDataChange?: (filteredData: any[]) => void\n  renderRow?: (row: any, index: number) => React.ReactNode\n  className?: string\n  pagination?: boolean\n  defaultRowsPerPage?: number\n}\n\ninterface SortConfig {\n  key: string | null\n  direction: 'asc' | 'desc' | null\n}\n\ninterface FilterConfig {\n  [key: string]: {\n    type: 'text' | 'select' | 'number'\n    value: string | string[]\n    operator?: 'contains' | 'equals' | 'gt' | 'lt' | 'gte' | 'lte'\n  }\n}\n\nexport default function FilterableTable({\n  data = [],\n  columns = [],\n  loading = false,\n  emptyMessage = 'Nessun dato disponibile',\n  onFilteredDataChange,\n  renderRow,\n  className,\n  pagination = true,\n  defaultRowsPerPage = 25\n}: FilterableTableProps) {\n  const [sortConfig, setSortConfig] = useState<SortConfig>({ key: null, direction: null })\n  const [filters, setFilters] = useState<FilterConfig>({})\n  const [openFilters, setOpenFilters] = useState<{ [key: string]: boolean }>({})\n  const [currentPage, setCurrentPage] = useState(0)\n  const [rowsPerPage, setRowsPerPage] = useState(defaultRowsPerPage)\n\n  // Get unique values for select filters\n  const getUniqueValues = (field: string) => {\n    const column = columns.find(col => col.field === field)\n    if (column?.getFilterValue) {\n      // Use custom filter value function if available\n      return [...new Set(data.map(item => column.getFilterValue!(item)).filter(Boolean))].sort()\n    }\n    // Default behavior: use field value directly\n    return [...new Set(data.map(item => item[field]).filter(Boolean))].sort()\n  }\n\n  // Apply filters and sorting\n  const filteredAndSortedData = useMemo(() => {\n    let filtered = [...data]\n\n    // Apply filters\n    Object.entries(filters).forEach(([field, filterConfig]) => {\n      if (!filterConfig.value || \n          (Array.isArray(filterConfig.value) && filterConfig.value.length === 0) ||\n          (typeof filterConfig.value === 'string' && filterConfig.value.trim() === '')) {\n        return\n      }\n\n      filtered = filtered.filter(item => {\n        const column = columns.find(col => col.field === field)\n        const itemValue = column?.getFilterValue ? column.getFilterValue(item) : item[field]\n\n        if (filterConfig.type === 'select') {\n          const selectedValues = Array.isArray(filterConfig.value) ? filterConfig.value : [filterConfig.value]\n          return selectedValues.includes(itemValue)\n        }\n\n        if (filterConfig.type === 'text') {\n          const searchValue = (filterConfig.value as string).toLowerCase()\n          const cellValue = String(itemValue || '').toLowerCase()\n\n          if (filterConfig.operator === 'equals') {\n            return cellValue === searchValue\n          }\n          return cellValue.includes(searchValue)\n        }\n        \n        if (filterConfig.type === 'number') {\n          const numValue = parseFloat(itemValue)\n          const filterValue = parseFloat(filterConfig.value as string)\n          \n          if (isNaN(numValue) || isNaN(filterValue)) return false\n          \n          switch (filterConfig.operator) {\n            case 'equals': return numValue === filterValue\n            case 'gt': return numValue > filterValue\n            case 'lt': return numValue < filterValue\n            case 'gte': return numValue >= filterValue\n            case 'lte': return numValue <= filterValue\n            default: return numValue === filterValue\n          }\n        }\n        \n        return true\n      })\n    })\n\n    // Apply sorting\n    if (sortConfig.key && sortConfig.direction) {\n      filtered.sort((a, b) => {\n        const aValue = a[sortConfig.key!]\n        const bValue = b[sortConfig.key!]\n        \n        // Handle null/undefined values\n        if (aValue == null && bValue == null) return 0\n        if (aValue == null) return sortConfig.direction === 'asc' ? -1 : 1\n        if (bValue == null) return sortConfig.direction === 'asc' ? 1 : -1\n        \n        // Determine if values are numbers\n        const aNum = parseFloat(aValue)\n        const bNum = parseFloat(bValue)\n        const isNumeric = !isNaN(aNum) && !isNaN(bNum)\n        \n        let comparison = 0\n        if (isNumeric) {\n          comparison = aNum - bNum\n        } else {\n          comparison = String(aValue).localeCompare(String(bValue))\n        }\n        \n        return sortConfig.direction === 'asc' ? comparison : -comparison\n      })\n    }\n\n    return filtered\n  }, [data, filters, sortConfig])\n\n  // Calculate paginated data\n  const paginatedData = useMemo(() => {\n    if (!pagination) return filteredAndSortedData\n\n    const startIndex = currentPage * rowsPerPage\n    const endIndex = startIndex + rowsPerPage\n    return filteredAndSortedData.slice(startIndex, endIndex)\n  }, [filteredAndSortedData, currentPage, rowsPerPage, pagination])\n\n  // Reset page when filters change\n  useEffect(() => {\n    setCurrentPage(0)\n  }, [filters])\n\n  // Calculate pagination info\n  const totalPages = Math.ceil(filteredAndSortedData.length / rowsPerPage)\n  const startRow = currentPage * rowsPerPage + 1\n  const endRow = Math.min((currentPage + 1) * rowsPerPage, filteredAndSortedData.length)\n\n  // Notify parent of filtered data changes\n  useEffect(() => {\n    if (onFilteredDataChange) {\n      onFilteredDataChange(filteredAndSortedData)\n    }\n  }, [filteredAndSortedData]) // Rimuoviamo onFilteredDataChange dalle dipendenze per evitare loop infiniti\n\n  const handleSort = (field: string) => {\n    const column = columns.find(col => col.field === field)\n    if (column?.disableSort) return\n\n    setSortConfig(prev => {\n      if (prev.key === field) {\n        if (prev.direction === 'asc') return { key: field, direction: 'desc' }\n        if (prev.direction === 'desc') return { key: null, direction: null }\n      }\n      return { key: field, direction: 'asc' }\n    })\n  }\n\n  const updateFilter = (field: string, filterConfig: Partial<FilterConfig[string]>) => {\n    setFilters(prev => ({\n      ...prev,\n      [field]: { ...prev[field], ...filterConfig }\n    }))\n  }\n\n  const clearFilter = (field: string) => {\n    setFilters(prev => {\n      const newFilters = { ...prev }\n      delete newFilters[field]\n      return newFilters\n    })\n  }\n\n  const clearAllFilters = () => {\n    setFilters({})\n  }\n\n  const getSortIcon = (field: string) => {\n    if (sortConfig.key !== field) return <ArrowUpDown className=\"h-3 w-3\" />\n    if (sortConfig.direction === 'asc') return <ArrowUp className=\"h-3 w-3\" />\n    if (sortConfig.direction === 'desc') return <ArrowDown className=\"h-3 w-3\" />\n    return <ArrowUpDown className=\"h-3 w-3\" />\n  }\n\n  const hasActiveFilters = Object.keys(filters).length > 0\n\n  if (loading) {\n    return (\n      <Card className={className}>\n        <CardContent className=\"p-6\">\n          <div className=\"text-center\">Caricamento...</div>\n        </CardContent>\n      </Card>\n    )\n  }\n\n  return (\n    <div className={className}>\n      {/* Active filters display */}\n      {hasActiveFilters && (\n        <div className=\"mb-4 flex flex-wrap gap-2 items-center\">\n          <span className=\"text-sm text-muted-foreground\">Filtri attivi:</span>\n          {Object.entries(filters).map(([field, filterConfig]) => {\n            const column = columns.find(col => col.field === field)\n            if (!column) return null\n            \n            const displayValue = Array.isArray(filterConfig.value) \n              ? filterConfig.value.join(', ')\n              : String(filterConfig.value)\n            \n            return (\n              <Badge key={field} variant=\"secondary\" className=\"gap-1\">\n                {column.headerName}: {displayValue}\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  className=\"h-auto p-0 hover:bg-transparent\"\n                  onClick={() => clearFilter(field)}\n                >\n                  <X className=\"h-3 w-3\" />\n                </Button>\n              </Badge>\n            )\n          })}\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={clearAllFilters}\n            className=\"h-6 px-2 text-xs\"\n          >\n            Pulisci tutti\n          </Button>\n        </div>\n      )}\n\n      {/* Table */}\n      <Card>\n        <CardContent className=\"p-0\">\n          <Table>\n            <TableHeader>\n              <TableRow className=\"bg-mariner-50 hover:bg-mariner-50\">\n                {columns.map((column) => (\n                  <TableHead\n                    key={column.field}\n                    className={cn(\n                      \"font-semibold text-mariner-900 border-b border-mariner-200\",\n                      column.align === 'center' && \"text-center\",\n                      column.align === 'right' && \"text-right\"\n                    )}\n                    style={{ width: column.width, ...column.headerStyle }}\n                  >\n                    {column.renderHeader ? (\n                      column.renderHeader()\n                    ) : (\n                      <div className=\"relative group\">\n                        <div className=\"flex items-center justify-between w-full\">\n                          <span className=\"truncate\">{column.headerName}</span>\n\n                          {/* Compact icons container - only visible on hover */}\n                          <div className=\"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\n                            {/* Sort button */}\n                            {!column.disableSort && (\n                              <Button\n                                variant=\"ghost\"\n                                size=\"sm\"\n                                className=\"h-4 w-4 p-0 hover:bg-mariner-100\"\n                                onClick={() => handleSort(column.field)}\n                              >\n                                {getSortIcon(column.field)}\n                              </Button>\n                            )}\n\n                            {/* Filter button */}\n                            {!column.disableFilter && (\n                              <Popover\n                                open={openFilters[column.field]}\n                                onOpenChange={(open) => setOpenFilters(prev => ({ ...prev, [column.field]: open }))}\n                              >\n                                <PopoverTrigger asChild>\n                                  <Button\n                                    variant=\"ghost\"\n                                    size=\"sm\"\n                                    className={cn(\n                                      \"h-4 w-4 p-0 hover:bg-mariner-100\",\n                                      filters[column.field] && \"text-mariner-600 opacity-100\"\n                                    )}\n                                  >\n                                    <Filter className=\"h-2.5 w-2.5\" />\n                                  </Button>\n                                </PopoverTrigger>\n                                <PopoverContent className=\"w-64\" align=\"start\">\n                                  <FilterContent\n                                    column={column}\n                                    data={data}\n                                    currentFilter={filters[column.field]}\n                                    onFilterChange={(filterConfig) => updateFilter(column.field, filterConfig)}\n                                    onClearFilter={() => clearFilter(column.field)}\n                                    getUniqueValues={() => getUniqueValues(column.field)}\n                                  />\n                                </PopoverContent>\n                              </Popover>\n                            )}\n                          </div>\n                        </div>\n\n                        {/* Active filter indicator */}\n                        {filters[column.field] && (\n                          <div className=\"absolute -top-1 -right-1 h-2 w-2 bg-mariner-600 rounded-full\"></div>\n                        )}\n                      </div>\n                    )}\n                  </TableHead>\n                ))}\n              </TableRow>\n            </TableHeader>\n            <TableBody>\n              {paginatedData.length > 0 ? (\n                paginatedData.map((row, index) => (\n                  renderRow ? (\n                    renderRow(row, currentPage * rowsPerPage + index)\n                  ) : (\n                    <TableRow\n                      key={index}\n                      className=\"hover:bg-mariner-50 border-b border-mariner-100\"\n                    >\n                      {columns.map((column) => (\n                        <TableCell\n                          key={column.field}\n                          className={cn(\n                            \"py-2 px-4\",\n                            column.align === 'center' && \"text-center\",\n                            column.align === 'right' && \"text-right\"\n                          )}\n                          style={column.cellStyle}\n                        >\n                          {column.renderCell ? column.renderCell(row) : row[column.field]}\n                        </TableCell>\n                      ))}\n                    </TableRow>\n                  )\n                ))\n              ) : (\n                <TableRow>\n                  <TableCell colSpan={columns.length} className=\"text-center py-8 text-muted-foreground\">\n                    {emptyMessage}\n                  </TableCell>\n                </TableRow>\n              )}\n            </TableBody>\n          </Table>\n        </CardContent>\n      </Card>\n\n      {/* Pagination Controls */}\n      {pagination && filteredAndSortedData.length > 0 && (\n        <div className=\"flex items-center justify-between mt-4\">\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-sm text-muted-foreground\">\n              Righe per pagina:\n            </span>\n            <Select\n              value={rowsPerPage.toString()}\n              onValueChange={(value) => {\n                setRowsPerPage(Number(value))\n                setCurrentPage(0)\n              }}\n            >\n              <SelectTrigger className=\"w-20\">\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"10\">10</SelectItem>\n                <SelectItem value=\"25\">25</SelectItem>\n                <SelectItem value=\"50\">50</SelectItem>\n                <SelectItem value=\"100\">100</SelectItem>\n                <SelectItem value={filteredAndSortedData.length.toString()}>Tutto</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-sm text-muted-foreground\">\n              {filteredAndSortedData.length > 0 ? `${startRow}-${endRow} di ${filteredAndSortedData.length}` : '0 di 0'}\n            </span>\n\n            <div className=\"flex items-center space-x-1\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => setCurrentPage(0)}\n                disabled={currentPage === 0}\n                className=\"h-8 w-8 p-0\"\n              >\n                <ChevronsLeft className=\"h-4 w-4\" />\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => setCurrentPage(prev => Math.max(0, prev - 1))}\n                disabled={currentPage === 0}\n                className=\"h-8 w-8 p-0\"\n              >\n                <ChevronLeft className=\"h-4 w-4\" />\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => setCurrentPage(prev => Math.min(totalPages - 1, prev + 1))}\n                disabled={currentPage >= totalPages - 1}\n                className=\"h-8 w-8 p-0\"\n              >\n                <ChevronRight className=\"h-4 w-4\" />\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => setCurrentPage(totalPages - 1)}\n                disabled={currentPage >= totalPages - 1}\n                className=\"h-8 w-8 p-0\"\n              >\n                <ChevronsRight className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n\n// Filter content component\ninterface FilterContentProps {\n  column: ColumnDef\n  data: any[]\n  currentFilter?: FilterConfig[string]\n  onFilterChange: (filterConfig: Partial<FilterConfig[string]>) => void\n  onClearFilter: () => void\n  getUniqueValues: () => any[]\n}\n\nfunction FilterContent({\n  column,\n  currentFilter,\n  onFilterChange,\n  onClearFilter,\n  getUniqueValues\n}: FilterContentProps) {\n  const [localValue, setLocalValue] = useState(currentFilter?.value || '')\n  const [operator, setOperator] = useState(currentFilter?.operator || 'contains')\n\n  const uniqueValues = getUniqueValues()\n  const isSelectType = column.dataType !== 'number' && uniqueValues.length <= 20\n  const isNumberType = column.dataType === 'number'\n\n  const applyFilter = () => {\n    if (isSelectType) {\n      onFilterChange({\n        type: 'select',\n        value: Array.isArray(localValue) ? localValue : [localValue]\n      })\n    } else if (isNumberType) {\n      onFilterChange({\n        type: 'number',\n        value: localValue as string,\n        operator\n      })\n    } else {\n      onFilterChange({\n        type: 'text',\n        value: localValue as string,\n        operator\n      })\n    }\n  }\n\n  return (\n    <div className=\"space-y-3\">\n      <div className=\"font-medium text-sm\">Filtra {column.headerName}</div>\n      \n      {isSelectType ? (\n        <div className=\"space-y-2 max-h-48 overflow-y-auto\">\n          {uniqueValues.map(value => (\n            <div key={value} className=\"flex items-center space-x-2\">\n              <Checkbox\n                id={`filter-${value}`}\n                checked={Array.isArray(localValue) ? localValue.includes(value) : localValue === value}\n                onCheckedChange={(checked) => {\n                  if (Array.isArray(localValue)) {\n                    setLocalValue(checked \n                      ? [...localValue, value]\n                      : localValue.filter(v => v !== value)\n                    )\n                  } else {\n                    setLocalValue(checked ? [value] : [])\n                  }\n                }}\n              />\n              <label htmlFor={`filter-${value}`} className=\"text-sm\">\n                {value}\n              </label>\n            </div>\n          ))}\n        </div>\n      ) : (\n        <div className=\"space-y-2\">\n          {isNumberType && (\n            <Select value={operator} onValueChange={setOperator}>\n              <SelectTrigger>\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"equals\">Uguale a</SelectItem>\n                <SelectItem value=\"gt\">Maggiore di</SelectItem>\n                <SelectItem value=\"lt\">Minore di</SelectItem>\n                <SelectItem value=\"gte\">Maggiore o uguale</SelectItem>\n                <SelectItem value=\"lte\">Minore o uguale</SelectItem>\n              </SelectContent>\n            </Select>\n          )}\n          \n          {!isNumberType && (\n            <Select value={operator} onValueChange={setOperator}>\n              <SelectTrigger>\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"contains\">Contiene</SelectItem>\n                <SelectItem value=\"equals\">Uguale a</SelectItem>\n              </SelectContent>\n            </Select>\n          )}\n          \n          <Input\n            placeholder={`Cerca ${column.headerName.toLowerCase()}...`}\n            value={localValue as string}\n            onChange={(e) => setLocalValue(e.target.value)}\n            onKeyDown={(e) => e.key === 'Enter' && applyFilter()}\n          />\n        </div>\n      )}\n      \n      <div className=\"flex gap-2\">\n        <Button size=\"sm\" onClick={applyFilter}>\n          Applica\n        </Button>\n        <Button size=\"sm\" variant=\"outline\" onClick={onClearFilter}>\n          Pulisci\n        </Button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AAOA;AAKA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AAzCA;;;;;;;;;;;;;AAmFe,SAAS,gBAAgB,EACtC,OAAO,EAAE,EACT,UAAU,EAAE,EACZ,UAAU,KAAK,EACf,eAAe,yBAAyB,EACxC,oBAAoB,EACpB,SAAS,EACT,SAAS,EACT,aAAa,IAAI,EACjB,qBAAqB,EAAE,EACF;IACrB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;QAAE,KAAK;QAAM,WAAW;IAAK;IACtF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,CAAC;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8B,CAAC;IAC5E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,uCAAuC;IACvC,MAAM,kBAAkB,CAAC;QACvB,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,MAAO,IAAI,KAAK,KAAK;QACjD,IAAI,QAAQ,gBAAgB;YAC1B,gDAAgD;YAChD,OAAO;mBAAI,IAAI,IAAI,KAAK,GAAG,CAAC,CAAA,OAAQ,OAAO,cAAc,CAAE,OAAO,MAAM,CAAC;aAAU,CAAC,IAAI;QAC1F;QACA,6CAA6C;QAC7C,OAAO;eAAI,IAAI,IAAI,KAAK,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC;SAAU,CAAC,IAAI;IACzE;IAEA,4BAA4B;IAC5B,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACpC,IAAI,WAAW;eAAI;SAAK;QAExB,gBAAgB;QAChB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,OAAO,aAAa;YACpD,IAAI,CAAC,aAAa,KAAK,IAClB,MAAM,OAAO,CAAC,aAAa,KAAK,KAAK,aAAa,KAAK,CAAC,MAAM,KAAK,KACnE,OAAO,aAAa,KAAK,KAAK,YAAY,aAAa,KAAK,CAAC,IAAI,OAAO,IAAK;gBAChF;YACF;YAEA,WAAW,SAAS,MAAM,CAAC,CAAA;gBACzB,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,MAAO,IAAI,KAAK,KAAK;gBACjD,MAAM,YAAY,QAAQ,iBAAiB,OAAO,cAAc,CAAC,QAAQ,IAAI,CAAC,MAAM;gBAEpF,IAAI,aAAa,IAAI,KAAK,UAAU;oBAClC,MAAM,iBAAiB,MAAM,OAAO,CAAC,aAAa,KAAK,IAAI,aAAa,KAAK,GAAG;wBAAC,aAAa,KAAK;qBAAC;oBACpG,OAAO,eAAe,QAAQ,CAAC;gBACjC;gBAEA,IAAI,aAAa,IAAI,KAAK,QAAQ;oBAChC,MAAM,cAAc,AAAC,aAAa,KAAK,CAAY,WAAW;oBAC9D,MAAM,YAAY,OAAO,aAAa,IAAI,WAAW;oBAErD,IAAI,aAAa,QAAQ,KAAK,UAAU;wBACtC,OAAO,cAAc;oBACvB;oBACA,OAAO,UAAU,QAAQ,CAAC;gBAC5B;gBAEA,IAAI,aAAa,IAAI,KAAK,UAAU;oBAClC,MAAM,WAAW,WAAW;oBAC5B,MAAM,cAAc,WAAW,aAAa,KAAK;oBAEjD,IAAI,MAAM,aAAa,MAAM,cAAc,OAAO;oBAElD,OAAQ,aAAa,QAAQ;wBAC3B,KAAK;4BAAU,OAAO,aAAa;wBACnC,KAAK;4BAAM,OAAO,WAAW;wBAC7B,KAAK;4BAAM,OAAO,WAAW;wBAC7B,KAAK;4BAAO,OAAO,YAAY;wBAC/B,KAAK;4BAAO,OAAO,YAAY;wBAC/B;4BAAS,OAAO,aAAa;oBAC/B;gBACF;gBAEA,OAAO;YACT;QACF;QAEA,gBAAgB;QAChB,IAAI,WAAW,GAAG,IAAI,WAAW,SAAS,EAAE;YAC1C,SAAS,IAAI,CAAC,CAAC,GAAG;gBAChB,MAAM,SAAS,CAAC,CAAC,WAAW,GAAG,CAAE;gBACjC,MAAM,SAAS,CAAC,CAAC,WAAW,GAAG,CAAE;gBAEjC,+BAA+B;gBAC/B,IAAI,UAAU,QAAQ,UAAU,MAAM,OAAO;gBAC7C,IAAI,UAAU,MAAM,OAAO,WAAW,SAAS,KAAK,QAAQ,CAAC,IAAI;gBACjE,IAAI,UAAU,MAAM,OAAO,WAAW,SAAS,KAAK,QAAQ,IAAI,CAAC;gBAEjE,kCAAkC;gBAClC,MAAM,OAAO,WAAW;gBACxB,MAAM,OAAO,WAAW;gBACxB,MAAM,YAAY,CAAC,MAAM,SAAS,CAAC,MAAM;gBAEzC,IAAI,aAAa;gBACjB,IAAI,WAAW;oBACb,aAAa,OAAO;gBACtB,OAAO;oBACL,aAAa,OAAO,QAAQ,aAAa,CAAC,OAAO;gBACnD;gBAEA,OAAO,WAAW,SAAS,KAAK,QAAQ,aAAa,CAAC;YACxD;QACF;QAEA,OAAO;IACT,GAAG;QAAC;QAAM;QAAS;KAAW;IAE9B,2BAA2B;IAC3B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC5B,IAAI,CAAC,YAAY,OAAO;QAExB,MAAM,aAAa,cAAc;QACjC,MAAM,WAAW,aAAa;QAC9B,OAAO,sBAAsB,KAAK,CAAC,YAAY;IACjD,GAAG;QAAC;QAAuB;QAAa;QAAa;KAAW;IAEhE,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe;IACjB,GAAG;QAAC;KAAQ;IAEZ,4BAA4B;IAC5B,MAAM,aAAa,KAAK,IAAI,CAAC,sBAAsB,MAAM,GAAG;IAC5D,MAAM,WAAW,cAAc,cAAc;IAC7C,MAAM,SAAS,KAAK,GAAG,CAAC,CAAC,cAAc,CAAC,IAAI,aAAa,sBAAsB,MAAM;IAErF,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,sBAAsB;YACxB,qBAAqB;QACvB;IACF,GAAG;QAAC;KAAsB,EAAE,6EAA6E;;IAEzG,MAAM,aAAa,CAAC;QAClB,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,MAAO,IAAI,KAAK,KAAK;QACjD,IAAI,QAAQ,aAAa;QAEzB,cAAc,CAAA;YACZ,IAAI,KAAK,GAAG,KAAK,OAAO;gBACtB,IAAI,KAAK,SAAS,KAAK,OAAO,OAAO;oBAAE,KAAK;oBAAO,WAAW;gBAAO;gBACrE,IAAI,KAAK,SAAS,KAAK,QAAQ,OAAO;oBAAE,KAAK;oBAAM,WAAW;gBAAK;YACrE;YACA,OAAO;gBAAE,KAAK;gBAAO,WAAW;YAAM;QACxC;IACF;IAEA,MAAM,eAAe,CAAC,OAAe;QACnC,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;oBAAE,GAAG,IAAI,CAAC,MAAM;oBAAE,GAAG,YAAY;gBAAC;YAC7C,CAAC;IACH;IAEA,MAAM,cAAc,CAAC;QACnB,WAAW,CAAA;YACT,MAAM,aAAa;gBAAE,GAAG,IAAI;YAAC;YAC7B,OAAO,UAAU,CAAC,MAAM;YACxB,OAAO;QACT;IACF;IAEA,MAAM,kBAAkB;QACtB,WAAW,CAAC;IACd;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,WAAW,GAAG,KAAK,OAAO,qBAAO,8OAAC,wNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;QAC5D,IAAI,WAAW,SAAS,KAAK,OAAO,qBAAO,8OAAC,4MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QAC9D,IAAI,WAAW,SAAS,KAAK,QAAQ,qBAAO,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;QACjE,qBAAO,8OAAC,wNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;IAChC;IAEA,MAAM,mBAAmB,OAAO,IAAI,CAAC,SAAS,MAAM,GAAG;IAEvD,IAAI,SAAS;QACX,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAW;sBACf,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;oBAAI,WAAU;8BAAc;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,8OAAC;QAAI,WAAW;;YAEb,kCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAAgC;;;;;;oBAC/C,OAAO,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,OAAO,aAAa;wBACjD,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,MAAO,IAAI,KAAK,KAAK;wBACjD,IAAI,CAAC,QAAQ,OAAO;wBAEpB,MAAM,eAAe,MAAM,OAAO,CAAC,aAAa,KAAK,IACjD,aAAa,KAAK,CAAC,IAAI,CAAC,QACxB,OAAO,aAAa,KAAK;wBAE7B,qBACE,8OAAC,iIAAA,CAAA,QAAK;4BAAa,SAAQ;4BAAY,WAAU;;gCAC9C,OAAO,UAAU;gCAAC;gCAAG;8CACtB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,YAAY;8CAE3B,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;2BARL;;;;;oBAYhB;kCACA,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAOL,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC,iIAAA,CAAA,QAAK;;0CACJ,8OAAC,iIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;oCAAC,WAAU;8CACjB,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,iIAAA,CAAA,YAAS;4CAER,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA,OAAO,KAAK,KAAK,YAAY,eAC7B,OAAO,KAAK,KAAK,WAAW;4CAE9B,OAAO;gDAAE,OAAO,OAAO,KAAK;gDAAE,GAAG,OAAO,WAAW;4CAAC;sDAEnD,OAAO,YAAY,GAClB,OAAO,YAAY,mBAEnB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAY,OAAO,UAAU;;;;;;0EAG7C,8OAAC;gEAAI,WAAU;;oEAEZ,CAAC,OAAO,WAAW,kBAClB,8OAAC,kIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,WAAU;wEACV,SAAS,IAAM,WAAW,OAAO,KAAK;kFAErC,YAAY,OAAO,KAAK;;;;;;oEAK5B,CAAC,OAAO,aAAa,kBACpB,8OAAC,mIAAA,CAAA,UAAO;wEACN,MAAM,WAAW,CAAC,OAAO,KAAK,CAAC;wEAC/B,cAAc,CAAC,OAAS,eAAe,CAAA,OAAQ,CAAC;oFAAE,GAAG,IAAI;oFAAE,CAAC,OAAO,KAAK,CAAC,EAAE;gFAAK,CAAC;;0FAEjF,8OAAC,mIAAA,CAAA,iBAAc;gFAAC,OAAO;0FACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oFACL,SAAQ;oFACR,MAAK;oFACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oCACA,OAAO,CAAC,OAAO,KAAK,CAAC,IAAI;8FAG3B,cAAA,8OAAC,sMAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;;;;;;;;;;;0FAGtB,8OAAC,mIAAA,CAAA,iBAAc;gFAAC,WAAU;gFAAO,OAAM;0FACrC,cAAA,8OAAC;oFACC,QAAQ;oFACR,MAAM;oFACN,eAAe,OAAO,CAAC,OAAO,KAAK,CAAC;oFACpC,gBAAgB,CAAC,eAAiB,aAAa,OAAO,KAAK,EAAE;oFAC7D,eAAe,IAAM,YAAY,OAAO,KAAK;oFAC7C,iBAAiB,IAAM,gBAAgB,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oDAS9D,OAAO,CAAC,OAAO,KAAK,CAAC,kBACpB,8OAAC;wDAAI,WAAU;;;;;;;;;;;;2CAhEhB,OAAO,KAAK;;;;;;;;;;;;;;;0CAwEzB,8OAAC,iIAAA,CAAA,YAAS;0CACP,cAAc,MAAM,GAAG,IACtB,cAAc,GAAG,CAAC,CAAC,KAAK,QACtB,YACE,UAAU,KAAK,cAAc,cAAc,uBAE3C,8OAAC,iIAAA,CAAA,WAAQ;wCAEP,WAAU;kDAET,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,iIAAA,CAAA,YAAS;gDAER,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,OAAO,KAAK,KAAK,YAAY,eAC7B,OAAO,KAAK,KAAK,WAAW;gDAE9B,OAAO,OAAO,SAAS;0DAEtB,OAAO,UAAU,GAAG,OAAO,UAAU,CAAC,OAAO,GAAG,CAAC,OAAO,KAAK,CAAC;+CAR1D,OAAO,KAAK;;;;;uCALhB;;;;8DAoBX,8OAAC,iIAAA,CAAA,WAAQ;8CACP,cAAA,8OAAC,iIAAA,CAAA,YAAS;wCAAC,SAAS,QAAQ,MAAM;wCAAE,WAAU;kDAC3C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUd,cAAc,sBAAsB,MAAM,GAAG,mBAC5C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAgC;;;;;;0CAGhD,8OAAC,kIAAA,CAAA,SAAM;gCACL,OAAO,YAAY,QAAQ;gCAC3B,eAAe,CAAC;oCACd,eAAe,OAAO;oCACtB,eAAe;gCACjB;;kDAEA,8OAAC,kIAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kDAEd,8OAAC,kIAAA,CAAA,gBAAa;;0DACZ,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAK;;;;;;0DACvB,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAK;;;;;;0DACvB,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAK;;;;;;0DACvB,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAM;;;;;;0DACxB,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAO,sBAAsB,MAAM,CAAC,QAAQ;0DAAI;;;;;;;;;;;;;;;;;;;;;;;;kCAKlE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CACb,sBAAsB,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC,EAAE,OAAO,IAAI,EAAE,sBAAsB,MAAM,EAAE,GAAG;;;;;;0CAGnG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe;wCAC9B,UAAU,gBAAgB;wCAC1B,WAAU;kDAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;kDAE1B,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;wCACzD,UAAU,gBAAgB;wCAC1B,WAAU;kDAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;kDAEzB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,aAAa,GAAG,OAAO;wCACtE,UAAU,eAAe,aAAa;wCACtC,WAAU;kDAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;kDAE1B,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe,aAAa;wCAC3C,UAAU,eAAe,aAAa;wCACtC,WAAU;kDAEV,cAAA,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzC;AAYA,SAAS,cAAc,EACrB,MAAM,EACN,aAAa,EACb,cAAc,EACd,aAAa,EACb,eAAe,EACI;IACnB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,SAAS;IACrE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,YAAY;IAEpE,MAAM,eAAe;IACrB,MAAM,eAAe,OAAO,QAAQ,KAAK,YAAY,aAAa,MAAM,IAAI;IAC5E,MAAM,eAAe,OAAO,QAAQ,KAAK;IAEzC,MAAM,cAAc;QAClB,IAAI,cAAc;YAChB,eAAe;gBACb,MAAM;gBACN,OAAO,MAAM,OAAO,CAAC,cAAc,aAAa;oBAAC;iBAAW;YAC9D;QACF,OAAO,IAAI,cAAc;YACvB,eAAe;gBACb,MAAM;gBACN,OAAO;gBACP;YACF;QACF,OAAO;YACL,eAAe;gBACb,MAAM;gBACN,OAAO;gBACP;YACF;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;oBAAsB;oBAAQ,OAAO,UAAU;;;;;;;YAE7D,6BACC,8OAAC;gBAAI,WAAU;0BACZ,aAAa,GAAG,CAAC,CAAA,sBAChB,8OAAC;wBAAgB,WAAU;;0CACzB,8OAAC,oIAAA,CAAA,WAAQ;gCACP,IAAI,CAAC,OAAO,EAAE,OAAO;gCACrB,SAAS,MAAM,OAAO,CAAC,cAAc,WAAW,QAAQ,CAAC,SAAS,eAAe;gCACjF,iBAAiB,CAAC;oCAChB,IAAI,MAAM,OAAO,CAAC,aAAa;wCAC7B,cAAc,UACV;+CAAI;4CAAY;yCAAM,GACtB,WAAW,MAAM,CAAC,CAAA,IAAK,MAAM;oCAEnC,OAAO;wCACL,cAAc,UAAU;4CAAC;yCAAM,GAAG,EAAE;oCACtC;gCACF;;;;;;0CAEF,8OAAC;gCAAM,SAAS,CAAC,OAAO,EAAE,OAAO;gCAAE,WAAU;0CAC1C;;;;;;;uBAhBK;;;;;;;;;qCAsBd,8OAAC;gBAAI,WAAU;;oBACZ,8BACC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAU,eAAe;;0CACtC,8OAAC,kIAAA,CAAA,gBAAa;0CACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0CAEd,8OAAC,kIAAA,CAAA,gBAAa;;kDACZ,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAS;;;;;;kDAC3B,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAK;;;;;;kDACvB,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAK;;;;;;kDACvB,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAM;;;;;;kDACxB,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAM;;;;;;;;;;;;;;;;;;oBAK7B,CAAC,8BACA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAU,eAAe;;0CACtC,8OAAC,kIAAA,CAAA,gBAAa;0CACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0CAEd,8OAAC,kIAAA,CAAA,gBAAa;;kDACZ,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAW;;;;;;kDAC7B,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAS;;;;;;;;;;;;;;;;;;kCAKjC,8OAAC,iIAAA,CAAA,QAAK;wBACJ,aAAa,CAAC,MAAM,EAAE,OAAO,UAAU,CAAC,WAAW,GAAG,GAAG,CAAC;wBAC1D,OAAO;wBACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wBAC7C,WAAW,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;;;;;;;0BAK7C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAK,SAAS;kCAAa;;;;;;kCAGxC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAK,SAAQ;wBAAU,SAAS;kCAAe;;;;;;;;;;;;;;;;;;AAMpE", "debugId": null}}, {"offset": {"line": 2353, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/common/TruncatedText.tsx"], "sourcesContent": ["import React, { useState } from 'react'\n\ninterface TruncatedTextProps {\n  text: string\n  maxLength?: number\n  className?: string\n}\n\nexport default function TruncatedText({\n  text,\n  maxLength = 20,\n  className = \"\"\n}: TruncatedTextProps) {\n  const [showTooltip, setShowTooltip] = useState(false)\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })\n\n  if (!text) return <span className=\"text-gray-400\">-</span>\n\n  const shouldTruncate = text.length > maxLength\n  const displayText = shouldTruncate ? `${text.substring(0, maxLength)}...` : text\n\n  if (!shouldTruncate) {\n    return <span className={className}>{text}</span>\n  }\n\n  const handleMouseEnter = (e: React.MouseEvent) => {\n    setMousePosition({ x: e.clientX, y: e.clientY })\n    setShowTooltip(true)\n  }\n\n  const handleMouseMove = (e: React.MouseEvent) => {\n    setMousePosition({ x: e.clientX, y: e.clientY })\n  }\n\n  return (\n    <div className=\"relative inline-block\">\n      <span\n        className={`cursor-help ${className}`}\n        style={{\n          textOverflow: 'ellipsis',\n          whiteSpace: 'nowrap',\n          overflow: 'hidden',\n          maxWidth: '100%',\n          display: 'inline-block'\n        }}\n        onMouseEnter={handleMouseEnter}\n        onMouseMove={handleMouseMove}\n        onMouseLeave={() => setShowTooltip(false)}\n        title={text} // Fallback browser tooltip\n      >\n        {displayText}\n      </span>\n\n      {/* Custom tooltip */}\n      {showTooltip && (\n        <div\n          className=\"fixed z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-md shadow-lg pointer-events-none\"\n          style={{\n            top: mousePosition.y - 40,\n            left: mousePosition.x - 150,\n            maxWidth: '300px',\n            wordWrap: 'break-word',\n            whiteSpace: 'normal'\n          }}\n        >\n          {text}\n          {/* Arrow */}\n          <div\n            className=\"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0\"\n            style={{\n              borderLeft: '5px solid transparent',\n              borderRight: '5px solid transparent',\n              borderTop: '5px solid #1f2937'\n            }}\n          />\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAQe,SAAS,cAAc,EACpC,IAAI,EACJ,YAAY,EAAE,EACd,YAAY,EAAE,EACK;IACnB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAEhE,IAAI,CAAC,MAAM,qBAAO,8OAAC;QAAK,WAAU;kBAAgB;;;;;;IAElD,MAAM,iBAAiB,KAAK,MAAM,GAAG;IACrC,MAAM,cAAc,iBAAiB,GAAG,KAAK,SAAS,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG;IAE5E,IAAI,CAAC,gBAAgB;QACnB,qBAAO,8OAAC;YAAK,WAAW;sBAAY;;;;;;IACtC;IAEA,MAAM,mBAAmB,CAAC;QACxB,iBAAiB;YAAE,GAAG,EAAE,OAAO;YAAE,GAAG,EAAE,OAAO;QAAC;QAC9C,eAAe;IACjB;IAEA,MAAM,kBAAkB,CAAC;QACvB,iBAAiB;YAAE,GAAG,EAAE,OAAO;YAAE,GAAG,EAAE,OAAO;QAAC;IAChD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,WAAW,CAAC,YAAY,EAAE,WAAW;gBACrC,OAAO;oBACL,cAAc;oBACd,YAAY;oBACZ,UAAU;oBACV,UAAU;oBACV,SAAS;gBACX;gBACA,cAAc;gBACd,aAAa;gBACb,cAAc,IAAM,eAAe;gBACnC,OAAO;0BAEN;;;;;;YAIF,6BACC,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,KAAK,cAAc,CAAC,GAAG;oBACvB,MAAM,cAAc,CAAC,GAAG;oBACxB,UAAU;oBACV,UAAU;oBACV,YAAY;gBACd;;oBAEC;kCAED,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,YAAY;4BACZ,aAAa;4BACb,WAAW;wBACb;;;;;;;;;;;;;;;;;;AAMZ", "debugId": null}}, {"offset": {"line": 2463, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/certificazioni/sections/InformazioniBase.tsx"], "sourcesContent": ["'use client'\n\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Label } from '@/components/ui/label'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { CertificazioneCavoCreate, StrumentoCertificato } from '@/types/certificazioni'\n\ninterface InformazioniBaseProps {\n  formData: Partial<CertificazioneCavoCreate>\n  cavi: any[]\n  responsabili: any[]\n  strumenti: StrumentoCertificato[]\n  validationErrors: Record<string, string>\n  isCavoLocked: boolean\n  onInputChange: (field: string, value: any) => void\n}\n\nexport function InformazioniBase({\n  formData,\n  cavi,\n  responsabili,\n  strumenti,\n  validationErrors,\n  isCavoLocked,\n  onInputChange\n}: InformazioniBaseProps) {\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n      {/* Cavo */}\n      <div className=\"space-y-2\">\n        <Label htmlFor=\"id_cavo\" className=\"text-sm font-medium text-gray-700\">Cavo *</Label>\n        <Select\n          value={formData.id_cavo}\n          onValueChange={(value) => onInputChange('id_cavo', value)}\n          disabled={isCavoLocked}\n        >\n          <SelectTrigger className={`h-11 text-sm ${validationErrors.id_cavo ? 'border-red-500' : 'border-gray-300'}`}>\n            <SelectValue placeholder=\"Seleziona cavo...\" />\n          </SelectTrigger>\n          <SelectContent>\n            {cavi.map((cavo) => (\n              <SelectItem key={cavo.id_cavo} value={cavo.id_cavo}>\n                <div className=\"flex flex-col py-1\">\n                  <span className=\"font-medium text-sm\">{cavo.id_cavo}</span>\n                  <span className=\"text-xs text-gray-500\">{cavo.tipologia} {cavo.sezione}</span>\n                </div>\n              </SelectItem>\n            ))}\n          </SelectContent>\n        </Select>\n        {validationErrors.id_cavo && (\n          <p className=\"text-sm text-red-600\">{validationErrors.id_cavo}</p>\n        )}\n      </div>\n\n      {/* Operatore */}\n      <div className=\"space-y-2\">\n        <Label htmlFor=\"id_operatore\" className=\"text-sm font-medium text-gray-700\">Operatore</Label>\n        <Select\n          value={formData.id_operatore?.toString() || ''}\n          onValueChange={(value) => onInputChange('id_operatore', parseInt(value))}\n        >\n          <SelectTrigger className=\"h-11 text-sm border-gray-300\">\n            <SelectValue placeholder=\"Seleziona operatore...\" />\n          </SelectTrigger>\n          <SelectContent>\n            {responsabili.map((resp) => (\n              <SelectItem key={resp.id_responsabile} value={resp.id_responsabile.toString()}>\n                <span className=\"text-sm\">{resp.nome_responsabile}</span>\n              </SelectItem>\n            ))}\n          </SelectContent>\n        </Select>\n      </div>\n\n      {/* Strumento */}\n      <div className=\"space-y-2\">\n        <Label htmlFor=\"id_strumento\" className=\"text-sm font-medium text-gray-700\">Strumento di Misura</Label>\n        <Select\n          value={formData.id_strumento?.toString() || ''}\n          onValueChange={(value) => {\n            const strumento = strumenti.find(s => s.id_strumento === parseInt(value))\n            onInputChange('id_strumento', parseInt(value))\n            if (strumento) {\n              onInputChange('strumento_utilizzato', `${strumento.marca} ${strumento.modello}`)\n            }\n          }}\n        >\n          <SelectTrigger className=\"h-11 text-sm border-gray-300\">\n            <SelectValue placeholder=\"Seleziona strumento...\" />\n          </SelectTrigger>\n          <SelectContent>\n            {strumenti.map((strumento) => (\n              <SelectItem key={strumento.id_strumento} value={strumento.id_strumento.toString()}>\n                <div className=\"flex flex-col py-1\">\n                  <span className=\"font-medium text-sm\">{strumento.nome}</span>\n                  <span className=\"text-xs text-gray-500\">{strumento.marca} {strumento.modello}</span>\n                </div>\n              </SelectItem>\n            ))}\n          </SelectContent>\n        </Select>\n      </div>\n\n      {/* Tipo Certificato */}\n      <div className=\"space-y-2\">\n        <Label htmlFor=\"tipo_certificato\" className=\"text-sm font-medium text-gray-700\">Tipo Certificato</Label>\n        <Select\n          value={formData.tipo_certificato || 'SINGOLO'}\n          onValueChange={(value) => onInputChange('tipo_certificato', value)}\n        >\n          <SelectTrigger className=\"h-11 text-sm border-gray-300\">\n            <SelectValue />\n          </SelectTrigger>\n          <SelectContent>\n            <SelectItem value=\"SINGOLO\">\n              <span className=\"text-sm\">🔍 Singolo</span>\n            </SelectItem>\n            <SelectItem value=\"GRUPPO\">\n              <span className=\"text-sm\">📊 Gruppo</span>\n            </SelectItem>\n          </SelectContent>\n        </Select>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAuBO,SAAS,iBAAiB,EAC/B,QAAQ,EACR,IAAI,EACJ,YAAY,EACZ,SAAS,EACT,gBAAgB,EAChB,YAAY,EACZ,aAAa,EACS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAU,WAAU;kCAAoC;;;;;;kCACvE,8OAAC,kIAAA,CAAA,SAAM;wBACL,OAAO,SAAS,OAAO;wBACvB,eAAe,CAAC,QAAU,cAAc,WAAW;wBACnD,UAAU;;0CAEV,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,WAAW,CAAC,aAAa,EAAE,iBAAiB,OAAO,GAAG,mBAAmB,mBAAmB;0CACzG,cAAA,8OAAC,kIAAA,CAAA,cAAW;oCAAC,aAAY;;;;;;;;;;;0CAE3B,8OAAC,kIAAA,CAAA,gBAAa;0CACX,KAAK,GAAG,CAAC,CAAC,qBACT,8OAAC,kIAAA,CAAA,aAAU;wCAAoB,OAAO,KAAK,OAAO;kDAChD,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAuB,KAAK,OAAO;;;;;;8DACnD,8OAAC;oDAAK,WAAU;;wDAAyB,KAAK,SAAS;wDAAC;wDAAE,KAAK,OAAO;;;;;;;;;;;;;uCAHzD,KAAK,OAAO;;;;;;;;;;;;;;;;oBASlC,iBAAiB,OAAO,kBACvB,8OAAC;wBAAE,WAAU;kCAAwB,iBAAiB,OAAO;;;;;;;;;;;;0BAKjE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAe,WAAU;kCAAoC;;;;;;kCAC5E,8OAAC,kIAAA,CAAA,SAAM;wBACL,OAAO,SAAS,YAAY,EAAE,cAAc;wBAC5C,eAAe,CAAC,QAAU,cAAc,gBAAgB,SAAS;;0CAEjE,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,WAAU;0CACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;oCAAC,aAAY;;;;;;;;;;;0CAE3B,8OAAC,kIAAA,CAAA,gBAAa;0CACX,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,kIAAA,CAAA,aAAU;wCAA4B,OAAO,KAAK,eAAe,CAAC,QAAQ;kDACzE,cAAA,8OAAC;4CAAK,WAAU;sDAAW,KAAK,iBAAiB;;;;;;uCADlC,KAAK,eAAe;;;;;;;;;;;;;;;;;;;;;;0BAS7C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAe,WAAU;kCAAoC;;;;;;kCAC5E,8OAAC,kIAAA,CAAA,SAAM;wBACL,OAAO,SAAS,YAAY,EAAE,cAAc;wBAC5C,eAAe,CAAC;4BACd,MAAM,YAAY,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK,SAAS;4BAClE,cAAc,gBAAgB,SAAS;4BACvC,IAAI,WAAW;gCACb,cAAc,wBAAwB,GAAG,UAAU,KAAK,CAAC,CAAC,EAAE,UAAU,OAAO,EAAE;4BACjF;wBACF;;0CAEA,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,WAAU;0CACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;oCAAC,aAAY;;;;;;;;;;;0CAE3B,8OAAC,kIAAA,CAAA,gBAAa;0CACX,UAAU,GAAG,CAAC,CAAC,0BACd,8OAAC,kIAAA,CAAA,aAAU;wCAA8B,OAAO,UAAU,YAAY,CAAC,QAAQ;kDAC7E,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAuB,UAAU,IAAI;;;;;;8DACrD,8OAAC;oDAAK,WAAU;;wDAAyB,UAAU,KAAK;wDAAC;wDAAE,UAAU,OAAO;;;;;;;;;;;;;uCAH/D,UAAU,YAAY;;;;;;;;;;;;;;;;;;;;;;0BAY/C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAmB,WAAU;kCAAoC;;;;;;kCAChF,8OAAC,kIAAA,CAAA,SAAM;wBACL,OAAO,SAAS,gBAAgB,IAAI;wBACpC,eAAe,CAAC,QAAU,cAAc,oBAAoB;;0CAE5D,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,WAAU;0CACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0CAEd,8OAAC,kIAAA,CAAA,gBAAa;;kDACZ,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAChB,cAAA,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;kDAE5B,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAChB,cAAA,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxC", "debugId": null}}, {"offset": {"line": 2814, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/certificazioni/sections/CondizioniAmbientali.tsx"], "sourcesContent": ["'use client'\n\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Label } from '@/components/ui/label'\nimport { Input } from '@/components/ui/input'\nimport { Button } from '@/components/ui/button'\nimport { Cloud, Loader2, Settings, X } from 'lucide-react'\nimport { CertificazioneCavoCreate } from '@/types/certificazioni'\n\ninterface WeatherData {\n  temperature: number\n  humidity: number\n  city?: string\n  isDemo: boolean\n  source: string\n}\n\ninterface CondizioniAmbientaliProps {\n  formData: Partial<CertificazioneCavoCreate>\n  weatherData: WeatherData | null\n  isLoadingWeather: boolean\n  isWeatherOverride: boolean\n  onInputChange: (field: string, value: any) => void\n  onToggleWeatherOverride: () => void\n}\n\nexport function CondizioniAmbientali({\n  formData,\n  weatherData,\n  isLoadingWeather,\n  isWeatherOverride,\n  onInputChange,\n  onToggleWeatherOverride\n}: CondizioniAmbientaliProps) {\n  if (!weatherData) return null\n\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n      {/* Dati Meteorologici */}\n      <div className={`p-4 rounded-lg border-2 col-span-full ${\n        weatherData.isDemo\n          ? 'bg-amber-50 border-amber-200'\n          : 'bg-emerald-50 border-emerald-200'\n      }`}>\n        <div className=\"flex items-start gap-4\">\n          {isLoadingWeather ? (\n            <Loader2 className=\"h-5 w-5 animate-spin text-blue-600 mt-1\" />\n          ) : (\n            <div className=\"text-2xl\">\n              {weatherData.isDemo ? '🔧' : '🌤️'}\n            </div>\n          )}\n\n          <div className=\"flex-1\">\n            <div className=\"flex items-center justify-between mb-2\">\n              <div>\n                <div className=\"text-lg font-semibold text-gray-900\">\n                  {weatherData.temperature}°C • {weatherData.humidity}% UR\n                </div>\n                {weatherData.city && (\n                  <div className=\"text-sm text-gray-600\">{weatherData.city}</div>\n                )}\n              </div>\n\n              <Button\n                type=\"button\"\n                variant={isWeatherOverride ? \"default\" : \"outline\"}\n                size=\"sm\"\n                onClick={onToggleWeatherOverride}\n                className=\"h-8\"\n              >\n                {isWeatherOverride ? (\n                  <>\n                    <X className=\"h-3 w-3 mr-1\" />\n                    Automatico\n                  </>\n                ) : (\n                  <>\n                    <Settings className=\"h-3 w-3 mr-1\" />\n                    Manuale\n                    </>\n                  )}\n                </Button>\n              </div>\n\n            <div className=\"text-xs text-gray-500\">\n              📡 {weatherData.source}\n              {weatherData.isDemo && ' • Dati dimostrativi'}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Override Manuale */}\n      {isWeatherOverride && (\n        <div className=\"col-span-full p-4 bg-blue-50 border-2 border-blue-200 rounded-lg\">\n          <div className=\"flex items-center gap-2 mb-4\">\n            <Settings className=\"h-4 w-4 text-blue-600\" />\n            <span className=\"text-sm font-medium text-blue-800\">\n              Inserimento Manuale Parametri\n            </span>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"temperatura_prova\" className=\"text-sm font-medium text-gray-700\">\n                Temperatura (°C)\n              </Label>\n              <Input\n                id=\"temperatura_prova\"\n                type=\"number\"\n                step=\"0.1\"\n                value={formData.temperatura_prova || ''}\n                onChange={(e) => onInputChange('temperatura_prova', parseFloat(e.target.value))}\n                placeholder=\"20.0\"\n                className=\"h-11 text-sm\"\n              />\n              <p className=\"text-xs text-gray-500\">Range tipico: 15-30°C</p>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"umidita_prova\" className=\"text-sm font-medium text-gray-700\">\n                Umidità Relativa (%)\n              </Label>\n              <Input\n                id=\"umidita_prova\"\n                type=\"number\"\n                min=\"0\"\n                max=\"100\"\n                value={formData.umidita_prova || ''}\n                onChange={(e) => onInputChange('umidita_prova', parseFloat(e.target.value))}\n                placeholder=\"50\"\n                className=\"h-11 text-sm\"\n              />\n              <p className=\"text-xs text-gray-500\">Range tipico: 30-70%</p>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AANA;;;;;;AA0BO,SAAS,qBAAqB,EACnC,QAAQ,EACR,WAAW,EACX,gBAAgB,EAChB,iBAAiB,EACjB,aAAa,EACb,uBAAuB,EACG;IAC1B,IAAI,CAAC,aAAa,OAAO;IAEzB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAW,CAAC,sCAAsC,EACrD,YAAY,MAAM,GACd,iCACA,oCACJ;0BACA,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,iCACC,8OAAC,iNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;iDAEnB,8OAAC;4BAAI,WAAU;sCACZ,YAAY,MAAM,GAAG,OAAO;;;;;;sCAIjC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;;wDACZ,YAAY,WAAW;wDAAC;wDAAM,YAAY,QAAQ;wDAAC;;;;;;;gDAErD,YAAY,IAAI,kBACf,8OAAC;oDAAI,WAAU;8DAAyB,YAAY,IAAI;;;;;;;;;;;;sDAI5D,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAS,oBAAoB,YAAY;4CACzC,MAAK;4CACL,SAAS;4CACT,WAAU;sDAET,kCACC;;kEACE,8OAAC,4LAAA,CAAA,IAAC;wDAAC,WAAU;;;;;;oDAAiB;;6EAIhC;;kEACE,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;8CAO7C,8OAAC;oCAAI,WAAU;;wCAAwB;wCACjC,YAAY,MAAM;wCACrB,YAAY,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;;;;;YAO9B,mCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;gCAAK,WAAU;0CAAoC;;;;;;;;;;;;kCAKtD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAoB,WAAU;kDAAoC;;;;;;kDAGjF,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,MAAK;wCACL,OAAO,SAAS,iBAAiB,IAAI;wCACrC,UAAU,CAAC,IAAM,cAAc,qBAAqB,WAAW,EAAE,MAAM,CAAC,KAAK;wCAC7E,aAAY;wCACZ,WAAU;;;;;;kDAEZ,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAGvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAgB,WAAU;kDAAoC;;;;;;kDAG7E,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,KAAI;wCACJ,KAAI;wCACJ,OAAO,SAAS,aAAa,IAAI;wCACjC,UAAU,CAAC,IAAM,cAAc,iBAAiB,WAAW,EAAE,MAAM,CAAC,KAAK;wCACzE,aAAY;wCACZ,WAAU;;;;;;kDAEZ,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnD", "debugId": null}}, {"offset": {"line": 3110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/certificazioni/CertificazioneForm.tsx"], "sourcesContent": ["'use client'\n\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { FileText, Save, Loader2, AlertCircle, X } from 'lucide-react'\nimport { CertificazioneCavo, StrumentoCertificato } from '@/types/certificazioni'\nimport { useCertificazioneForm } from '@/hooks/useCertificazioneForm'\n\n// Componenti sezioni\nimport { InformazioniBase } from './sections/InformazioniBase'\nimport { CondizioniAmbientali } from './sections/CondizioniAmbientali'\nimport { MisurazioniTest } from './sections/MisurazioniTest'\nimport { NoteEStato } from './sections/NoteEStato'\n\ninterface CertificazioneFormProps {\n  cantiereId: number\n  certificazione?: CertificazioneCavo | null\n  strumenti: StrumentoCertificato[]\n  preselectedCavoId?: string\n  onSuccess: (certificazione: CertificazioneCavo) => void\n  onCancel: () => void\n}\n\n/**\n * Form di certificazione CEI 64-8 completamente ridisegnato\n * Layout moderno a singola colonna con sezioni organizzate\n */\nexport default function CertificazioneForm({\n  cantiereId,\n  certificazione,\n  strumenti,\n  preselectedCavoId,\n  onSuccess,\n  onCancel\n}: CertificazioneFormProps) {\n  const {\n    // Dati\n    formData,\n    cavi,\n    responsabili,\n    weatherData,\n\n    // Stati\n    isLoading,\n    isSaving,\n    isLoadingWeather,\n    error,\n    validationErrors,\n    isWeatherOverride,\n    isEdit,\n    isCavoLocked,\n\n    // Funzioni\n    handleInputChange,\n    handleSubmit,\n    setIsWeatherOverride,\n    onCancel: handleCancel\n  } = useCertificazioneForm({\n    cantiereId,\n    certificazione,\n    strumenti,\n    preselectedCavoId,\n    onSuccess,\n    onCancel\n  })\n\n  // Loading state\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center h-96\">\n        <div className=\"text-center\">\n          <Loader2 className=\"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600\" />\n          <p className=\"text-gray-600\">Caricamento dati certificazione...</p>\n        </div>\n      </div>\n    )\n  }\n\n  // Render del componente principale\n  return (\n    <div className=\"h-full w-full bg-gray-50\">\n      {/* Header Moderno */}\n      <div className=\"bg-white border-b border-gray-200 sticky top-0 z-10\">\n        <div className=\"max-w-4xl mx-auto px-6 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"p-2 bg-blue-100 rounded-lg\">\n                <FileText className=\"h-6 w-6 text-blue-600\" />\n              </div>\n              <div>\n                <h1 className=\"text-xl font-semibold text-gray-900\">\n                  {isEdit ? 'Modifica Certificazione' : 'Nuova Certificazione CEI 64-8'}\n                </h1>\n                {preselectedCavoId && (\n                  <p className=\"text-sm text-gray-500\">Cavo: {preselectedCavoId}</p>\n                )}\n              </div>\n            </div>\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={onCancel}\n              className=\"text-gray-400 hover:text-gray-600\"\n            >\n              <X className=\"h-5 w-5\" />\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Alert Errori */}\n      {error && (\n        <div className=\"bg-red-50 border-b border-red-200\">\n          <div className=\"max-w-4xl mx-auto px-6 py-4\">\n            <Alert variant=\"destructive\" className=\"border-red-200 bg-red-50\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          </div>\n        </div>\n      )}\n\n      {/* Contenuto Principale */}\n      <div className=\"max-w-4xl mx-auto px-6 py-8\">\n        <form onSubmit={(e) => { e.preventDefault(); handleSubmit(); }} className=\"space-y-8\">\n\n          {/* Sezione 1: Informazioni Base */}\n          <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\">\n            <div className=\"px-6 py-4 border-b border-gray-100 bg-gray-50\">\n              <h2 className=\"text-lg font-semibold text-gray-900\">📋 Informazioni Base</h2>\n              <p className=\"text-sm text-gray-600 mt-1\">Dati principali del cavo e operatore</p>\n            </div>\n            <div className=\"p-6\">\n              <InformazioniBase\n                formData={formData}\n                cavi={cavi}\n                responsabili={responsabili}\n                strumenti={strumenti}\n                validationErrors={validationErrors}\n                isCavoLocked={isCavoLocked}\n                onInputChange={handleInputChange}\n              />\n            </div>\n          </div>\n\n          {/* Sezione 2: Condizioni Ambientali */}\n          <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\">\n            <div className=\"px-6 py-4 border-b border-gray-100 bg-gray-50\">\n              <h2 className=\"text-lg font-semibold text-gray-900\">🌤️ Condizioni Ambientali</h2>\n              <p className=\"text-sm text-gray-600 mt-1\">Temperatura e umidità durante la certificazione</p>\n            </div>\n            <div className=\"p-6\">\n              <CondizioniAmbientali\n                formData={formData}\n                weatherData={weatherData}\n                isLoadingWeather={isLoadingWeather}\n                isWeatherOverride={isWeatherOverride}\n                onInputChange={handleInputChange}\n                onToggleWeatherOverride={() => setIsWeatherOverride(!isWeatherOverride)}\n              />\n            </div>\n          </div>\n\n          {/* Sezione 3: Misurazioni e Test */}\n          <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\">\n            <div className=\"px-6 py-4 border-b border-gray-100 bg-gray-50\">\n              <h2 className=\"text-lg font-semibold text-gray-900\">⚡ Misurazioni e Test CEI 64-8</h2>\n              <p className=\"text-sm text-gray-600 mt-1\">Valori di isolamento, continuità e parametri di prova</p>\n            </div>\n            <div className=\"p-6\">\n              <MisurazioniTest\n                formData={formData}\n                validationErrors={validationErrors}\n                onInputChange={handleInputChange}\n              />\n            </div>\n          </div>\n\n          {/* Sezione 4: Note e Stato */}\n          <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\">\n            <div className=\"px-6 py-4 border-b border-gray-100 bg-gray-50\">\n              <h2 className=\"text-lg font-semibold text-gray-900\">📝 Note e Stato Certificazione</h2>\n              <p className=\"text-sm text-gray-600 mt-1\">Osservazioni aggiuntive e stato finale</p>\n            </div>\n            <div className=\"p-6\">\n              <NoteEStato\n                formData={formData}\n                onInputChange={handleInputChange}\n              />\n            </div>\n          </div>\n\n          {/* Footer Azioni */}\n          <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n            <div className=\"flex justify-end gap-4\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={onCancel}\n                disabled={isSaving}\n                className=\"px-8 py-2\"\n              >\n                Annulla\n              </Button>\n              <Button\n                type=\"submit\"\n                disabled={isSaving}\n                className=\"px-8 py-2 bg-blue-600 hover:bg-blue-700\"\n              >\n                {isSaving ? (\n                  <>\n                    <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\n                    Salvando...\n                  </>\n                ) : (\n                  <>\n                    <Save className=\"h-4 w-4 mr-2\" />\n                    {isEdit ? 'Aggiorna Certificazione' : 'Salva Certificazione'}\n                  </>\n                )}\n              </Button>\n            </div>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AAEA,qBAAqB;AACrB;AACA;AACA;AACA;AAZA;;;;;;;;;;AA2Be,SAAS,mBAAmB,EACzC,UAAU,EACV,cAAc,EACd,SAAS,EACT,iBAAiB,EACjB,SAAS,EACT,QAAQ,EACgB;IACxB,MAAM,EACJ,OAAO;IACP,QAAQ,EACR,IAAI,EACJ,YAAY,EACZ,WAAW,EAEX,QAAQ;IACR,SAAS,EACT,QAAQ,EACR,gBAAgB,EAChB,KAAK,EACL,gBAAgB,EAChB,iBAAiB,EACjB,MAAM,EACN,YAAY,EAEZ,WAAW;IACX,iBAAiB,EACjB,YAAY,EACZ,oBAAoB,EACpB,UAAU,YAAY,EACvB,GAAG,CAAA,GAAA,qIAAA,CAAA,wBAAqB,AAAD,EAAE;QACxB;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,gBAAgB;IAChB,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,mCAAmC;IACnC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DACX,SAAS,4BAA4B;;;;;;4CAEvC,mCACC,8OAAC;gDAAE,WAAU;;oDAAwB;oDAAO;;;;;;;;;;;;;;;;;;;0CAIlD,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOpB,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAc,WAAU;;0CACrC,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC,iIAAA,CAAA,mBAAgB;0CAAE;;;;;;;;;;;;;;;;;;;;;;0BAO3B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,UAAU,CAAC;wBAAQ,EAAE,cAAc;wBAAI;oBAAgB;oBAAG,WAAU;;sCAGxE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsC;;;;;;sDACpD,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAE5C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oKAAA,CAAA,mBAAgB;wCACf,UAAU;wCACV,MAAM;wCACN,cAAc;wCACd,WAAW;wCACX,kBAAkB;wCAClB,cAAc;wCACd,eAAe;;;;;;;;;;;;;;;;;sCAMrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsC;;;;;;sDACpD,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAE5C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,wKAAA,CAAA,uBAAoB;wCACnB,UAAU;wCACV,aAAa;wCACb,kBAAkB;wCAClB,mBAAmB;wCACnB,eAAe;wCACf,yBAAyB,IAAM,qBAAqB,CAAC;;;;;;;;;;;;;;;;;sCAM3D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsC;;;;;;sDACpD,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAE5C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,mKAAA,CAAA,kBAAe;wCACd,UAAU;wCACV,kBAAkB;wCAClB,eAAe;;;;;;;;;;;;;;;;;sCAMrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsC;;;;;;sDACpD,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAE5C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,8JAAA,CAAA,aAAU;wCACT,UAAU;wCACV,eAAe;;;;;;;;;;;;;;;;;sCAMrB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,SAAS;wCACT,UAAU;wCACV,WAAU;kDACX;;;;;;kDAGD,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,UAAU;wCACV,WAAU;kDAET,yBACC;;8DACE,8OAAC,iNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAA8B;;yEAInD;;8DACE,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDACf,SAAS,4BAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1D", "debugId": null}}]}