'use client'

import { useState, useEffect } from 'react'
import '@/styles/admin.css'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useAuth } from '@/contexts/AuthContext'
import { useCantiere } from '@/hooks/useCantiere'
import { CantiereErrorBoundary } from '@/components/cantiere/CantiereErrorBoundary'
import { reportsApi, cantieriApi } from '@/lib/api'
import { ReportAvanzamento, ReportBOQ, Cantiere } from '@/types'
import {
  BarChart3,
  Download,
  Calendar,
  TrendingUp,
  Target,
  Activity,
  Clock,
  CheckCircle,
  Loader2,
  AlertCircle,
  AlertTriangle,
  FileText,
  Package,
  Users,
  Zap,
  RefreshCw
} from 'lucide-react'
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts'

export default function ReportsPage() {
  const [activeTab, setActiveTab] = useState('avanzamento')
  const [selectedPeriod, setSelectedPeriod] = useState('month')
  const [reportAvanzamento, setReportAvanzamento] = useState<any>(null)
  const [reportBOQ, setReportBOQ] = useState<any>(null)
  const [reportUtilizzoBobine, setReportUtilizzoBobine] = useState<any>(null)
  const [reportProgress, setReportProgress] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  const { user, isLoading: authLoading } = useAuth()
  const { cantiereId, cantiere, isValidCantiere, isLoading: cantiereLoading, error: cantiereError } = useCantiere()

  // Load all basic reports on component mount - MIGLIORATO per evitare race conditions
  useEffect(() => {

    const loadAllReports = async () => {
      setIsLoading(true)
      try {
        // Usa il cantiere dal nuovo hook per gestione robusta
        const currentCantiereId = cantiereId

        // Test del token
        const token = localStorage.getItem('token')
        console.log('🏗️ ReportsPage: Caricamento report per cantiere:', currentCantiereId)

        if (!currentCantiereId || currentCantiereId <= 0) {
          console.warn('🏗️ ReportsPage: Nessun cantiere valido selezionato')
          setError(cantiereError || 'Nessun cantiere selezionato. Seleziona un cantiere da "Gestisci Cantieri".')
          setIsLoading(false)
          return
        }

        // Create individual promises that handle their own errors - come nella webapp originale
        // Aggiungiamo il parametro formato=video per ottenere i dati invece dei file

        // Helper function per timeout - aumentato timeout per API lente
        const fetchWithTimeout = (url: string, options: any, timeout = 30000) => {
          return Promise.race([
            fetch(url, options),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error(`Timeout dopo ${timeout/1000}s per ${url}`)), timeout)
            )
          ]) as Promise<Response>
        }

        const progressPromise = fetchWithTimeout(`http://localhost:8001/api/reports/${currentCantiereId}/progress?formato=video`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        })
          .then(res => {
            return res.json()
          })
          .then(data => {
            return data
          })
          .catch(err => {
            return { content: null, error: err.message.includes('Timeout') ? 'Timeout API Progress' : 'Errore API Progress' }
          })

        const boqPromise = fetchWithTimeout(`http://localhost:8001/api/reports/${currentCantiereId}/boq?formato=video`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        })
          .then(res => {
            return res.json()
          })
          .then(data => {
            return data
          })
          .catch(err => {
            return { content: null, error: err.message.includes('Timeout') ? 'Timeout API BOQ' : 'Errore API BOQ' }
          })

        const utilizzoPromise = fetchWithTimeout(`http://localhost:8001/api/reports/${currentCantiereId}/storico-bobine?formato=video`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        })
          .then(res => {
            return res.json()
          })
          .then(data => {
            return data
          })
          .catch(err => {
            return { content: null, error: err.message.includes('Timeout') ? 'Timeout API Utilizzo Bobine' : 'Errore API Utilizzo Bobine' }
          })

        // Wait for all promises to resolve (they won't reject due to the catch handlers)
        const [progressData, boqData, utilizzoData] = await Promise.all([
          progressPromise,
          boqPromise,
          utilizzoPromise
        ])

        // Debug: vediamo la struttura dei dati BOQ

        // Set the data for each report - manteniamo la struttura completa con .content
        setReportProgress(progressData)
        setReportBOQ(boqData)
        setReportUtilizzoBobine(utilizzoData)

        // Check for timeout errors specifically
        const hasTimeoutErrors = [progressData, boqData, utilizzoData].some(data =>
          data?.error?.includes('Timeout')
        )

        // Only set error to null if we successfully loaded at least one report
        if (progressData?.content || boqData?.content || utilizzoData?.content ||
            progressData || boqData || utilizzoData) {
          if (hasTimeoutErrors) {
            setError('Alcuni report hanno riscontrato timeout. I dati disponibili sono mostrati sotto.')
          } else {
            setError('')
          }
        } else {
          setError('Errore nel caricamento dei report. Riprova più tardi.')
        }

        // Always stop loading after processing the data
        setIsLoading(false)
      } catch (err) {
        // This catch block should rarely be hit due to the individual error handling above
        setError('Errore nel caricamento dei report. Riprova più tardi.')
      } finally {
        setIsLoading(false)
      }
    }

    // Aspetta che l'autenticazione e il cantiere siano caricati
    if (authLoading || cantiereLoading) {
      console.log('🏗️ ReportsPage: Caricamento in corso, attendo...', { authLoading, cantiereLoading })
      return
    }

    // Carica i report se c'è un cantiere valido
    if (isValidCantiere && cantiereId && cantiereId > 0) {
      console.log('🏗️ ReportsPage: Cantiere valido trovato, carico report')
      loadAllReports()
    } else {
      console.warn('🏗️ ReportsPage: Nessun cantiere valido')
      setIsLoading(false)
      setError(cantiereError || 'Nessun cantiere selezionato. Seleziona un cantiere da "Gestisci Cantieri".')
    }
  }, [cantiereId, isValidCantiere, authLoading, cantiereLoading, cantiereError])

  const handleRefresh = () => {
    // Ricarica i report per il cantiere corrente
    if (isValidCantiere && cantiereId) {
      console.log('🏗️ ReportsPage: Refresh report per cantiere:', cantiereId)
      setIsLoading(true)
      setError('')
      setReportProgress(null)
      setReportBOQ(null)
      setReportUtilizzoBobine(null)
      setReportAvanzamento(null)
      // Ricarica direttamente senza reload della pagina
      loadAllReports()
    } else {
      console.warn('🏗️ ReportsPage: Impossibile fare refresh, nessun cantiere valido')
    }
  }

  const handleExportReport = async (reportType: string, format: string = 'pdf') => {
    try {
      const currentCantiereId = cantiere?.id_cantiere
      if (!currentCantiereId) {
        return
      }

      let response
      switch (reportType) {
        case 'progress':
          response = await reportsApi.getReportProgress(currentCantiereId)
          break
        case 'boq':
          response = await reportsApi.getReportBOQ(currentCantiereId)
          break
        case 'utilizzo-bobine':
          response = await reportsApi.getReportUtilizzoBobine(currentCantiereId)
          break
        default:
          return
      }

      if (response.data.file_url) {
        // Apri il file in una nuova finestra
        window.open(response.data.file_url, '_blank')
      }
    } catch (error) {
    }
  }

  // Calcolo IAP (Indice di Avanzamento Ponderato)
  const calculateIAP = (nTot: number, nInst: number, nColl: number, nCert: number): number => {
    const Wp = 2.0  // Peso fase Posa
    const Wc = 1.5  // Peso fase Collegamento
    const Wz = 0.5  // Peso fase Certificazione

    if (nTot === 0) return 0

    const sforzoSoloInstallati = (nInst - nColl) * Wp
    const sforzoSoloCollegati = (nColl - nCert) * (Wp + Wc)
    const sforzoCertificati = nCert * (Wp + Wc + Wz)
    const numeratore = sforzoSoloInstallati + sforzoSoloCollegati + sforzoCertificati

    const denominatore = nTot * (Wp + Wc + Wz)
    return Math.round((numeratore / denominatore) * 10000) / 100
  }

  return (
    <CantiereErrorBoundary>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
        <div className="max-w-[90%] mx-auto py-6 space-y-6">



        {/* Loading State */}
        {(isLoading || authLoading) ? (
          <div className="flex items-center justify-center py-12">
            <div className="flex items-center gap-2">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span>Caricamento report...</span>
            </div>
          </div>
        ) : error ? (
          <div className="p-6 border border-amber-200 rounded-lg bg-amber-50">
            <div className="flex items-center mb-4">
              <AlertCircle className="h-5 w-5 text-amber-600 mr-2" />
              <span className="text-amber-800 font-medium">
                {error.includes('Nessun cantiere selezionato') || error.includes('Cantiere non selezionato') ? 'Cantiere non selezionato' :
                 error.includes('timeout') || error.includes('Timeout') ? 'Timeout API' : 'Errore caricamento report'}
              </span>
            </div>
            <p className="text-amber-700 mb-4">{error}</p>
            {error.includes('timeout') || error.includes('Timeout') ? (
              <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded">
                <p className="text-blue-800 text-sm">
                  💡 <strong>Suggerimento:</strong> Le API stanno impiegando più tempo del previsto.
                  Prova ad aggiornare la pagina o riprova tra qualche minuto.
                </p>
              </div>
            ) : null}
            <div className="flex gap-2">
              <Button
                onClick={() => window.location.href = '/cantieri'}
                className="bg-amber-600 hover:bg-amber-700 text-white"
              >
                Gestisci Cantieri
              </Button>
              <Button
                variant="outline"
                onClick={handleRefresh}
                className="border-amber-600 text-amber-700 hover:bg-amber-100"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Riprova
              </Button>
            </div>
          </div>
        ) : (
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            {/* Navigazione Tab Stile Admin */}
            <div className="bg-white rounded-lg border border-slate-200 shadow-sm p-1 mb-6 mt-6">
              <div className="grid grid-cols-4 gap-1 h-auto bg-transparent p-0">
                <button
                  onClick={() => setActiveTab('avanzamento')}
                  className={`admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 border border-transparent ${
                    activeTab === 'avanzamento'
                      ? 'bg-blue-50 text-blue-700 border-blue-200 shadow-sm'
                      : 'hover:bg-slate-50'
                  }`}
                  data-state={activeTab === 'avanzamento' ? 'active' : 'inactive'}
                >
                  <Target className="h-4 w-4" />
                  <span className="font-medium">Avanzamento</span>
                </button>
                <button
                  onClick={() => setActiveTab('boq')}
                  className={`admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 border border-transparent ${
                    activeTab === 'boq'
                      ? 'bg-blue-50 text-blue-700 border-blue-200 shadow-sm'
                      : 'hover:bg-slate-50'
                  }`}
                  data-state={activeTab === 'boq' ? 'active' : 'inactive'}
                >
                  <FileText className="h-4 w-4" />
                  <span className="font-medium">BOQ</span>
                </button>
                <button
                  onClick={() => setActiveTab('bobine')}
                  className={`admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 border border-transparent ${
                    activeTab === 'bobine'
                      ? 'bg-blue-50 text-blue-700 border-blue-200 shadow-sm'
                      : 'hover:bg-slate-50'
                  }`}
                  data-state={activeTab === 'bobine' ? 'active' : 'inactive'}
                >
                  <Package className="h-4 w-4" />
                  <span className="font-medium">Bobine</span>
                </button>
                <button
                  onClick={() => setActiveTab('produttivita')}
                  className={`admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 border border-transparent ${
                    activeTab === 'produttivita'
                      ? 'bg-blue-50 text-blue-700 border-blue-200 shadow-sm'
                      : 'hover:bg-slate-50'
                  }`}
                  data-state={activeTab === 'produttivita' ? 'active' : 'inactive'}
                >
                  <Zap className="h-4 w-4" />
                  <span className="font-medium">Produttività</span>
                </button>
              </div>
            </div>

            {/* Tab Content: Avanzamento */}
            <TabsContent value="avanzamento" className="space-y-6">
              {reportProgress?.content ? (
                <>
                  {/* KPI Cards Moderne */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
                      <div className="flex items-center justify-between mb-4">
                        <div className="p-2 bg-blue-50 rounded-lg">
                          <Target className="h-5 w-5 text-blue-600" />
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-bold text-gray-900">
                            {reportProgress.content.metri_totali?.toLocaleString() || 0}
                          </div>
                          <div className="text-xs text-blue-600 font-medium">metri</div>
                        </div>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-600 mb-1">Metri Totali</h3>
                        <p className="text-xs text-gray-500">
                          {reportProgress.content.totale_cavi || 0} cavi totali
                        </p>
                      </div>
                    </div>

                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
                      <div className="flex items-center justify-between mb-4">
                        <div className="p-2 bg-green-50 rounded-lg">
                          <CheckCircle className="h-5 w-5 text-green-600" />
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-bold text-gray-900">
                            {reportProgress.content.metri_posati?.toLocaleString() || 0}
                          </div>
                          <div className="text-xs text-green-600 font-medium">metri</div>
                        </div>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-600 mb-2">Metri Posati</h3>
                        <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                          <div
                            className="bg-green-500 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${Math.min(reportProgress.content.percentuale_avanzamento || 0, 100)}%` }}
                          ></div>
                        </div>
                        <p className="text-xs text-gray-500">
                          {reportProgress.content.percentuale_avanzamento?.toFixed(1) || 0}% completato
                        </p>
                      </div>
                    </div>

                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
                      <div className="flex items-center justify-between mb-4">
                        <div className="p-2 bg-purple-50 rounded-lg">
                          <TrendingUp className="h-5 w-5 text-purple-600" />
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-bold text-gray-900">
                            {reportProgress.content.media_giornaliera?.toFixed(1) || 0}
                          </div>
                          <div className="text-xs text-purple-600 font-medium">m/giorno</div>
                        </div>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-600 mb-1">Media Giornaliera</h3>
                        <div className="flex items-center">
                          <Activity className="h-3 w-3 text-purple-500 mr-1" />
                          <span className="text-xs text-gray-500">
                            {reportProgress.content.giorni_lavorativi_effettivi || 0} giorni attivi
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
                      <div className="flex items-center justify-between mb-4">
                        <div className="p-2 bg-orange-50 rounded-lg">
                          <Clock className="h-5 w-5 text-orange-600" />
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-bold text-gray-900">
                            {reportProgress.content.data_completamento ?
                              new Date(reportProgress.content.data_completamento).toLocaleDateString('it-IT') :
                              'Da calcolare'
                            }
                          </div>
                          <div className="text-xs text-orange-600 font-medium">stima</div>
                        </div>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-600 mb-1">Completamento</h3>
                        <div className="flex items-center">
                          <Calendar className="h-3 w-3 text-orange-500 mr-1" />
                          <span className="text-xs text-gray-500">
                            {reportProgress.content.giorni_stimati || 0} giorni rimanenti
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Charts Moderni */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Posa Recente - Chart Migliorato */}
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                      <div className="mb-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-1">Posa Recente</h3>
                        <p className="text-sm text-gray-600">Ultimi 10 giorni di attività</p>
                      </div>
                      <div className="h-80">
                        <ResponsiveContainer width="100%" height="100%">
                          <LineChart data={[...(reportProgress.content.posa_recente || [])].reverse()}>
                            <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                            <XAxis
                              dataKey="data"
                              tick={{ fontSize: 12, fill: '#64748b' }}
                              axisLine={{ stroke: '#e2e8f0' }}
                            />
                            <YAxis
                              tick={{ fontSize: 12, fill: '#64748b' }}
                              axisLine={{ stroke: '#e2e8f0' }}
                            />
                            <Tooltip
                              contentStyle={{
                                backgroundColor: 'white',
                                border: '1px solid #e2e8f0',
                                borderRadius: '8px',
                                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                                fontSize: '14px'
                              }}
                              labelStyle={{ color: '#374151', fontWeight: '500' }}
                            />
                            <Line
                              type="monotone"
                              dataKey="metri"
                              stroke="#3b82f6"
                              strokeWidth={3}
                              dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                              activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2, fill: 'white' }}
                              name="Metri Posati"
                            />
                          </LineChart>
                        </ResponsiveContainer>
                      </div>
                    </div>

                    {/* Statistiche Cavi - Widget Moderno */}
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                      <div className="mb-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-1">Stato Cavi</h3>
                        <p className="text-sm text-gray-600">Distribuzione per stato di avanzamento</p>
                      </div>
                      <div className="space-y-6">
                        <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200">
                          <div className="flex items-center gap-3">
                            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span className="text-sm font-medium text-gray-700">Cavi Posati</span>
                          </div>
                          <span className="text-lg font-bold text-green-700">
                            {reportProgress.content.cavi_posati || 0}
                          </span>
                        </div>
                        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200">
                          <div className="flex items-center gap-3">
                            <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                            <span className="text-sm font-medium text-gray-700">Cavi Rimanenti</span>
                          </div>
                          <span className="text-lg font-bold text-gray-700">
                            {reportProgress.content.cavi_rimanenti || 0}
                          </span>
                        </div>
                        <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200">
                          <div className="flex items-center gap-3">
                            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                            <span className="text-sm font-medium text-gray-700">Percentuale Completamento</span>
                          </div>
                          <span className="text-lg font-bold text-blue-700">
                            {reportProgress.content.percentuale_cavi?.toFixed(1) || 0}%
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
                  <Target className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <h3 className="text-lg font-semibold text-gray-700 mb-2">Nessun Dato Disponibile</h3>
                  <p className="text-gray-500">Nessun dato di avanzamento disponibile per questo cantiere</p>
                </div>
              )}
            </TabsContent>

            {/* Tab Content: BOQ */}
            <TabsContent value="boq" className="space-y-6">
              {reportBOQ?.error ? (
                <div className="bg-white rounded-lg shadow-sm border border-amber-200 p-12 text-center">
                  <AlertCircle className="h-12 w-12 mx-auto mb-4 text-amber-500" />
                  <h3 className="text-lg font-semibold text-gray-700 mb-2">API BOQ Temporaneamente Non Disponibile</h3>
                  <p className="text-gray-500 mb-4">Il servizio BOQ sta riscontrando problemi di performance.</p>
                  <p className="text-sm text-gray-400">Stiamo lavorando per risolvere il problema.</p>
                </div>
              ) : reportBOQ?.content ? (
                <>


                  {/* Alert Metri Orfani */}
                  {reportBOQ.content.metri_orfani && reportBOQ.content.metri_orfani.metri_orfani_totali > 0 && (
                    <Card className="border-red-200 bg-red-50">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-red-700 flex items-center">
                          <AlertTriangle className="h-5 w-5 mr-2" />
                          🚨 METRI POSATI SENZA TRACCIABILITÀ BOBINA
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-red-800 font-medium mb-2">
                          <strong>{reportBOQ.content.metri_orfani.metri_orfani_totali}m</strong> installati con BOBINA_VUOTA
                          ({reportBOQ.content.metri_orfani.num_cavi_orfani} cavi)
                        </p>
                        <div className="text-sm text-red-700 space-y-1">
                          {Array.isArray(reportBOQ.content.metri_orfani.dettaglio_per_categoria) ?
                            reportBOQ.content.metri_orfani.dettaglio_per_categoria.map((categoria: any, index: number) => (
                              <div key={index}>
                                • <strong>{categoria.tipologia} {categoria.formazione}</strong>: {categoria.metri_orfani}m ({categoria.num_cavi} cavi)
                              </div>
                            )) : (
                              <div>Dettaglio metri orfani non disponibile</div>
                            )
                          }
                        </div>
                        <div className="mt-3 p-3 bg-amber-50 border border-amber-200 rounded">
                          <p className="text-amber-800 text-sm">
                            ⚠️ <strong>NOTA:</strong> I metri orfani NON sono inclusi nel calcolo acquisti.
                            Prima di acquistare, verificare se questi metri possono essere associati a bobine esistenti.
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* Riepilogo Generale - KPI Cards Moderne */}
                  {reportBOQ.content.riepilogo && (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
                        <div className="flex items-center justify-between mb-4">
                          <div className="p-2 bg-red-50 rounded-lg">
                            <AlertTriangle className="h-5 w-5 text-red-600" />
                          </div>
                          <div className="text-right">
                            <div className="text-2xl font-bold text-gray-900">
                              {reportBOQ.content.riepilogo.totale_metri_mancanti?.toLocaleString() || 0}
                            </div>
                            <div className="text-xs text-red-600 font-medium">metri</div>
                          </div>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-600 mb-1">Metri da Acquistare</h3>
                          <p className="text-xs text-gray-500">per completamento progetto</p>
                        </div>
                      </div>

                      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
                        <div className="flex items-center justify-between mb-4">
                          <div className="p-2 bg-green-50 rounded-lg">
                            <Package className="h-5 w-5 text-green-600" />
                          </div>
                          <div className="text-right">
                            <div className="text-2xl font-bold text-gray-900">
                              {reportBOQ.content.riepilogo.totale_metri_residui?.toLocaleString() || 0}
                            </div>
                            <div className="text-xs text-green-600 font-medium">metri</div>
                          </div>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-600 mb-1">Metri Residui</h3>
                          <p className="text-xs text-gray-500">disponibili in magazzino</p>
                        </div>
                      </div>

                      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
                        <div className="flex items-center justify-between mb-4">
                          <div className="p-2 bg-purple-50 rounded-lg">
                            <CheckCircle className="h-5 w-5 text-purple-600" />
                          </div>
                          <div className="text-right">
                            <div className="text-2xl font-bold text-gray-900">
                              {reportBOQ.content.riepilogo.percentuale_completamento?.toFixed(1) || 0}
                            </div>
                            <div className="text-xs text-purple-600 font-medium">%</div>
                          </div>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-600 mb-1">Completamento</h3>
                          <p className="text-xs text-gray-500">progetto completato</p>
                        </div>
                      </div>

                      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
                        <div className="flex items-center justify-between mb-4">
                          <div className="p-2 bg-orange-50 rounded-lg">
                            <FileText className="h-5 w-5 text-orange-600" />
                          </div>
                          <div className="text-right">
                            <div className="text-2xl font-bold text-gray-900">
                              {reportBOQ.content.riepilogo.categorie_necessitano_acquisto || 0}
                            </div>
                            <div className="text-xs text-orange-600 font-medium">categorie</div>
                          </div>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-600 mb-1">Categorie</h3>
                          <p className="text-xs text-gray-500">necessitano acquisto</p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Distinta Materiali - Tabella Moderna */}
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="text-left p-4 text-xs font-medium text-gray-500 uppercase tracking-wider">Tipologia</th>
                            <th className="text-left p-4 text-xs font-medium text-gray-500 uppercase tracking-wider">Formazione</th>
                            <th className="text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider">Cavi</th>
                            <th className="text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider">Metri Teorici</th>
                            <th className="text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider">Metri Posati</th>
                            <th className="text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider">Metri da Posare</th>
                            <th className="text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider">Bobine</th>
                            <th className="text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider">Metri Residui</th>
                            <th className="text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider">Metri Mancanti</th>
                            <th className="text-center p-4 text-xs font-medium text-gray-500 uppercase tracking-wider">Acquisto</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {reportBOQ.content.distinta_materiali?.map((item: any, index: number) => (
                            <tr key={index} className={`hover:bg-gray-50 transition-colors duration-150 ${item.ha_bobina_vuota ? 'bg-red-50' : ''}`}>
                              <td className="p-4 text-sm font-medium text-gray-900">{item.tipologia}</td>
                              <td className="p-4 text-sm text-gray-700">{item.formazione}</td>
                              <td className="p-4 text-sm text-gray-700 text-right">{item.num_cavi}</td>
                              <td className="p-4 text-sm text-gray-700 text-right">{item.metri_teorici_totali?.toLocaleString()}</td>
                              <td className="p-4 text-sm text-gray-700 text-right">{item.metri_reali_posati?.toLocaleString()}</td>
                              <td className="p-4 text-sm text-gray-700 text-right">{item.metri_da_posare?.toLocaleString()}</td>
                              <td className="p-4 text-sm text-gray-700 text-right">{item.num_bobine}</td>
                              <td className="p-4 text-sm text-gray-700 text-right">{item.metri_residui?.toLocaleString()}</td>
                              <td className="p-4 text-sm font-medium text-right">
                                {item.metri_mancanti > 0 ? (
                                  <span className="text-red-600">{item.metri_mancanti?.toLocaleString()}m</span>
                                ) : (
                                  <span className="text-green-600">0m</span>
                                )}
                              </td>
                              <td className="p-4 text-center">
                                {item.necessita_acquisto ? (
                                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    Sì
                                  </span>
                                ) : (
                                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    No
                                  </span>
                                )}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>

                  {/* Bobine per Tipo - Tabella Moderna */}
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="text-left p-4 text-xs font-medium text-gray-500 uppercase tracking-wider">Tipologia</th>
                            <th className="text-left p-4 text-xs font-medium text-gray-500 uppercase tracking-wider">Formazione</th>
                            <th className="text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider">Numero Bobine</th>
                            <th className="text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider">Metri Disponibili</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {reportBOQ.content.bobine_per_tipo?.map((item: any, index: number) => (
                            <tr key={index} className="hover:bg-gray-50 transition-colors duration-150">
                              <td className="p-4 text-sm font-medium text-gray-900">{item.tipologia}</td>
                              <td className="p-4 text-sm text-gray-700">{item.formazione}</td>
                              <td className="p-4 text-sm text-gray-700 text-right">{item.num_bobine}</td>
                              <td className="p-4 text-sm font-medium text-right">
                                <span className={item.metri_disponibili > 0 ? 'text-green-600' : 'text-red-600'}>
                                  {item.metri_disponibili?.toLocaleString()}m
                                </span>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </>
              ) : (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
                  <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <h3 className="text-lg font-semibold text-gray-700 mb-2">Nessun Dato Disponibile</h3>
                  <p className="text-gray-500">Nessun dato BOQ disponibile per questo cantiere</p>
                </div>
              )}
            </TabsContent>

            {/* Tab Content: Bobine */}
            <TabsContent value="bobine" className="space-y-6">
              {reportUtilizzoBobine?.content ? (
                <>


                  {/* Statistiche Bobine - KPI Cards Moderne */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
                      <div className="flex items-center justify-between mb-4">
                        <div className="p-2 bg-blue-50 rounded-lg">
                          <Package className="h-5 w-5 text-blue-600" />
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-bold text-gray-900">
                            {reportUtilizzoBobine.content.totale_bobine || 0}
                          </div>
                          <div className="text-xs text-blue-600 font-medium">bobine</div>
                        </div>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-600 mb-1">Totale Bobine</h3>
                        <p className="text-xs text-gray-500">bobine nel cantiere</p>
                      </div>
                    </div>

                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
                      <div className="flex items-center justify-between mb-4">
                        <div className="p-2 bg-green-50 rounded-lg">
                          <Activity className="h-5 w-5 text-green-600" />
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-bold text-gray-900">
                            {reportUtilizzoBobine.content.bobine?.filter((b: any) =>
                              b.stato === 'In uso' || b.stato === 'Disponibile'
                            ).length || 0}
                          </div>
                          <div className="text-xs text-green-600 font-medium">attive</div>
                        </div>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-600 mb-1">Bobine Attive</h3>
                        <p className="text-xs text-gray-500">disponibili/in uso</p>
                      </div>
                    </div>

                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
                      <div className="flex items-center justify-between mb-4">
                        <div className="p-2 bg-purple-50 rounded-lg">
                          <TrendingUp className="h-5 w-5 text-purple-600" />
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-bold text-gray-900">
                            {reportUtilizzoBobine.content.bobine?.length > 0 ?
                              (reportUtilizzoBobine.content.bobine.reduce((acc: number, b: any) =>
                                acc + (b.percentuale_utilizzo || 0), 0) / reportUtilizzoBobine.content.bobine.length
                              ).toFixed(1) : 0}
                          </div>
                          <div className="text-xs text-purple-600 font-medium">%</div>
                        </div>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-600 mb-1">Utilizzo Medio</h3>
                        <p className="text-xs text-gray-500">utilizzo medio</p>
                      </div>
                    </div>
                  </div>

                  {/* Lista Bobine - Tabella Moderna */}
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="text-left p-4 text-xs font-medium text-gray-500 uppercase tracking-wider">Codice</th>
                            <th className="text-left p-4 text-xs font-medium text-gray-500 uppercase tracking-wider">Tipologia</th>
                            <th className="text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider">Metri Totali</th>
                            <th className="text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider">Metri Utilizzati</th>
                            <th className="text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider">Metri Residui</th>
                            <th className="text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider">Utilizzo %</th>
                            <th className="text-left p-4 text-xs font-medium text-gray-500 uppercase tracking-wider">Stato</th>
                            <th className="text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider">Cavi</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {reportUtilizzoBobine.content.bobine?.map((bobina: any, index: number) => (
                            <tr key={index} className="hover:bg-gray-50 transition-colors duration-150">
                              <td className="p-4 text-sm font-medium text-gray-900">{bobina.codice}</td>
                              <td className="p-4 text-sm text-gray-700">{bobina.tipologia}</td>
                              <td className="p-4 text-sm text-gray-700 text-right">{bobina.metri_totali?.toLocaleString()}</td>
                              <td className="p-4 text-sm text-gray-700 text-right">{bobina.metri_utilizzati?.toLocaleString()}</td>
                              <td className="p-4 text-sm text-gray-700 text-right">{bobina.metri_residui?.toLocaleString()}</td>
                              <td className="p-4 text-sm text-gray-700 text-right">
                                <div className="flex items-center justify-end gap-2">
                                  <span className="text-sm font-medium">{bobina.percentuale_utilizzo?.toFixed(1)}%</span>
                                  <div className="w-16 bg-gray-200 rounded-full h-2">
                                    <div
                                      className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                                      style={{ width: `${Math.min(bobina.percentuale_utilizzo || 0, 100)}%` }}
                                    ></div>
                                  </div>
                                </div>
                              </td>
                              <td className="p-4">
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                  bobina.stato === 'Disponibile' ? 'bg-green-100 text-green-800' :
                                  bobina.stato === 'In uso' ? 'bg-blue-100 text-blue-800' :
                                  bobina.stato === 'Terminata' ? 'bg-gray-100 text-gray-800' :
                                  bobina.stato === 'Over' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'
                                }`}>
                                  {bobina.stato}
                                </span>
                              </td>
                              <td className="p-4 text-sm text-gray-700 text-right">{bobina.totale_cavi_associati || 0}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </>
              ) : (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
                  <Package className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <h3 className="text-lg font-semibold text-gray-700 mb-2">Nessun Dato Disponibile</h3>
                  <p className="text-gray-500">Nessun dato bobine disponibile per questo cantiere</p>
                </div>
              )}
            </TabsContent>

            {/* Tab Content: Produttività */}
            <TabsContent value="produttivita" className="space-y-6">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
                <div className="p-4 bg-blue-50 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center">
                  <Zap className="h-10 w-10 text-blue-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Produttività</h3>
                <p className="text-gray-600 mb-2">Funzionalità in fase di sviluppo</p>
                <p className="text-sm text-gray-500">
                  Includerà calcoli IAP, statistiche team e analisi performance
                </p>
              </div>
            </TabsContent>

          </Tabs>
        )}

        </div>
      </div>
    </CantiereErrorBoundary>
  )
}
