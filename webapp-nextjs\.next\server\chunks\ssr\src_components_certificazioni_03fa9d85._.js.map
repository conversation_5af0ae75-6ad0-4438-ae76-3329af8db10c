{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/certificazioni/sections/InformazioniBase.tsx"], "sourcesContent": ["'use client'\n\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Label } from '@/components/ui/label'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { CertificazioneCavoCreate, StrumentoCertificato } from '@/types/certificazioni'\n\ninterface InformazioniBaseProps {\n  formData: Partial<CertificazioneCavoCreate>\n  cavi: any[]\n  responsabili: any[]\n  strumenti: StrumentoCertificato[]\n  validationErrors: Record<string, string>\n  isCavoLocked: boolean\n  onInputChange: (field: string, value: any) => void\n}\n\nexport function InformazioniBase({\n  formData,\n  cavi,\n  responsabili,\n  strumenti,\n  validationErrors,\n  isCavoLocked,\n  onInputChange\n}: InformazioniBaseProps) {\n  return (\n    <Card>\n      <CardHeader className=\"pb-1 px-3 pt-2\">\n        <CardTitle className=\"text-sm font-semibold\">Informazioni Base</CardTitle>\n      </CardHeader>\n      <CardContent className=\"space-y-2 pt-0 px-3 pb-2\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n          {/* Cavo */}\n          <div className=\"space-y-1\">\n            <Label htmlFor=\"id_cavo\" className=\"text-sm\">Cavo *</Label>\n            <Select\n              value={formData.id_cavo}\n              onValueChange={(value) => onInputChange('id_cavo', value)}\n              disabled={isCavoLocked}\n            >\n              <SelectTrigger>\n                <SelectValue placeholder=\"Seleziona cavo...\" />\n              </SelectTrigger>\n              <SelectContent>\n                {cavi.map((cavo) => (\n                  <SelectItem key={cavo.id_cavo} value={cavo.id_cavo}>\n                    {cavo.id_cavo} - {cavo.descrizione}\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n            {validationErrors.id_cavo && (\n              <p className=\"text-sm text-red-600\">{validationErrors.id_cavo}</p>\n            )}\n          </div>\n\n          {/* Operatore */}\n          <div className=\"space-y-1\">\n            <Label htmlFor=\"id_operatore\" className=\"text-sm\">Operatore</Label>\n            <Select\n              value={formData.id_operatore?.toString() || ''}\n              onValueChange={(value) => onInputChange('id_operatore', parseInt(value))}\n            >\n              <SelectTrigger>\n                <SelectValue placeholder=\"Seleziona operatore...\" />\n              </SelectTrigger>\n              <SelectContent>\n                {responsabili.map((resp) => (\n                  <SelectItem key={resp.id_responsabile} value={resp.id_responsabile.toString()}>\n                    {resp.nome} {resp.cognome}\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* Strumento */}\n          <div className=\"space-y-1\">\n            <Label htmlFor=\"id_strumento\" className=\"text-sm\">Strumento</Label>\n            <Select\n              value={formData.id_strumento?.toString() || ''}\n              onValueChange={(value) => {\n                const strumento = strumenti.find(s => s.id_strumento === parseInt(value))\n                onInputChange('id_strumento', parseInt(value))\n                if (strumento) {\n                  onInputChange('strumento_utilizzato', `${strumento.marca} ${strumento.modello}`)\n                }\n              }}\n            >\n              <SelectTrigger>\n                <SelectValue placeholder=\"Seleziona strumento...\" />\n              </SelectTrigger>\n              <SelectContent>\n                {strumenti.map((strumento) => (\n                  <SelectItem key={strumento.id_strumento} value={strumento.id_strumento.toString()}>\n                    {strumento.marca} {strumento.modello} - {strumento.numero_serie}\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* Tipo Certificato */}\n          <div className=\"space-y-1\">\n            <Label htmlFor=\"tipo_certificato\" className=\"text-sm\">Tipo Certificato</Label>\n            <Select\n              value={formData.tipo_certificato || 'SINGOLO'}\n              onValueChange={(value) => onInputChange('tipo_certificato', value)}\n            >\n              <SelectTrigger>\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"SINGOLO\">Singolo</SelectItem>\n                <SelectItem value=\"GRUPPO\">Gruppo</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAuBO,SAAS,iBAAiB,EAC/B,QAAQ,EACR,IAAI,EACJ,YAAY,EACZ,SAAS,EACT,gBAAgB,EAChB,YAAY,EACZ,aAAa,EACS;IACtB,qBACE,8OAAC,gIAAA,CAAA,OAAI;;0BACH,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;8BAAwB;;;;;;;;;;;0BAE/C,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;8CAAU;;;;;;8CAC7C,8OAAC,kIAAA,CAAA,SAAM;oCACL,OAAO,SAAS,OAAO;oCACvB,eAAe,CAAC,QAAU,cAAc,WAAW;oCACnD,UAAU;;sDAEV,8OAAC,kIAAA,CAAA,gBAAa;sDACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,8OAAC,kIAAA,CAAA,gBAAa;sDACX,KAAK,GAAG,CAAC,CAAC,qBACT,8OAAC,kIAAA,CAAA,aAAU;oDAAoB,OAAO,KAAK,OAAO;;wDAC/C,KAAK,OAAO;wDAAC;wDAAI,KAAK,WAAW;;mDADnB,KAAK,OAAO;;;;;;;;;;;;;;;;gCAMlC,iBAAiB,OAAO,kBACvB,8OAAC;oCAAE,WAAU;8CAAwB,iBAAiB,OAAO;;;;;;;;;;;;sCAKjE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAe,WAAU;8CAAU;;;;;;8CAClD,8OAAC,kIAAA,CAAA,SAAM;oCACL,OAAO,SAAS,YAAY,EAAE,cAAc;oCAC5C,eAAe,CAAC,QAAU,cAAc,gBAAgB,SAAS;;sDAEjE,8OAAC,kIAAA,CAAA,gBAAa;sDACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,8OAAC,kIAAA,CAAA,gBAAa;sDACX,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,kIAAA,CAAA,aAAU;oDAA4B,OAAO,KAAK,eAAe,CAAC,QAAQ;;wDACxE,KAAK,IAAI;wDAAC;wDAAE,KAAK,OAAO;;mDADV,KAAK,eAAe;;;;;;;;;;;;;;;;;;;;;;sCAS7C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAe,WAAU;8CAAU;;;;;;8CAClD,8OAAC,kIAAA,CAAA,SAAM;oCACL,OAAO,SAAS,YAAY,EAAE,cAAc;oCAC5C,eAAe,CAAC;wCACd,MAAM,YAAY,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK,SAAS;wCAClE,cAAc,gBAAgB,SAAS;wCACvC,IAAI,WAAW;4CACb,cAAc,wBAAwB,GAAG,UAAU,KAAK,CAAC,CAAC,EAAE,UAAU,OAAO,EAAE;wCACjF;oCACF;;sDAEA,8OAAC,kIAAA,CAAA,gBAAa;sDACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,8OAAC,kIAAA,CAAA,gBAAa;sDACX,UAAU,GAAG,CAAC,CAAC,0BACd,8OAAC,kIAAA,CAAA,aAAU;oDAA8B,OAAO,UAAU,YAAY,CAAC,QAAQ;;wDAC5E,UAAU,KAAK;wDAAC;wDAAE,UAAU,OAAO;wDAAC;wDAAI,UAAU,YAAY;;mDADhD,UAAU,YAAY;;;;;;;;;;;;;;;;;;;;;;sCAS/C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAmB,WAAU;8CAAU;;;;;;8CACtD,8OAAC,kIAAA,CAAA,SAAM;oCACL,OAAO,SAAS,gBAAgB,IAAI;oCACpC,eAAe,CAAC,QAAU,cAAc,oBAAoB;;sDAE5D,8OAAC,kIAAA,CAAA,gBAAa;sDACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;sDAEd,8OAAC,kIAAA,CAAA,gBAAa;;8DACZ,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAU;;;;;;8DAC5B,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3C", "debugId": null}}, {"offset": {"line": 323, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/certificazioni/sections/CondizioniAmbientali.tsx"], "sourcesContent": ["'use client'\n\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Label } from '@/components/ui/label'\nimport { Input } from '@/components/ui/input'\nimport { Button } from '@/components/ui/button'\nimport { Cloud, Loader2, Settings, X } from 'lucide-react'\nimport { CertificazioneCavoCreate } from '@/types/certificazioni'\n\ninterface WeatherData {\n  temperature: number\n  humidity: number\n  city?: string\n  isDemo: boolean\n  source: string\n}\n\ninterface CondizioniAmbientaliProps {\n  formData: Partial<CertificazioneCavoCreate>\n  weatherData: WeatherData | null\n  isLoadingWeather: boolean\n  isWeatherOverride: boolean\n  onInputChange: (field: string, value: any) => void\n  onToggleWeatherOverride: () => void\n}\n\nexport function CondizioniAmbientali({\n  formData,\n  weatherData,\n  isLoadingWeather,\n  isWeatherOverride,\n  onInputChange,\n  onToggleWeatherOverride\n}: CondizioniAmbientaliProps) {\n  if (!weatherData) return null\n\n  return (\n    <Card>\n      <CardHeader className=\"pb-1 px-3 pt-2\">\n        <CardTitle className=\"flex items-center gap-2 text-sm font-semibold\">\n          <Cloud className=\"h-3 w-3\" />\n          Condizioni Ambientali\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"pt-0 px-3 pb-2\">\n        <div className={`p-3 rounded-lg border ${weatherData.isDemo ? 'bg-yellow-50 border-yellow-200' : 'bg-green-50 border-green-200'}`}>\n          <div className=\"flex items-center gap-3\">\n            {isLoadingWeather ? (\n              <Loader2 className=\"h-4 w-4 animate-spin\" />\n            ) : (\n              <Cloud className=\"h-4 w-4 text-blue-600\" />\n            )}\n            <div className=\"flex-1\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"text-sm font-medium\">\n                  {weatherData.city && `${weatherData.city} - `}\n                  {weatherData.temperature}°C, {weatherData.humidity}% UR\n                </div>\n                <Button\n                  type=\"button\"\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={onToggleWeatherOverride}\n                  className=\"h-6 px-2 text-xs\"\n                >\n                  {isWeatherOverride ? (\n                    <>\n                      <X className=\"h-3 w-3 mr-1\" />\n                      Auto\n                    </>\n                  ) : (\n                    <>\n                      <Settings className=\"h-3 w-3 mr-1\" />\n                      Modifica\n                    </>\n                  )}\n                </Button>\n              </div>\n              <div className=\"text-xs text-gray-600 mt-1\">\n                {weatherData.source}\n                {weatherData.isDemo && ' (Dati dimostrativi)'}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Override manuale */}\n        {isWeatherOverride && (\n          <div className=\"mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg\">\n            <div className=\"text-sm font-medium text-blue-800 mb-2\">\n              Inserimento Manuale\n            </div>\n            <div className=\"grid grid-cols-2 gap-3\">\n              <div className=\"space-y-1\">\n                <Label htmlFor=\"temperatura_prova\" className=\"text-sm\">Temperatura (°C)</Label>\n                <Input\n                  id=\"temperatura_prova\"\n                  type=\"number\"\n                  value={formData.temperatura_prova || ''}\n                  onChange={(e) => onInputChange('temperatura_prova', parseFloat(e.target.value))}\n                  placeholder=\"20\"\n                  className=\"text-sm\"\n                />\n              </div>\n              <div className=\"space-y-1\">\n                <Label htmlFor=\"umidita_prova\" className=\"text-sm\">Umidità (%)</Label>\n                <Input\n                  id=\"umidita_prova\"\n                  type=\"number\"\n                  value={formData.umidita_prova || ''}\n                  onChange={(e) => onInputChange('umidita_prova', parseFloat(e.target.value))}\n                  placeholder=\"50\"\n                  className=\"text-sm\"\n                />\n              </div>\n            </div>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AANA;;;;;;;AA0BO,SAAS,qBAAqB,EACnC,QAAQ,EACR,WAAW,EACX,gBAAgB,EAChB,iBAAiB,EACjB,aAAa,EACb,uBAAuB,EACG;IAC1B,IAAI,CAAC,aAAa,OAAO;IAEzB,qBACE,8OAAC,gIAAA,CAAA,OAAI;;0BACH,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBAAY;;;;;;;;;;;;0BAIjC,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAI,WAAW,CAAC,sBAAsB,EAAE,YAAY,MAAM,GAAG,mCAAmC,gCAAgC;kCAC/H,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,iCACC,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAEnB,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CAEnB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;wDACZ,YAAY,IAAI,IAAI,GAAG,YAAY,IAAI,CAAC,GAAG,CAAC;wDAC5C,YAAY,WAAW;wDAAC;wDAAK,YAAY,QAAQ;wDAAC;;;;;;;8DAErD,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;8DAET,kCACC;;0EACE,8OAAC,4LAAA,CAAA,IAAC;gEAAC,WAAU;;;;;;4DAAiB;;qFAIhC;;0EACE,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;sDAM7C,8OAAC;4CAAI,WAAU;;gDACZ,YAAY,MAAM;gDAClB,YAAY,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;;;;;oBAO9B,mCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAyC;;;;;;0CAGxD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAoB,WAAU;0DAAU;;;;;;0DACvD,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,iBAAiB,IAAI;gDACrC,UAAU,CAAC,IAAM,cAAc,qBAAqB,WAAW,EAAE,MAAM,CAAC,KAAK;gDAC7E,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAGd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAgB,WAAU;0DAAU;;;;;;0DACnD,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,aAAa,IAAI;gDACjC,UAAU,CAAC,IAAM,cAAc,iBAAiB,WAAW,EAAE,MAAM,CAAC,KAAK;gDACzE,aAAY;gDACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5B", "debugId": null}}, {"offset": {"line": 584, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/certificazioni/sections/MisurazioniTest.tsx"], "sourcesContent": ["'use client'\n\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Label } from '@/components/ui/label'\nimport { Input } from '@/components/ui/input'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Settings } from 'lucide-react'\nimport { CertificazioneCavoCreate } from '@/types/certificazioni'\n\ninterface MisurazioniTestProps {\n  formData: Partial<CertificazioneCavoCreate>\n  validationErrors: Record<string, string>\n  onInputChange: (field: string, value: any) => void\n}\n\nexport function MisurazioniTest({\n  formData,\n  validationErrors,\n  onInputChange\n}: MisurazioniTestProps) {\n  return (\n    <Card>\n      <CardHeader className=\"pb-1 px-3 pt-2\">\n        <CardTitle className=\"flex items-center gap-2 text-sm font-semibold\">\n          <Settings className=\"h-3 w-3\" />\n          Misurazioni e Test\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"space-y-2 pt-0 px-3 pb-2\">\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-3\">\n          {/* Prima riga: Misurazioni principali */}\n          <div className=\"space-y-1\">\n            <Label htmlFor=\"valore_isolamento\" className=\"text-sm\">Isolamento (MΩ) *</Label>\n            <Input\n              id=\"valore_isolamento\"\n              type=\"number\"\n              step=\"0.01\"\n              value={formData.valore_isolamento || ''}\n              onChange={(e) => onInputChange('valore_isolamento', parseFloat(e.target.value))}\n              placeholder=\"1000\"\n              className=\"text-sm\"\n            />\n            {validationErrors.valore_isolamento && (\n              <p className=\"text-xs text-red-600\">{validationErrors.valore_isolamento}</p>\n            )}\n          </div>\n\n          <div className=\"space-y-1\">\n            <Label htmlFor=\"valore_continuita\" className=\"text-sm\">Continuità *</Label>\n            <Select\n              value={formData.valore_continuita}\n              onValueChange={(value) => onInputChange('valore_continuita', value)}\n            >\n              <SelectTrigger className=\"text-sm\">\n                <SelectValue placeholder=\"Seleziona...\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"CONFORME\">✅ CONFORME</SelectItem>\n                <SelectItem value=\"NON_CONFORME\">❌ NON CONFORME</SelectItem>\n              </SelectContent>\n            </Select>\n            {validationErrors.valore_continuita && (\n              <p className=\"text-xs text-red-600\">{validationErrors.valore_continuita}</p>\n            )}\n          </div>\n\n          <div className=\"space-y-1\">\n            <Label htmlFor=\"valore_resistenza\" className=\"text-sm\">Resistenza (Ω) *</Label>\n            <Input\n              id=\"valore_resistenza\"\n              type=\"number\"\n              step=\"0.01\"\n              value={formData.valore_resistenza || ''}\n              onChange={(e) => onInputChange('valore_resistenza', parseFloat(e.target.value))}\n              placeholder=\"0.5\"\n              className=\"text-sm\"\n            />\n            {validationErrors.valore_resistenza && (\n              <p className=\"text-xs text-red-600\">{validationErrors.valore_resistenza}</p>\n            )}\n          </div>\n        </div>\n\n        {/* Seconda riga: Parametri di prova */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n          <div className=\"space-y-1\">\n            <Label htmlFor=\"tensione_prova_isolamento\" className=\"text-sm\">Tensione Prova (V)</Label>\n            <Input\n              id=\"tensione_prova_isolamento\"\n              type=\"number\"\n              value={formData.tensione_prova_isolamento || 500}\n              onChange={(e) => onInputChange('tensione_prova_isolamento', parseInt(e.target.value))}\n              className=\"text-sm\"\n            />\n          </div>\n\n          <div className=\"space-y-1\">\n            <Label htmlFor=\"durata_prova\" className=\"text-sm\">Durata Prova (min)</Label>\n            <Input\n              id=\"durata_prova\"\n              type=\"number\"\n              value={formData.durata_prova || ''}\n              onChange={(e) => onInputChange('durata_prova', parseInt(e.target.value))}\n              placeholder=\"1\"\n              className=\"text-sm\"\n            />\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAOA;AAZA;;;;;;;AAqBO,SAAS,gBAAgB,EAC9B,QAAQ,EACR,gBAAgB,EAChB,aAAa,EACQ;IACrB,qBACE,8OAAC,gIAAA,CAAA,OAAI;;0BACH,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAAY;;;;;;;;;;;;0BAIpC,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAoB,WAAU;kDAAU;;;;;;kDACvD,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,MAAK;wCACL,OAAO,SAAS,iBAAiB,IAAI;wCACrC,UAAU,CAAC,IAAM,cAAc,qBAAqB,WAAW,EAAE,MAAM,CAAC,KAAK;wCAC7E,aAAY;wCACZ,WAAU;;;;;;oCAEX,iBAAiB,iBAAiB,kBACjC,8OAAC;wCAAE,WAAU;kDAAwB,iBAAiB,iBAAiB;;;;;;;;;;;;0CAI3E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAoB,WAAU;kDAAU;;;;;;kDACvD,8OAAC,kIAAA,CAAA,SAAM;wCACL,OAAO,SAAS,iBAAiB;wCACjC,eAAe,CAAC,QAAU,cAAc,qBAAqB;;0DAE7D,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,8OAAC,kIAAA,CAAA,gBAAa;;kEACZ,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAW;;;;;;kEAC7B,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAe;;;;;;;;;;;;;;;;;;oCAGpC,iBAAiB,iBAAiB,kBACjC,8OAAC;wCAAE,WAAU;kDAAwB,iBAAiB,iBAAiB;;;;;;;;;;;;0CAI3E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAoB,WAAU;kDAAU;;;;;;kDACvD,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,MAAK;wCACL,OAAO,SAAS,iBAAiB,IAAI;wCACrC,UAAU,CAAC,IAAM,cAAc,qBAAqB,WAAW,EAAE,MAAM,CAAC,KAAK;wCAC7E,aAAY;wCACZ,WAAU;;;;;;oCAEX,iBAAiB,iBAAiB,kBACjC,8OAAC;wCAAE,WAAU;kDAAwB,iBAAiB,iBAAiB;;;;;;;;;;;;;;;;;;kCAM7E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAA4B,WAAU;kDAAU;;;;;;kDAC/D,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,yBAAyB,IAAI;wCAC7C,UAAU,CAAC,IAAM,cAAc,6BAA6B,SAAS,EAAE,MAAM,CAAC,KAAK;wCACnF,WAAU;;;;;;;;;;;;0CAId,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAe,WAAU;kDAAU;;;;;;kDAClD,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,YAAY,IAAI;wCAChC,UAAU,CAAC,IAAM,cAAc,gBAAgB,SAAS,EAAE,MAAM,CAAC,KAAK;wCACtE,aAAY;wCACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxB", "debugId": null}}, {"offset": {"line": 878, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/certificazioni/sections/NoteEStato.tsx"], "sourcesContent": ["'use client'\n\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { CertificazioneCavoCreate } from '@/types/certificazioni'\n\ninterface NoteEStatoProps {\n  formData: Partial<CertificazioneCavoCreate>\n  onInputChange: (field: string, value: any) => void\n}\n\nexport function NoteEStato({\n  formData,\n  onInputChange\n}: NoteEStatoProps) {\n  return (\n    <Card>\n      <CardHeader className=\"pb-1 px-3 pt-2\">\n        <CardTitle className=\"text-sm font-semibold\">Note e Stato</CardTitle>\n      </CardHeader>\n      <CardContent className=\"space-y-2 px-3 pb-2\">\n        <div className=\"space-y-1\">\n          <Label htmlFor=\"note\" className=\"text-sm\">Note</Label>\n          <Textarea\n            id=\"note\"\n            value={formData.note}\n            onChange={(e) => onInputChange('note', e.target.value)}\n            placeholder=\"Note aggiuntive...\"\n            rows={2}\n            className=\"text-sm\"\n          />\n        </div>\n\n        <div className=\"space-y-1\">\n          <Label htmlFor=\"stato_certificato\" className=\"text-sm\">Stato Certificato</Label>\n          <Select\n            value={formData.stato_certificato || 'BOZZA'}\n            onValueChange={(value) => onInputChange('stato_certificato', value)}\n          >\n            <SelectTrigger className=\"text-sm\">\n              <SelectValue />\n            </SelectTrigger>\n            <SelectContent>\n              <SelectItem value=\"BOZZA\">Bozza</SelectItem>\n              <SelectItem value=\"CONFORME\">Conforme</SelectItem>\n              <SelectItem value=\"NON_CONFORME\">Non Conforme</SelectItem>\n              <SelectItem value=\"CONFORME_CON_RISERVA\">Conforme con Riserva</SelectItem>\n            </SelectContent>\n          </Select>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAmBO,SAAS,WAAW,EACzB,QAAQ,EACR,aAAa,EACG;IAChB,qBACE,8OAAC,gIAAA,CAAA,OAAI;;0BACH,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;8BAAwB;;;;;;;;;;;0BAE/C,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAO,WAAU;0CAAU;;;;;;0CAC1C,8OAAC,oIAAA,CAAA,WAAQ;gCACP,IAAG;gCACH,OAAO,SAAS,IAAI;gCACpB,UAAU,CAAC,IAAM,cAAc,QAAQ,EAAE,MAAM,CAAC,KAAK;gCACrD,aAAY;gCACZ,MAAM;gCACN,WAAU;;;;;;;;;;;;kCAId,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAoB,WAAU;0CAAU;;;;;;0CACvD,8OAAC,kIAAA,CAAA,SAAM;gCACL,OAAO,SAAS,iBAAiB,IAAI;gCACrC,eAAe,CAAC,QAAU,cAAc,qBAAqB;;kDAE7D,8OAAC,kIAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kDAEd,8OAAC,kIAAA,CAAA,gBAAa;;0DACZ,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAQ;;;;;;0DAC1B,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAW;;;;;;0DAC7B,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAe;;;;;;0DACjC,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvD", "debugId": null}}, {"offset": {"line": 1042, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/certificazioni/CertificazioneForm.tsx"], "sourcesContent": ["'use client'\n\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { FileText, Save, Loader2, AlertCircle } from 'lucide-react'\nimport { CertificazioneCavo, StrumentoCertificato } from '@/types/certificazioni'\nimport { useCertificazioneForm } from '@/hooks/useCertificazioneForm'\n\n// Componenti sezioni\nimport { InformazioniBase } from './sections/InformazioniBase'\nimport { CondizioniAmbientali } from './sections/CondizioniAmbientali'\nimport { MisurazioniTest } from './sections/MisurazioniTest'\nimport { NoteEStato } from './sections/NoteEStato'\n\ninterface CertificazioneFormProps {\n  cantiereId: number\n  certificazione?: CertificazioneCavo | null\n  strumenti: StrumentoCertificato[]\n  preselectedCavoId?: string\n  onSuccess: (certificazione: CertificazioneCavo) => void\n  onCancel: () => void\n}\n\n/**\n * Componente principale per la gestione delle certificazioni CEI 64-8\n * Utilizza il pattern di separazione delle responsabilità:\n * - useCertificazioneForm: gestisce tutta la logica di business\n * - Sotto-componenti: gestiscono la visualizzazione delle singole sezioni\n */\nexport default function CertificazioneForm({\n  cantiereId,\n  certificazione,\n  strumenti,\n  preselectedCavoId,\n  onSuccess,\n  onCancel\n}: CertificazioneFormProps) {\n  // Utilizzo del custom hook per la gestione della logica\n  const {\n    // Dati\n    formData,\n    cavi,\n    responsabili,\n    weatherData,\n\n    // Stati\n    isLoading,\n    isSaving,\n    isLoadingWeather,\n    error,\n    validationErrors,\n    isWeatherOverride,\n    isEdit,\n    isCavoLocked,\n\n    // Funzioni\n    handleInputChange,\n    handleSubmit,\n    setIsWeatherOverride,\n    onCancel: handleCancel\n  } = useCertificazioneForm({\n    cantiereId,\n    certificazione,\n    strumenti,\n    preselectedCavoId,\n    onSuccess,\n    onCancel\n  })\n  // Loading state\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <Loader2 className=\"h-8 w-8 animate-spin\" />\n        <span className=\"ml-2\">Caricamento dati...</span>\n      </div>\n    )\n  }\n\n  // Render del componente principale\n  return (\n    <div className=\"h-full w-full\">\n      {/* Header Compatto */}\n      <div className=\"flex items-center justify-between p-3 border-b bg-white\">\n        <div className=\"flex items-center gap-2\">\n          <FileText className=\"h-4 w-4 text-blue-600\" />\n          <h1 className=\"text-base font-semibold text-slate-900\">\n            {isEdit ? 'Modifica Certificazione' : 'Nuova Certificazione'}\n          </h1>\n        </div>\n\n        <Button onClick={handleSubmit} disabled={isSaving} size=\"sm\">\n          {isSaving ? <Loader2 className=\"h-4 w-4 animate-spin mr-2\" /> : <Save className=\"h-4 w-4 mr-2\" />}\n          {isEdit ? 'Aggiorna' : 'Salva'}\n        </Button>\n      </div>\n\n      {/* Contenuto in Grid Layout */}\n      <div className=\"p-3 h-[calc(100%-60px)] overflow-y-auto\">\n        {error && (\n          <Alert variant=\"destructive\" className=\"mb-2\">\n            <AlertCircle className=\"h-4 w-4\" />\n            <AlertDescription>{error}</AlertDescription>\n          </Alert>\n        )}\n\n        <form onSubmit={(e) => { e.preventDefault(); handleSubmit(); }}>\n          {/* Layout a 2 colonne per ottimizzare spazio */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-3 h-full\">\n\n            {/* COLONNA SINISTRA */}\n            <div className=\"space-y-2\">\n              <InformazioniBase\n                formData={formData}\n                cavi={cavi}\n                responsabili={responsabili}\n                strumenti={strumenti}\n                validationErrors={validationErrors}\n                isCavoLocked={isCavoLocked}\n                onInputChange={handleInputChange}\n              />\n\n              <CondizioniAmbientali\n                formData={formData}\n                weatherData={weatherData}\n                isLoadingWeather={isLoadingWeather}\n                isWeatherOverride={isWeatherOverride}\n                onInputChange={handleInputChange}\n                onToggleWeatherOverride={() => setIsWeatherOverride(!isWeatherOverride)}\n              />\n            </div>\n\n            {/* COLONNA DESTRA */}\n            <div className=\"space-y-2\">\n              <MisurazioniTest\n                formData={formData}\n                validationErrors={validationErrors}\n                onInputChange={handleInputChange}\n              />\n\n              <NoteEStato\n                formData={formData}\n                onInputChange={handleInputChange}\n              />\n            </div>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AAEA,qBAAqB;AACrB;AACA;AACA;AACA;AAZA;;;;;;;;;;AA6Be,SAAS,mBAAmB,EACzC,UAAU,EACV,cAAc,EACd,SAAS,EACT,iBAAiB,EACjB,SAAS,EACT,QAAQ,EACgB;IACxB,wDAAwD;IACxD,MAAM,EACJ,OAAO;IACP,QAAQ,EACR,IAAI,EACJ,YAAY,EACZ,WAAW,EAEX,QAAQ;IACR,SAAS,EACT,QAAQ,EACR,gBAAgB,EAChB,KAAK,EACL,gBAAgB,EAChB,iBAAiB,EACjB,MAAM,EACN,YAAY,EAEZ,WAAW;IACX,iBAAiB,EACjB,YAAY,EACZ,oBAAoB,EACpB,UAAU,YAAY,EACvB,GAAG,CAAA,GAAA,qIAAA,CAAA,wBAAqB,AAAD,EAAE;QACxB;QACA;QACA;QACA;QACA;QACA;IACF;IACA,gBAAgB;IAChB,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,iNAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;8BACnB,8OAAC;oBAAK,WAAU;8BAAO;;;;;;;;;;;;IAG7B;IAEA,mCAAmC;IACnC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;gCAAG,WAAU;0CACX,SAAS,4BAA4B;;;;;;;;;;;;kCAI1C,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAc,UAAU;wBAAU,MAAK;;4BACrD,yBAAW,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;qDAAiC,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAC/E,SAAS,aAAa;;;;;;;;;;;;;0BAK3B,8OAAC;gBAAI,WAAU;;oBACZ,uBACC,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAc,WAAU;;0CACrC,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC,iIAAA,CAAA,mBAAgB;0CAAE;;;;;;;;;;;;kCAIvB,8OAAC;wBAAK,UAAU,CAAC;4BAAQ,EAAE,cAAc;4BAAI;wBAAgB;kCAE3D,cAAA,8OAAC;4BAAI,WAAU;;8CAGb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oKAAA,CAAA,mBAAgB;4CACf,UAAU;4CACV,MAAM;4CACN,cAAc;4CACd,WAAW;4CACX,kBAAkB;4CAClB,cAAc;4CACd,eAAe;;;;;;sDAGjB,8OAAC,wKAAA,CAAA,uBAAoB;4CACnB,UAAU;4CACV,aAAa;4CACb,kBAAkB;4CAClB,mBAAmB;4CACnB,eAAe;4CACf,yBAAyB,IAAM,qBAAqB,CAAC;;;;;;;;;;;;8CAKzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,mKAAA,CAAA,kBAAe;4CACd,UAAU;4CACV,kBAAkB;4CAClB,eAAe;;;;;;sDAGjB,8OAAC,8JAAA,CAAA,aAAU;4CACT,UAAU;4CACV,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/B", "debugId": null}}]}