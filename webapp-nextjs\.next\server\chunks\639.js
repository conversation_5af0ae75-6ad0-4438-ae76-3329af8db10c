exports.id=639,exports.ids=[639],exports.modules={4780:(e,t,a)=>{"use strict";a.d(t,{cn:()=>s});var i=a(49384),r=a(82348);function s(...e){return(0,r.QP)((0,i.$)(e))}},10501:(e,t,a)=>{"use strict";a.d(t,{Navbar:()=>N});var i=a(60687),r=a(43210),s=a(85814),n=a.n(s),o=a(16189),c=a(29523),l=a(63213),d=a(32192),p=a(17313),u=a(23361),m=a(19080),h=a(10022),v=a(53411),x=a(6727),f=a(58559),g=a(58869),b=a(40083),$=a(11860),C=a(12941),j=a(41312);let w=(e,t,a,i)=>{let r={name:"owner"===e?"Menu Admin":"user"===e?"Lista Cantieri":"cantieri_user"===e?"Gestione Cavi":"Home",href:"owner"===e?"/admin":"user"===e?"/cantieri":"cantieri_user"===e?"/cavi":"/",icon:d.A};if("owner"===e&&!t)return[r];if("user"===e||t&&a?.role==="user"){let e=[r];return t&&e.push({name:"Cantieri",href:"/cantieri",icon:p.A}),i&&e.push({name:"Visualizza Cavi",href:"/cavi",icon:u.A},{name:"Parco Cavi",href:"/parco-cavi",icon:m.A},{name:"Gestione Excel",href:"/excel",icon:h.A},{name:"Report",href:"/reports",icon:v.A},{name:"Gestione Comande",href:"/comande",icon:x.A},{name:"Produttivit\xe0",href:"/productivity",icon:f.A}),e}if("cantieri_user"===e||t&&a?.role==="cantieri_user"){let t=[r];return i&&("cantieri_user"!==e&&t.push({name:"Visualizza Cavi",href:"/cavi",icon:u.A}),t.push({name:"Parco Cavi",href:"/parco-cavi",icon:m.A},{name:"Gestione Excel",href:"/excel",icon:h.A},{name:"Report",href:"/reports",icon:v.A},{name:"Gestione Comande",href:"/comande",icon:x.A},{name:"Produttivit\xe0",href:"/productivity",icon:f.A})),t}return[r]};function N(){let[e,t]=(0,r.useState)(!1),a=(0,o.usePathname)(),{user:s,cantiere:d,isAuthenticated:m,isImpersonating:h,impersonatedUser:v,logout:x}=(0,l.A)(),f=d?.id_cantiere||0,N=d?.commessa||`Cantiere ${f}`,A=w(s?.ruolo,h,v,f);return"/login"!==a&&m?(0,i.jsxs)("nav",{className:"fixed top-0 left-0 right-0 z-50 bg-white border-b border-slate-200 shadow-sm",children:[(0,i.jsx)("div",{className:"max-w-[90%] mx-auto px-4 sm:px-6 lg:px-8",children:(0,i.jsxs)("div",{className:"flex justify-between h-16",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3 cursor-default",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center",children:(0,i.jsx)(u.A,{className:"w-5 h-5 text-white"})}),(0,i.jsxs)("div",{className:"hidden sm:block",children:[(0,i.jsx)("h1",{className:"text-xl font-bold text-slate-900",children:"CABLYS"}),(0,i.jsx)("p",{className:"text-xs text-slate-500 -mt-1",children:"Cable Installation System"})]})]}),(0,i.jsx)("div",{className:"hidden md:flex items-center space-x-1",children:A.map(e=>{let t=a===e.href||"/"!==e.href&&a.startsWith(e.href),r=e.icon;return(0,i.jsx)(n(),{href:e.href,children:(0,i.jsxs)("div",{className:`flex items-center space-x-2 px-3 py-2 transition-all duration-200 ease-in-out rounded-md ${t?"text-blue-700 bg-blue-50 border border-blue-200 font-medium":"text-slate-600 hover:text-slate-900 hover:bg-blue-50 hover:border-blue-200 border border-transparent"}`,children:[(0,i.jsx)(r,{className:"w-4 h-4"}),(0,i.jsx)("span",{className:"hidden lg:inline",children:e.name})]})},e.name)})})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-4 ml-8",children:[f&&f>0&&(0,i.jsxs)("div",{className:"hidden sm:flex items-center space-x-2 px-2 py-1 bg-blue-50 border border-blue-200 rounded-md",children:[(0,i.jsx)(p.A,{className:"w-3 h-3 text-blue-600"}),(0,i.jsx)("div",{className:"text-xs",children:(0,i.jsx)("span",{className:"text-blue-900 font-medium",children:N})})]}),(0,i.jsxs)("div",{className:"hidden sm:flex items-center space-x-3",children:[(0,i.jsx)("div",{className:"text-right",children:(0,i.jsx)("p",{className:"text-sm font-medium text-slate-900",children:h&&v?v.username:s?.username})}),(0,i.jsx)("div",{className:"w-6 h-6 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center",children:(0,i.jsx)(g.A,{className:"w-3 h-3 text-white"})}),(0,i.jsxs)(c.$,{variant:"ghost",size:"sm",className:"flex items-center space-x-2 px-3 py-2 transition-all duration-200 ease-in-out rounded-md border border-transparent text-red-600 hover:text-red-700 hover:bg-red-50 hover:border-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2",onClick:x,title:"Logout",children:[(0,i.jsx)(b.A,{className:"w-4 h-4"}),(0,i.jsx)("span",{className:"hidden lg:inline",children:"Logout"})]})]}),(0,i.jsx)("div",{className:"md:hidden",children:(0,i.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>t(!e),className:"text-slate-600 hover:bg-blue-50 hover:text-blue-600 transition-all duration-200 ease-in-out rounded-md",children:e?(0,i.jsx)($.A,{className:"w-5 h-5"}):(0,i.jsx)(C.A,{className:"w-5 h-5"})})})]})]})}),e&&(0,i.jsxs)("div",{className:"md:hidden border-t border-slate-200 bg-white",children:[(0,i.jsx)("div",{className:"px-2 pt-2 pb-3 space-y-1",children:A.map(e=>{let r=a===e.href||"/"!==e.href&&a.startsWith(e.href),s=e.icon;return(0,i.jsx)(n(),{href:e.href,children:(0,i.jsxs)("div",{className:`w-full flex items-center justify-start space-x-3 px-3 py-2 transition-all duration-200 ease-in-out rounded-md ${r?"text-blue-700 bg-blue-50 border border-blue-200 font-medium":"text-slate-600 hover:text-slate-900 hover:bg-blue-50 border border-transparent"}`,onClick:()=>t(!1),children:[(0,i.jsx)(s,{className:"w-4 h-4"}),(0,i.jsx)("span",{children:e.name})]})},e.name)})}),(0,i.jsxs)("div",{className:"border-t border-slate-200 px-4 py-3",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)("div",{className:"w-6 h-6 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center",children:s?(0,i.jsx)(g.A,{className:"w-3 h-3 text-white"}):(0,i.jsx)(p.A,{className:"w-3 h-3 text-white"})}),(0,i.jsx)("div",{children:(0,i.jsx)("p",{className:"text-sm font-medium text-slate-900",children:h&&v?v.username:s?s.username:d?.commessa})})]}),(0,i.jsx)(c.$,{variant:"ghost",size:"sm",onClick:x,title:"Logout",className:"hover:bg-red-50 hover:text-red-600 transition-all duration-200 ease-in-out rounded-md",children:(0,i.jsx)(b.A,{className:"w-4 h-4"})})]}),s?.ruolo==="owner"&&!h&&(0,i.jsxs)("div",{className:"mt-3 pt-3 border-t border-slate-200",children:[(0,i.jsx)("p",{className:"text-xs font-medium text-slate-500 mb-2",children:"AMMINISTRAZIONE"}),(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsx)(n(),{href:"/admin",className:"block px-3 py-2 text-sm text-slate-700 hover:bg-slate-100 rounded-md transition-colors duration-150",onClick:()=>t(!1),children:(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)(j.A,{className:"w-4 h-4 text-slate-500"}),(0,i.jsx)("span",{children:"Pannello Admin"})]})}),(0,i.jsx)(n(),{href:"/admin?tab=users",className:"block px-3 py-2 text-sm text-slate-700 hover:bg-slate-100 rounded-md transition-colors duration-150",onClick:()=>t(!1),children:(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)(j.A,{className:"w-4 h-4 text-slate-500"}),(0,i.jsx)("span",{children:"Gestione Utenti"})]})})]})]})]})]})]}):null}},29131:(e,t,a)=>{"use strict";a.d(t,{AuthProvider:()=>r});var i=a(12907);(0,i.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\contexts\\AuthContext.tsx","useAuth");let r=(0,i.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\contexts\\AuthContext.tsx","AuthProvider")},29523:(e,t,a)=>{"use strict";a.d(t,{$:()=>c});var i=a(60687);a(43210);var r=a(8730),s=a(24224),n=a(4780);let o=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c({className:e,variant:t,size:a,asChild:s=!1,...c}){let l=s?r.DX:"button";return(0,i.jsx)(l,{"data-slot":"button",className:(0,n.cn)(o({variant:t,size:a,className:e})),...c})}},29867:(e,t,a)=>{"use strict";a.d(t,{dj:()=>u});var i=a(43210);let r=0,s=new Map,n=e=>{if(s.has(e))return;let t=setTimeout(()=>{s.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);s.set(e,t)},o=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:a}=t;return a?n(a):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},c=[],l={toasts:[]};function d(e){l=o(l,e),c.forEach(e=>{e(l)})}function p({...e}){let t=(r=(r+1)%Number.MAX_VALUE).toString(),a=()=>d({type:"DISMISS_TOAST",toastId:t});return d({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||a()}}}),{id:t,dismiss:a,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function u(){let[e,t]=(0,i.useState)(l);return{...e,toast:p,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},50346:()=>{},51021:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,86346,23)),Promise.resolve().then(a.t.bind(a,27924,23)),Promise.resolve().then(a.t.bind(a,35656,23)),Promise.resolve().then(a.t.bind(a,40099,23)),Promise.resolve().then(a.t.bind(a,38243,23)),Promise.resolve().then(a.t.bind(a,28827,23)),Promise.resolve().then(a.t.bind(a,62763,23)),Promise.resolve().then(a.t.bind(a,97173,23))},54679:(e,t,a)=>{"use strict";a.d(t,{default:()=>i});let i=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\components\\\\layout\\\\MainContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\components\\layout\\MainContent.tsx","default")},55830:(e,t,a)=>{Promise.resolve().then(a.bind(a,54679)),Promise.resolve().then(a.bind(a,93319)),Promise.resolve().then(a.bind(a,79737)),Promise.resolve().then(a.bind(a,29131))},61135:()=>{},62185:(e,t,a)=>{"use strict";a.d(t,{AR:()=>l,At:()=>n,CV:()=>c,Fw:()=>o,ZQ:()=>s,_I:()=>x,dG:()=>f,km:()=>d,kw:()=>p,l9:()=>u,mg:()=>h,om:()=>m,ug:()=>v});let i=a(51060).A.create({baseURL:"http://localhost:8001",timeout:3e4,headers:{"Content-Type":"application/json"}});i.interceptors.request.use(e=>e,e=>Promise.reject(e)),i.interceptors.response.use(e=>e,e=>(e.response?.status,Promise.reject(e)));let r={get:async(e,t)=>(await i.get(e,t)).data,post:async(e,t,a)=>(await i.post(e,t,a)).data,put:async(e,t,a)=>(await i.put(e,t,a)).data,delete:async(e,t)=>(await i.delete(e,t)).data},s={login:async e=>{let t=new FormData;return t.append("username",e.username),t.append("password",e.password),(await i.post("/api/auth/login",t,{headers:{"Content-Type":"application/x-www-form-urlencoded"}})).data},loginCantiere:e=>r.post("/api/auth/login/cantiere",{codice_univoco:e.codice_cantiere,password:e.password_cantiere}),verifyToken:()=>r.post("/api/auth/test-token"),logout:()=>{localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),window.location.href="/login"}},n={getCavi:(e,t)=>r.get(`/api/cavi/${e}`,{params:t}),getCavo:(e,t)=>r.get(`/api/cavi/${e}/${t}`),checkCavo:(e,t)=>r.get(`/api/cavi/${e}/check/${t}`),createCavo:(e,t)=>r.post(`/api/cavi/${e}`,t),updateCavo:(e,t,a)=>r.put(`/api/cavi/${e}/${t}`,a),deleteCavo:(e,t,a)=>r.delete(`/api/cavi/${e}/${t}`,{data:a}),updateMetriPosati:(e,t,a,i,s)=>r.post(`/api/cavi/${e}/${t}/metri-posati`,{metri_posati:a,id_bobina:i,force_over:s||!1}),updateBobina:(e,t,a,i)=>r.post(`/api/cavi/${e}/${t}/bobina`,{id_bobina:a,force_over:i||!1}),cancelInstallation:(e,t)=>r.post(`/api/cavi/${e}/${t}/cancel-installation`),collegaCavo:(e,t,a,i)=>r.post(`/api/cavi/${e}/${t}/collegamento`,{lato:a,responsabile:i}),scollegaCavo:(e,t,a)=>{let i={};return a&&(i.data={lato:a}),r.delete(`/api/cavi/${e}/${t}/collegamento`,i)},markAsSpare:(e,t,a,i=!0)=>a?r.post(`/api/cavi/${e}/${t}/mark-as-spare`,{force:i}):r.post(`/api/cavi/${e}/${t}/reactivate-spare`,{}),debugCavi:e=>r.get(`/api/cavi/debug/${e}`),debugCaviRaw:e=>r.get(`/api/cavi/debug/raw/${e}`)},o={getBobine:(e,t)=>r.get(`/api/parco-cavi/${e}`,{params:t}),getBobina:(e,t)=>r.get(`/api/parco-cavi/${e}/${t}`),getBobineCompatibili:(e,t)=>r.get(`/api/parco-cavi/${e}/compatibili`,{params:t}),createBobina:(e,t)=>r.post(`/api/parco-cavi/${e}`,t),updateBobina:(e,t,a)=>r.put(`/api/parco-cavi/${e}/${t}`,a),deleteBobina:(e,t)=>r.delete(`/api/parco-cavi/${e}/${t}`),isFirstBobinaInsertion:e=>r.get(`/api/parco-cavi/${e}/is-first-insertion`),updateBobina:(e,t,a)=>r.put(`/api/parco-cavi/${e}/${t}`,a),deleteBobina:(e,t)=>r.delete(`/api/parco-cavi/${e}/${t}`),checkDisponibilita:(e,t,a)=>r.get(`/api/parco-cavi/${e}/${t}/disponibilita`,{params:{metri_richiesti:a}})},c={getComande:e=>r.get(`/api/comande/cantiere/${e}`),getComanda:(e,t)=>r.get(`/api/comande/${t}`),getCaviComanda:e=>r.get(`/api/comande/${e}/cavi`),createComanda:(e,t)=>r.post(`/api/comande/cantiere/${e}`,t),createComandaWithCavi:(e,t,a)=>r.post(`/api/comande/cantiere/${e}/crea-con-cavi`,t,{params:{lista_id_cavi:a}}),updateDatiComanda:(e,t,a)=>r.put(`/api/comande/${e}/${t}`,a),updateComanda:(e,t,a)=>r.put(`/api/comande/cantiere/${e}/${t}`,a),deleteComanda:(e,t)=>r.delete(`/api/comande/cantiere/${e}/${t}`),assegnaCavi:(e,t,a)=>r.post(`/api/comande/cantiere/${e}/${t}/assegna-cavi`,{cavi_ids:a}),rimuoviCavi:(e,t,a)=>r.delete(`/api/comande/cantiere/${e}/${t}/rimuovi-cavi`,{data:{cavi_ids:a}}),getStatistiche:e=>r.get(`/api/comande/cantiere/${e}/statistiche`),cambiaStato:(e,t,a)=>r.put(`/api/comande/cantiere/${e}/${t}/stato`,{nuovo_stato:a})},l={getResponsabili:e=>r.get(`/api/responsabili/cantiere/${e}`),createResponsabile:(e,t)=>r.post(`/api/responsabili/${e}`,t),updateResponsabile:(e,t,a)=>r.put(`/api/responsabili/${e}/${t}`,a),deleteResponsabile:(e,t)=>r.delete(`/api/responsabili/${e}/${t}`)},d={getCertificazioni:(e,t)=>r.get(`/api/cantieri/${e}/certificazioni`,{params:t?{filtro_cavo:t}:{}}),createCertificazione:(e,t)=>r.post(`/api/cantieri/${e}/certificazioni`,t),getCertificazione:(e,t)=>r.get(`/api/cantieri/${e}/certificazioni/${t}`),updateCertificazione:(e,t,a)=>r.put(`/api/cantieri/${e}/certificazioni/${t}`,a),deleteCertificazione:(e,t)=>r.delete(`/api/cantieri/${e}/certificazioni/${t}`),generatePDF:(e,t)=>r.get(`/api/cantieri/${e}/certificazioni/${t}/pdf`,{responseType:"blob"}),getStatistiche:e=>r.get(`/api/cantieri/${e}/certificazioni/statistiche`),exportCertificazioni:(e,t)=>r.get(`/api/cantieri/${e}/certificazioni/export`,{params:t,responseType:"blob"}),generateReport:(e,t="completo")=>r.get(`/api/cantieri/${e}/certificazioni/report/${t}`),bulkDelete:(e,t)=>r.post(`/api/cantieri/${e}/certificazioni/bulk-delete`,{ids:t}),generateBulkPdf:(e,t)=>r.post(`/api/cantieri/${e}/certificazioni/bulk-pdf`,{ids:t},{responseType:"blob"}),validateCertificazione:(e,t)=>r.post(`/api/cantieri/${e}/certificazioni/validate`,t)},p={getStrumenti:e=>r.get(`/api/cantieri/${e}/strumenti`),createStrumento:(e,t)=>r.post(`/api/cantieri/${e}/strumenti`,t),updateStrumento:(e,t,a)=>r.put(`/api/cantieri/${e}/strumenti/${t}`,a),deleteStrumento:(e,t)=>r.delete(`/api/cantieri/${e}/strumenti/${t}`)},u={getRapporti:(e,t=0,a=100)=>r.get(`/api/cantieri/${e}/rapporti`,{params:{skip:t,limit:a}}),createRapporto:(e,t)=>r.post(`/api/cantieri/${e}/rapporti`,t),getRapporto:(e,t)=>r.get(`/api/cantieri/${e}/rapporti/${t}`),updateRapporto:(e,t,a)=>r.put(`/api/cantieri/${e}/rapporti/${t}`,a),deleteRapporto:(e,t)=>r.delete(`/api/cantieri/${e}/rapporti/${t}`),aggiornaStatistiche:(e,t)=>r.post(`/api/cantieri/${e}/rapporti/${t}/aggiorna-statistiche`)},m={getNonConformita:e=>r.get(`/api/cantieri/${e}/non-conformita`),createNonConformita:(e,t)=>r.post(`/api/cantieri/${e}/non-conformita`,t),updateNonConformita:(e,t,a)=>r.put(`/api/cantieri/${e}/non-conformita/${t}`,a),deleteNonConformita:(e,t)=>r.delete(`/api/cantieri/${e}/non-conformita/${t}`)},h={importCavi:(e,t,a)=>{let i=new FormData;return i.append("file",t),i.append("revisione",a),r.post(`/api/excel/${e}/import-cavi`,i,{headers:{"Content-Type":"multipart/form-data"}})},importBobine:(e,t)=>{let a=new FormData;return a.append("file",t),r.post(`/api/excel/${e}/import-parco-bobine`,a,{headers:{"Content-Type":"multipart/form-data"}})},exportCavi:e=>r.get(`/api/excel/${e}/export-cavi`,{responseType:"blob"}),exportBobine:e=>r.get(`/api/excel/${e}/export-parco-bobine`,{responseType:"blob"})},v={getReportAvanzamento:e=>r.get(`/api/reports/${e}/avanzamento`),getReportBOQ:e=>r.get(`/api/reports/${e}/boq`),getReportUtilizzoBobine:e=>r.get(`/api/reports/${e}/storico-bobine`),getReportProgress:e=>r.get(`/api/reports/${e}/progress`),getReportPosaPeriodo:(e,t,a)=>{let i=new URLSearchParams;t&&i.append("data_inizio",t),a&&i.append("data_fine",a);let s=i.toString();return r.get(`/api/reports/${e}/posa-periodo${s?`?${s}`:""}`)}},x={getCantieri:()=>r.get("/api/cantieri"),getCantiere:e=>r.get(`/api/cantieri/${e}`),createCantiere:e=>r.post("/api/cantieri",e),updateCantiere:(e,t)=>r.put(`/api/cantieri/${e}`,t),getCantiereStatistics:e=>r.get(`/api/cantieri/${e}/statistics`),getWeatherData:e=>r.get(`/api/cantieri/${e}/weather`)},f={getUsers:()=>r.get("/api/users"),getUser:e=>r.get(`/api/users/${e}`),createUser:e=>r.post("/api/users",e),updateUser:(e,t)=>r.put(`/api/users/${e}`,t),deleteUser:e=>r.delete(`/api/users/${e}`),toggleUserStatus:e=>r.get(`/api/users/toggle/${e}`),checkExpiredUsers:()=>r.get("/api/users/check-expired"),impersonateUser:e=>r.post("/api/auth/impersonate",{user_id:e}),getDatabaseData:()=>r.get("/api/users/db-raw"),resetDatabase:()=>r.post("/api/admin/reset-database")}},63213:(e,t,a)=>{"use strict";a.d(t,{A:()=>o,AuthProvider:()=>c});var i=a(60687),r=a(43210),s=a(62185);let n=(0,r.createContext)(void 0);function o(){let e=(0,r.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function c({children:e}){let[t,a]=(0,r.useState)(null),[o,c]=(0,r.useState)(null),[l,d]=(0,r.useState)(!0),[p,u]=(0,r.useState)(()=>!1),[m,h]=(0,r.useState)(()=>null),[v,x]=(0,r.useState)(null),[f,g]=(0,r.useState)(null),[b,$]=(0,r.useState)(null),C=!!t&&t.id_utente||!!o&&o.id_cantiere,j=async()=>{try{return void d(!1)}catch(e){console.error("\uD83D\uDD10 AuthContext: Errore generale durante checkAuth:",e),a(null),c(null),u(!1),h(null)}finally{console.log("\uD83D\uDD10 AuthContext: checkAuth completato, impostazione loading = false"),d(!1)}},w=async(e,t)=>{try{console.log("\uD83D\uDD10 AuthContext: Inizio login per:",e),d(!0);let a=await s.ZQ.login({username:e,password:t});return console.log("\uD83D\uDCE1 AuthContext: Risposta backend ricevuta:",a),console.log("\uD83D\uDD04 AuthContext: Fallback SSR"),{id_utente:a.user_id,username:a.username,ruolo:a.role}}catch(e){throw console.error("❌ AuthContext: Errore durante login:",e),e}finally{d(!1)}},N=async(e,t)=>{try{console.log("\uD83D\uDD10 AuthContext: Inizio login cantiere:",e),d(!0);let a=await s.ZQ.loginCantiere({codice_cantiere:e,password_cantiere:t});return console.log("\uD83D\uDD10 AuthContext: Risposta login cantiere:",a),{id_cantiere:a.cantiere_id,commessa:a.cantiere_name,codice_univoco:e,id_utente:a.user_id}}catch(e){throw e}finally{d(!1)}},A=async e=>{try{await s.dG.impersonateUser(e)}catch(e){throw e}};return(0,i.jsx)(n.Provider,{value:{user:t,cantiere:o,isAuthenticated:C,isLoading:l,isImpersonating:p,impersonatedUser:m,expirationWarning:v,daysUntilExpiration:f,expirationDate:b,login:w,loginCantiere:N,logout:()=>{},checkAuth:j,impersonateUser:A,selectCantiere:e=>{if(!e||!e.id_cantiere||e.id_cantiere<=0)return void console.error("\uD83C\uDFD7️ AuthContext: Tentativo di selezione cantiere non valido:",e);try{let t=e.commessa||`Cantiere ${e.id_cantiere}`;localStorage.setItem("selectedCantiereId",e.id_cantiere.toString()),localStorage.setItem("selectedCantiereName",t);let a={...e,commessa:t};console.log("\uD83C\uDFD7️ AuthContext: Cantiere selezionato:",a),c(a),localStorage.removeItem("cantiere_data")}catch(e){console.error("\uD83C\uDFD7️ AuthContext: Errore nella selezione cantiere:",e)}},clearCantiere:()=>{console.log("\uD83C\uDFD7️ AuthContext: Pulizia stato cantiere"),c(null),localStorage.removeItem("selectedCantiereId"),localStorage.removeItem("selectedCantiereName"),localStorage.removeItem("cantiere_data")},dismissExpirationWarning:()=>{x(null),g(null),$(null)}},children:e})}},69336:(e,t,a)=>{"use strict";a.d(t,{default:()=>h});var i=a(60687),r=a(43210),s=a(63213);let n=()=>{let{isAuthenticated:e,user:t,cantiere:a,checkAuth:i,logout:n}=(0,s.A)(),o=(0,r.useRef)(null),c=(0,r.useRef)(Date.now()),l=()=>{o.current&&(clearInterval(o.current),o.current=null)};return(0,r.useEffect)(()=>{},[e,t,a]),(0,r.useEffect)(()=>()=>{l()},[]),{updateActivity:()=>{c.current=Date.now()},checkSessionExpiry:()=>!0}};var o=a(91821),c=a(29523),l=a(43649),d=a(48730),p=a(40228),u=a(11860);function m(){let{expirationWarning:e,daysUntilExpiration:t,expirationDate:a,dismissExpirationWarning:r}=(0,s.A)();return e?(0,i.jsx)(o.Fc,{variant:0===t||1===t?"destructive":"default",className:"mb-4 border-l-4",children:(0,i.jsxs)("div",{className:"flex items-start justify-between",children:[(0,i.jsxs)("div",{className:"flex items-start space-x-2",children:[0===t?(0,i.jsx)(l.A,{className:"h-4 w-4"}):1===t?(0,i.jsx)(d.A,{className:"h-4 w-4"}):(0,i.jsx)(p.A,{className:"h-4 w-4"}),(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsx)(o.TN,{className:"font-medium",children:e}),a&&(0,i.jsxs)(o.TN,{className:"text-sm mt-1 opacity-90",children:["Data di scadenza: ",new Date(a).toLocaleDateString("it-IT",{day:"2-digit",month:"2-digit",year:"numeric"})]}),(0,i.jsx)(o.TN,{className:"text-sm mt-2 opacity-80",children:"Contatta l'amministratore per rinnovare il tuo account."})]})]}),(0,i.jsx)(c.$,{variant:"ghost",size:"sm",onClick:r,className:"h-6 w-6 p-0 hover:bg-transparent",children:(0,i.jsx)(u.A,{className:"h-4 w-4"})})]})}):null}function h({children:e}){let{isAuthenticated:t}=(0,s.A)();return n(),(0,i.jsxs)("main",{className:"pt-16",children:[t&&(0,i.jsx)("div",{className:"container mx-auto px-4 py-2",children:(0,i.jsx)(m,{})}),e]})}},69581:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,16444,23)),Promise.resolve().then(a.t.bind(a,16042,23)),Promise.resolve().then(a.t.bind(a,88170,23)),Promise.resolve().then(a.t.bind(a,49477,23)),Promise.resolve().then(a.t.bind(a,29345,23)),Promise.resolve().then(a.t.bind(a,12089,23)),Promise.resolve().then(a.t.bind(a,46577,23)),Promise.resolve().then(a.t.bind(a,31307,23))},79390:(e,t,a)=>{Promise.resolve().then(a.bind(a,69336)),Promise.resolve().then(a.bind(a,10501)),Promise.resolve().then(a.bind(a,91347)),Promise.resolve().then(a.bind(a,63213))},79737:(e,t,a)=>{"use strict";a.d(t,{Toaster:()=>i});let i=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\components\\ui\\toaster.tsx","Toaster")},91347:(e,t,a)=>{"use strict";a.d(t,{Toaster:()=>l});var i=a(60687),r=a(29867),s=a(29523),n=a(93613),o=a(5336),c=a(11860);function l(){let{toasts:e,dismiss:t}=(0,r.dj)();return(0,i.jsx)("div",{className:"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",children:e.map(e=>(0,i.jsxs)("div",{className:"group pointer-events-auto relative flex w-full items-center justify-between space-x-2 overflow-hidden rounded-md border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",style:{backgroundColor:"destructive"===e.variant?"#fef2f2":"#f0f9ff",borderColor:"destructive"===e.variant?"#fecaca":"#bae6fd"},children:[(0,i.jsxs)("div",{className:"flex items-start space-x-2",children:["destructive"===e.variant?(0,i.jsx)(n.A,{className:"h-4 w-4 text-red-600 mt-0.5"}):(0,i.jsx)(o.A,{className:"h-4 w-4 text-green-600 mt-0.5"}),(0,i.jsxs)("div",{className:"grid gap-1",children:[e.title&&(0,i.jsx)("div",{className:`text-sm font-semibold ${"destructive"===e.variant?"text-red-900":"text-gray-900"}`,children:e.title}),e.description&&(0,i.jsx)("div",{className:`text-sm ${"destructive"===e.variant?"text-red-700":"text-gray-700"}`,children:e.description})]})]}),(0,i.jsx)(s.$,{variant:"ghost",size:"sm",className:"absolute right-1 top-1 h-6 w-6 p-0 hover:bg-transparent",onClick:()=>t(e.id),children:(0,i.jsx)(c.A,{className:"h-3 w-3"})})]},e.id))})}},91821:(e,t,a)=>{"use strict";a.d(t,{Fc:()=>c,TN:()=>l});var i=a(60687),r=a(43210),s=a(24224),n=a(4780);let o=(0,s.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),c=r.forwardRef(({className:e,variant:t,...a},r)=>(0,i.jsx)("div",{ref:r,role:"alert",className:(0,n.cn)(o({variant:t}),e),...a}));c.displayName="Alert",r.forwardRef(({className:e,...t},a)=>(0,i.jsx)("h5",{ref:a,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",e),...t})).displayName="AlertTitle";let l=r.forwardRef(({className:e,...t},a)=>(0,i.jsx)("div",{ref:a,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",e),...t}));l.displayName="AlertDescription"},93319:(e,t,a)=>{"use strict";a.d(t,{Navbar:()=>i});let i=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call Navbar() from the server but Navbar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\components\\layout\\Navbar.tsx","Navbar")},94431:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>m,metadata:()=>u});var i=a(37413),r=a(35759),s=a.n(r),n=a(29404),o=a.n(n);a(61135),a(50346);var c=a(93319),l=a(54679),d=a(29131),p=a(79737);let u={title:"CABLYS - Cable Installation Advance System",description:"Sistema avanzato per la gestione dell'installazione cavi",manifest:"/manifest.json",themeColor:"#2563eb",viewport:"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no",appleWebApp:{capable:!0,statusBarStyle:"default",title:"CABLYS"}};function m({children:e}){return(0,i.jsx)("html",{lang:"it",children:(0,i.jsx)("body",{className:`${s().variable} ${o().variable} antialiased`,children:(0,i.jsxs)(d.AuthProvider,{children:[(0,i.jsxs)("div",{className:"min-h-screen bg-slate-50",children:[(0,i.jsx)(c.Navbar,{}),(0,i.jsx)(l.default,{children:e})]}),(0,i.jsx)(p.Toaster,{})]})})})}}};