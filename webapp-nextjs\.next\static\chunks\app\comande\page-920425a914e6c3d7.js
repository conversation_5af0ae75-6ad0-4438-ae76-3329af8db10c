(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7484],{21815:(e,a,s)=>{Promise.resolve().then(s.bind(s,56834))},24944:(e,a,s)=>{"use strict";s.d(a,{k:()=>r});var t=s(95155);s(12115);var i=s(55863),n=s(59434);function r(e){let{className:a,value:s,...r}=e;return(0,t.jsx)(i.bL,{"data-slot":"progress",className:(0,n.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",a),...r,children:(0,t.jsx)(i.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:"translateX(-".concat(100-(s||0),"%)")}})})}},26126:(e,a,s)=>{"use strict";s.d(a,{E:()=>o});var t=s(95155);s(12115);var i=s(99708),n=s(74466),r=s(59434);let l=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:a,variant:s,asChild:n=!1,...o}=e,c=n?i.DX:"span";return(0,t.jsx)(c,{"data-slot":"badge",className:(0,r.cn)(l({variant:s}),a),...o})}},30285:(e,a,s)=>{"use strict";s.d(a,{$:()=>o});var t=s(95155);s(12115);var i=s(99708),n=s(74466),r=s(59434);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:a,variant:s,size:n,asChild:o=!1,...c}=e,d=o?i.DX:"button";return(0,t.jsx)(d,{"data-slot":"button",className:(0,r.cn)(l({variant:s,size:n,className:a})),...c})}},47262:(e,a,s)=>{"use strict";s.d(a,{S:()=>l});var t=s(95155);s(12115);var i=s(76981),n=s(5196),r=s(59434);function l(e){let{className:a,...s}=e;return(0,t.jsx)(i.bL,{"data-slot":"checkbox",className:(0,r.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...s,children:(0,t.jsx)(i.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,t.jsx)(n.A,{className:"size-3.5"})})})}},54165:(e,a,s)=>{"use strict";s.d(a,{Cf:()=>m,Es:()=>x,L3:()=>h,c7:()=>u,lG:()=>l,rr:()=>p,zM:()=>o});var t=s(95155);s(12115);var i=s(15452),n=s(54416),r=s(59434);function l(e){let{...a}=e;return(0,t.jsx)(i.bL,{"data-slot":"dialog",...a})}function o(e){let{...a}=e;return(0,t.jsx)(i.l9,{"data-slot":"dialog-trigger",...a})}function c(e){let{...a}=e;return(0,t.jsx)(i.ZL,{"data-slot":"dialog-portal",...a})}function d(e){let{className:a,...s}=e;return(0,t.jsx)(i.hJ,{"data-slot":"dialog-overlay",className:(0,r.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...s})}function m(e){let{className:a,children:s,showCloseButton:l=!0,...o}=e;return(0,t.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,t.jsx)(d,{}),(0,t.jsxs)(i.UC,{"data-slot":"dialog-content",className:(0,r.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...o,children:[s,l&&(0,t.jsxs)(i.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,t.jsx)(n.A,{}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"dialog-header",className:(0,r.cn)("flex flex-col gap-2 text-center sm:text-left",a),...s})}function x(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"dialog-footer",className:(0,r.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...s})}function h(e){let{className:a,...s}=e;return(0,t.jsx)(i.hE,{"data-slot":"dialog-title",className:(0,r.cn)("text-lg leading-none font-semibold",a),...s})}function p(e){let{className:a,...s}=e;return(0,t.jsx)(i.VY,{"data-slot":"dialog-description",className:(0,r.cn)("text-muted-foreground text-sm",a),...s})}},55365:(e,a,s)=>{"use strict";s.d(a,{Fc:()=>o,TN:()=>c});var t=s(95155),i=s(12115),n=s(74466),r=s(59434);let l=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=i.forwardRef((e,a)=>{let{className:s,variant:i,...n}=e;return(0,t.jsx)("div",{ref:a,role:"alert",className:(0,r.cn)(l({variant:i}),s),...n})});o.displayName="Alert",i.forwardRef((e,a)=>{let{className:s,...i}=e;return(0,t.jsx)("h5",{ref:a,className:(0,r.cn)("mb-1 font-medium leading-none tracking-tight",s),...i})}).displayName="AlertTitle";let c=i.forwardRef((e,a)=>{let{className:s,...i}=e;return(0,t.jsx)("div",{ref:a,className:(0,r.cn)("text-sm [&_p]:leading-relaxed",s),...i})});c.displayName="AlertDescription"},56834:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>Q});var t=s(95155),i=s(12115),n=s(66695),r=s(30285),l=s(26126),o=s(62523),c=s(63743),d=s(85127),m=s(40283),u=s(25731),x=s(54165),h=s(85057),p=s(88539),v=s(59409),g=s(55365),f=s(25273),b=s(85339),j=s(51154),N=s(71007);let A={POSA:"POSA",COLLEGAMENTO_PARTENZA:"COLLEGAMENTO_PARTENZA",COLLEGAMENTO_ARRIVO:"COLLEGAMENTO_ARRIVO",CERTIFICAZIONE:"CERTIFICAZIONE"},_={INSTALLATO:"Installato"};function C(e,a,s){let t={isValid:!0,errors:[],warnings:[],info:[],caviValidi:[],caviProblematici:[]};return e&&0!==e.length?s&&""!==s.trim()?(e.forEach(e=>{let i=function(e,a,s){let t={id_cavo:e.id_cavo,isValid:!0,errors:[],warnings:[],info:[]},i=function(e,a){let s={errors:[],warnings:[],info:[]},t=e.stato_installazione===_.INSTALLATO,i=e.metratura_reale&&parseFloat(e.metratura_reale)>0,n=e.collegamenti&&parseInt(e.collegamenti)>0,r="CERTIFICATO"===e.stato_certificazione;switch(a){case A.POSA:t&&s.errors.push("Cavo ".concat(e.id_cavo," \xe8 gi\xe0 installato e non pu\xf2 essere assegnato a comanda POSA")),i&&s.warnings.push("Cavo ".concat(e.id_cavo," ha gi\xe0 metratura reale registrata"));break;case A.COLLEGAMENTO_PARTENZA:case A.COLLEGAMENTO_ARRIVO:t||i||s.warnings.push("Cavo ".concat(e.id_cavo," non risulta installato. Verificare prerequisiti.")),n&&s.warnings.push("Cavo ".concat(e.id_cavo," risulta gi\xe0 collegato"));break;case A.CERTIFICAZIONE:t||s.errors.push("Cavo ".concat(e.id_cavo," deve essere installato per la certificazione")),n||s.warnings.push("Cavo ".concat(e.id_cavo," non risulta collegato. Verificare prerequisiti.")),r&&s.warnings.push("Cavo ".concat(e.id_cavo," \xe8 gi\xe0 certificato"))}return s}(e,a);t.errors.push(...i.errors),t.warnings.push(...i.warnings),t.info.push(...i.info);let n=function(e,a){let s={errors:[],warnings:[],info:[]};switch(a){case A.POSA:e.comanda_posa&&s.errors.push("Cavo ".concat(e.id_cavo," ha gi\xe0 comanda POSA assegnata: ").concat(e.comanda_posa));break;case A.COLLEGAMENTO_PARTENZA:e.comanda_partenza&&s.errors.push("Cavo ".concat(e.id_cavo," ha gi\xe0 comanda COLLEGAMENTO_PARTENZA assegnata: ").concat(e.comanda_partenza));break;case A.COLLEGAMENTO_ARRIVO:e.comanda_arrivo&&s.errors.push("Cavo ".concat(e.id_cavo," ha gi\xe0 comanda COLLEGAMENTO_ARRIVO assegnata: ").concat(e.comanda_arrivo));break;case A.CERTIFICAZIONE:e.comanda_certificazione&&s.errors.push("Cavo ".concat(e.id_cavo," ha gi\xe0 comanda CERTIFICAZIONE assegnata: ").concat(e.comanda_certificazione))}return s}(e,a);t.errors.push(...n.errors),t.warnings.push(...n.warnings),t.info.push(...n.info);let r=function(e,a){let s={warnings:[],info:[]};switch(a){case A.COLLEGAMENTO_PARTENZA:case A.COLLEGAMENTO_ARRIVO:!e.comanda_posa&&(!e.metratura_reale||0>=parseFloat(e.metratura_reale))&&s.warnings.push("Cavo ".concat(e.id_cavo," non ha comanda posa assegnata e non risulta installato. Verificare prerequisiti."));break;case A.CERTIFICAZIONE:e.comanda_partenza||e.comanda_arrivo||s.warnings.push("Cavo ".concat(e.id_cavo," non ha comande di collegamento assegnate. Verificare prerequisiti."))}return s}(e,a);t.warnings.push(...r.warnings),t.info.push(...r.info);let l=function(e,a,s){let t={warnings:[],info:[]},i=[...new Set(Object.values({posa:e.responsabile_posa||"",partenza:e.responsabile_partenza||"",arrivo:e.responsabile_arrivo||"",certificazione:e.responsabile_certificazione||""}).filter(e=>e&&""!==e.trim()))];return i.length>1&&!i.includes(s)&&t.warnings.push("Cavo ".concat(e.id_cavo," ha gi\xe0 responsabili diversi (").concat(i.join(", "),"). Nuovo responsabile: ").concat(s)),t}(e,0,s);return t.warnings.push(...l.warnings),t.info.push(...l.info),t.isValid=0===t.errors.length,t}(e,a,s);t.errors.push(...i.errors),t.warnings.push(...i.warnings),t.info.push(...i.info),i.isValid?t.caviValidi.push(e):t.caviProblematici.push({cavo:e,issues:i.errors})}),t.isValid=0===t.errors.length):(t.errors.push("Responsabile non specificato"),t.isValid=!1):(t.errors.push("Nessun cavo selezionato per la comanda"),t.isValid=!1),t}function y(e){let a=[];return e.errors.length>0&&(a.push("❌ Errori (".concat(e.errors.length,"):")),e.errors.forEach(e=>a.push("  • ".concat(e)))),e.warnings.length>0&&(a.push("⚠️ Avvisi (".concat(e.warnings.length,"):")),e.warnings.forEach(e=>a.push("  • ".concat(e)))),e.info.length>0&&(a.push("ℹ️ Informazioni (".concat(e.info.length,"):")),e.info.forEach(e=>a.push("  • ".concat(e)))),e.caviValidi.length>0&&a.push("✅ Cavi validi: ".concat(e.caviValidi.length)),e.caviProblematici.length>0&&a.push("❌ Cavi problematici: ".concat(e.caviProblematici.length)),a.join("\n")}function E(e){let{open:a,onClose:s,caviSelezionati:n=[],tipoComanda:l,onSuccess:c,onError:d,onComandaCreated:A}=e,[_,E]=(0,i.useState)(!1),[w,S]=(0,i.useState)(""),[O,T]=(0,i.useState)([]),[z,R]=(0,i.useState)(!1),[I,L]=(0,i.useState)(""),[k,P]=(0,i.useState)(!1),{cantiere:V}=(0,m.A)(),[F,M]=(0,i.useState)(0);(0,i.useEffect)(()=>{M((null==V?void 0:V.id_cantiere)||parseInt(localStorage.getItem("selectedCantiereId")||"0"))},[V]);let[U,G]=(0,i.useState)({tipo_comanda:l||"POSA",responsabile:"",descrizione:"",data_scadenza:"",numero_componenti_squadra:1});(0,i.useEffect)(()=>{a&&F>0&&D()},[a,F]),(0,i.useEffect)(()=>{a||(G({tipo_comanda:l||"POSA",responsabile:"",descrizione:"",data_scadenza:"",numero_componenti_squadra:1}),S(""))},[a,l]);let D=async()=>{try{R(!0);let e=await u.AR.getResponsabili(F),a=(null==e?void 0:e.data)||e||[];T(Array.isArray(a)?a:[])}catch(e){T([])}finally{R(!1)}},Z=async()=>{var e,a,t;try{let a;if(E(!0),S(""),!U.tipo_comanda)return void S("Seleziona il tipo di comanda");if(!U.responsabile)return void S("Seleziona un responsabile");if(!F||F<=0)return void S("Cantiere non selezionato");if(n.length>0){let e=C(n,U.tipo_comanda,U.responsabile);if(!e.isValid){S("Validazione cavi fallita. Controllare i dettagli nella sezione validazione."),L(y(e)),P(!0);return}e.warnings.length>0&&(L(y(e)),P(!0))}let t={tipo_comanda:U.tipo_comanda,responsabile:U.responsabile,descrizione:U.descrizione||null,data_scadenza:U.data_scadenza||null,numero_componenti_squadra:U.numero_componenti_squadra},i=(null==(a=n&&n.length>0?await u.CV.createComandaWithCavi(F,t,n):await u.CV.createComanda(F,t))||null==(e=a.data)?void 0:e.codice_comanda)||(null==a?void 0:a.codice_comanda),r=n&&n.length>0?"Comanda ".concat(i," creata con successo per ").concat(n.length," cavi"):"Comanda ".concat(i," creata con successo");c(r),null==A||A(),s()}catch(e){d((null==(t=e.response)||null==(a=t.data)?void 0:a.detail)||e.message||"Errore durante la creazione della comanda")}finally{E(!1)}};return(0,t.jsx)(x.lG,{open:a,onOpenChange:s,children:(0,t.jsxs)(x.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(x.c7,{children:[(0,t.jsxs)(x.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(f.A,{className:"h-5 w-5"}),"Crea Nuova Comanda"]}),(0,t.jsx)(x.rr,{children:n&&n.length>0?"Crea una comanda per ".concat(n.length," cavi selezionati"):"Crea una nuova comanda di lavoro"})]}),(0,t.jsxs)("div",{className:"space-y-6 py-4",children:[w&&(0,t.jsxs)(g.Fc,{variant:"destructive",children:[(0,t.jsx)(b.A,{className:"h-4 w-4"}),(0,t.jsx)(g.TN,{children:w})]}),n.length>0&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("h4",{className:"text-sm font-medium",children:["Validazione Cavi (",n.length," selezionati)"]}),(0,t.jsx)(r.$,{type:"button",variant:"outline",size:"sm",onClick:()=>{if(0===n.length){L("Nessun cavo selezionato per la validazione"),P(!0);return}L(y(C(n,U.tipo_comanda,U.responsabile))),P(!0)},disabled:!U.tipo_comanda||!U.responsabile,children:"Valida Cavi"})]}),k&&I&&(0,t.jsxs)(g.Fc,{children:[(0,t.jsx)(b.A,{className:"h-4 w-4"}),(0,t.jsx)(g.TN,{children:(0,t.jsx)("pre",{className:"whitespace-pre-wrap text-xs font-mono",children:I})})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(h.J,{htmlFor:"tipo",children:"Tipo Comanda *"}),(0,t.jsxs)(v.l6,{value:U.tipo_comanda,onValueChange:e=>G(a=>({...a,tipo_comanda:e})),children:[(0,t.jsx)(v.bq,{children:(0,t.jsx)(v.yv,{})}),(0,t.jsx)(v.gC,{children:[{value:"POSA",label:"\uD83D\uDD27 Posa Cavi",description:"Installazione e posa dei cavi"},{value:"COLLEGAMENTO_PARTENZA",label:"\uD83D\uDD0C Collegamento Partenza",description:"Collegamento lato partenza"},{value:"COLLEGAMENTO_ARRIVO",label:"⚡ Collegamento Arrivo",description:"Collegamento lato arrivo"},{value:"CERTIFICAZIONE",label:"\uD83D\uDCCB Certificazione",description:"Test e certificazione"}].map(e=>(0,t.jsx)(v.eb,{value:e.value,children:(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:e.label}),(0,t.jsx)("div",{className:"text-sm text-slate-500",children:e.description})]})},e.value))})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(h.J,{htmlFor:"responsabile",children:"Responsabile *"}),z?(0,t.jsxs)("div",{className:"flex items-center gap-2 p-2 text-sm text-slate-500",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 animate-spin"}),"Caricamento responsabili..."]}):(0,t.jsxs)(v.l6,{value:U.responsabile,onValueChange:e=>G(a=>({...a,responsabile:e})),children:[(0,t.jsx)(v.bq,{children:(0,t.jsx)(v.yv,{placeholder:"Seleziona responsabile"})}),(0,t.jsx)(v.gC,{children:O.map(e=>(0,t.jsx)(v.eb,{value:e.nome_responsabile,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(N.A,{className:"h-4 w-4"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:e.nome_responsabile}),e.numero_telefono&&(0,t.jsx)("div",{className:"text-sm text-slate-500",children:e.numero_telefono})]})]})},e.id_responsabile))})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(h.J,{htmlFor:"descrizione",children:"Descrizione"}),(0,t.jsx)(p.T,{id:"descrizione",placeholder:"Descrizione opzionale della comanda...",value:U.descrizione,onChange:e=>G(a=>({...a,descrizione:e.target.value})),rows:3})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(h.J,{htmlFor:"data_scadenza",children:"Data Scadenza"}),(0,t.jsx)(o.p,{id:"data_scadenza",type:"date",value:U.data_scadenza,onChange:e=>G(a=>({...a,data_scadenza:e.target.value}))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(h.J,{htmlFor:"squadra",children:"Componenti Squadra"}),(0,t.jsx)(o.p,{id:"squadra",type:"number",min:"1",max:"20",value:U.numero_componenti_squadra,onChange:e=>G(a=>({...a,numero_componenti_squadra:parseInt(e.target.value)||1}))})]})]}),n&&n.length>0&&(0,t.jsxs)("div",{className:"p-3 bg-blue-50 rounded-lg border border-blue-200",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 text-blue-700",children:[(0,t.jsx)(f.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{className:"font-medium",children:["Cavi da assegnare: ",n.length]})]}),(0,t.jsx)("div",{className:"text-sm text-blue-600 mt-1",children:"I cavi selezionati verranno automaticamente assegnati a questa comanda"})]})]}),(0,t.jsxs)(x.Es,{children:[(0,t.jsx)(r.$,{variant:"outline",onClick:s,disabled:_,children:"Annulla"}),(0,t.jsxs)(r.$,{onClick:Z,disabled:_,children:[_&&(0,t.jsx)(j.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Crea Comanda"]})]})]})})}var w=s(17580),S=s(84616),O=s(54416),T=s(4229),z=s(19420),R=s(28883),I=s(13717),L=s(62525);function k(e){let{open:a,onClose:s,onSuccess:l,onError:c}=e,[d,p]=(0,i.useState)(!1),[v,f]=(0,i.useState)(""),[A,_]=(0,i.useState)([]),[C,y]=(0,i.useState)(null),[E,k]=(0,i.useState)(!1),{cantiere:P}=(0,m.A)(),[V,F]=(0,i.useState)(0);(0,i.useEffect)(()=>{F((null==P?void 0:P.id_cantiere)||parseInt(localStorage.getItem("selectedCantiereId")||"0"))},[P]);let[M,U]=(0,i.useState)({nome_responsabile:"",numero_telefono:"",mail:""});(0,i.useEffect)(()=>{a&&V>0&&G()},[a,V]),(0,i.useEffect)(()=>{a||(U({nome_responsabile:"",numero_telefono:"",mail:""}),f(""),y(null),k(!1))},[a]);let G=async()=>{try{p(!0);let e=await u.AR.getResponsabili(V),a=(null==e?void 0:e.data)||e||[];_(Array.isArray(a)?a:[])}catch(e){f("Errore durante il caricamento dei responsabili")}finally{p(!1)}},D=async()=>{try{p(!0),f("");let e={nome_responsabile:M.nome_responsabile.trim(),numero_telefono:M.numero_telefono.trim()||null,mail:M.mail.trim()||null},a=function(e){var a,s;let t=[];return e.nome_responsabile&&e.nome_responsabile.trim()||t.push("Il nome del responsabile \xe8 obbligatorio"),e.mail||e.numero_telefono||t.push("Almeno uno tra email e telefono deve essere specificato"),e.mail&&(a=e.mail,!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(a))&&t.push("Formato email non valido"),e.numero_telefono&&(s=e.numero_telefono,!/^[\+]?[0-9\s\-\(\)]{8,15}$/.test(s.replace(/\s/g,"")))&&t.push("Formato telefono non valido"),{isValid:0===t.length,errors:t}}(e);if(!a.isValid)return void f("Errori di validazione: ".concat(a.errors.join(", ")));await u.AR.createResponsabile(V,e),l("Responsabile aggiunto con successo"),U({nome_responsabile:"",numero_telefono:"",mail:""}),k(!1),G()}catch(s){var e,a;f((null==(a=s.response)||null==(e=a.data)?void 0:e.detail)||"Errore durante la creazione del responsabile")}finally{p(!1)}},Z=e=>{U({nome_responsabile:e.nome_responsabile,numero_telefono:e.numero_telefono||"",mail:e.mail||""}),y(e.id_responsabile),k(!1)},B=async()=>{if(C)try{if(p(!0),f(""),!M.nome_responsabile.trim())return void f("Il nome del responsabile \xe8 obbligatorio");if(M.mail&&!M.mail.includes("@"))return void f("Inserisci un indirizzo email valido");let e={nome_responsabile:M.nome_responsabile.trim(),numero_telefono:M.numero_telefono.trim()||null,mail:M.mail.trim()||null};await u.AR.updateResponsabile(V,C,e),l("Responsabile aggiornato con successo"),U({nome_responsabile:"",numero_telefono:"",mail:""}),y(null),G()}catch(s){var e,a;f((null==(a=s.response)||null==(e=a.data)?void 0:e.detail)||"Errore durante l'aggiornamento del responsabile")}finally{p(!1)}},$=async(e,a)=>{if(confirm('Sei sicuro di voler eliminare il responsabile "'.concat(a,'"?')))try{p(!0),await u.AR.deleteResponsabile(V,e),l("Responsabile eliminato con successo"),G()}catch(e){var s,t;c((null==(t=e.response)||null==(s=t.data)?void 0:s.detail)||"Errore durante l'eliminazione del responsabile")}finally{p(!1)}},q=()=>{y(null),k(!1),U({nome_responsabile:"",numero_telefono:"",mail:""}),f("")};return(0,t.jsx)(x.lG,{open:a,onOpenChange:s,children:(0,t.jsxs)(x.Cf,{className:"sm:max-w-[700px] max-h-[80vh] overflow-y-auto",children:[(0,t.jsxs)(x.c7,{children:[(0,t.jsxs)(x.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(w.A,{className:"h-5 w-5"}),"Gestisci Responsabili"]}),(0,t.jsxs)(x.rr,{children:["Gestisci i responsabili per il cantiere ",localStorage.getItem("selectedCantiereName")||V]})]}),(0,t.jsxs)("div",{className:"space-y-6 py-4",children:[v&&(0,t.jsxs)(g.Fc,{variant:"destructive",children:[(0,t.jsx)(b.A,{className:"h-4 w-4"}),(0,t.jsx)(g.TN,{children:v})]}),!E&&!C&&(0,t.jsxs)(r.$,{onClick:()=>k(!0),className:"w-full",variant:"outline",children:[(0,t.jsx)(S.A,{className:"h-4 w-4 mr-2"}),"Aggiungi Nuovo Responsabile"]}),(E||C)&&(0,t.jsx)(n.Zp,{className:"border-2 border-blue-200",children:(0,t.jsxs)(n.Wu,{className:"p-4 space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h4",{className:"font-medium",children:C?"Modifica Responsabile":"Nuovo Responsabile"}),(0,t.jsx)(r.$,{variant:"ghost",size:"sm",onClick:q,children:(0,t.jsx)(O.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(h.J,{htmlFor:"nome",children:"Nome Responsabile *"}),(0,t.jsx)(o.p,{id:"nome",placeholder:"Nome e cognome",value:M.nome_responsabile,onChange:e=>U(a=>({...a,nome_responsabile:e.target.value}))})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(h.J,{htmlFor:"telefono",children:"Numero Telefono"}),(0,t.jsx)(o.p,{id:"telefono",placeholder:"+39 ************",value:M.numero_telefono,onChange:e=>U(a=>({...a,numero_telefono:e.target.value}))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(h.J,{htmlFor:"email",children:"Email"}),(0,t.jsx)(o.p,{id:"email",type:"email",placeholder:"<EMAIL>",value:M.mail,onChange:e=>U(a=>({...a,mail:e.target.value}))})]})]})]}),(0,t.jsxs)("div",{className:"flex gap-2 pt-2",children:[(0,t.jsxs)(r.$,{onClick:C?B:D,disabled:d,className:"flex-1",children:[d&&(0,t.jsx)(j.A,{className:"mr-2 h-4 w-4 animate-spin"}),(0,t.jsx)(T.A,{className:"mr-2 h-4 w-4"}),C?"Aggiorna":"Aggiungi"]}),(0,t.jsx)(r.$,{variant:"outline",onClick:q,children:"Annulla"})]})]})}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("h4",{className:"font-medium flex items-center gap-2",children:[(0,t.jsx)(w.A,{className:"h-4 w-4"}),"Responsabili Esistenti (",A.length,")"]}),d&&0===A.length?(0,t.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 animate-spin"}),"Caricamento responsabili..."]})}):0===A.length?(0,t.jsx)("div",{className:"text-center py-8 text-slate-500",children:"Nessun responsabile trovato"}):(0,t.jsx)("div",{className:"space-y-2",children:A.map(e=>(0,t.jsx)(n.Zp,{className:C===e.id_responsabile?"border-blue-300":"",children:(0,t.jsx)(n.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 text-slate-500"}),(0,t.jsx)("span",{className:"font-medium",children:e.nome_responsabile})]}),(0,t.jsxs)("div",{className:"space-y-1 text-sm text-slate-600",children:[e.numero_telefono&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(z.A,{className:"h-3 w-3"}),e.numero_telefono]}),e.mail&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(R.A,{className:"h-3 w-3"}),e.mail]})]})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(r.$,{variant:"ghost",size:"sm",onClick:()=>Z(e),disabled:d||C===e.id_responsabile,children:(0,t.jsx)(I.A,{className:"h-4 w-4"})}),(0,t.jsx)(r.$,{variant:"ghost",size:"sm",onClick:()=>$(e.id_responsabile,e.nome_responsabile),disabled:d,className:"text-red-600 hover:text-red-700",children:(0,t.jsx)(L.A,{className:"h-4 w-4"})})]})]})})},e.id_responsabile))})]})]}),(0,t.jsx)(x.Es,{children:(0,t.jsx)(r.$,{variant:"outline",onClick:s,children:"Chiudi"})})]})})}var P=s(24944),V=s(79397),F=s(69074),M=s(14186),U=s(40646),G=s(3493);function D(e){let{open:a,onClose:s,codiceComanda:o,onSuccess:d,onError:h}=e,[p,v]=(0,i.useState)(!1),[A,_]=(0,i.useState)(""),[C,y]=(0,i.useState)(null),{cantiere:E}=(0,m.A)(),[S,O]=(0,i.useState)(0);(0,i.useEffect)(()=>{O((null==E?void 0:E.id_cantiere)||parseInt(localStorage.getItem("selectedCantiereId")||"0"))},[E]),(0,i.useEffect)(()=>{a&&o&&S>0&&T()},[a,o,S]),(0,i.useEffect)(()=>{a||(y(null),_(""))},[a]);let T=async()=>{if(o)try{v(!0),_("");let e=await u.CV.getComanda(S,o),a=(null==e?void 0:e.data)||e;y(a)}catch(e){_("Errore durante il caricamento dei dettagli della comanda")}finally{v(!1)}},I=e=>new Date(e).toLocaleDateString("it-IT",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"});return o?(0,t.jsx)(x.lG,{open:a,onOpenChange:s,children:(0,t.jsxs)(x.Cf,{className:"sm:max-w-[800px] max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(x.c7,{children:[(0,t.jsxs)(x.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(f.A,{className:"h-5 w-5"}),"Dettagli Comanda ",o]}),(0,t.jsx)(x.rr,{children:"Visualizza tutti i dettagli e lo stato della comanda"})]}),(0,t.jsxs)("div",{className:"space-y-6 py-4",children:[A&&(0,t.jsxs)(g.Fc,{variant:"destructive",children:[(0,t.jsx)(b.A,{className:"h-4 w-4"}),(0,t.jsx)(g.TN,{children:A})]}),p?(0,t.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(j.A,{className:"h-5 w-5 animate-spin"}),"Caricamento dettagli comanda..."]})}):C?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(V.A,{className:"h-5 w-5"}),"Informazioni Generali"]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(e=>{let a={POSA:{label:"Posa Cavi",icon:"\uD83D\uDD27"},COLLEGAMENTO_PARTENZA:{label:"Collegamento Partenza",icon:"\uD83D\uDD0C"},COLLEGAMENTO_ARRIVO:{label:"Collegamento Arrivo",icon:"⚡"},CERTIFICAZIONE:{label:"Certificazione",icon:"\uD83D\uDCCB"}}[e]||{label:e,icon:"❓"};return(0,t.jsxs)(l.E,{variant:"outline",className:"bg-blue-50 text-blue-700 border-blue-200",children:[a.icon," ",a.label]})})(C.tipo_comanda),(e=>{let a=(0,c.Fw)(e);return(0,t.jsx)(l.E,{className:a.badge,children:e})})(C.stato)]})]})}),(0,t.jsx)(n.Wu,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(F.A,{className:"h-4 w-4 text-slate-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-500",children:"Data Creazione"}),(0,t.jsx)("p",{className:"font-medium",children:I(C.data_creazione)})]})]}),C.data_scadenza&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(M.A,{className:"h-4 w-4 text-slate-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-500",children:"Scadenza"}),(0,t.jsx)("p",{className:"font-medium",children:I(C.data_scadenza)})]})]}),C.data_completamento&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(U.A,{className:"h-4 w-4 text-green-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-500",children:"Completamento"}),(0,t.jsx)("p",{className:"font-medium text-green-700",children:I(C.data_completamento)})]})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(w.A,{className:"h-4 w-4 text-slate-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-500",children:"Componenti Squadra"}),(0,t.jsxs)("p",{className:"font-medium",children:[C.numero_componenti_squadra," persone"]})]})]}),C.descrizione&&(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-500",children:"Descrizione"}),(0,t.jsx)("p",{className:"font-medium",children:C.descrizione})]})]})]})})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(N.A,{className:"h-5 w-5"}),"Responsabile"]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)("div",{className:"flex items-start gap-4",children:(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"font-medium text-lg",children:C.responsabile||"Non assegnato"}),C.responsabile_dettagli&&(0,t.jsxs)("div",{className:"mt-2 space-y-1 text-sm text-slate-600",children:[C.responsabile_dettagli.numero_telefono&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(z.A,{className:"h-3 w-3"}),C.responsabile_dettagli.numero_telefono]}),C.responsabile_dettagli.mail&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(R.A,{className:"h-3 w-3"}),C.responsabile_dettagli.mail]})]})]})})})]}),C.progresso&&(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(V.A,{className:"h-5 w-5"}),"Progresso Lavori"]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"Completamento"}),(0,t.jsxs)("span",{className:"text-sm font-medium",children:[C.progresso.percentuale,"%"]})]}),(0,t.jsx)(P.k,{value:C.progresso.percentuale,className:"h-2"}),(0,t.jsxs)("div",{className:"flex justify-between text-sm text-slate-600",children:[(0,t.jsxs)("span",{children:[C.progresso.completati," completati"]}),(0,t.jsxs)("span",{children:[C.progresso.totale," totali"]})]})]})})]}),C.cavi_assegnati&&C.cavi_assegnati.length>0&&(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(G.A,{className:"h-5 w-5"}),"Cavi Assegnati (",C.cavi_assegnati.length,")"]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:C.cavi_assegnati.map((e,a)=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-2 bg-slate-50 rounded",children:[(0,t.jsx)("span",{className:"font-mono text-sm",children:e.id_cavo||e}),e.stato&&(0,t.jsx)(l.E,{variant:"outline",className:"text-xs",children:e.stato})]},a))})})]})]}):(0,t.jsx)("div",{className:"text-center py-8 text-slate-500",children:"Nessun dettaglio disponibile"})]}),(0,t.jsxs)(x.Es,{children:[(0,t.jsx)(r.$,{variant:"outline",onClick:s,children:"Chiudi"}),C&&(0,t.jsx)(r.$,{onClick:()=>{d("Funzione di modifica in sviluppo")},children:"Modifica Comanda"})]})]})}):null}var Z=s(1243),B=s(87481);function $(e){let{open:a,onClose:s,codiceComanda:n,tipoComanda:c,onSuccess:d,onError:m}=e,[p,v]=(0,i.useState)([]),[g,f]=(0,i.useState)({}),[b,N]=(0,i.useState)(!1),[A,_]=(0,i.useState)(!1),[C,y]=(0,i.useState)(null),{toast:E}=(0,B.dj)();(0,i.useEffect)(()=>{a&&n&&w()},[a,n]);let w=async()=>{var e,a,s;try{if(N(!0),y(null),!localStorage.getItem("selectedCantiereId"))throw Error("Nessun cantiere selezionato");let a=await u.CV.getCaviComanda(n);v(a.data.cavi||[]);let s={};null==(e=a.data.cavi)||e.forEach(e=>{s[e.id_cavo]={metratura_reale:e.metratura_reale||0,numero_persone_impiegate:1,sistemazione:"",fascettatura:""}}),f(s)}catch(t){let e=(null==(s=t.response)||null==(a=s.data)?void 0:a.detail)||t.message||"Errore nel caricamento dei cavi";y(e),null==m||m(e)}finally{N(!1)}},S=(e,a,s)=>{f(t=>({...t,[e]:{...t[e],[a]:s}}))},O=async()=>{try{_(!0),y(null);let e="",a={};"POSA"===c?(e="dati-posa",a={dati_posa:g}):("COLLEGAMENTO_PARTENZA"===c||"COLLEGAMENTO_ARRIVO"===c)&&(e="dati-collegamento",a={dati_collegamento:g}),await u.CV.updateDatiComanda(n,e,a);let t="POSA"===c?"Metri posati inseriti con successo":"Metri collegati inseriti con successo";null==d||d(t),E({title:"Successo",description:t}),s()}catch(t){var e,a;let s=(null==(a=t.response)||null==(e=a.data)?void 0:e.detail)||t.message||"Errore nel salvataggio";y(s),null==m||m(s),E({title:"Errore",description:s,variant:"destructive"})}finally{_(!1)}};return(0,t.jsx)(x.lG,{open:a,onOpenChange:s,children:(0,t.jsxs)(x.Cf,{className:"max-w-4xl max-h-[80vh] overflow-y-auto",children:[(0,t.jsxs)(x.c7,{children:[(0,t.jsxs)(x.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(U.A,{className:"h-5 w-5 text-blue-600"}),(()=>{switch(c){case"POSA":return"Inserisci Metri Posati";case"COLLEGAMENTO_PARTENZA":return"Inserisci Metri Collegati - Partenza";case"COLLEGAMENTO_ARRIVO":return"Inserisci Metri Collegati - Arrivo";default:return"Inserisci Metri"}})()]}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[(()=>{switch(c){case"POSA":return"Inserisci i metri realmente posati per ogni cavo";case"COLLEGAMENTO_PARTENZA":return"Inserisci i metri collegati lato partenza";case"COLLEGAMENTO_ARRIVO":return"Inserisci i metri collegati lato arrivo";default:return"Inserisci i metri"}})()," - Comanda: ",n]})]}),b?(0,t.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,t.jsx)(j.A,{className:"h-6 w-6 animate-spin mr-2"}),"Caricamento cavi..."]}):C?(0,t.jsxs)("div",{className:"flex items-center justify-center py-8 text-red-600",children:[(0,t.jsx)(Z.A,{className:"h-5 w-5 mr-2"}),C]}):0===p.length?(0,t.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Nessun cavo trovato per questa comanda"}):(0,t.jsx)("div",{className:"space-y-4",children:p.map(e=>{var a,s,i,n;return(0,t.jsxs)("div",{className:"border rounded-lg p-4 bg-gray-50",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-blue-600",children:e.id_cavo}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[e.tipologia," - ",e.formazione," - ",e.metratura_teorica,"m teorici"]})]}),(0,t.jsx)(l.E,{variant:"Installato"===e.stato_installazione?"default":"secondary",children:e.stato_installazione})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(h.J,{htmlFor:"metri-".concat(e.id_cavo),children:"POSA"===c?"Metri Posati":"Metri Collegati"}),(0,t.jsx)(o.p,{id:"metri-".concat(e.id_cavo),type:"number",min:"0",step:"0.1",value:(null==(a=g[e.id_cavo])?void 0:a.metratura_reale)||0,onChange:a=>S(e.id_cavo,"metratura_reale",parseFloat(a.target.value)||0),className:"mt-1"})]}),"POSA"===c&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(h.J,{htmlFor:"persone-".concat(e.id_cavo),children:"Persone Impiegate"}),(0,t.jsx)(o.p,{id:"persone-".concat(e.id_cavo),type:"number",min:"1",value:(null==(s=g[e.id_cavo])?void 0:s.numero_persone_impiegate)||1,onChange:a=>S(e.id_cavo,"numero_persone_impiegate",parseInt(a.target.value)||1),className:"mt-1"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(h.J,{htmlFor:"sistemazione-".concat(e.id_cavo),children:"Sistemazione"}),(0,t.jsx)(o.p,{id:"sistemazione-".concat(e.id_cavo),value:(null==(i=g[e.id_cavo])?void 0:i.sistemazione)||"",onChange:a=>S(e.id_cavo,"sistemazione",a.target.value),className:"mt-1",placeholder:"Es: Interrato, Aereo..."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(h.J,{htmlFor:"fascettatura-".concat(e.id_cavo),children:"Fascettatura"}),(0,t.jsx)(o.p,{id:"fascettatura-".concat(e.id_cavo),value:(null==(n=g[e.id_cavo])?void 0:n.fascettatura)||"",onChange:a=>S(e.id_cavo,"fascettatura",a.target.value),className:"mt-1",placeholder:"Es: Standard, Rinforzata..."})]})]})]})]},e.id_cavo)})}),(0,t.jsxs)("div",{className:"flex justify-end gap-2 pt-4 border-t",children:[(0,t.jsx)(r.$,{variant:"outline",onClick:s,disabled:A,children:"Annulla"}),(0,t.jsx)(r.$,{onClick:O,disabled:A||0===p.length,className:"bg-blue-600 hover:bg-blue-700",children:A?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(j.A,{className:"h-4 w-4 animate-spin mr-2"}),"Salvando..."]}):"Salva Metri"})]})]})})}var q=s(47262),J=s(48313);function W(e){let{open:a,onClose:s,codiceComanda:n,onSuccess:c,onError:d}=e,[p,f]=(0,i.useState)([]),[N,A]=(0,i.useState)([]),[_,C]=(0,i.useState)({}),[y,E]=(0,i.useState)({}),[w,S]=(0,i.useState)(!1),[O,T]=(0,i.useState)(!1),[z,R]=(0,i.useState)(null),[I,L]=(0,i.useState)(""),[k,P]=(0,i.useState)(0),[V,F]=(0,i.useState)([]),[M,G]=(0,i.useState)(null),{toast:D}=(0,B.dj)(),{cantiere:Z}=(0,m.A)();(0,i.useEffect)(()=>{a&&n&&W()},[a,n]),(0,i.useEffect)(()=>{a&&(null==Z?void 0:Z.id_cantiere)&&H()},[a,null==Z?void 0:Z.id_cantiere]),(0,i.useEffect)(()=>{a||$()},[a]);let $=()=>{f([]),A([]),C({}),E({}),R(null),L(""),P(0),F([]),G(null)},W=async()=>{try{S(!0),R(null);let e=await u.CV.getCaviComanda(n),a=((null==e?void 0:e.data)||e||[]).filter(e=>"Installato"!==e.stato_installazione);f(a);let s={};a.forEach(e=>{s[e.id_cavo]={metratura_reale:0,numero_persone_impiegate:1,sistemazione:!1,fascettatura:!1,id_bobina:"BOBINA_VUOTA"}}),C(s)}catch(e){R("Errore durante il caricamento dei cavi della comanda")}finally{S(!1)}},H=async()=>{try{if(!(null==Z?void 0:Z.id_cantiere))return;let e=await u.Fw.getBobine(Z.id_cantiere),a=((null==e?void 0:e.data)||e||[]).filter(e=>"disponibile"===e.stato&&e.metri_residui>0);A(a)}catch(e){R("Errore durante il caricamento delle bobine")}},X=(e,a)=>{let s=parseFloat(a)||0;C(a=>{let t={...a,[e]:{...a[e],metratura_reale:s}};if(I&&"BOBINA_VUOTA"!==I){let a=N.find(e=>e.id_bobina===I);if(a){let s=0;Object.keys(t).forEach(e=>{var a;let i=(null==(a=t[e])?void 0:a.metratura_reale)||0;i>0&&(s+=i)});let i=a.metri_residui-s;if(P(i),i<0&&!M){G(e);let a=[];Object.keys(t).forEach(s=>{var i;0===((null==(i=t[s])?void 0:i.metratura_reale)||0)&&s!==e&&a.push(s)}),F(a)}else i>=0&&M===e&&(G(null),F([]))}}return t}),Q(e,s)},Q=(e,a)=>{let s=p.find(a=>a.id_cavo===e);s&&E(t=>{let i={...t};return delete i[e],a>0&&s.metratura_teorica,i})},Y=(e,a)=>{let s=parseInt(a)||1;C(a=>({...a,[e]:{...a[e],numero_persone_impiegate:s}}))},K=(e,a)=>{C(s=>({...s,[e]:{...s[e],sistemazione:a}}))},ee=(e,a)=>{C(s=>({...s,[e]:{...s[e],fascettatura:a}}))},ea=async()=>{try{if(T(!0),R(null),Object.keys(y).length>0)return void R("Correggere gli errori di validazione prima di salvare");let e={};if(Object.keys(_).forEach(a=>{let s=_[a];if(((null==s?void 0:s.metratura_reale)||0)>0){let t=M===a||k<0;e[a]={...s,id_bobina:I||"BOBINA_VUOTA",force_over:t}}}),0===Object.keys(e).length)return void R("Inserire almeno un metro per almeno un cavo");console.log("\uD83D\uDCBE InserisciMetriPosatiDialog: Salvataggio dati posa:",{codiceComanda:n,caviDaSalvare:Object.keys(e).length,datiPosaFiltrati:e,selectedBobina:I,metriResiduiSimulati:k,cavoCheCausaOver:M});let a=await fetch("/api/comande/".concat(n,"/dati-posa-bobine"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("token"))},body:JSON.stringify(e)});if(!a.ok){let e=await a.json();throw Error(e.detail||"Errore durante il salvataggio")}let t="Metri posati inseriti con successo per ".concat(Object.keys(e).length," cavi");null==c||c(t),D({title:"Successo",description:t}),s()}catch(t){var e,a;let s=(null==t||null==(a=t.response)||null==(e=a.data)?void 0:e.detail)||"Errore durante il salvataggio dei metri posati";R(s),null==d||d(s)}finally{T(!1)}};return(0,t.jsx)(x.lG,{open:a,onOpenChange:s,children:(0,t.jsxs)(x.Cf,{className:"max-w-6xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(x.c7,{children:[(0,t.jsxs)(x.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(J.A,{className:"h-5 w-5 text-blue-600"}),"Inserisci Metri Posati - Comanda ",n]}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Inserisci i metri posati per ogni cavo della comanda POSA"})]}),z&&(0,t.jsxs)(g.Fc,{variant:"destructive",children:[(0,t.jsx)(b.A,{className:"h-4 w-4"}),(0,t.jsx)(g.TN,{children:z})]}),w?(0,t.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,t.jsx)(j.A,{className:"h-6 w-6 animate-spin mr-2"}),"Caricamento cavi..."]}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"border rounded-lg p-4 bg-blue-50",children:[(0,t.jsx)("h3",{className:"font-medium text-blue-900 mb-3",children:"Selezione Bobina Principale"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(h.J,{htmlFor:"bobina-principale",children:"Bobina da Utilizzare"}),(0,t.jsxs)(v.l6,{value:I,onValueChange:e=>{if(L(e),F([]),G(null),C(a=>{let s={...a};return Object.keys(s).forEach(a=>{s[a]={...s[a],id_bobina:e}}),s}),e&&"BOBINA_VUOTA"!==e){let a=N.find(a=>a.id_bobina===e);a&&P(a.metri_residui)}else P(0)},children:[(0,t.jsx)(v.bq,{children:(0,t.jsx)(v.yv,{placeholder:"Seleziona bobina principale..."})}),(0,t.jsxs)(v.gC,{children:[(0,t.jsx)(v.eb,{value:"BOBINA_VUOTA",children:"\uD83D\uDD04 BOBINA_VUOTA (Assegna dopo)"}),N.map(e=>(0,t.jsxs)(v.eb,{value:e.id_bobina,children:["✅ ",e.id_bobina," - ",e.tipologia," ",e.sezione," (",e.metri_residui,"m)"]},e.id_bobina))]})]})]}),I&&"BOBINA_VUOTA"!==I&&(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsx)("span",{className:"font-medium",children:"Metri Residui: "}),(0,t.jsxs)("span",{className:k<0?"text-red-600 font-bold":"text-green-600",children:[k.toFixed(1),"m"]})]}),k<0&&(0,t.jsx)(l.E,{variant:"destructive",className:"text-xs",children:"OVER"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h3",{className:"font-medium",children:["Cavi da Installare (",p.length,")"]}),p.map(e=>{let a=_[e.id_cavo],s=y[e.id_cavo],i=V.includes(e.id_cavo),n=M===e.id_cavo,r=k<0&&"BOBINA_VUOTA"!==I;return(0,t.jsxs)("div",{className:"border rounded-lg p-4 space-y-4 ".concat(i?"bg-red-50 border-red-200":n?"bg-orange-50 border-orange-200":""),children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"font-medium flex items-center gap-2",children:[e.id_cavo,i&&(0,t.jsx)(l.E,{variant:"destructive",className:"text-xs",children:"BLOCCATO"}),n&&(0,t.jsx)(l.E,{variant:"outline",className:"text-xs border-orange-500 text-orange-700",children:"CAUSA OVER"})]}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[e.tipologia," - ",e.formazione," - ",e.metratura_teorica,"m teorici"]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(l.E,{variant:"Installato"===e.stato_installazione?"default":"secondary",children:e.stato_installazione}),r&&(0,t.jsx)(l.E,{variant:"destructive",className:"text-xs",children:"OVER"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(h.J,{htmlFor:"metri-".concat(e.id_cavo),children:"Metri Posati *"}),(0,t.jsx)(o.p,{id:"metri-".concat(e.id_cavo),type:"number",min:"0",step:"0.1",value:(null==a?void 0:a.metratura_reale)||"",onChange:a=>X(e.id_cavo,a.target.value),className:s?"border-red-500":i?"border-red-300 bg-red-50":"",placeholder:i?"Bloccato (OVER)":"0.0",disabled:i}),s&&(0,t.jsx)("p",{className:"text-xs text-red-500 mt-1",children:s}),i&&(0,t.jsx)("p",{className:"text-xs text-red-600 mt-1",children:"⚠️ Cavo bloccato: bobina in stato OVER"}),n&&!i&&(0,t.jsx)("p",{className:"text-xs text-orange-600 mt-1",children:"⚠️ Questo cavo causa lo stato OVER della bobina"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(h.J,{htmlFor:"persone-".concat(e.id_cavo),children:"Persone Impiegate"}),(0,t.jsx)(o.p,{id:"persone-".concat(e.id_cavo),type:"number",min:"1",value:(null==a?void 0:a.numero_persone_impiegate)||1,onChange:a=>Y(e.id_cavo,a.target.value)})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(q.S,{id:"sistemazione-".concat(e.id_cavo),checked:(null==a?void 0:a.sistemazione)||!1,onCheckedChange:a=>K(e.id_cavo,!!a)}),(0,t.jsx)(h.J,{htmlFor:"sistemazione-".concat(e.id_cavo),children:"Sistemazione"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(q.S,{id:"fascettatura-".concat(e.id_cavo),checked:(null==a?void 0:a.fascettatura)||!1,onCheckedChange:a=>ee(e.id_cavo,!!a)}),(0,t.jsx)(h.J,{htmlFor:"fascettatura-".concat(e.id_cavo),children:"Fascettatura"})]})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,t.jsx)("span",{className:"font-medium",children:"Bobina assegnata: "}),(0,t.jsx)("span",{className:"BOBINA_VUOTA"===I?"text-orange-600":"text-blue-600",children:I||"Nessuna"})]})]},e.id_cavo)})]})]}),(0,t.jsxs)("div",{className:"flex justify-end gap-2 pt-4 border-t",children:[(0,t.jsx)(r.$,{variant:"outline",onClick:s,children:"Annulla"}),(0,t.jsx)(r.$,{onClick:ea,disabled:O||w||0===p.length,children:O?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(j.A,{className:"h-4 w-4 animate-spin mr-2"}),"Salvando..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(U.A,{className:"h-4 w-4 mr-2"}),"Salva Metri Posati"]})})]})]})})}var H=s(47924),X=s(92657);function Q(){let[e,a]=(0,i.useState)("active"),[s,x]=(0,i.useState)([]),[h,p]=(0,i.useState)([]),[v,g]=(0,i.useState)(!0),[b,N]=(0,i.useState)(""),[A,_]=(0,i.useState)(""),[C,y]=(0,i.useState)("all"),[O,T]=(0,i.useState)("all"),[z,R]=(0,i.useState)(!1),[P,V]=(0,i.useState)(!1),[F,M]=(0,i.useState)(!1),[U,G]=(0,i.useState)(!1),[q,J]=(0,i.useState)(!1),[Q,Y]=(0,i.useState)(null),[K,ee]=(0,i.useState)(null),{user:ea,cantiere:es}=(0,m.A)(),{toast:et}=(0,B.dj)(),[ei,en]=(0,i.useState)(0);(0,i.useEffect)(()=>{en((null==es?void 0:es.id_cantiere)||parseInt(localStorage.getItem("selectedCantiereId")||"0"))},[es]),(0,i.useEffect)(()=>{ei&&ei>0&&er()},[ei]);let er=async()=>{var e,a,s;try{if(g(!0),N(""),!ei||ei<=0)return void N("Cantiere non selezionato");let[a,s]=await Promise.all([u.CV.getComande(ei),u.AR.getResponsabili(ei)]),t=(null==a||null==(e=a.data)?void 0:e.comande)||(null==a?void 0:a.comande)||(null==a?void 0:a.data)||a||[],i=(null==s?void 0:s.data)||s||[];x(Array.isArray(t)?t:[]),p(Array.isArray(i)?i:[])}catch(e){N((null==(s=e.response)||null==(a=s.data)?void 0:a.detail)||"Errore durante il caricamento dei dati")}finally{g(!1)}},el=e=>{et({title:"Successo",description:e}),er()},eo=e=>{et({title:"Errore",description:e,variant:"destructive"})},ec=async e=>{if(confirm("Sei sicuro di voler eliminare la comanda ".concat(e,"?")))try{g(!0),await u.CV.deleteComanda(ei,e),el("Comanda ".concat(e," eliminata con successo"))}catch(e){eo("Errore durante l'eliminazione della comanda")}finally{g(!1)}},ed=e=>{switch(e){case"COMPLETATA":return(0,t.jsx)(l.E,{className:"bg-green-100 text-green-800",children:"Completata"});case"IN_CORSO":return(0,t.jsx)(l.E,{className:"bg-blue-100 text-blue-800",children:"In Corso"});case"ASSEGNATA":return(0,t.jsx)(l.E,{className:"bg-yellow-100 text-yellow-800",children:"Assegnata"});case"CREATA":return(0,t.jsx)(l.E,{className:"bg-gray-100 text-gray-800",children:"Creata"});case"ANNULLATA":return(0,t.jsx)(l.E,{className:"bg-red-100 text-red-800",children:"Annullata"});default:return(0,t.jsx)(l.E,{variant:"secondary",children:e})}},em=e=>{let a=(0,c.Fw)(e);return(0,t.jsx)(l.E,{className:a.badge,children:{POSA:"\uD83D\uDD27 Posa",COLLEGAMENTO_PARTENZA:"\uD83D\uDD0C Coll. Partenza",COLLEGAMENTO_ARRIVO:"⚡ Coll. Arrivo",CERTIFICAZIONE:"\uD83D\uDCCB Certificazione"}[e]||e.replace(/_/g," ")})},eu=Array.isArray(s)?s.filter(a=>{let s=!0;switch(e){case"active":s="IN_CORSO"===a.stato||"ASSEGNATA"===a.stato||"CREATA"===a.stato;break;case"completed":s="COMPLETATA"===a.stato;break;default:s=!0}let t=""===A||a.codice_comanda.toLowerCase().includes(A.toLowerCase())||a.descrizione&&a.descrizione.toLowerCase().includes(A.toLowerCase())||a.responsabile&&a.responsabile.toLowerCase().includes(A.toLowerCase()),i="all"===C||a.responsabile===C,n="all"===O||a.tipo_comanda===O;return s&&t&&i&&n}):[],ex={totali:Array.isArray(s)?s.length:0,in_corso:Array.isArray(s)?s.filter(e=>"IN_CORSO"===e.stato).length:0,completate:Array.isArray(s)?s.filter(e=>"COMPLETATA"===e.stato).length:0,pianificate:Array.isArray(s)?s.filter(e=>"CREATA"===e.stato||"ASSEGNATA"===e.stato).length:0,filtrate:eu.length};return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:[(0,t.jsxs)("div",{className:"max-w-[90%] mx-auto space-y-6",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-slate-900 mb-2",children:"Gestione Comande"}),(0,t.jsx)("p",{className:"text-slate-600",children:ei>0?"Cantiere ".concat(localStorage.getItem("selectedCantiereName")||ei):"Nessun cantiere selezionato"})]}),(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(H.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,t.jsx)(o.p,{placeholder:"Cerca per codice, responsabile, tipo, stato o descrizione...",value:A,onChange:e=>_(e.target.value),className:"pl-10 bg-gray-50 hover:bg-blue-50 focus:bg-white transition-colors"})]})}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-2 mb-6",children:[(0,t.jsxs)(r.$,{onClick:()=>R(!0),className:"bg-blue-600 hover:bg-blue-700 text-white",children:[(0,t.jsx)(S.A,{className:"h-4 w-4 mr-2"}),"Nuova Comanda"]}),(0,t.jsxs)(r.$,{variant:"outline",onClick:()=>{et({title:"Funzione in sviluppo",description:"L'assegnazione cavi sar\xe0 disponibile presto"})},disabled:0===eu.length,children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Assegna Cavi"]}),(0,t.jsxs)(r.$,{variant:"outline",onClick:()=>V(!0),children:[(0,t.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Gestisci Responsabili"]})]}),(0,t.jsx)("div",{className:"mb-4",children:(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["Elenco Comande (",eu.length," di ",ex.totali,")"]})}),(0,t.jsx)(n.Zp,{className:"border border-gray-200 rounded-lg",children:(0,t.jsx)(n.Wu,{className:"p-0",children:(0,t.jsxs)(d.XI,{children:[(0,t.jsx)(d.A0,{children:(0,t.jsxs)(d.Hj,{className:"bg-gray-50",children:[(0,t.jsx)(d.nd,{className:"font-semibold",children:"Codice"}),(0,t.jsx)(d.nd,{className:"font-semibold",children:"Tipo"}),(0,t.jsx)(d.nd,{className:"font-semibold",children:"Responsabile"}),(0,t.jsx)(d.nd,{className:"font-semibold",children:"Contatti"}),(0,t.jsx)(d.nd,{className:"font-semibold",children:"Stato"}),(0,t.jsx)(d.nd,{className:"font-semibold",children:"Data Creazione"}),(0,t.jsx)(d.nd,{className:"font-semibold text-center",children:"Cavi"}),(0,t.jsx)(d.nd,{className:"font-semibold text-center",children:"Completamento"}),(0,t.jsx)(d.nd,{className:"font-semibold text-center",children:"Azioni"})]})}),(0,t.jsx)(d.BF,{children:v?(0,t.jsx)(d.Hj,{children:(0,t.jsx)(d.nA,{colSpan:9,className:"text-center py-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 animate-spin"}),"Caricamento comande..."]})})}):b?(0,t.jsx)(d.Hj,{children:(0,t.jsx)(d.nA,{colSpan:9,className:"text-center py-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2 text-red-600",children:[(0,t.jsx)(Z.A,{className:"h-4 w-4"}),b]})})}):0===eu.length?(0,t.jsx)(d.Hj,{children:(0,t.jsx)(d.nA,{colSpan:9,className:"text-center py-8 text-slate-500",children:"Nessuna comanda trovata"})}):eu.map(e=>(0,t.jsxs)(d.Hj,{className:"hover:bg-gray-50",children:[(0,t.jsx)(d.nA,{children:(0,t.jsx)("div",{className:"font-semibold text-blue-600",children:e.codice_comanda})}),(0,t.jsx)(d.nA,{children:em(e.tipo_comanda)}),(0,t.jsx)(d.nA,{children:(0,t.jsx)("div",{className:"font-medium",children:e.responsabile||"Non assegnato"})}),(0,t.jsx)(d.nA,{children:(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[e.responsabile_telefono&&(0,t.jsxs)("div",{children:["\uD83D\uDCDE ",e.responsabile_telefono]}),e.responsabile_email&&(0,t.jsxs)("div",{children:["✉️ ",e.responsabile_email]})]})}),(0,t.jsx)(d.nA,{children:ed(e.stato)}),(0,t.jsx)(d.nA,{children:(0,t.jsx)("div",{className:"text-sm",children:new Date(e.data_creazione).toLocaleDateString("it-IT")})}),(0,t.jsx)(d.nA,{className:"text-center",children:(0,t.jsx)("div",{className:"font-semibold text-blue-600",children:e.numero_cavi_assegnati||0})}),(0,t.jsx)(d.nA,{className:"text-center",children:(0,t.jsxs)("div",{className:"font-semibold",children:[(e.percentuale_completamento||0).toFixed(1),"%"]})}),(0,t.jsx)(d.nA,{className:"text-center",children:(0,t.jsxs)("div",{className:"flex gap-1 justify-center",children:[(0,t.jsx)(r.$,{variant:"ghost",size:"sm",onClick:()=>{Y(e.codice_comanda),M(!0)},title:"Visualizza",children:(0,t.jsx)(X.A,{className:"h-4 w-4"})}),(0,t.jsx)(r.$,{variant:"ghost",size:"sm",onClick:()=>{et({title:"Funzione in sviluppo",description:"La modifica comande sar\xe0 disponibile presto"})},title:"Modifica",children:(0,t.jsx)(I.A,{className:"h-4 w-4"})}),["POSA","COLLEGAMENTO_PARTENZA","COLLEGAMENTO_ARRIVO"].includes(e.tipo_comanda)&&(0,t.jsx)(r.$,{variant:"ghost",size:"sm",onClick:()=>{Y(e.codice_comanda),ee(e.tipo_comanda),"POSA"===e.tipo_comanda?J(!0):G(!0)},title:"POSA"===e.tipo_comanda?"Inserisci Metri Posati":"Inserisci Metri Collegati",children:(0,t.jsx)(f.A,{className:"h-4 w-4"})}),(0,t.jsx)(r.$,{variant:"ghost",size:"sm",onClick:()=>ec(e.codice_comanda),disabled:v,className:"text-red-600 hover:text-red-700",title:"Elimina",children:(0,t.jsx)(L.A,{className:"h-4 w-4"})})]})})]},e.codice_comanda))})]})})})]}),(0,t.jsx)(E,{open:z,onClose:()=>R(!1),onSuccess:el,onError:eo,onComandaCreated:()=>er()}),(0,t.jsx)(k,{open:P,onClose:()=>V(!1),onSuccess:el,onError:eo}),(0,t.jsx)(D,{open:F,onClose:()=>{M(!1),Y(null)},codiceComanda:Q,onSuccess:el,onError:eo}),(0,t.jsx)($,{open:U,onClose:()=>{G(!1),Y(null),ee(null)},codiceComanda:Q||"",tipoComanda:K||"POSA",onSuccess:e=>{el(e),loadComande()},onError:eo}),(0,t.jsx)(W,{open:q,onClose:()=>{J(!1),Y(null),ee(null)},codiceComanda:Q||"",onSuccess:e=>{el(e),loadComande()},onError:eo})]})}},59409:(e,a,s)=>{"use strict";s.d(a,{bq:()=>m,eb:()=>x,gC:()=>u,l6:()=>c,yv:()=>d});var t=s(95155);s(12115);var i=s(38715),n=s(66474),r=s(5196),l=s(47863),o=s(59434);function c(e){let{...a}=e;return(0,t.jsx)(i.bL,{"data-slot":"select",...a})}function d(e){let{...a}=e;return(0,t.jsx)(i.WT,{"data-slot":"select-value",...a})}function m(e){let{className:a,size:s="default",children:r,...l}=e;return(0,t.jsxs)(i.l9,{"data-slot":"select-trigger","data-size":s,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...l,children:[r,(0,t.jsx)(i.In,{asChild:!0,children:(0,t.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function u(e){let{className:a,children:s,position:n="popper",...r}=e;return(0,t.jsx)(i.ZL,{children:(0,t.jsxs)(i.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:n,...r,children:[(0,t.jsx)(h,{}),(0,t.jsx)(i.LM,{className:(0,o.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:s}),(0,t.jsx)(p,{})]})})}function x(e){let{className:a,children:s,...n}=e;return(0,t.jsxs)(i.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",a),...n,children:[(0,t.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,t.jsx)(i.VF,{children:(0,t.jsx)(r.A,{className:"size-4"})})}),(0,t.jsx)(i.p4,{children:s})]})}function h(e){let{className:a,...s}=e;return(0,t.jsx)(i.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",a),...s,children:(0,t.jsx)(l.A,{className:"size-4"})})}function p(e){let{className:a,...s}=e;return(0,t.jsx)(i.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",a),...s,children:(0,t.jsx)(n.A,{className:"size-4"})})}},59434:(e,a,s)=>{"use strict";s.d(a,{cn:()=>n});var t=s(52596),i=s(39688);function n(){for(var e=arguments.length,a=Array(e),s=0;s<e;s++)a[s]=arguments[s];return(0,i.QP)((0,t.$)(a))}},62523:(e,a,s)=>{"use strict";s.d(a,{p:()=>r});var t=s(95155),i=s(12115),n=s(59434);let r=i.forwardRef((e,a)=>{let{className:s,type:i,...r}=e;return(0,t.jsx)("input",{type:i,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),ref:a,...r})});r.displayName="Input"},63743:(e,a,s)=>{"use strict";s.d(a,{Fw:()=>d,NM:()=>c,Nj:()=>o,Tr:()=>r,mU:()=>t});let t={PRIMARY:{bg:"bg-blue-50",text:"text-blue-600",border:"border-blue-300",hover:"hover:bg-blue-50",active:"hover:border-blue-400",hex:"#007bff"},NEUTRAL:{text_dark:"text-gray-800",text_medium:"text-gray-600",text_light:"text-gray-500",bg_white:"bg-white",bg_light:"bg-gray-50",border:"border-gray-300",hex_dark:"#343A40",hex_medium:"#6c757d",hex_light:"#DEE2E6"},STATUS:{SUCCESS:{bg:"bg-green-50",text:"text-green-700",border:"border-green-200",hex:"#28A745"},WARNING:{bg:"bg-orange-50",text:"text-orange-700",border:"border-orange-200",hex:"#FD7E14"},ERROR:{bg:"bg-red-50",text:"text-red-700",border:"border-red-200",hex:"#DC3545"}}};t.STATUS.SUCCESS,t.STATUS.WARNING,t.NEUTRAL,t.STATUS.ERROR,t.NEUTRAL,t.STATUS.ERROR;let i={DA_INSTALLARE:t.NEUTRAL,INSTALLATO:t.STATUS.SUCCESS,COLLEGATO_PARTENZA:t.STATUS.WARNING,COLLEGATO_ARRIVO:t.STATUS.WARNING,COLLEGATO:t.STATUS.SUCCESS,CERTIFICATO:t.STATUS.SUCCESS,SPARE:t.STATUS.WARNING,ERRORE:t.STATUS.ERROR},n={ATTIVA:t.STATUS.SUCCESS,COMPLETATA:t.STATUS.SUCCESS,ANNULLATA:t.NEUTRAL,IN_CORSO:t.STATUS.WARNING,ERRORE:t.STATUS.ERROR},r=e=>{let a=i[null==e?void 0:e.toUpperCase().replace(/\s+/g,"_")]||i.ERRORE;return{badge:"".concat(a.bg," ").concat(a.text," rounded-full px-3 py-1 text-xs font-medium"),text:a.text,bg:a.bg,border:a.border,hex:a.hex}},l=()=>({button:"inline-flex items-center justify-center gap-1 px-3 py-2 text-xs font-medium bg-white text-gray-800 border-b-2 border-[#315cfd] rounded-full cursor-pointer min-w-[4rem] h-8 transition-colors duration-300 hover:border-2 hover:bg-[#315cfd] hover:text-white",text:"text-gray-800",border:"border-b-[#315cfd]",hover:"hover:bg-[#315cfd] hover:text-white hover:border-2"}),o=()=>l(),c=()=>({text:"inline-flex items-center gap-1 px-2 py-1 text-xs font-medium ".concat(t.NEUTRAL.text_light),color:t.NEUTRAL.text_light}),d=e=>{let a=n[null==e?void 0:e.toUpperCase().replace(/\s+/g,"_")]||n.ERRORE;return{badge:"".concat(a.bg," ").concat(a.text," ").concat(a.border),button:"".concat(a.bg," ").concat(a.text," ").concat(a.border," ").concat(a.hover),alert:"".concat(a.bg," ").concat(a.text," ").concat(a.border),text:a.text,bg:a.bg,border:a.border,hover:a.hover,hex:a.hex}};t.STATUS.ERROR,t.STATUS.WARNING,t.NEUTRAL,t.NEUTRAL},66695:(e,a,s)=>{"use strict";s.d(a,{BT:()=>o,Wu:()=>c,ZB:()=>l,Zp:()=>n,aR:()=>r});var t=s(95155);s(12115);var i=s(59434);function n(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,i.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...s})}function r(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,i.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...s})}function l(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,i.cn)("leading-none font-semibold",a),...s})}function o(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,i.cn)("text-muted-foreground text-sm",a),...s})}function c(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,i.cn)("px-6",a),...s})}},85057:(e,a,s)=>{"use strict";s.d(a,{J:()=>r});var t=s(95155);s(12115);var i=s(40968),n=s(59434);function r(e){let{className:a,...s}=e;return(0,t.jsx)(i.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...s})}},85127:(e,a,s)=>{"use strict";s.d(a,{A0:()=>r,BF:()=>l,Hj:()=>o,XI:()=>n,nA:()=>d,nd:()=>c});var t=s(95155);s(12115);var i=s(59434);function n(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,t.jsx)("table",{"data-slot":"table",className:(0,i.cn)("w-full caption-bottom text-sm border-collapse",a),...s})})}function r(e){let{className:a,...s}=e;return(0,t.jsx)("thead",{"data-slot":"table-header",className:(0,i.cn)("[&_tr]:border-b",a),...s})}function l(e){let{className:a,...s}=e;return(0,t.jsx)("tbody",{"data-slot":"table-body",className:(0,i.cn)("[&_tr:last-child]:border-0",a),...s})}function o(e){let{className:a,...s}=e;return(0,t.jsx)("tr",{"data-slot":"table-row",className:(0,i.cn)("data-[state=selected]:bg-muted border-b",a),...s})}function c(e){let{className:a,...s}=e;return(0,t.jsx)("th",{"data-slot":"table-head",className:(0,i.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...s})}function d(e){let{className:a,...s}=e;return(0,t.jsx)("td",{"data-slot":"table-cell",className:(0,i.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...s})}},87481:(e,a,s)=>{"use strict";s.d(a,{dj:()=>u});var t=s(12115);let i=0,n=new Map,r=e=>{if(n.has(e))return;let a=setTimeout(()=>{n.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,a)},l=(e,a)=>{switch(a.type){case"ADD_TOAST":return{...e,toasts:[a.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===a.toast.id?{...e,...a.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=a;return s?r(s):e.toasts.forEach(e=>{r(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===a.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==a.toastId)}}},o=[],c={toasts:[]};function d(e){c=l(c,e),o.forEach(e=>{e(c)})}function m(e){let{...a}=e,s=(i=(i+1)%Number.MAX_VALUE).toString(),t=()=>d({type:"DISMISS_TOAST",toastId:s});return d({type:"ADD_TOAST",toast:{...a,id:s,open:!0,onOpenChange:e=>{e||t()}}}),{id:s,dismiss:t,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:s}})}}function u(){let[e,a]=(0,t.useState)(c);return(0,t.useEffect)(()=>(o.push(a),()=>{let e=o.indexOf(a);e>-1&&o.splice(e,1)}),[]),{...e,toast:m,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},88539:(e,a,s)=>{"use strict";s.d(a,{T:()=>r});var t=s(95155),i=s(12115),n=s(59434);let r=i.forwardRef((e,a)=>{let{className:s,...i}=e;return(0,t.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:a,...i})});r.displayName="Textarea"}},e=>{var a=a=>e(e.s=a);e.O(0,[3455,3464,9816,9384,5171,6987,6918,283,8441,1684,7358],()=>a(21815)),_N_E=e.O()}]);