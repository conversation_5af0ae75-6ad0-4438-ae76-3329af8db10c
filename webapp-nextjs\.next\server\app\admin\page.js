(()=>{var e={};e.id=698,e.ids=[698],e.modules={1132:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\admin\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,a,t)=>{"use strict";t.d(a,{A0:()=>n,BF:()=>l,Hj:()=>d,XI:()=>i,nA:()=>c,nd:()=>o});var s=t(60687);t(43210);var r=t(4780);function i({className:e,...a}){return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm border-collapse",e),...a})})}function n({className:e,...a}){return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",e),...a})}function l({className:e,...a}){return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",e),...a})}function d({className:e,...a}){return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("data-[state=selected]:bg-muted border-b",e),...a})}function o({className:e,...a}){return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...a})}function c({className:e,...a}){return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...a})}},9245:()=>{},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},12597:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},15079:(e,a,t)=>{"use strict";t.d(a,{bq:()=>m,eb:()=>x,gC:()=>u,l6:()=>o,yv:()=>c});var s=t(60687);t(43210);var r=t(97822),i=t(78272),n=t(13964),l=t(3589),d=t(4780);function o({...e}){return(0,s.jsx)(r.bL,{"data-slot":"select",...e})}function c({...e}){return(0,s.jsx)(r.WT,{"data-slot":"select-value",...e})}function m({className:e,size:a="default",children:t,...n}){return(0,s.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":a,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n,children:[t,(0,s.jsx)(r.In,{asChild:!0,children:(0,s.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function u({className:e,children:a,position:t="popper",...i}){return(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...i,children:[(0,s.jsx)(p,{}),(0,s.jsx)(r.LM,{className:(0,d.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,s.jsx)(h,{})]})})}function x({className:e,children:a,...t}){return(0,s.jsxs)(r.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...t,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(n.A,{className:"size-4"})})}),(0,s.jsx)(r.p4,{children:a})]})}function p({className:e,...a}){return(0,s.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,s.jsx)(l.A,{className:"size-4"})})}function h({className:e,...a}){return(0,s.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,s.jsx)(i.A,{className:"size-4"})})}},15391:(e,a,t)=>{"use strict";t.d(a,{Fw:()=>c,NM:()=>o,Nj:()=>d,Tr:()=>n,mU:()=>s});let s={PRIMARY:{bg:"bg-blue-50",text:"text-blue-600",border:"border-blue-300",hover:"hover:bg-blue-50",active:"hover:border-blue-400",hex:"#007bff"},NEUTRAL:{text_dark:"text-gray-800",text_medium:"text-gray-600",text_light:"text-gray-500",bg_white:"bg-white",bg_light:"bg-gray-50",border:"border-gray-300",hex_dark:"#343A40",hex_medium:"#6c757d",hex_light:"#DEE2E6"},STATUS:{SUCCESS:{bg:"bg-green-50",text:"text-green-700",border:"border-green-200",hex:"#28A745"},WARNING:{bg:"bg-orange-50",text:"text-orange-700",border:"border-orange-200",hex:"#FD7E14"},ERROR:{bg:"bg-red-50",text:"text-red-700",border:"border-red-200",hex:"#DC3545"}}};s.STATUS.SUCCESS,s.STATUS.WARNING,s.NEUTRAL,s.STATUS.ERROR,s.NEUTRAL,s.STATUS.ERROR;let r={DA_INSTALLARE:s.NEUTRAL,INSTALLATO:s.STATUS.SUCCESS,COLLEGATO_PARTENZA:s.STATUS.WARNING,COLLEGATO_ARRIVO:s.STATUS.WARNING,COLLEGATO:s.STATUS.SUCCESS,CERTIFICATO:s.STATUS.SUCCESS,SPARE:s.STATUS.WARNING,ERRORE:s.STATUS.ERROR},i={ATTIVA:s.STATUS.SUCCESS,COMPLETATA:s.STATUS.SUCCESS,ANNULLATA:s.NEUTRAL,IN_CORSO:s.STATUS.WARNING,ERRORE:s.STATUS.ERROR},n=e=>{let a=r[e?.toUpperCase().replace(/\s+/g,"_")]||r.ERRORE;return{badge:`${a.bg} ${a.text} rounded-full px-3 py-1 text-xs font-medium`,text:a.text,bg:a.bg,border:a.border,hex:a.hex}},l=()=>({button:"inline-flex items-center justify-center gap-1 px-3 py-2 text-xs font-medium bg-white text-gray-800 border-b-2 border-[#315cfd] rounded-full cursor-pointer min-w-[4rem] h-8 transition-colors duration-300 hover:border-2 hover:bg-[#315cfd] hover:text-white",text:"text-gray-800",border:"border-b-[#315cfd]",hover:"hover:bg-[#315cfd] hover:text-white hover:border-2"}),d=()=>l(),o=()=>({text:`inline-flex items-center gap-1 px-2 py-1 text-xs font-medium ${s.NEUTRAL.text_light}`,color:s.NEUTRAL.text_light}),c=e=>{let a=i[e?.toUpperCase().replace(/\s+/g,"_")]||i.ERRORE;return{badge:`${a.bg} ${a.text} ${a.border}`,button:`${a.bg} ${a.text} ${a.border} ${a.hover}`,alert:`${a.bg} ${a.text} ${a.border}`,text:a.text,bg:a.bg,border:a.border,hover:a.hover,hex:a.hex}};s.STATUS.ERROR,s.STATUS.WARNING,s.NEUTRAL,s.NEUTRAL},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24883:(e,a,t)=>{Promise.resolve().then(t.bind(t,43795))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29844:(e,a,t)=>{"use strict";t.d(a,{tU:()=>T,av:()=>_,j7:()=>R,Xi:()=>k});var s=t(60687),r=t(43210),i=t(70569),n=t(11273),l=t(72942),d=t(46059),o=t(14163),c=t(43),m=t(65551),u=t(96963),x="Tabs",[p,h]=(0,n.A)(x,[l.RG]),g=(0,l.RG)(),[b,v]=p(x),j=r.forwardRef((e,a)=>{let{__scopeTabs:t,value:r,onValueChange:i,defaultValue:n,orientation:l="horizontal",dir:d,activationMode:p="automatic",...h}=e,g=(0,c.jH)(d),[v,j]=(0,m.i)({prop:r,onChange:i,defaultProp:n??"",caller:x});return(0,s.jsx)(b,{scope:t,baseId:(0,u.B)(),value:v,onValueChange:j,orientation:l,dir:g,activationMode:p,children:(0,s.jsx)(o.sG.div,{dir:g,"data-orientation":l,...h,ref:a})})});j.displayName=x;var f="TabsList",N=r.forwardRef((e,a)=>{let{__scopeTabs:t,loop:r=!0,...i}=e,n=v(f,t),d=g(t);return(0,s.jsx)(l.bL,{asChild:!0,...d,orientation:n.orientation,dir:n.dir,loop:r,children:(0,s.jsx)(o.sG.div,{role:"tablist","aria-orientation":n.orientation,...i,ref:a})})});N.displayName=f;var w="TabsTrigger",y=r.forwardRef((e,a)=>{let{__scopeTabs:t,value:r,disabled:n=!1,...d}=e,c=v(w,t),m=g(t),u=S(c.baseId,r),x=E(c.baseId,r),p=r===c.value;return(0,s.jsx)(l.q7,{asChild:!0,...m,focusable:!n,active:p,children:(0,s.jsx)(o.sG.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":x,"data-state":p?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:u,...d,ref:a,onMouseDown:(0,i.m)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(r)}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(r)}),onFocus:(0,i.m)(e.onFocus,()=>{let e="manual"!==c.activationMode;p||n||!e||c.onValueChange(r)})})})});y.displayName=w;var A="TabsContent",z=r.forwardRef((e,a)=>{let{__scopeTabs:t,value:i,forceMount:n,children:l,...c}=e,m=v(A,t),u=S(m.baseId,i),x=E(m.baseId,i),p=i===m.value,h=r.useRef(p);return r.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,s.jsx)(d.C,{present:n||p,children:({present:t})=>(0,s.jsx)(o.sG.div,{"data-state":p?"active":"inactive","data-orientation":m.orientation,role:"tabpanel","aria-labelledby":u,hidden:!t,id:x,tabIndex:0,...c,ref:a,style:{...e.style,animationDuration:h.current?"0s":void 0},children:t&&l})})});function S(e,a){return`${e}-trigger-${a}`}function E(e,a){return`${e}-content-${a}`}z.displayName=A;var C=t(4780);let T=j,R=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(N,{ref:t,className:(0,C.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...a}));R.displayName=N.displayName;let k=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(y,{ref:t,className:(0,C.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...a}));k.displayName=y.displayName;let _=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(z,{ref:t,className:(0,C.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...a}));_.displayName=z.displayName},33873:e=>{"use strict";e.exports=require("path")},41550:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},43795:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>el});var s=t(60687),r=t(43210),i=t.n(r);t(9245);var n=t(44493),l=t(4780),d=t(41862);let o=i().forwardRef(({className:e,variant:a="primary",size:t="md",loading:r=!1,glow:i=!1,icon:n,children:o,disabled:c,...m},u)=>{let x=c||r;return(0,s.jsxs)("button",{className:(0,l.cn)("relative overflow-hidden font-medium rounded-lg transition-all duration-300 ease-in-out transform focus:outline-none",{primary:"btn-primary",secondary:"btn-secondary",success:"btn-success",danger:"btn-danger",outline:"btn-outline",quick:"btn-quick"}[a],{sm:"btn-sm",md:"px-6 py-3",lg:"btn-lg"}[t],i&&"quick"!==a&&"btn-glow",x&&"opacity-50 cursor-not-allowed hover:shadow-none",e),disabled:x,ref:u,...m,children:[(0,s.jsx)("span",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent translate-x-[-100%] transition-transform duration-700 ease-in-out group-hover:translate-x-[100%]"}),(0,s.jsxs)("span",{className:"relative flex items-center justify-center gap-2",children:[r?(0,s.jsx)(d.A,{className:"h-4 w-4 animate-spin btn-icon"}):n?(0,s.jsx)("span",{className:"btn-icon",children:n}):null,o]})]})});o.displayName="AnimatedButton";let c=e=>(0,s.jsx)(o,{variant:"primary",...e}),m=e=>(0,s.jsx)(o,{variant:"secondary",...e}),u=e=>(0,s.jsx)(o,{variant:"danger",...e});var x=t(43649),p=t(11860),h=t(63143),g=t(48730),b=t(5336),v=t(88233),j=t(29523);function f({user:e,onEdit:a,onToggleStatus:t,onDelete:i}){let[n,l]=(0,r.useState)(!1);return n?(0,s.jsxs)("div",{className:"flex items-center gap-1 bg-red-50 border border-red-200 rounded-md p-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(x.A,{className:"h-4 w-4 text-red-600"}),(0,s.jsx)("span",{className:"text-xs text-red-700 font-medium",children:"Eliminare?"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1 ml-2",children:[(0,s.jsx)(j.$,{size:"sm",variant:"destructive",onClick:e=>{e.preventDefault(),e.stopPropagation(),l(!1),i()},className:"h-6 px-2 text-xs",children:"S\xec"}),(0,s.jsx)(j.$,{size:"sm",variant:"outline",onClick:e=>{e.preventDefault(),e.stopPropagation(),l(!1)},className:"h-6 px-2 text-xs",children:(0,s.jsx)(p.A,{className:"h-3 w-3"})})]})]}):(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),a()},type:"button",className:"p-1.5 rounded-md hover:bg-blue-50 transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1","aria-label":`Modifica utente ${e.username}`,children:(0,s.jsx)(h.A,{className:"h-4 w-4 text-blue-600 hover:text-blue-700"})}),(0,s.jsx)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-slate-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10",children:"Modifica utente"})]}),(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsx)("button",{onClick:a=>{a.preventDefault(),a.stopPropagation(),(!e.abilitato||window.confirm(`Sei sicuro di voler disabilitare l'utente "${e.username}"?

L'utente non potr\xe0 pi\xf9 accedere al sistema.`))&&t()},disabled:"owner"===e.ruolo,type:"button",className:`p-1.5 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 ${"owner"===e.ruolo?"opacity-50 cursor-not-allowed":"hover:scale-105 hover:bg-slate-50 focus:ring-slate-500"}`,"aria-label":e.abilitato?`Disabilita utente ${e.username}`:`Abilita utente ${e.username}`,children:e.abilitato?(0,s.jsx)(g.A,{className:"h-4 w-4 text-red-500 hover:text-red-600"}):(0,s.jsx)(b.A,{className:"h-4 w-4 text-green-500 hover:text-green-600"})}),"owner"!==e.ruolo&&(0,s.jsx)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-slate-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10",children:e.abilitato?"Disabilita utente":"Abilita utente"})]}),(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),l(!0)},disabled:"owner"===e.ruolo,type:"button",className:`p-1.5 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 ${"owner"===e.ruolo?"opacity-50 cursor-not-allowed":"hover:scale-105 hover:bg-red-50 focus:ring-red-500"}`,"aria-label":`Elimina utente ${e.username}`,children:(0,s.jsx)(v.A,{className:"h-4 w-4 text-red-500 hover:text-red-600"})}),"owner"!==e.ruolo&&(0,s.jsx)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-slate-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10",children:"Elimina utente"})]})]})}var N=t(96834),w=t(89667),y=t(15391),A=t(6211),z=t(29844),S=t(63213),E=t(16189),C=t(62185),T=t(80013),R=t(15079),k=t(56896),_=t(81806),U=t(93613),I=t(58869),D=t(99891),L=t(12597),P=t(13861),M=t(17313),O=t(41550),V=t(62688);let $=(0,V.A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);var G=t(84027),q=t(96882),F=t(8819);function Z({user:e,onSave:a,onCancel:t}){let[i,l]=(0,r.useState)({username:"",password:"",ruolo:"user",data_scadenza:"",abilitato:!0,ragione_sociale:"",indirizzo:"",nazione:"",email:"",vat:"",referente_aziendale:""}),[d,o]=(0,r.useState)({}),[u,x]=(0,r.useState)(!1),[v,f]=(0,r.useState)(""),[y,A]=(0,r.useState)(""),[z,S]=(0,r.useState)(!1),[E,V]=(0,r.useState)(0),[Z,B]=(0,r.useState)({}),[W,X]=(0,r.useState)(!1),J=e=>{let a=0;return e.length>=8&&(a+=25),/[a-z]/.test(e)&&(a+=25),/[A-Z]/.test(e)&&(a+=25),/[0-9]/.test(e)&&(a+=25),/[^A-Za-z0-9]/.test(e)&&(a+=25),Math.min(a,100)},H=(a,t)=>{let s="pending";switch(a){case"username":s=t&&t.length>=3?"valid":"invalid";break;case"password":s=e?!t||t.length>=8?"valid":"invalid":t&&t.length>=8?"valid":"invalid";break;case"ragione_sociale":s=t&&t.length>=2?"valid":"invalid";break;case"email":s=t?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t)?"valid":"invalid":"valid";break;default:s="valid"}return B(e=>({...e,[a]:s})),s},Q=(e,a)=>{l(t=>({...t,[e]:a})),H(e,a),"password"===e&&V(J(a)),d[e]&&o(a=>({...a,[e]:""})),y&&A(""),v&&f("")},K=()=>{let a=(0,_.GN)({username:i.username,password:e?void 0:i.password,ragione_sociale:i.ragione_sociale,email:i.email,vat:i.vat,indirizzo:i.indirizzo,nazione:i.nazione,referente_aziendale:i.referente_aziendale});return o(a.errors),a.isValid},Y=async t=>{t.preventDefault();let s=`user-form-${e?.id_utente||"new"}-${Date.now()}`;if(!(0,_.Eb)(s,5,6e4))return void f("Troppi tentativi. Riprova tra un minuto.");if(K()){x(!0),f("");try{let t,s={...i};e||(s.ruolo="user"),e&&!s.password.trim()&&delete s.password,s.data_scadenza&&(s.data_scadenza=s.data_scadenza),t=e?await C.dG.updateUser(e.id_utente,s):await C.dG.createUser(s),A(e?"Utente aggiornato con successo!":"Nuovo utente creato con successo!"),setTimeout(()=>{a(t)},1500)}catch(e){f(e.response?.data?.detail||e.message||"Errore durante il salvataggio dell'utente")}finally{x(!1)}}},ee=({status:e})=>"valid"===e?(0,s.jsx)(b.A,{className:"h-4 w-4 text-green-500"}):"invalid"===e?(0,s.jsx)(U.A,{className:"h-4 w-4 text-red-500"}):null;return(0,s.jsxs)(n.Zp,{className:"shadow-lg",children:[(0,s.jsx)(n.aR,{className:"bg-gradient-to-r from-blue-50 to-indigo-50 border-b",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:e?(0,s.jsx)(h.A,{className:"h-5 w-5 text-blue-600"}):(0,s.jsx)(I.A,{className:"h-5 w-5 text-blue-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)(n.ZB,{className:"text-xl",children:e?`Modifica Utente: ${e.username}`:"Crea Nuovo Utente Standard"}),(0,s.jsx)(n.BT,{children:e?"Aggiorna le informazioni dell'utente esistente":"Inserisci i dati per creare un nuovo utente nel sistema"})]})]})}),(0,s.jsxs)(n.Wu,{className:"p-6",children:[v&&(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6 flex items-center gap-3 animate-in slide-in-from-top-2",children:[(0,s.jsx)(U.A,{className:"h-5 w-5 text-red-600 flex-shrink-0"}),(0,s.jsx)("p",{className:"text-red-600",children:v})]}),y&&(0,s.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-6 flex items-center gap-3 animate-in slide-in-from-top-2",children:[(0,s.jsx)(b.A,{className:"h-5 w-5 text-green-600 flex-shrink-0"}),(0,s.jsx)("p",{className:"text-green-600",children:y})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm text-slate-600 mb-2",children:[(0,s.jsx)("span",{children:"Completamento form"}),(0,s.jsx)("span",{children:W?"✓ Completo":"In corso..."})]}),(0,s.jsx)("div",{className:"w-full bg-slate-200 rounded-full h-2",children:(0,s.jsx)("div",{className:`h-2 rounded-full transition-all duration-500 ${W?"bg-green-500":"bg-blue-500"}`,style:{width:`${W?100:60}%`}})})]}),(0,s.jsxs)("form",{onSubmit:Y,className:"space-y-8",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 pb-2 border-b border-slate-200",children:[(0,s.jsx)(D.A,{className:"h-5 w-5 text-blue-600"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-slate-900",children:"Credenziali di Accesso"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(T.J,{htmlFor:"username",className:"flex items-center gap-2",children:["Username *",(0,s.jsx)(ee,{status:Z.username||"pending"})]}),(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)(w.p,{id:"username",value:i.username,onChange:e=>Q("username",e.target.value),disabled:u,className:`${d.username?"border-red-500":"valid"===Z.username?"border-green-500":""} transition-colors duration-200`,placeholder:"Inserisci username univoco"})}),d.username&&(0,s.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,s.jsx)(U.A,{className:"h-3 w-3"}),d.username]}),"valid"===Z.username&&!d.username&&(0,s.jsxs)("p",{className:"text-sm text-green-600 flex items-center gap-1",children:[(0,s.jsx)(b.A,{className:"h-3 w-3"}),"Username valido"]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(T.J,{htmlFor:"password",className:"flex items-center gap-2",children:[e?"Nuova Password (lascia vuoto per non modificare)":"Password *",(0,s.jsx)(ee,{status:Z.password||"pending"})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(w.p,{id:"password",type:z?"text":"password",value:i.password,onChange:e=>Q("password",e.target.value),disabled:u,className:`${d.password?"border-red-500":"valid"===Z.password?"border-green-500":""} pr-10 transition-colors duration-200`,placeholder:e?"Lascia vuoto per mantenere la password attuale":"Inserisci password sicura"}),(0,s.jsx)(j.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>S(!z),disabled:u,children:z?(0,s.jsx)(L.A,{className:"h-4 w-4 text-gray-400"}):(0,s.jsx)(P.A,{className:"h-4 w-4 text-gray-400"})})]}),d.password&&(0,s.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,s.jsx)(U.A,{className:"h-3 w-3"}),d.password]}),(0,s.jsx)(()=>i.password?(0,s.jsxs)("div",{className:"mt-2 space-y-1",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,s.jsx)("span",{className:"text-slate-600",children:"Forza password:"}),(0,s.jsx)("span",{className:`font-medium ${E<50?"text-red-600":E<75?"text-yellow-600":"text-green-600"}`,children:E<25?"Molto debole":E<50?"Debole":E<75?"Media":E<100?"Forte":"Molto forte"})]}),(0,s.jsx)("div",{className:"w-full bg-slate-200 rounded-full h-2",children:(0,s.jsx)("div",{className:`h-2 rounded-full transition-all duration-300 ${E<50?"bg-red-500":E<75?"bg-yellow-500":"bg-green-500"}`,style:{width:`${E}%`}})})]}):null,{})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 pb-2 border-b border-slate-200",children:[(0,s.jsx)(M.A,{className:"h-5 w-5 text-blue-600"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-slate-900",children:"Informazioni Aziendali"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(T.J,{htmlFor:"ragione_sociale",className:"flex items-center gap-2",children:["Ragione Sociale *",(0,s.jsx)(ee,{status:Z.ragione_sociale||"pending"})]}),(0,s.jsx)(w.p,{id:"ragione_sociale",value:i.ragione_sociale,onChange:e=>Q("ragione_sociale",e.target.value),disabled:u,className:`${d.ragione_sociale?"border-red-500":"valid"===Z.ragione_sociale?"border-green-500":""} transition-colors duration-200`,placeholder:"Nome dell'azienda o organizzazione"}),d.ragione_sociale&&(0,s.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,s.jsx)(U.A,{className:"h-3 w-3"}),d.ragione_sociale]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(T.J,{htmlFor:"email",className:"flex items-center gap-2",children:[(0,s.jsx)(O.A,{className:"h-4 w-4"}),"Email",(0,s.jsx)(ee,{status:Z.email||"pending"})]}),(0,s.jsx)(w.p,{id:"email",type:"email",value:i.email,onChange:e=>Q("email",e.target.value),disabled:u,className:`${d.email?"border-red-500":"valid"===Z.email?"border-green-500":""} transition-colors duration-200`,placeholder:"<EMAIL>"}),d.email&&(0,s.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,s.jsx)(U.A,{className:"h-3 w-3"}),d.email]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(T.J,{htmlFor:"indirizzo",className:"flex items-center gap-2",children:[(0,s.jsx)($,{className:"h-4 w-4"}),"Indirizzo"]}),(0,s.jsx)(w.p,{id:"indirizzo",value:i.indirizzo,onChange:e=>Q("indirizzo",e.target.value),disabled:u,placeholder:"Via, numero civico, citt\xe0",className:"transition-colors duration-200"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"nazione",children:"Nazione"}),(0,s.jsx)(w.p,{id:"nazione",value:i.nazione,onChange:e=>Q("nazione",e.target.value),disabled:u,placeholder:"Italia",className:"transition-colors duration-200"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"vat",children:"Partita IVA"}),(0,s.jsx)(w.p,{id:"vat",value:i.vat,onChange:e=>Q("vat",e.target.value),disabled:u,placeholder:"*************",className:"transition-colors duration-200"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"referente_aziendale",children:"Referente Aziendale"}),(0,s.jsx)(w.p,{id:"referente_aziendale",value:i.referente_aziendale,onChange:e=>Q("referente_aziendale",e.target.value),disabled:u,placeholder:"Nome e cognome del referente",className:"transition-colors duration-200"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 pb-2 border-b border-slate-200",children:[(0,s.jsx)(G.A,{className:"h-5 w-5 text-blue-600"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-slate-900",children:"Configurazioni Account"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(T.J,{htmlFor:"data_scadenza",className:"flex items-center gap-2",children:[(0,s.jsx)(g.A,{className:"h-4 w-4"}),"Data Scadenza"]}),(0,s.jsx)(w.p,{id:"data_scadenza",type:"date",value:i.data_scadenza,onChange:e=>Q("data_scadenza",e.target.value),disabled:u,className:"transition-colors duration-200"}),(0,s.jsx)("p",{className:"text-xs text-slate-500",children:"Lascia vuoto per account senza scadenza"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"ruolo",children:"Ruolo Utente"}),e?(0,s.jsxs)(R.l6,{value:i.ruolo,onValueChange:e=>Q("ruolo",e),disabled:u,children:[(0,s.jsx)(R.bq,{className:"transition-colors duration-200",children:(0,s.jsx)(R.yv,{})}),(0,s.jsxs)(R.gC,{children:[(0,s.jsx)(R.eb,{value:"user",children:"User Standard"}),(0,s.jsx)(R.eb,{value:"cantieri_user",children:"Cantieri User"})]})]}):(0,s.jsxs)("div",{className:"px-3 py-2 bg-blue-50 border border-blue-200 rounded-md text-sm text-blue-700 flex items-center gap-2",children:[(0,s.jsx)(N.E,{variant:"outline",className:"bg-blue-100 text-blue-700",children:"User Standard"}),(0,s.jsx)("span",{children:"Ruolo predefinito per nuovi utenti"})]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3 p-4 bg-slate-50 rounded-lg",children:[(0,s.jsx)(k.S,{id:"abilitato",checked:i.abilitato,onCheckedChange:e=>Q("abilitato",e),disabled:u||e&&"owner"===e.ruolo}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)(T.J,{htmlFor:"abilitato",className:"font-medium",children:"Account Abilitato"}),(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"L'utente pu\xf2 accedere al sistema e utilizzare le funzionalit\xe0"})]}),i.abilitato?(0,s.jsx)(N.E,{className:"bg-green-100 text-green-700",children:"Attivo"}):(0,s.jsx)(N.E,{variant:"outline",className:"bg-red-100 text-red-700",children:"Disabilitato"})]})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-center gap-4 pt-8 border-t border-slate-200",children:[(0,s.jsx)("div",{className:"text-sm text-slate-600",children:W?(0,s.jsxs)("span",{className:"flex items-center gap-1 text-green-600",children:[(0,s.jsx)(b.A,{className:"h-4 w-4"}),"Form completato correttamente"]}):(0,s.jsxs)("span",{className:"flex items-center gap-1",children:[(0,s.jsx)(q.A,{className:"h-4 w-4"}),"Completa i campi obbligatori per continuare"]})}),(0,s.jsxs)("div",{className:"flex space-x-4",children:[(0,s.jsx)(m,{type:"button",onClick:t,disabled:u,icon:(0,s.jsx)(p.A,{className:"h-4 w-4"}),className:"min-w-[120px]",children:"Annulla"}),(0,s.jsx)(c,{type:"submit",loading:u,disabled:!W,icon:(0,s.jsx)(F.A,{className:"h-4 w-4"}),glow:W,className:"min-w-[120px]",children:u?"Salvataggio...":e?"Aggiorna Utente":"Crea Utente"})]})]})]})]})]})}var B=t(61611),W=t(78122);function X(){let[e,a]=(0,r.useState)(null),[t,i]=(0,r.useState)(!1),[l,o]=(0,r.useState)(""),m=async()=>{i(!0),o("");try{let e=await C.dG.getDatabaseData();a(e)}catch(e){o(e.response?.data?.detail||e.message||"Errore durante il caricamento dei dati del database")}finally{i(!1)}},u=(e,a,t)=>{if(!a||0===a.length)return(0,s.jsxs)("div",{className:"text-center py-4 text-slate-500 border rounded-lg",children:["Nessun dato disponibile per ",t]});let r=Object.keys(a[0]);return(0,s.jsxs)("div",{className:"border rounded-lg overflow-hidden mb-6",children:[(0,s.jsxs)("div",{className:"bg-slate-100 px-4 py-3 border-b",children:[(0,s.jsx)("h4",{className:"font-medium text-slate-900",children:t}),(0,s.jsxs)("p",{className:"text-sm text-slate-600",children:["Totale record: ",a.length]})]}),(0,s.jsx)("div",{className:"overflow-x-auto max-h-96",children:(0,s.jsxs)(A.XI,{children:[(0,s.jsx)(A.A0,{className:"sticky top-0 bg-slate-50",children:(0,s.jsx)(A.Hj,{children:r.map(e=>(0,s.jsx)(A.nd,{className:"font-medium",children:e},e))})}),(0,s.jsx)(A.BF,{children:a.map((e,a)=>(0,s.jsx)(A.Hj,{children:r.map(a=>(0,s.jsx)(A.nA,{className:"font-mono text-sm",children:null!==e[a]&&void 0!==e[a]?String(e[a]):(0,s.jsx)("span",{className:"text-slate-400",children:"NULL"})},a))},a))})]})})]})},x=[{key:"users",title:"Utenti",description:"Tutti gli utenti del sistema"},{key:"cantieri",title:"Cantieri",description:"Tutti i cantieri/progetti"},{key:"cavi",title:"Cavi",description:"Tutti i cavi installati"},{key:"parco_cavi",title:"Bobine",description:"Tutte le bobine del parco cavi"},{key:"strumenti_certificati",title:"Strumenti",description:"Strumenti certificati"},{key:"certificazioni_cavi",title:"Certificazioni",description:"Certificazioni dei cavi"}];return(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(B.A,{className:"h-5 w-5"}),"Visualizzazione Database Raw"]}),(0,s.jsx)(c,{size:"sm",onClick:m,loading:t,icon:(0,s.jsx)(W.A,{className:"h-4 w-4"}),children:"Aggiorna"})]})}),(0,s.jsxs)(n.Wu,{className:"space-y-6",children:[(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(P.A,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-blue-900",children:"Visualizzazione Raw del Database"}),(0,s.jsx)("p",{className:"text-sm text-blue-700 mt-1",children:"Questa sezione mostra i dati grezzi delle tabelle del database. Utile per debugging e analisi dei dati."})]})]})}),t?(0,s.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,s.jsx)(d.A,{className:"h-8 w-8 animate-spin mr-3"}),(0,s.jsx)("span",{className:"text-lg",children:"Caricamento dati database..."})]}):l?(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6",children:[(0,s.jsx)("p",{className:"text-red-600 font-medium",children:"Errore durante il caricamento:"}),(0,s.jsx)("p",{className:"text-red-600",children:l})]}):e?(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-sm text-blue-700",children:"Visualizzazione completa di tutte le tabelle del database. I dati sono mostrati in formato raw per debugging e analisi."})}),x.map(a=>e[a.key]&&(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-slate-900",children:a.title}),(0,s.jsx)("p",{className:"text-sm text-slate-600",children:a.description})]}),u(a.key,e[a.key],a.title)]},a.key)),(0,s.jsxs)("div",{className:"bg-slate-50 border border-slate-200 rounded-lg p-4",children:[(0,s.jsx)("h4",{className:"font-medium text-slate-900 mb-2",children:"Riepilogo Database"}),(0,s.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4 text-sm",children:x.map(a=>(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsxs)("span",{className:"text-slate-600",children:[a.title,":"]}),(0,s.jsxs)("span",{className:"font-medium",children:[e[a.key]?e[a.key].length:0," record"]})]},a.key))})]})]}):(0,s.jsx)("div",{className:"text-center py-12 text-slate-500",children:"Nessun dato disponibile"})]})]})}let J=(0,V.A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);function H(){let{user:e}=(0,S.A)(),[a,t]=(0,r.useState)(""),[i,l]=(0,r.useState)(!1),[d,o]=(0,r.useState)(""),[c,p]=(0,r.useState)(!1),[h,b]=(0,r.useState)(!1),[f,N]=(0,r.useState)(""),[y,A]=(0,r.useState)(""),[z,E]=(0,r.useState)(10),[R,_]=(0,r.useState)(!1),[U,I]=(0,r.useState)(!1),M=async()=>{if(!U)return void N("Devi completare il countdown di sicurezza");b(!0),N(""),A("");try{if(!d.trim())throw Error("Password amministratore richiesta");await C.dG.resetDatabase(),A("Database resettato con successo! Tutti i dati sono stati eliminati."),t(""),l(!1),o(""),I(!1),_(!1),E(10)}catch(e){N(e.response?.data?.detail||e.message||"Errore durante il reset del database")}finally{b(!1)}},O="RESET DATABASE"===a&&i&&d.trim()&&!h&&!R,V=U&&!h;return(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsxs)(n.ZB,{className:"flex items-center gap-2 text-red-600",children:[(0,s.jsx)(J,{className:"h-5 w-5"}),"Reset Database"]})}),(0,s.jsxs)(n.Wu,{className:"space-y-6",children:[(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(x.A,{className:"h-6 w-6 text-red-600 mt-0.5"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-bold text-red-900 text-lg",children:"⚠️ ATTENZIONE - OPERAZIONE IRREVERSIBILE"}),(0,s.jsxs)("div",{className:"text-red-700 mt-2 space-y-2",children:[(0,s.jsx)("p",{className:"font-medium",children:"Questa operazione eliminer\xe0 PERMANENTEMENTE tutti i dati dal database:"}),(0,s.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-sm",children:[(0,s.jsx)("li",{children:"Tutti gli utenti (eccetto l'amministratore principale)"}),(0,s.jsx)("li",{children:"Tutti i cantieri e i progetti"}),(0,s.jsx)("li",{children:"Tutti i cavi installati"}),(0,s.jsx)("li",{children:"Tutte le bobine del parco cavi"}),(0,s.jsx)("li",{children:"Tutti i comandi e le certificazioni"}),(0,s.jsx)("li",{children:"Tutti i report e i dati di produttivit\xe0"})]}),(0,s.jsx)("p",{className:"font-bold text-red-800 mt-3",children:"NON \xc8 POSSIBILE RECUPERARE I DATI DOPO IL RESET!"})]})]})]})}),f&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-red-600",children:f})}),y&&(0,s.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-green-600",children:y})}),(0,s.jsxs)("div",{className:"space-y-4 border-t pt-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-slate-900 mb-4",children:"Conferma Reset Database"}),(0,s.jsx)("p",{className:"text-sm text-slate-600 mb-4",children:"Per procedere con il reset, devi confermare l'operazione seguendo questi passaggi:"})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(T.J,{htmlFor:"confirm-text",className:"text-sm font-medium",children:["1. Digita esattamente: ",(0,s.jsx)("code",{className:"bg-slate-100 px-2 py-1 rounded text-red-600 font-bold",children:"RESET DATABASE"})]}),(0,s.jsx)(w.p,{id:"confirm-text",value:a,onChange:e=>t(e.target.value),placeholder:"Digita: RESET DATABASE",disabled:h||R,className:"RESET DATABASE"===a?"border-green-500":""})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)(k.S,{id:"confirm-checkbox",checked:i,onCheckedChange:l,disabled:h||R}),(0,s.jsx)(T.J,{htmlFor:"confirm-checkbox",className:"text-sm leading-relaxed",children:"2. Confermo di aver compreso che questa operazione eliminer\xe0 TUTTI i dati dal database in modo PERMANENTE e IRREVERSIBILE. Ho effettuato un backup se necessario."})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(T.J,{htmlFor:"admin-password",className:"text-sm font-medium flex items-center gap-2",children:[(0,s.jsx)(D.A,{className:"h-4 w-4 text-blue-600"}),"3. Inserisci la tua password di amministratore per confermare l'identit\xe0"]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(w.p,{id:"admin-password",type:c?"text":"password",value:d,onChange:e=>o(e.target.value),placeholder:"Password amministratore",disabled:h||R,className:d.trim()?"border-green-500":""}),(0,s.jsx)(j.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0",onClick:()=>p(!c),disabled:h||R,children:c?(0,s.jsx)(L.A,{className:"h-4 w-4"}):(0,s.jsx)(P.A,{className:"h-4 w-4"})})]})]})]}),(0,s.jsxs)("div",{className:"bg-slate-50 border border-slate-200 rounded-lg p-4",children:[(0,s.jsx)("h5",{className:"font-medium text-slate-900 mb-3",children:"Stato Conferma:"}),(0,s.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:`w-3 h-3 rounded-full ${"RESET DATABASE"===a?"bg-green-500":"bg-red-500"}`}),(0,s.jsxs)("span",{children:["Testo di conferma: ","RESET DATABASE"===a?"✓ Corretto":"✗ Richiesto"]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:`w-3 h-3 rounded-full ${i?"bg-green-500":"bg-red-500"}`}),(0,s.jsxs)("span",{children:["Checkbox confermata: ",i?"✓ S\xec":"✗ Richiesta"]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:`w-3 h-3 rounded-full ${d.trim()?"bg-green-500":"bg-red-500"}`}),(0,s.jsxs)("span",{children:["Password amministratore: ",d.trim()?"✓ Inserita":"✗ Richiesta"]})]}),R&&(0,s.jsxs)("div",{className:"flex items-center gap-2 mt-3 p-2 bg-orange-50 border border-orange-200 rounded",children:[(0,s.jsx)(g.A,{className:"h-4 w-4 text-orange-600"}),(0,s.jsxs)("span",{className:"text-orange-700 font-medium",children:["Countdown di sicurezza: ",z," secondi"]})]}),U&&(0,s.jsxs)("div",{className:"flex items-center gap-2 mt-3 p-2 bg-red-50 border border-red-200 rounded",children:[(0,s.jsx)(x.A,{className:"h-4 w-4 text-red-600"}),(0,s.jsx)("span",{className:"text-red-700 font-medium",children:"⚠️ Pronto per il reset - Ultima possibilit\xe0 di annullare"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[!U&&!R&&(0,s.jsx)(u,{onClick:()=>{"RESET DATABASE"===a&&i&&d.trim()?(_(!0),E(10),I(!1),N("")):N("Completa tutti i passaggi di conferma prima di procedere")},disabled:!O,className:"w-full",size:"lg",icon:(0,s.jsx)(g.A,{className:"h-5 w-5"}),children:"INIZIA COUNTDOWN DI SICUREZZA (10 secondi)"}),R&&(0,s.jsxs)("div",{className:"w-full p-4 bg-orange-50 border-2 border-orange-300 rounded-lg text-center",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[(0,s.jsx)(g.A,{className:"h-6 w-6 text-orange-600 animate-pulse"}),(0,s.jsxs)("span",{className:"text-lg font-bold text-orange-700",children:["Countdown: ",z," secondi"]})]}),(0,s.jsx)("p",{className:"text-sm text-orange-600 mt-2",children:"Il pulsante di reset si attiver\xe0 al termine del countdown"})]}),U&&(0,s.jsx)(u,{onClick:M,disabled:!V,className:"w-full animate-pulse",size:"lg",loading:h,icon:(0,s.jsx)(v.A,{className:"h-5 w-5"}),glow:!0,children:h?"RESET IN CORSO...":"\uD83D\uDEA8 RESET DATABASE - ELIMINA TUTTI I DATI \uD83D\uDEA8"}),!O&&!U&&!R&&(0,s.jsx)("p",{className:"text-center text-sm text-slate-500",children:"Completa tutti i passaggi di conferma per iniziare il countdown"}),U&&(0,s.jsx)(m,{onClick:()=>{I(!1),_(!1),E(10)},className:"w-full",size:"lg",disabled:h,children:"ANNULLA RESET"})]})]}),(0,s.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 text-sm",children:[(0,s.jsx)("h5",{className:"font-medium text-blue-900 mb-2",children:"Informazioni Tecniche:"}),(0,s.jsxs)("ul",{className:"text-blue-700 space-y-1",children:[(0,s.jsx)("li",{children:"• Il reset manterr\xe0 la struttura delle tabelle"}),(0,s.jsx)("li",{children:"• L'utente amministratore principale verr\xe0 ricreato"}),(0,s.jsx)("li",{children:"• Le configurazioni di sistema verranno ripristinate ai valori di default"}),(0,s.jsx)("li",{children:"• L'operazione pu\xf2 richiedere alcuni minuti per completarsi"})]})]})]})]})}var Q=t(23361);let K=(0,V.A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]]),Y=(0,V.A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]);var ee=t(10022),ea=t(96474);function et(){let[e,a]=(0,r.useState)("categorie");return(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(Q.A,{className:"h-5 w-5"}),"Database Tipologie Cavi"]})}),(0,s.jsxs)(n.Wu,{className:"space-y-6",children:[(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(Q.A,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-blue-900",children:"Database Enciclopedico Tipologie Cavi"}),(0,s.jsx)("p",{className:"text-sm text-blue-700 mt-1",children:"Gestisci il database delle tipologie di cavi organizzato per categorie, produttori, standard e tipologie specifiche. Questo database serve come riferimento per la classificazione e gestione dei cavi nei progetti."})]})]})}),(0,s.jsxs)(z.tU,{value:e,onValueChange:a,className:"w-full",children:[(0,s.jsxs)(z.j7,{className:"grid w-full grid-cols-4",children:[(0,s.jsxs)(z.Xi,{value:"categorie",className:"flex items-center gap-2",children:[(0,s.jsx)(K,{className:"h-4 w-4"}),"Categorie"]}),(0,s.jsxs)(z.Xi,{value:"produttori",className:"flex items-center gap-2",children:[(0,s.jsx)(Y,{className:"h-4 w-4"}),"Produttori"]}),(0,s.jsxs)(z.Xi,{value:"standard",className:"flex items-center gap-2",children:[(0,s.jsx)(ee.A,{className:"h-4 w-4"}),"Standard"]}),(0,s.jsxs)(z.Xi,{value:"tipologie",className:"flex items-center gap-2",children:[(0,s.jsx)(Q.A,{className:"h-4 w-4"}),"Tipologie"]})]}),(0,s.jsxs)(z.av,{value:"categorie",className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Categorie Cavi"}),(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci le categorie principali di cavi (es. Energia, Controllo, Strumentazione, ecc.)"})]}),(0,s.jsxs)(j.$,{children:[(0,s.jsx)(ea.A,{className:"h-4 w-4 mr-2"}),"Nuova Categoria"]})]}),(0,s.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,s.jsx)(K,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,s.jsx)("p",{children:"Gestione categorie cavi - Da implementare"}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile creare, modificare ed eliminare le categorie di cavi"})]})]}),(0,s.jsxs)(z.av,{value:"produttori",className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Produttori"}),(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci l'elenco dei produttori di cavi (es. Prysmian, Nexans, General Cable, ecc.)"})]}),(0,s.jsxs)(j.$,{children:[(0,s.jsx)(ea.A,{className:"h-4 w-4 mr-2"}),"Nuovo Produttore"]})]}),(0,s.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,s.jsx)(Y,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,s.jsx)("p",{children:"Gestione produttori - Da implementare"}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile gestire l'anagrafica dei produttori di cavi"})]})]}),(0,s.jsxs)(z.av,{value:"standard",className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Standard e Normative"}),(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci gli standard tecnici e le normative (es. CEI, IEC, EN, CENELEC, ecc.)"})]}),(0,s.jsxs)(j.$,{children:[(0,s.jsx)(ea.A,{className:"h-4 w-4 mr-2"}),"Nuovo Standard"]})]}),(0,s.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,s.jsx)(ee.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,s.jsx)("p",{children:"Gestione standard - Da implementare"}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile gestire gli standard tecnici e le normative di riferimento"})]})]}),(0,s.jsxs)(z.av,{value:"tipologie",className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Tipologie Specifiche"}),(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci le tipologie specifiche di cavi con tutte le caratteristiche tecniche"})]}),(0,s.jsxs)(j.$,{children:[(0,s.jsx)(ea.A,{className:"h-4 w-4 mr-2"}),"Nuova Tipologia"]})]}),(0,s.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,s.jsx)(Q.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,s.jsx)("p",{children:"Gestione tipologie - Da implementare"}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile gestire le tipologie specifiche con caratteristiche tecniche dettagliate"})]})]})]}),(0,s.jsxs)("div",{className:"bg-slate-50 border border-slate-200 rounded-lg p-4",children:[(0,s.jsx)("h5",{className:"font-medium text-slate-900 mb-2",children:"Struttura Database Tipologie:"}),(0,s.jsxs)("div",{className:"text-sm text-slate-600 space-y-1",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Categorie:"})," Classificazione principale (Energia, Controllo, Strumentazione, Dati, ecc.)"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Produttori:"})," Aziende produttrici con informazioni di contatto"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Standard:"})," Normative tecniche di riferimento (CEI, IEC, EN, CENELEC)"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Tipologie:"})," Specifiche tecniche dettagliate per ogni tipo di cavo"]})]})]})]})]})}var es=t(41312);let er=(0,V.A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);var ei=t(99270);let en=(0,V.A)("log-in",[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]]);function el(){let e=(0,E.useRouter)(),[a,t]=(0,r.useState)("visualizza-utenti"),[i,l]=(0,r.useState)(""),[o,m]=(0,r.useState)([]),[u,x]=(0,r.useState)([]),[p,g]=(0,r.useState)(!0),[b,v]=(0,r.useState)(""),[j,T]=(0,r.useState)(null),[R,k]=(0,r.useState)({open:!1,message:"",severity:"success"}),{user:_,impersonateUser:U}=(0,S.A)(),I=async()=>{try{if(g(!0),v(""),"visualizza-utenti"===a||"crea-utente"===a||"accedi-come-utente"===a){let e=await C.dG.getUsers();m(e)}else if("cantieri"===a){let e=await C._I.getCantieri();x(e)}}catch(e){v(e.response?.data?.detail||e.message||"Errore durante il caricamento dei dati")}finally{g(!1)}},D=e=>{T(e),t("modifica-utente")},L=async e=>{try{await C.dG.toggleUserStatus(e),I()}catch(e){v(e.response?.data?.detail||"Errore durante la modifica dello stato utente")}},P=async e=>{if(confirm("Sei sicuro di voler eliminare questo utente?"))try{await C.dG.deleteUser(e),I()}catch(e){v(e.response?.data?.detail||"Errore durante l'eliminazione dell'utente")}},M=e=>{T(null),t("visualizza-utenti"),I()},O=()=>{T(null),t("visualizza-utenti")},V=async a=>{try{await U(a.id_utente),"user"===a.ruolo?e.push("/cantieri"):"cantieri_user"===a.ruolo?e.push("/cavi"):e.push("/")}catch(e){v(e.response?.data?.detail||e.message||"Errore durante l'impersonificazione")}},$=e=>{let a="NEUTRAL";switch(e){case"owner":a="PROGRESS";break;case"user":a="INFO";break;case"cantieri_user":a="SUCCESS";break;default:a="NEUTRAL"}let t=(0,y.getSoftColorClasses)(a);return(0,s.jsx)(N.E,{className:t.badge,children:e})},G=(e,a)=>{let t="SUCCESS",r="Attivo",i="●";if(e)if(a){let e=new Date(a),s=new Date;e<s?(t="ERROR",r="Scaduto",i="⚠"):e.getTime()-s.getTime()<6048e5?(t="WARNING",r="In Scadenza",i="⏰"):i="✓"}else i="✓";else t="ERROR",r="Disabilitato",i="●";let n=(0,y.getSoftColorClasses)(t);return(0,s.jsxs)(N.E,{className:`${n.badge} flex items-center gap-1`,children:[(0,s.jsx)("span",{className:"text-xs",role:"img","aria-hidden":"true",children:i}),(0,s.jsx)("span",{children:r})]})},q=o.filter(e=>e.username?.toLowerCase().includes(i.toLowerCase())||e.ragione_sociale?.toLowerCase().includes(i.toLowerCase())||e.email?.toLowerCase().includes(i.toLowerCase()));return _&&"owner"===_.ruolo?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,s.jsx)("div",{className:"max-w-[90%] mx-auto space-y-6",children:(0,s.jsxs)(z.tU,{value:a,onValueChange:t,className:"w-full",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"bg-white rounded-lg border border-slate-200 shadow-sm p-1",children:(0,s.jsxs)(z.j7,{className:`grid w-full ${j?"grid-cols-5":"grid-cols-4"} gap-1 h-auto bg-transparent p-0`,children:[(0,s.jsxs)(z.Xi,{value:"visualizza-utenti",className:"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent",children:[(0,s.jsx)(es.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"font-medium",children:"Gestione Utenti"})]}),(0,s.jsxs)(z.Xi,{value:"crea-utente",className:"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent",children:[(0,s.jsx)(er,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"font-medium",children:"Crea Nuovo Utente"})]}),j&&(0,s.jsxs)(z.Xi,{value:"modifica-utente",className:"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent",children:[(0,s.jsx)(h.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"font-medium",children:"Modifica Utente"})]}),(0,s.jsxs)(z.Xi,{value:"database-tipologie-cavi",className:"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent",children:[(0,s.jsx)(Q.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"font-medium",children:"Database Tipologie Cavi"})]}),(0,s.jsxs)(z.Xi,{value:"visualizza-database-raw",className:"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent",children:[(0,s.jsx)(B.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"font-medium",children:"Gestione Dati Avanzata"})]})]})}),(0,s.jsx)("div",{className:"bg-gradient-to-r from-red-50 to-orange-50 rounded-lg border border-red-200 shadow-sm p-1",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 px-4 py-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full animate-pulse"}),(0,s.jsx)("span",{className:"text-sm font-medium text-red-800",children:"Impostazioni Avanzate e Pericolose"})]}),(0,s.jsx)(z.j7,{className:"h-auto bg-transparent p-0",children:(0,s.jsxs)(z.Xi,{value:"reset-database",className:"admin-tab-danger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-red-100 data-[state=active]:text-red-700 data-[state=active]:border-red-300 data-[state=active]:shadow-sm hover:bg-red-100 hover:text-red-700 border border-transparent text-red-600 font-medium",children:[(0,s.jsx)(J,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Reset Database"})]})})]})})]}),(0,s.jsxs)(z.av,{value:"visualizza-utenti",className:"space-y-4",children:[b&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-red-600",children:b})}),(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(es.A,{className:"h-5 w-5 text-blue-600"}),"Lista Utenti"]}),(0,s.jsx)(n.BT,{children:"Gestisci tutti gli utenti del sistema CABLYS"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(ei.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400"}),(0,s.jsx)(w.p,{placeholder:"Cerca per username, email o ragione sociale...",value:i,onChange:e=>l(e.target.value),className:"pl-10 w-80"})]}),(0,s.jsxs)(N.E,{variant:"outline",className:"text-xs",children:[q.length," utenti"]})]})]})}),(0,s.jsx)(n.Wu,{children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsx)("div",{className:"rounded-md border",children:(0,s.jsxs)(A.XI,{children:[(0,s.jsx)(A.A0,{children:(0,s.jsxs)(A.Hj,{children:[(0,s.jsx)(A.nd,{className:"w-[50px] text-center",children:"ID"}),(0,s.jsx)(A.nd,{className:"w-[100px]",children:"Username"}),(0,s.jsx)(A.nd,{className:"w-[80px] text-center",children:"Password"}),(0,s.jsx)(A.nd,{className:"w-[80px] text-center",children:"Ruolo"}),(0,s.jsx)(A.nd,{className:"w-[180px]",children:"Ragione Sociale"}),(0,s.jsx)(A.nd,{className:"w-[160px]",children:"Email"}),(0,s.jsx)(A.nd,{className:"w-[80px] text-center",children:"VAT"}),(0,s.jsx)(A.nd,{className:"w-[80px] text-center",children:"Nazione"}),(0,s.jsx)(A.nd,{className:"w-[120px]",children:"Referente"}),(0,s.jsx)(A.nd,{className:"w-[90px] text-center",children:"Scadenza"}),(0,s.jsx)(A.nd,{className:"w-[80px] text-center",children:"Stato"}),(0,s.jsx)(A.nd,{className:"w-[100px] text-center",children:"Azioni"})]})}),(0,s.jsx)(A.BF,{children:p?(0,s.jsx)(A.Hj,{children:(0,s.jsx)(A.nA,{colSpan:12,className:"text-center py-8",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)(d.A,{className:"h-4 w-4 animate-spin"}),"Caricamento..."]})})}):0===o.length?(0,s.jsx)(A.Hj,{children:(0,s.jsx)(A.nA,{colSpan:12,className:"text-center py-8 text-slate-500",children:"Nessun utente trovato"})}):q.map(e=>(0,s.jsxs)(A.Hj,{className:"users-table-row",children:[(0,s.jsx)(A.nA,{className:"text-center",children:(0,s.jsxs)(N.E,{variant:"outline",className:"text-xs font-mono",children:["#",e.id_utente]})}),(0,s.jsx)(A.nA,{className:"font-semibold text-slate-900",children:e.username}),(0,s.jsx)(A.nA,{className:"text-center",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-1",children:[(0,s.jsx)("div",{className:"flex gap-1",children:[...Array(8)].map((e,a)=>(0,s.jsx)("div",{className:"w-1.5 h-1.5 bg-slate-400 rounded-full"},a))}),(0,s.jsx)("div",{className:`w-2 h-2 rounded-full ml-2 ${e.password_plain?"bg-green-500":"bg-red-500"}`,title:e.password_plain?"Password configurata":"Password non configurata"})]})}),(0,s.jsx)(A.nA,{className:"text-center",children:$(e.ruolo)}),(0,s.jsx)(A.nA,{className:"max-w-[250px] truncate",title:e.ragione_sociale,children:(0,s.jsx)("span",{className:"text-slate-900",children:e.ragione_sociale||"-"})}),(0,s.jsx)(A.nA,{className:"max-w-[200px] truncate text-sm text-slate-600",title:e.email,children:e.email||"-"}),(0,s.jsx)(A.nA,{className:"text-center text-sm text-slate-600",children:e.vat||"-"}),(0,s.jsx)(A.nA,{className:"text-center text-sm text-slate-600",children:e.nazione||"-"}),(0,s.jsx)(A.nA,{className:"max-w-[150px] truncate text-sm text-slate-600",title:e.referente_aziendale,children:e.referente_aziendale||"-"}),(0,s.jsx)(A.nA,{className:"text-center text-sm text-slate-600",children:e.data_scadenza?new Date(e.data_scadenza).toLocaleDateString("it-IT"):"N/A"}),(0,s.jsx)(A.nA,{className:"text-center",children:G(e.abilitato,e.data_scadenza)}),(0,s.jsx)(A.nA,{className:"text-center",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)(f,{user:e,onEdit:()=>D(e),onToggleStatus:()=>L(e.id_utente),onDelete:()=>P(e.id_utente)}),(0,s.jsx)(c,{size:"sm",onClick:()=>V(e),disabled:"owner"===e.ruolo||!e.abilitato,className:"px-3 py-1.5 text-xs",icon:(0,s.jsx)(en,{className:"h-3.5 w-3.5"}),children:"Entra"})]})})]},e.id_utente))})]})})})})]})]}),(0,s.jsx)(z.av,{value:"crea-utente",className:"space-y-4",children:(0,s.jsx)(Z,{user:null,onSave:M,onCancel:O})}),j&&(0,s.jsx)(z.av,{value:"modifica-utente",className:"space-y-4",children:(0,s.jsx)(Z,{user:j,onSave:M,onCancel:O})}),(0,s.jsx)(z.av,{value:"database-tipologie-cavi",className:"space-y-4",children:(0,s.jsx)(et,{})}),(0,s.jsx)(z.av,{value:"visualizza-database-raw",className:"space-y-4",children:(0,s.jsx)(X,{})}),(0,s.jsx)(z.av,{value:"reset-database",className:"space-y-4",children:(0,s.jsx)(H,{})})]})})}):null}},44493:(e,a,t)=>{"use strict";t.d(a,{BT:()=>d,Wu:()=>o,ZB:()=>l,Zp:()=>i,aR:()=>n});var s=t(60687);t(43210);var r=t(4780);function i({className:e,...a}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...a})}function n({className:e,...a}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...a})}function l({className:e,...a}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",e),...a})}function d({className:e,...a}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...a})}function o({className:e,...a}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...a})}},46059:(e,a,t)=>{"use strict";t.d(a,{C:()=>n});var s=t(43210),r=t(98599),i=t(66156),n=e=>{let{present:a,children:t}=e,n=function(e){var a,t;let[r,n]=s.useState(),d=s.useRef(null),o=s.useRef(e),c=s.useRef("none"),[m,u]=(a=e?"mounted":"unmounted",t={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},s.useReducer((e,a)=>t[e][a]??e,a));return s.useEffect(()=>{let e=l(d.current);c.current="mounted"===m?e:"none"},[m]),(0,i.N)(()=>{let a=d.current,t=o.current;if(t!==e){let s=c.current,r=l(a);e?u("MOUNT"):"none"===r||a?.display==="none"?u("UNMOUNT"):t&&s!==r?u("ANIMATION_OUT"):u("UNMOUNT"),o.current=e}},[e,u]),(0,i.N)(()=>{if(r){let e,a=r.ownerDocument.defaultView??window,t=t=>{let s=l(d.current).includes(t.animationName);if(t.target===r&&s&&(u("ANIMATION_END"),!o.current)){let t=r.style.animationFillMode;r.style.animationFillMode="forwards",e=a.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=t)})}},s=e=>{e.target===r&&(c.current=l(d.current))};return r.addEventListener("animationstart",s),r.addEventListener("animationcancel",t),r.addEventListener("animationend",t),()=>{a.clearTimeout(e),r.removeEventListener("animationstart",s),r.removeEventListener("animationcancel",t),r.removeEventListener("animationend",t)}}u("ANIMATION_END")},[r,u]),{isPresent:["mounted","unmountSuspended"].includes(m),ref:s.useCallback(e=>{d.current=e?getComputedStyle(e):null,n(e)},[])}}(a),d="function"==typeof t?t({present:n.isPresent}):s.Children.only(t),o=(0,r.s)(n.ref,function(e){let a=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,t=a&&"isReactWarning"in a&&a.isReactWarning;return t?e.ref:(t=(a=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?e.props.ref:e.props.ref||e.ref}(d));return"function"==typeof t||n.isPresent?s.cloneElement(d,{ref:o}):null};function l(e){return e?.animationName||"none"}n.displayName="Presence"},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56896:(e,a,t)=>{"use strict";t.d(a,{S:()=>l});var s=t(60687);t(43210);var r=t(40211),i=t(13964),n=t(4780);function l({className:e,...a}){return(0,s.jsx)(r.bL,{"data-slot":"checkbox",className:(0,n.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...a,children:(0,s.jsx)(r.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,s.jsx)(i.A,{className:"size-3.5"})})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>r});var s=t(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},80013:(e,a,t)=>{"use strict";t.d(a,{J:()=>n});var s=t(60687);t(43210);var r=t(78148),i=t(4780);function n({className:e,...a}){return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...a})}},81630:e=>{"use strict";e.exports=require("http")},81806:(e,a,t)=>{"use strict";t.d(a,{Eb:()=>p,GN:()=>h});let s=/[<>\"'&\x00-\x1f\x7f-\x9f]/g,r=/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,i=/(<script|javascript:|vbscript:|onload|onerror|onclick)/gi,n=e=>"string"!=typeof e?"":e.trim().replace(s,"").replace(/\s+/g," ").substring(0,1e3),l=e=>{let a=n(e);return a.length<3?{isValid:!1,error:"Username deve essere almeno 3 caratteri"}:a.length>20?{isValid:!1,error:"Username non pu\xf2 superare 20 caratteri"}:/^[a-zA-Z0-9._-]+$/.test(a)?/^[._-]|[._-]$/.test(a)?{isValid:!1,error:"Username non pu\xf2 iniziare o finire con caratteri speciali"}:{isValid:!0}:{isValid:!1,error:"Username pu\xf2 contenere solo lettere, numeri, punti, underscore e trattini"}},d=e=>{if(!e||e.length<8)return{isValid:!1,error:"Password deve essere almeno 8 caratteri",strength:0};if(e.length>128)return{isValid:!1,error:"Password troppo lunga (max 128 caratteri)",strength:0};let a=0;return(/[a-z]/.test(e)&&a++,/[A-Z]/.test(e)&&a++,/[0-9]/.test(e)&&a++,/[^a-zA-Z0-9]/.test(e)&&a++,e.length>=12&&a++,a<3)?{isValid:!1,error:"Password deve contenere almeno: 1 minuscola, 1 maiuscola, 1 numero o 1 carattere speciale",strength:a}:["password","123456","admin","qwerty","letmein"].some(a=>e.toLowerCase().includes(a))?{isValid:!1,error:"Password troppo comune",strength:a}:{isValid:!0,strength:a}},o=e=>{let a=n(e);return a?a.length>254?{isValid:!1,error:"Email troppo lunga"}:/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(a)?{isValid:!0}:{isValid:!1,error:"Formato email non valido"}:{isValid:!1,error:"Email \xe8 obbligatoria"}},c=(e,a=255)=>n(e).length>a?{isValid:!1,error:`Testo troppo lungo (max ${a} caratteri)`}:i.test(e)||r.test(e)?{isValid:!1,error:"Contenuto non consentito rilevato"}:{isValid:!0},m=e=>{let a=n(e);return a?a.length<2?{isValid:!1,error:"Ragione sociale troppo corta"}:a.length>100?{isValid:!1,error:"Ragione sociale troppo lunga (max 100 caratteri)"}:/^[a-zA-Z0-9\s\.\-&']+$/.test(a)?{isValid:!0}:{isValid:!1,error:"Ragione sociale contiene caratteri non consentiti"}:{isValid:!1,error:"Ragione sociale \xe8 obbligatoria"}},u=e=>{if(!e)return{isValid:!0};let a=n(e).replace(/\s/g,"");return a.length<8||a.length>15?{isValid:!1,error:"VAT deve essere tra 8 e 15 caratteri"}:/^[A-Z0-9]+$/i.test(a)?{isValid:!0}:{isValid:!1,error:"VAT pu\xf2 contenere solo lettere e numeri"}},x=new Map,p=(e,a,t)=>{let s=Date.now(),r=x.get(e);return!r||s>r.resetTime?(x.set(e,{count:1,resetTime:s+t}),!0):!(r.count>=a)&&(r.count++,!0)},h=e=>{let a={},t=l(e.username);if(t.isValid||(a.username=t.error),e.password){let t=d(e.password);t.isValid||(a.password=t.error)}let s=m(e.ragione_sociale);if(s.isValid||(a.ragione_sociale=s.error),e.email){let t=o(e.email);t.isValid||(a.email=t.error)}if(e.vat){let t=u(e.vat);t.isValid||(a.vat=t.error)}if(e.indirizzo){let t=c(e.indirizzo,200);t.isValid||(a.indirizzo=t.error)}if(e.nazione){let t=c(e.nazione,50);t.isValid||(a.nazione=t.error)}if(e.referente_aziendale){let t=c(e.referente_aziendale,100);t.isValid||(a.referente_aziendale=t.error)}return{isValid:0===Object.keys(a).length,errors:a}}},83997:e=>{"use strict";e.exports=require("tty")},88091:(e,a,t)=>{Promise.resolve().then(t.bind(t,1132))},89667:(e,a,t)=>{"use strict";t.d(a,{p:()=>n});var s=t(60687),r=t(43210),i=t(4780);let n=r.forwardRef(({className:e,type:a,...t},r)=>(0,s.jsx)("input",{type:a,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),ref:r,...t}));n.displayName="Input"},94735:e=>{"use strict";e.exports=require("events")},96504:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o});var s=t(65239),r=t(48088),i=t(88170),n=t.n(i),l=t(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(a,d);let o={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1132)),"C:\\CMS\\webapp-nextjs\\src\\app\\admin\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\CMS\\webapp-nextjs\\src\\app\\admin\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},96834:(e,a,t)=>{"use strict";t.d(a,{E:()=>d});var s=t(60687);t(43210);var r=t(8730),i=t(24224),n=t(4780);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:a,asChild:t=!1,...i}){let d=t?r.DX:"span";return(0,s.jsx)(d,{"data-slot":"badge",className:(0,n.cn)(l({variant:a}),e),...i})}},99270:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},99891:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var a=require("../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),s=a.X(0,[447,991,658,947,400,818,829,639],()=>t(96504));module.exports=s})();