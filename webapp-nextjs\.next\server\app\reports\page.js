(()=>{var t={};t.id=22,t.ids=[22],t.modules={22:(t,e,r)=>{var n=r(75254),i=r(20623),o=r(48169),a=r(40542),c=r(45058);t.exports=function(t){return"function"==typeof t?t:null==t?o:"object"==typeof t?a(t)?i(t[0],t[1]):n(t):c(t)}},658:(t,e,r)=>{t.exports=r(41547)(r(85718),"Map")},1566:(t,e,r)=>{var n=r(89167),i=r(658),o=r(30401),a=r(34772),c=r(17830),u=r(29395),s=r(12290),l="[object Map]",f="[object Promise]",p="[object Set]",d="[object WeakMap]",h="[object DataView]",y=s(n),v=s(i),m=s(o),b=s(a),g=s(c),x=u;(n&&x(new n(new ArrayBuffer(1)))!=h||i&&x(new i)!=l||o&&x(o.resolve())!=f||a&&x(new a)!=p||c&&x(new c)!=d)&&(x=function(t){var e=u(t),r="[object Object]"==e?t.constructor:void 0,n=r?s(r):"";if(n)switch(n){case y:return h;case v:return l;case m:return f;case b:return p;case g:return d}return e}),t.exports=x},1707:(t,e,r)=>{var n=r(35142),i=r(46436);t.exports=function(t,e){e=n(e,t);for(var r=0,o=e.length;null!=t&&r<o;)t=t[i(e[r++])];return r&&r==o?t:void 0}},1944:t=>{t.exports=function(){return!1}},2408:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t){r[++e]=t}),r}},2896:(t,e,r)=>{var n=r(81488),i=r(59467);t.exports=function(t,e){return null!=t&&i(t,e,n)}},2984:(t,e,r)=>{var n=r(49227);t.exports=function(t,e,r){for(var i=-1,o=t.length;++i<o;){var a=t[i],c=e(a);if(null!=c&&(void 0===u?c==c&&!n(c):r(c,u)))var u=c,s=a}return s}},3105:t=>{t.exports=function(t){return t.split("")}},3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4999:(t,e,r)=>{t.exports=r(85718).Uint8Array},5231:(t,e,r)=>{var n=r(29395),i=r(55048);t.exports=function(t){if(!i(t))return!1;var e=n(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}},5359:t=>{t.exports=function(t){var e=null==t?0:t.length;return e?t[e-1]:void 0}},5566:(t,e,r)=>{var n=r(41011),i=r(34117),o=r(66713),a=r(42403);t.exports=function(t){return function(e){var r=i(e=a(e))?o(e):void 0,c=r?r[0]:e.charAt(0),u=r?n(r,1).join(""):e.slice(1);return c[t]()+u}}},6053:t=>{var e=/\s/;t.exports=function(t){for(var r=t.length;r--&&e.test(t.charAt(r)););return r}},6330:t=>{t.exports=function(){return[]}},7383:(t,e,r)=>{var n=r(67009),i=r(32269),o=r(38428),a=r(55048);t.exports=function(t,e,r){if(!a(r))return!1;var c=typeof e;return("number"==c?!!(i(r)&&o(e,r.length)):"string"==c&&e in r)&&n(r[e],t)}},7651:(t,e,r)=>{var n=r(82038),i=r(52931),o=r(32269);t.exports=function(t){return o(t)?n(t):i(t)}},8336:(t,e,r)=>{var n=r(45803);t.exports=function(t,e){var r=t.__data__;return n(e)?r["string"==typeof e?"string":"hash"]:r.map}},8852:(t,e,r)=>{var n=r(1707);t.exports=function(t){return function(e){return n(e,t)}}},9245:()=>{},10090:(t,e,r)=>{var n=r(80458),i=r(89624),o=r(47282),a=o&&o.isTypedArray;t.exports=a?i(a):n},10653:(t,e,r)=>{var n=r(21456),i=r(63979),o=r(7651);t.exports=function(t){return n(t,o,i)}},10663:t=>{t.exports="object"==typeof global&&global&&global.Object===Object&&global},10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11117:t=>{"use strict";var e=Object.prototype.hasOwnProperty,r="~";function n(){}function i(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function o(t,e,n,o,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var c=new i(n,o||t,a),u=r?r+e:e;return t._events[u]?t._events[u].fn?t._events[u]=[t._events[u],c]:t._events[u].push(c):(t._events[u]=c,t._eventsCount++),t}function a(t,e){0==--t._eventsCount?t._events=new n:delete t._events[e]}function c(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),c.prototype.eventNames=function(){var t,n,i=[];if(0===this._eventsCount)return i;for(n in t=this._events)e.call(t,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(t)):i},c.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,o=n.length,a=Array(o);i<o;i++)a[i]=n[i].fn;return a},c.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},c.prototype.emit=function(t,e,n,i,o,a){var c=r?r+t:t;if(!this._events[c])return!1;var u,s,l=this._events[c],f=arguments.length;if(l.fn){switch(l.once&&this.removeListener(t,l.fn,void 0,!0),f){case 1:return l.fn.call(l.context),!0;case 2:return l.fn.call(l.context,e),!0;case 3:return l.fn.call(l.context,e,n),!0;case 4:return l.fn.call(l.context,e,n,i),!0;case 5:return l.fn.call(l.context,e,n,i,o),!0;case 6:return l.fn.call(l.context,e,n,i,o,a),!0}for(s=1,u=Array(f-1);s<f;s++)u[s-1]=arguments[s];l.fn.apply(l.context,u)}else{var p,d=l.length;for(s=0;s<d;s++)switch(l[s].once&&this.removeListener(t,l[s].fn,void 0,!0),f){case 1:l[s].fn.call(l[s].context);break;case 2:l[s].fn.call(l[s].context,e);break;case 3:l[s].fn.call(l[s].context,e,n);break;case 4:l[s].fn.call(l[s].context,e,n,i);break;default:if(!u)for(p=1,u=Array(f-1);p<f;p++)u[p-1]=arguments[p];l[s].fn.apply(l[s].context,u)}}return!0},c.prototype.on=function(t,e,r){return o(this,t,e,r,!1)},c.prototype.once=function(t,e,r){return o(this,t,e,r,!0)},c.prototype.removeListener=function(t,e,n,i){var o=r?r+t:t;if(!this._events[o])return this;if(!e)return a(this,o),this;var c=this._events[o];if(c.fn)c.fn!==e||i&&!c.once||n&&c.context!==n||a(this,o);else{for(var u=0,s=[],l=c.length;u<l;u++)(c[u].fn!==e||i&&!c[u].once||n&&c[u].context!==n)&&s.push(c[u]);s.length?this._events[o]=1===s.length?s[0]:s:a(this,o)}return this},c.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&a(this,e)):(this._events=new n,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=r,c.EventEmitter=c,t.exports=c},11273:(t,e,r)=>{"use strict";r.d(e,{A:()=>a,q:()=>o});var n=r(43210),i=r(60687);function o(t,e){let r=n.createContext(e),o=t=>{let{children:e,...o}=t,a=n.useMemo(()=>o,Object.values(o));return(0,i.jsx)(r.Provider,{value:a,children:e})};return o.displayName=t+"Provider",[o,function(i){let o=n.useContext(r);if(o)return o;if(void 0!==e)return e;throw Error(`\`${i}\` must be used within \`${t}\``)}]}function a(t,e=[]){let r=[],o=()=>{let e=r.map(t=>n.createContext(t));return function(r){let i=r?.[t]||e;return n.useMemo(()=>({[`__scope${t}`]:{...r,[t]:i}}),[r,i])}};return o.scopeName=t,[function(e,o){let a=n.createContext(o),c=r.length;r=[...r,o];let u=e=>{let{scope:r,children:o,...u}=e,s=r?.[t]?.[c]||a,l=n.useMemo(()=>u,Object.values(u));return(0,i.jsx)(s.Provider,{value:l,children:o})};return u.displayName=e+"Provider",[u,function(r,i){let u=i?.[t]?.[c]||a,s=n.useContext(u);if(s)return s;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${e}\``)}]},function(...t){let e=t[0];if(1===t.length)return e;let r=()=>{let r=t.map(t=>({useScope:t(),scopeName:t.scopeName}));return function(t){let i=r.reduce((e,{useScope:r,scopeName:n})=>{let i=r(t)[`__scope${n}`];return{...e,...i}},{});return n.useMemo(()=>({[`__scope${e.scopeName}`]:i}),[i])}};return r.scopeName=e.scopeName,r}(o,...e)]}},11424:(t,e,r)=>{var n=r(47603);t.exports=r(66400)(n)},11539:(t,e,r)=>{var n=r(37643),i=r(55048),o=r(49227),a=0/0,c=/^[-+]0x[0-9a-f]+$/i,u=/^0b[01]+$/i,s=/^0o[0-7]+$/i,l=parseInt;t.exports=function(t){if("number"==typeof t)return t;if(o(t))return a;if(i(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=i(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=n(t);var r=u.test(t);return r||s.test(t)?l(t.slice(2),r?2:8):c.test(t)?a:+t}},12290:t=>{var e=Function.prototype.toString;t.exports=function(t){if(null!=t){try{return e.call(t)}catch(t){}try{return t+""}catch(t){}}return""}},12344:(t,e,r)=>{t.exports=r(65984)()},12412:t=>{"use strict";t.exports=require("assert")},13495:(t,e,r)=>{"use strict";r.d(e,{c:()=>i});var n=r(43210);function i(t){let e=n.useRef(t);return n.useEffect(()=>{e.current=t}),n.useMemo(()=>(...t)=>e.current?.(...t),[])}},14163:(t,e,r)=>{"use strict";r.d(e,{hO:()=>u,sG:()=>c});var n=r(43210),i=r(51215),o=r(8730),a=r(60687),c=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((t,e)=>{let r=(0,o.TL)(`Primitive.${e}`),i=n.forwardRef((t,n)=>{let{asChild:i,...o}=t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(i?r:e,{...o,ref:n})});return i.displayName=`Primitive.${e}`,{...t,[e]:i}},{});function u(t,e){t&&i.flushSync(()=>t.dispatchEvent(e))}},14675:t=>{t.exports=function(t){return function(){return t}}},15451:(t,e,r)=>{var n=r(29395),i=r(27467);t.exports=function(t){return i(t)&&"[object Arguments]"==n(t)}},15871:(t,e,r)=>{var n=r(36341),i=r(27467);t.exports=function t(e,r,o,a,c){return e===r||(null!=e&&null!=r&&(i(e)||i(r))?n(e,r,o,a,t,c):e!=e&&r!=r)}},15883:(t,e,r)=>{var n=r(2984),i=r(46063),o=r(48169);t.exports=function(t){return t&&t.length?n(t,o,i):void 0}},15909:(t,e,r)=>{var n=r(87506),i=r(66930),o=r(658);t.exports=function(){this.size=0,this.__data__={hash:new n,map:new(o||i),string:new n}}},16854:t=>{t.exports=function(t){return this.__data__.has(t)}},17518:(t,e,r)=>{var n=r(21367),i=r(1707),o=r(22),a=r(54765),c=r(43378),u=r(89624),s=r(65727),l=r(48169),f=r(40542);t.exports=function(t,e,r){e=e.length?n(e,function(t){return f(t)?function(e){return i(e,1===t.length?t[0]:t)}:t}):[l];var p=-1;return e=n(e,u(o)),c(a(t,function(t,r,i){return{criteria:n(e,function(e){return e(t)}),index:++p,value:t}}),function(t,e){return s(t,e,r)})}},17830:(t,e,r)=>{t.exports=r(41547)(r(85718),"WeakMap")},18234:(t,e,r)=>{var n=r(91290),i=r(22),o=r(84482),a=Math.max;t.exports=function(t,e,r){var c=null==t?0:t.length;if(!c)return -1;var u=null==r?0:o(r);return u<0&&(u=a(c+u,0)),n(t,i(e,3),u)}},19121:t=>{"use strict";t.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19976:(t,e,r)=>{var n=r(8336);t.exports=function(t,e){var r=n(this,t),i=r.size;return r.set(t,e),this.size+=+(r.size!=i),this}},20540:(t,e,r)=>{var n=r(55048),i=r(70151),o=r(11539),a=Math.max,c=Math.min;t.exports=function(t,e,r){var u,s,l,f,p,d,h=0,y=!1,v=!1,m=!0;if("function"!=typeof t)throw TypeError("Expected a function");function b(e){var r=u,n=s;return u=s=void 0,h=e,f=t.apply(n,r)}function g(t){var r=t-d,n=t-h;return void 0===d||r>=e||r<0||v&&n>=l}function x(){var t,r,n,o=i();if(g(o))return w(o);p=setTimeout(x,(t=o-d,r=o-h,n=e-t,v?c(n,l-r):n))}function w(t){return(p=void 0,m&&u)?b(t):(u=s=void 0,f)}function O(){var t,r=i(),n=g(r);if(u=arguments,s=this,d=r,n){if(void 0===p)return h=t=d,p=setTimeout(x,e),y?b(t):f;if(v)return clearTimeout(p),p=setTimeout(x,e),b(d)}return void 0===p&&(p=setTimeout(x,e)),f}return e=o(e)||0,n(r)&&(y=!!r.leading,l=(v="maxWait"in r)?a(o(r.maxWait)||0,e):l,m="trailing"in r?!!r.trailing:m),O.cancel=function(){void 0!==p&&clearTimeout(p),h=0,u=d=s=p=void 0},O.flush=function(){return void 0===p?f:w(i())},O}},20623:(t,e,r)=>{var n=r(15871),i=r(40491),o=r(2896),a=r(67619),c=r(34883),u=r(41132),s=r(46436);t.exports=function(t,e){return a(t)&&c(e)?u(s(t),e):function(r){var a=i(r,t);return void 0===a&&a===e?o(r,t):n(e,a,3)}}},21367:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,i=Array(n);++r<n;)i[r]=e(t[r],r,t);return i}},21456:(t,e,r)=>{var n=r(41693),i=r(40542);t.exports=function(t,e,r){var o=e(t);return i(t)?o:n(o,r(t))}},21592:(t,e,r)=>{var n=r(42205),i=r(61837);t.exports=function(t,e){return n(i(t,e),1)}},21630:(t,e,r)=>{var n=r(10653),i=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,o,a,c){var u=1&r,s=n(t),l=s.length;if(l!=n(e).length&&!u)return!1;for(var f=l;f--;){var p=s[f];if(!(u?p in e:i.call(e,p)))return!1}var d=c.get(t),h=c.get(e);if(d&&h)return d==e&&h==t;var y=!0;c.set(t,e),c.set(e,t);for(var v=u;++f<l;){var m=t[p=s[f]],b=e[p];if(o)var g=u?o(b,m,p,e,t,c):o(m,b,p,t,e,c);if(!(void 0===g?m===b||a(m,b,r,o,c):g)){y=!1;break}v||(v="constructor"==p)}if(y&&!v){var x=t.constructor,w=e.constructor;x!=w&&"constructor"in t&&"constructor"in e&&!("function"==typeof x&&x instanceof x&&"function"==typeof w&&w instanceof w)&&(y=!1)}return c.delete(t),c.delete(e),y}},21820:t=>{"use strict";t.exports=require("os")},22964:(t,e,r)=>{t.exports=r(23729)(r(18234))},23729:(t,e,r)=>{var n=r(22),i=r(32269),o=r(7651);t.exports=function(t){return function(e,r,a){var c=Object(e);if(!i(e)){var u=n(r,3);e=o(e),r=function(t){return u(c[t],t,c)}}var s=t(e,r,a);return s>-1?c[u?e[s]:s]:void 0}}},25118:t=>{t.exports=function(t){return this.__data__.has(t)}},25541:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},27006:(t,e,r)=>{var n=r(46328),i=r(99525),o=r(58276);t.exports=function(t,e,r,a,c,u){var s=1&r,l=t.length,f=e.length;if(l!=f&&!(s&&f>l))return!1;var p=u.get(t),d=u.get(e);if(p&&d)return p==e&&d==t;var h=-1,y=!0,v=2&r?new n:void 0;for(u.set(t,e),u.set(e,t);++h<l;){var m=t[h],b=e[h];if(a)var g=s?a(b,m,h,e,t,u):a(m,b,h,t,e,u);if(void 0!==g){if(g)continue;y=!1;break}if(v){if(!i(e,function(t,e){if(!o(v,e)&&(m===t||c(m,t,r,a,u)))return v.push(e)})){y=!1;break}}else if(!(m===b||c(m,b,r,a,u))){y=!1;break}}return u.delete(t),u.delete(e),y}},27467:t=>{t.exports=function(t){return null!=t&&"object"==typeof t}},27669:t=>{t.exports=function(){this.__data__=[],this.size=0}},27910:t=>{"use strict";t.exports=require("stream")},28354:t=>{"use strict";t.exports=require("util")},28837:(t,e,r)=>{var n=r(57797),i=Array.prototype.splice;t.exports=function(t){var e=this.__data__,r=n(e,t);return!(r<0)&&(r==e.length-1?e.pop():i.call(e,r,1),--this.size,!0)}},28947:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},28977:(t,e,r)=>{var n=r(11539),i=1/0;t.exports=function(t){return t?(t=n(t))===i||t===-i?(t<0?-1:1)*17976931348623157e292:t==t?t:0:0===t?t:0}},29021:t=>{"use strict";t.exports=require("fs")},29205:(t,e,r)=>{var n=r(8336);t.exports=function(t){var e=n(this,t).delete(t);return this.size-=!!e,e}},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29395:(t,e,r)=>{var n=r(79474),i=r(70222),o=r(84713),a=n?n.toStringTag:void 0;t.exports=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":a&&a in Object(t)?i(t):o(t)}},29508:(t,e,r)=>{var n=r(8336);t.exports=function(t){return n(this,t).get(t)}},29844:(t,e,r)=>{"use strict";r.d(e,{tU:()=>_,av:()=>T,j7:()=>N,Xi:()=>M});var n=r(60687),i=r(43210),o=r(70569),a=r(11273),c=r(72942),u=r(46059),s=r(14163),l=r(43),f=r(65551),p=r(96963),d="Tabs",[h,y]=(0,a.A)(d,[c.RG]),v=(0,c.RG)(),[m,b]=h(d),g=i.forwardRef((t,e)=>{let{__scopeTabs:r,value:i,onValueChange:o,defaultValue:a,orientation:c="horizontal",dir:u,activationMode:h="automatic",...y}=t,v=(0,l.jH)(u),[b,g]=(0,f.i)({prop:i,onChange:o,defaultProp:a??"",caller:d});return(0,n.jsx)(m,{scope:r,baseId:(0,p.B)(),value:b,onValueChange:g,orientation:c,dir:v,activationMode:h,children:(0,n.jsx)(s.sG.div,{dir:v,"data-orientation":c,...y,ref:e})})});g.displayName=d;var x="TabsList",w=i.forwardRef((t,e)=>{let{__scopeTabs:r,loop:i=!0,...o}=t,a=b(x,r),u=v(r);return(0,n.jsx)(c.bL,{asChild:!0,...u,orientation:a.orientation,dir:a.dir,loop:i,children:(0,n.jsx)(s.sG.div,{role:"tablist","aria-orientation":a.orientation,...o,ref:e})})});w.displayName=x;var O="TabsTrigger",j=i.forwardRef((t,e)=>{let{__scopeTabs:r,value:i,disabled:a=!1,...u}=t,l=b(O,r),f=v(r),p=A(l.baseId,i),d=E(l.baseId,i),h=i===l.value;return(0,n.jsx)(c.q7,{asChild:!0,...f,focusable:!a,active:h,children:(0,n.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":d,"data-state":h?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:p,...u,ref:e,onMouseDown:(0,o.m)(t.onMouseDown,t=>{a||0!==t.button||!1!==t.ctrlKey?t.preventDefault():l.onValueChange(i)}),onKeyDown:(0,o.m)(t.onKeyDown,t=>{[" ","Enter"].includes(t.key)&&l.onValueChange(i)}),onFocus:(0,o.m)(t.onFocus,()=>{let t="manual"!==l.activationMode;h||a||!t||l.onValueChange(i)})})})});j.displayName=O;var S="TabsContent",P=i.forwardRef((t,e)=>{let{__scopeTabs:r,value:o,forceMount:a,children:c,...l}=t,f=b(S,r),p=A(f.baseId,o),d=E(f.baseId,o),h=o===f.value,y=i.useRef(h);return i.useEffect(()=>{let t=requestAnimationFrame(()=>y.current=!1);return()=>cancelAnimationFrame(t)},[]),(0,n.jsx)(u.C,{present:a||h,children:({present:r})=>(0,n.jsx)(s.sG.div,{"data-state":h?"active":"inactive","data-orientation":f.orientation,role:"tabpanel","aria-labelledby":p,hidden:!r,id:d,tabIndex:0,...l,ref:e,style:{...t.style,animationDuration:y.current?"0s":void 0},children:r&&c})})});function A(t,e){return`${t}-trigger-${e}`}function E(t,e){return`${t}-content-${e}`}P.displayName=S;var k=r(4780);let _=g,N=i.forwardRef(({className:t,...e},r)=>(0,n.jsx)(w,{ref:r,className:(0,k.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...e}));N.displayName=w.displayName;let M=i.forwardRef(({className:t,...e},r)=>(0,n.jsx)(j,{ref:r,className:(0,k.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...e}));M.displayName=j.displayName;let T=i.forwardRef(({className:t,...e},r)=>(0,n.jsx)(P,{ref:r,className:(0,k.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...e}));T.displayName=P.displayName},30316:(t,e,r)=>{var n=r(67554);t.exports=function(t,e){var r=!0;return n(t,function(t,n,i){return r=!!e(t,n,i)}),r}},30401:(t,e,r)=>{t.exports=r(41547)(r(85718),"Promise")},30854:(t,e,r)=>{var n=r(66930),i=r(658),o=r(95746);t.exports=function(t,e){var r=this.__data__;if(r instanceof n){var a=r.__data__;if(!i||a.length<199)return a.push([t,e]),this.size=++r.size,this;r=this.__data__=new o(a)}return r.set(t,e),this.size=r.size,this}},32269:(t,e,r)=>{var n=r(5231),i=r(69619);t.exports=function(t){return null!=t&&i(t.length)&&!n(t)}},33873:t=>{"use strict";t.exports=require("path")},34117:t=>{var e=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");t.exports=function(t){return e.test(t)}},34452:t=>{"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},34746:t=>{t.exports=function(t){return this.__data__.get(t)}},34772:(t,e,r)=>{t.exports=r(41547)(r(85718),"Set")},34821:t=>{t.exports=function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}},34883:(t,e,r)=>{var n=r(55048);t.exports=function(t){return t==t&&!n(t)}},34990:(t,e,r)=>{t.exports=r(87321)()},35142:(t,e,r)=>{var n=r(40542),i=r(67619),o=r(51449),a=r(42403);t.exports=function(t,e){return n(t)?t:i(t,e)?[t]:o(a(t))}},35163:(t,e,r)=>{var n=r(15451),i=r(27467),o=Object.prototype,a=o.hasOwnProperty,c=o.propertyIsEnumerable;t.exports=n(function(){return arguments}())?n:function(t){return i(t)&&a.call(t,"callee")&&!c.call(t,"callee")}},35697:(t,e,r)=>{var n=r(79474),i=r(4999),o=r(67009),a=r(27006),c=r(59774),u=r(2408),s=n?n.prototype:void 0,l=s?s.valueOf:void 0;t.exports=function(t,e,r,n,s,f,p){switch(r){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)break;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":if(t.byteLength!=e.byteLength||!f(new i(t),new i(e)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return o(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var d=c;case"[object Set]":var h=1&n;if(d||(d=u),t.size!=e.size&&!h)break;var y=p.get(t);if(y)return y==e;n|=2,p.set(t,e);var v=a(d(t),d(e),n,s,f,p);return p.delete(t),v;case"[object Symbol]":if(l)return l.call(t)==l.call(e)}return!1}},35800:(t,e,r)=>{var n=r(57797);t.exports=function(t){return n(this.__data__,t)>-1}},36315:(t,e,r)=>{var n=r(22),i=r(92662);t.exports=function(t,e){return t&&t.length?i(t,n(e,2)):[]}},36341:(t,e,r)=>{var n=r(67200),i=r(27006),o=r(35697),a=r(21630),c=r(1566),u=r(40542),s=r(80329),l=r(10090),f="[object Arguments]",p="[object Array]",d="[object Object]",h=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,y,v,m){var b=u(t),g=u(e),x=b?p:c(t),w=g?p:c(e);x=x==f?d:x,w=w==f?d:w;var O=x==d,j=w==d,S=x==w;if(S&&s(t)){if(!s(e))return!1;b=!0,O=!1}if(S&&!O)return m||(m=new n),b||l(t)?i(t,e,r,y,v,m):o(t,e,x,r,y,v,m);if(!(1&r)){var P=O&&h.call(t,"__wrapped__"),A=j&&h.call(e,"__wrapped__");if(P||A){var E=P?t.value():t,k=A?e.value():e;return m||(m=new n),v(E,k,r,y,m)}}return!!S&&(m||(m=new n),a(t,e,r,y,v,m))}},36959:t=>{t.exports=function(){}},37456:t=>{t.exports=function(t){return null==t}},37575:(t,e,r)=>{var n=r(66930);t.exports=function(){this.__data__=new n,this.size=0}},37643:(t,e,r)=>{var n=r(6053),i=/^\s+/;t.exports=function(t){return t?t.slice(0,n(t)+1).replace(i,""):t}},38404:(t,e,r)=>{var n=r(29395),i=r(65932),o=r(27467),a=Object.prototype,c=Function.prototype.toString,u=a.hasOwnProperty,s=c.call(Object);t.exports=function(t){if(!o(t)||"[object Object]"!=n(t))return!1;var e=i(t);if(null===e)return!0;var r=u.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&c.call(r)==s}},38428:t=>{var e=/^(?:0|[1-9]\d*)$/;t.exports=function(t,r){var n=typeof t;return!!(r=null==r?0x1fffffffffffff:r)&&("number"==n||"symbol"!=n&&e.test(t))&&t>-1&&t%1==0&&t<r}},39672:(t,e,r)=>{var n=r(58141);t.exports=function(t,e){var r=this.__data__;return this.size+=+!this.has(t),r[t]=n&&void 0===e?"__lodash_hash_undefined__":e,this}},39774:t=>{t.exports=function(t){return t!=t}},40491:(t,e,r)=>{var n=r(1707);t.exports=function(t,e,r){var i=null==t?void 0:n(t,e);return void 0===i?r:i}},40542:t=>{t.exports=Array.isArray},41011:(t,e,r)=>{var n=r(41353);t.exports=function(t,e,r){var i=t.length;return r=void 0===r?i:r,!e&&r>=i?t:n(t,e,r)}},41132:t=>{t.exports=function(t,e){return function(r){return null!=r&&r[t]===e&&(void 0!==e||t in Object(r))}}},41157:(t,e,r)=>{var n=r(91928);t.exports=function(t,e,r){"__proto__"==e&&n?n(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}},41353:t=>{t.exports=function(t,e,r){var n=-1,i=t.length;e<0&&(e=-e>i?0:i+e),(r=r>i?i:r)<0&&(r+=i),i=e>r?0:r-e>>>0,e>>>=0;for(var o=Array(i);++n<i;)o[n]=t[n+e];return o}},41547:(t,e,r)=>{var n=r(61548),i=r(90851);t.exports=function(t,e){var r=i(t,e);return n(r)?r:void 0}},41693:t=>{t.exports=function(t,e){for(var r=-1,n=e.length,i=t.length;++r<n;)t[i+r]=e[r];return t}},41862:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},42082:t=>{t.exports=function(t){return function(e){return null==e?void 0:e[t]}}},42205:(t,e,r)=>{var n=r(41693),i=r(85450);t.exports=function t(e,r,o,a,c){var u=-1,s=e.length;for(o||(o=i),c||(c=[]);++u<s;){var l=e[u];r>0&&o(l)?r>1?t(l,r-1,o,a,c):n(c,l):a||(c[c.length]=l)}return c}},42403:(t,e,r)=>{var n=r(80195);t.exports=function(t){return null==t?"":n(t)}},43378:t=>{t.exports=function(t,e){var r=t.length;for(t.sort(e);r--;)t[r]=t[r].value;return t}},44493:(t,e,r)=>{"use strict";r.d(e,{BT:()=>u,Wu:()=>s,ZB:()=>c,Zp:()=>o,aR:()=>a});var n=r(60687);r(43210);var i=r(4780);function o({className:t,...e}){return(0,n.jsx)("div",{"data-slot":"card",className:(0,i.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...e})}function a({className:t,...e}){return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,i.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...e})}function c({className:t,...e}){return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,i.cn)("leading-none font-semibold",t),...e})}function u({className:t,...e}){return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,i.cn)("text-muted-foreground text-sm",t),...e})}function s({className:t,...e}){return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,i.cn)("px-6",t),...e})}},45058:(t,e,r)=>{var n=r(42082),i=r(8852),o=r(67619),a=r(46436);t.exports=function(t){return o(t)?n(a(t)):i(t)}},45180:(t,e,r)=>{"use strict";r.r(e),r.d(e,{GlobalError:()=>a.a,__next_app__:()=>f,pages:()=>l,routeModule:()=>p,tree:()=>s});var n=r(65239),i=r(48088),o=r(88170),a=r.n(o),c=r(30893),u={};for(let t in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(t)&&(u[t]=()=>c[t]);r.d(e,u);let s={children:["",{children:["reports",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,75900)),"C:\\CMS\\webapp-nextjs\\src\\app\\reports\\page.tsx"]}]},{metadata:{icon:[async t=>(await Promise.resolve().then(r.bind(r,70440))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async t=>(await Promise.resolve().then(r.bind(r,70440))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\CMS\\webapp-nextjs\\src\\app\\reports\\page.tsx"],f={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/reports/page",pathname:"/reports",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:s}})},45583:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},45603:(t,e,r)=>{var n=r(20540),i=r(55048);t.exports=function(t,e,r){var o=!0,a=!0;if("function"!=typeof t)throw TypeError("Expected a function");return i(r)&&(o="leading"in r?!!r.leading:o,a="trailing"in r?!!r.trailing:a),n(t,e,{leading:o,maxWait:e,trailing:a})}},45803:t=>{t.exports=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}},46059:(t,e,r)=>{"use strict";r.d(e,{C:()=>a});var n=r(43210),i=r(98599),o=r(66156),a=t=>{let{present:e,children:r}=t,a=function(t){var e,r;let[i,a]=n.useState(),u=n.useRef(null),s=n.useRef(t),l=n.useRef("none"),[f,p]=(e=t?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((t,e)=>r[t][e]??t,e));return n.useEffect(()=>{let t=c(u.current);l.current="mounted"===f?t:"none"},[f]),(0,o.N)(()=>{let e=u.current,r=s.current;if(r!==t){let n=l.current,i=c(e);t?p("MOUNT"):"none"===i||e?.display==="none"?p("UNMOUNT"):r&&n!==i?p("ANIMATION_OUT"):p("UNMOUNT"),s.current=t}},[t,p]),(0,o.N)(()=>{if(i){let t,e=i.ownerDocument.defaultView??window,r=r=>{let n=c(u.current).includes(r.animationName);if(r.target===i&&n&&(p("ANIMATION_END"),!s.current)){let r=i.style.animationFillMode;i.style.animationFillMode="forwards",t=e.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=r)})}},n=t=>{t.target===i&&(l.current=c(u.current))};return i.addEventListener("animationstart",n),i.addEventListener("animationcancel",r),i.addEventListener("animationend",r),()=>{e.clearTimeout(t),i.removeEventListener("animationstart",n),i.removeEventListener("animationcancel",r),i.removeEventListener("animationend",r)}}p("ANIMATION_END")},[i,p]),{isPresent:["mounted","unmountSuspended"].includes(f),ref:n.useCallback(t=>{u.current=t?getComputedStyle(t):null,a(t)},[])}}(e),u="function"==typeof r?r({present:a.isPresent}):n.Children.only(r),s=(0,i.s)(a.ref,function(t){let e=Object.getOwnPropertyDescriptor(t.props,"ref")?.get,r=e&&"isReactWarning"in e&&e.isReactWarning;return r?t.ref:(r=(e=Object.getOwnPropertyDescriptor(t,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?t.props.ref:t.props.ref||t.ref}(u));return"function"==typeof r||a.isPresent?n.cloneElement(u,{ref:s}):null};function c(t){return t?.animationName||"none"}a.displayName="Presence"},46063:t=>{t.exports=function(t,e){return t<e}},46229:(t,e,r)=>{var n=r(48169),i=r(66354),o=r(11424);t.exports=function(t,e){return o(i(t,e,n),t+"")}},46328:(t,e,r)=>{var n=r(95746),i=r(89185),o=r(16854);function a(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new n;++e<r;)this.add(t[e])}a.prototype.add=a.prototype.push=i,a.prototype.has=o,t.exports=a},46436:(t,e,r)=>{var n=r(49227),i=1/0;t.exports=function(t){if("string"==typeof t||n(t))return t;var e=t+"";return"0"==e&&1/t==-i?"-0":e}},47212:(t,e,r)=>{var n=r(87270),i=r(30316),o=r(22),a=r(40542),c=r(7383);t.exports=function(t,e,r){var u=a(t)?n:i;return r&&c(t,e,r)&&(e=void 0),u(t,o(e,3))}},47282:(t,e,r)=>{t=r.nmd(t);var n=r(10663),i=e&&!e.nodeType&&e,o=i&&t&&!t.nodeType&&t,a=o&&o.exports===i&&n.process,c=function(){try{var t=o&&o.require&&o.require("util").types;if(t)return t;return a&&a.binding&&a.binding("util")}catch(t){}}();t.exports=c},47603:(t,e,r)=>{var n=r(14675),i=r(91928),o=r(48169);t.exports=i?function(t,e){return i(t,"toString",{configurable:!0,enumerable:!1,value:n(e),writable:!0})}:o},48169:t=>{t.exports=function(t){return t}},48385:t=>{var e="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\ud83c[\udffb-\udfff]",i="[^"+e+"]",o="(?:\ud83c[\udde6-\uddff]){2}",a="[\ud800-\udbff][\udc00-\udfff]",c="(?:"+r+"|"+n+")?",u="[\\ufe0e\\ufe0f]?",s="(?:\\u200d(?:"+[i,o,a].join("|")+")"+u+c+")*",l=RegExp(n+"(?="+n+")|"+("(?:"+[i+r+"?",r,o,a,"["+e+"]"].join("|"))+")"+(u+c+s),"g");t.exports=function(t){return t.match(l)||[]}},49227:(t,e,r)=>{var n=r(29395),i=r(27467);t.exports=function(t){return"symbol"==typeof t||i(t)&&"[object Symbol]"==n(t)}},51449:(t,e,r)=>{var n=r(85745),i=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,o=/\\(\\)?/g;t.exports=n(function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(i,function(t,r,n,i){e.push(n?i.replace(o,"$1"):r||t)}),e})},52599:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,i=0,o=[];++r<n;){var a=t[r];e(a,r,t)&&(o[i++]=a)}return o}},52823:(t,e,r)=>{var n=r(85406),i=function(){var t=/[^.]+$/.exec(n&&n.keys&&n.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();t.exports=function(t){return!!i&&i in t}},52931:(t,e,r)=>{var n=r(77834),i=r(89605),o=Object.prototype.hasOwnProperty;t.exports=function(t){if(!n(t))return i(t);var e=[];for(var r in Object(t))o.call(t,r)&&"constructor"!=r&&e.push(r);return e}},53425:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>hY});var n={};r.r(n),r.d(n,{scaleBand:()=>nI,scaleDiverging:()=>function t(){var e=oO(cL()(oi));return e.copy=function(){return cI(e,t())},n_.apply(e,arguments)},scaleDivergingLog:()=>function t(){var e=oM(cL()).domain([.1,1,10]);return e.copy=function(){return cI(e,t()).base(e.base())},n_.apply(e,arguments)},scaleDivergingPow:()=>cz,scaleDivergingSqrt:()=>cU,scaleDivergingSymlog:()=>function t(){var e=oD(cL());return e.copy=function(){return cI(e,t()).constant(e.constant())},n_.apply(e,arguments)},scaleIdentity:()=>function t(e){var r;function n(t){return null==t||isNaN(t*=1)?r:t}return n.invert=n,n.domain=n.range=function(t){return arguments.length?(e=Array.from(t,or),n):e.slice()},n.unknown=function(t){return arguments.length?(r=t,n):r},n.copy=function(){return t(e).unknown(r)},e=arguments.length?Array.from(e,or):[0,1],oO(n)},scaleImplicit:()=>nC,scaleLinear:()=>oj,scaleLog:()=>function t(){let e=oM(os()).domain([1,10]);return e.copy=()=>ou(e,t()).base(e.base()),nk.apply(e,arguments),e},scaleOrdinal:()=>nD,scalePoint:()=>nB,scalePow:()=>oz,scaleQuantile:()=>function t(){var e,r=[],n=[],i=[];function o(){var t=0,e=Math.max(1,n.length);for(i=Array(e-1);++t<e;)i[t-1]=function(t,e,r=iw){if(!(!(n=t.length)||isNaN(e*=1))){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,i=(n-1)*e,o=Math.floor(i),a=+r(t[o],o,t);return a+(r(t[o+1],o+1,t)-a)*(i-o)}}(r,t/e);return a}function a(t){return null==t||isNaN(t*=1)?e:n[ij(i,t)]}return a.invertExtent=function(t){var e=n.indexOf(t);return e<0?[NaN,NaN]:[e>0?i[e-1]:r[0],e<i.length?i[e]:r[r.length-1]]},a.domain=function(t){if(!arguments.length)return r.slice();for(let e of(r=[],t))null==e||isNaN(e*=1)||r.push(e);return r.sort(im),o()},a.range=function(t){return arguments.length?(n=Array.from(t),o()):n.slice()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.quantiles=function(){return i.slice()},a.copy=function(){return t().domain(r).range(n).unknown(e)},nk.apply(a,arguments)},scaleQuantize:()=>function t(){var e,r=0,n=1,i=1,o=[.5],a=[0,1];function c(t){return null!=t&&t<=t?a[ij(o,t,0,i)]:e}function u(){var t=-1;for(o=Array(i);++t<i;)o[t]=((t+1)*n-(t-i)*r)/(i+1);return c}return c.domain=function(t){return arguments.length?([r,n]=t,r*=1,n*=1,u()):[r,n]},c.range=function(t){return arguments.length?(i=(a=Array.from(t)).length-1,u()):a.slice()},c.invertExtent=function(t){var e=a.indexOf(t);return e<0?[NaN,NaN]:e<1?[r,o[0]]:e>=i?[o[i-1],n]:[o[e-1],o[e]]},c.unknown=function(t){return arguments.length&&(e=t),c},c.thresholds=function(){return o.slice()},c.copy=function(){return t().domain([r,n]).range(a).unknown(e)},nk.apply(oO(c),arguments)},scaleRadial:()=>function t(){var e,r=ol(),n=[0,1],i=!1;function o(t){var n,o=Math.sign(n=r(t))*Math.sqrt(Math.abs(n));return isNaN(o)?e:i?Math.round(o):o}return o.invert=function(t){return r.invert(oF(t))},o.domain=function(t){return arguments.length?(r.domain(t),o):r.domain()},o.range=function(t){return arguments.length?(r.range((n=Array.from(t,or)).map(oF)),o):n.slice()},o.rangeRound=function(t){return o.range(t).round(!0)},o.round=function(t){return arguments.length?(i=!!t,o):i},o.clamp=function(t){return arguments.length?(r.clamp(t),o):r.clamp()},o.unknown=function(t){return arguments.length?(e=t,o):e},o.copy=function(){return t(r.domain(),n).round(i).clamp(r.clamp()).unknown(e)},nk.apply(o,arguments),oO(o)},scaleSequential:()=>function t(){var e=oO(cD()(oi));return e.copy=function(){return cI(e,t())},n_.apply(e,arguments)},scaleSequentialLog:()=>function t(){var e=oM(cD()).domain([1,10]);return e.copy=function(){return cI(e,t()).base(e.base())},n_.apply(e,arguments)},scaleSequentialPow:()=>cB,scaleSequentialQuantile:()=>function t(){var e=[],r=oi;function n(t){if(null!=t&&!isNaN(t*=1))return r((ij(e,t,1)-1)/(e.length-1))}return n.domain=function(t){if(!arguments.length)return e.slice();for(let r of(e=[],t))null==r||isNaN(r*=1)||e.push(r);return e.sort(im),n},n.interpolator=function(t){return arguments.length?(r=t,n):r},n.range=function(){return e.map((t,n)=>r(n/(e.length-1)))},n.quantiles=function(t){return Array.from({length:t+1},(r,n)=>(function(t,e,r){if(!(!(n=(t=Float64Array.from(function*(t,e){if(void 0===e)for(let e of t)null!=e&&(e*=1)>=e&&(yield e);else{let r=-1;for(let n of t)null!=(n=e(n,++r,t))&&(n*=1)>=n&&(yield n)}}(t,void 0))).length)||isNaN(e*=1))){if(e<=0||n<2)return oq(t);if(e>=1)return o$(t);var n,i=(n-1)*e,o=Math.floor(i),a=o$((function t(e,r,n=0,i=1/0,o){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),i=Math.floor(Math.min(e.length-1,i)),!(n<=r&&r<=i))return e;for(o=void 0===o?oW:function(t=im){if(t===im)return oW;if("function"!=typeof t)throw TypeError("compare is not a function");return(e,r)=>{let n=t(e,r);return n||0===n?n:(0===t(r,r))-(0===t(e,e))}}(o);i>n;){if(i-n>600){let a=i-n+1,c=r-n+1,u=Math.log(a),s=.5*Math.exp(2*u/3),l=.5*Math.sqrt(u*s*(a-s)/a)*(c-a/2<0?-1:1),f=Math.max(n,Math.floor(r-c*s/a+l)),p=Math.min(i,Math.floor(r+(a-c)*s/a+l));t(e,r,f,p,o)}let a=e[r],c=n,u=i;for(oG(e,n,r),o(e[i],a)>0&&oG(e,n,i);c<u;){for(oG(e,c,u),++c,--u;0>o(e[c],a);)++c;for(;o(e[u],a)>0;)--u}0===o(e[n],a)?oG(e,n,u):oG(e,++u,i),u<=r&&(n=u+1),r<=u&&(i=u-1)}return e})(t,o).subarray(0,o+1));return a+(oq(t.subarray(o+1))-a)*(i-o)}})(e,n/t))},n.copy=function(){return t(r).domain(e)},n_.apply(n,arguments)},scaleSequentialSqrt:()=>cR,scaleSequentialSymlog:()=>function t(){var e=oD(cD());return e.copy=function(){return cI(e,t()).constant(e.constant())},n_.apply(e,arguments)},scaleSqrt:()=>oU,scaleSymlog:()=>function t(){var e=oD(os());return e.copy=function(){return ou(e,t()).constant(e.constant())},nk.apply(e,arguments)},scaleThreshold:()=>function t(){var e,r=[.5],n=[0,1],i=1;function o(t){return null!=t&&t<=t?n[ij(r,t,0,i)]:e}return o.domain=function(t){return arguments.length?(i=Math.min((r=Array.from(t)).length,n.length-1),o):r.slice()},o.range=function(t){return arguments.length?(n=Array.from(t),i=Math.min(r.length,n.length-1),o):n.slice()},o.invertExtent=function(t){var e=n.indexOf(t);return[r[e-1],r[e]]},o.unknown=function(t){return arguments.length?(e=t,o):e},o.copy=function(){return t().domain(r).range(n).unknown(e)},nk.apply(o,arguments)},scaleTime:()=>cT,scaleUtc:()=>cC,tickFormat:()=>ow});var i=r(60687),o=r(43210),a=r.n(o);r(9245);var c=r(44493),u=r(29523),s=r(29844),l=r(63213),f=r(76628),p=r(25653);r(62185);var d=r(41862),h=r(93613),y=r(78122),v=r(28947),m=r(10022),b=r(19080),g=r(45583),x=r(5336),w=r(25541),O=r(58559),j=r(48730),S=r(40228),P=r(43649),A=r(49384),E=r(45603),k=r.n(E),_=r(63866),N=r.n(_),M=r(77822),T=r.n(M),C=r(40491),D=r.n(C),I=r(93490),B=r.n(I),R=function(t){return 0===t?0:t>0?1:-1},L=function(t){return N()(t)&&t.indexOf("%")===t.length-1},z=function(t){return B()(t)&&!T()(t)},U=function(t){return z(t)||N()(t)},F=0,$=function(t){var e=++F;return"".concat(t||"").concat(e)},q=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!z(t)&&!N()(t))return n;if(L(t)){var o=t.indexOf("%");r=e*parseFloat(t.slice(0,o))/100}else r=+t;return T()(r)&&(r=n),i&&r>e&&(r=e),r},W=function(t){if(!t)return null;var e=Object.keys(t);return e&&e.length?t[e[0]]:null},G=function(t){if(!Array.isArray(t))return!1;for(var e=t.length,r={},n=0;n<e;n++)if(r[t[n]])return!0;else r[t[n]]=!0;return!1},X=function(t,e){return z(t)&&z(e)?function(r){return t+r*(e-t)}:function(){return e}};function H(t,e,r){return t&&t.length?t.find(function(t){return t&&("function"==typeof e?e(t):D()(t,e))===r}):null}var V=function(t,e){return z(t)&&z(e)?t-e:N()(t)&&N()(e)?t.localeCompare(e):t instanceof Date&&e instanceof Date?t.getTime()-e.getTime():String(t).localeCompare(String(e))},K=function(t,e){for(var r=arguments.length,n=Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i]},Y=r(37456),Z=r.n(Y),J=r(5231),Q=r.n(J),tt=r(55048),te=r.n(tt),tr=r(93780);function tn(t,e){for(var r in t)if(({}).hasOwnProperty.call(t,r)&&(!({}).hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if(({}).hasOwnProperty.call(e,n)&&!({}).hasOwnProperty.call(t,n))return!1;return!0}function ti(t){return(ti="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var to=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],ta=["points","pathLength"],tc={svg:["viewBox","children"],polygon:ta,polyline:ta},tu=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],ts=function(t,e){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var r=t;if((0,o.isValidElement)(t)&&(r=t.props),!te()(r))return null;var n={};return Object.keys(r).forEach(function(t){tu.includes(t)&&(n[t]=e||function(e){return r[t](r,e)})}),n},tl=function(t,e,r){if(!te()(t)||"object"!==ti(t))return null;var n=null;return Object.keys(t).forEach(function(i){var o=t[i];tu.includes(i)&&"function"==typeof o&&(n||(n={}),n[i]=function(t){return o(e,r,t),null})}),n},tf=["children"],tp=["children"];function td(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function th(t){return(th="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var ty={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},tv=function(t){return"string"==typeof t?t:t?t.displayName||t.name||"Component":""},tm=null,tb=null,tg=function t(e){if(e===tm&&Array.isArray(tb))return tb;var r=[];return o.Children.forEach(e,function(e){Z()(e)||((0,tr.isFragment)(e)?r=r.concat(t(e.props.children)):r.push(e))}),tb=r,tm=e,r};function tx(t,e){var r=[],n=[];return n=Array.isArray(e)?e.map(function(t){return tv(t)}):[tv(e)],tg(t).forEach(function(t){var e=D()(t,"type.displayName")||D()(t,"type.name");-1!==n.indexOf(e)&&r.push(t)}),r}function tw(t,e){var r=tx(t,e);return r&&r[0]}var tO=function(t){if(!t||!t.props)return!1;var e=t.props,r=e.width,n=e.height;return!!z(r)&&!(r<=0)&&!!z(n)&&!(n<=0)},tj=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],tS=function(t,e,r,n){var i,o=null!=(i=null==tc?void 0:tc[n])?i:[];return e.startsWith("data-")||!Q()(t)&&(n&&o.includes(e)||to.includes(e))||r&&tu.includes(e)},tP=function(t,e,r){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var n=t;if((0,o.isValidElement)(t)&&(n=t.props),!te()(n))return null;var i={};return Object.keys(n).forEach(function(t){var o;tS(null==(o=n)?void 0:o[t],t,e,r)&&(i[t]=n[t])}),i},tA=function t(e,r){if(e===r)return!0;var n=o.Children.count(e);if(n!==o.Children.count(r))return!1;if(0===n)return!0;if(1===n)return tE(Array.isArray(e)?e[0]:e,Array.isArray(r)?r[0]:r);for(var i=0;i<n;i++){var a=e[i],c=r[i];if(Array.isArray(a)||Array.isArray(c)){if(!t(a,c))return!1}else if(!tE(a,c))return!1}return!0},tE=function(t,e){if(Z()(t)&&Z()(e))return!0;if(!Z()(t)&&!Z()(e)){var r=t.props||{},n=r.children,i=td(r,tf),o=e.props||{},a=o.children,c=td(o,tp);if(n&&a)return tn(i,c)&&tA(n,a);if(!n&&!a)return tn(i,c)}return!1},tk=function(t,e){var r=[],n={};return tg(t).forEach(function(t,i){var o;if((o=t)&&o.type&&N()(o.type)&&tj.indexOf(o.type)>=0)r.push(t);else if(t){var a=tv(t.type),c=e[a]||{},u=c.handler,s=c.once;if(u&&(!s||!n[a])){var l=u(t,a,i);r.push(l),n[a]=!0}}}),r},t_=function(t){var e=t&&t.type;return e&&ty[e]?ty[e]:null};function tN(t){return(tN="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tM(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tT(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tM(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=tN(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tN(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tN(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tM(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tC(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var tD=(0,o.forwardRef)(function(t,e){var r,n=t.aspect,i=t.initialDimension,c=void 0===i?{width:-1,height:-1}:i,u=t.width,s=void 0===u?"100%":u,l=t.height,f=void 0===l?"100%":l,p=t.minWidth,d=void 0===p?0:p,h=t.minHeight,y=t.maxHeight,v=t.children,m=t.debounce,b=void 0===m?0:m,g=t.id,x=t.className,w=t.onResize,O=t.style,j=(0,o.useRef)(null),S=(0,o.useRef)();S.current=w,(0,o.useImperativeHandle)(e,function(){return Object.defineProperty(j.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),j.current},configurable:!0})});var P=function(t){if(Array.isArray(t))return t}(r=(0,o.useState)({containerWidth:c.width,containerHeight:c.height}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,s=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw i}}return c}}(r,2)||function(t,e){if(t){if("string"==typeof t)return tC(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tC(t,e)}}(r,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),E=P[0],_=P[1],N=(0,o.useCallback)(function(t,e){_(function(r){var n=Math.round(t),i=Math.round(e);return r.containerWidth===n&&r.containerHeight===i?r:{containerWidth:n,containerHeight:i}})},[]);(0,o.useEffect)(function(){var t=function(t){var e,r=t[0].contentRect,n=r.width,i=r.height;N(n,i),null==(e=S.current)||e.call(S,n,i)};b>0&&(t=k()(t,b,{trailing:!0,leading:!1}));var e=new ResizeObserver(t),r=j.current.getBoundingClientRect();return N(r.width,r.height),e.observe(j.current),function(){e.disconnect()}},[N,b]);var M=(0,o.useMemo)(function(){var t=E.containerWidth,e=E.containerHeight;if(t<0||e<0)return null;K(L(s)||L(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",s,f),K(!n||n>0,"The aspect(%s) must be greater than zero.",n);var r=L(s)?t:s,i=L(f)?e:f;n&&n>0&&(r?i=r/n:i&&(r=i*n),y&&i>y&&(i=y)),K(r>0||i>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",r,i,s,f,d,h,n);var c=!Array.isArray(v)&&tv(v.type).endsWith("Chart");return a().Children.map(v,function(t){return a().isValidElement(t)?(0,o.cloneElement)(t,tT({width:r,height:i},c?{style:tT({height:"100%",width:"100%",maxHeight:i,maxWidth:r},t.props.style)}:{})):t})},[n,v,f,y,h,d,E,s]);return a().createElement("div",{id:g?"".concat(g):void 0,className:(0,A.A)("recharts-responsive-container",x),style:tT(tT({},void 0===O?{}:O),{},{width:s,height:f,minWidth:d,minHeight:h,maxHeight:y}),ref:j},M)}),tI=r(34990),tB=r.n(tI),tR=r(85938),tL=r.n(tR);function tz(t,e){if(!t)throw Error("Invariant failed")}var tU=["children","width","height","viewBox","className","style","title","desc"];function tF(){return(tF=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function t$(t){var e=t.children,r=t.width,n=t.height,i=t.viewBox,o=t.className,c=t.style,u=t.title,s=t.desc,l=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,tU),f=i||{width:r,height:n,x:0,y:0},p=(0,A.A)("recharts-surface",o);return a().createElement("svg",tF({},tP(l,!0,"svg"),{className:p,width:r,height:n,style:c,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),a().createElement("title",null,u),a().createElement("desc",null,s),e)}var tq=["children","className"];function tW(){return(tW=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var tG=a().forwardRef(function(t,e){var r=t.children,n=t.className,i=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,tq),o=(0,A.A)("recharts-layer",n);return a().createElement("g",tW({className:o},tP(i,!0),{ref:e}),r)});function tX(t){return(tX="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tH(){return(tH=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tV(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function tK(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tY(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tK(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=tX(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tX(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tX(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tK(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tZ(t){return Array.isArray(t)&&U(t[0])&&U(t[1])?t.join(" ~ "):t}var tJ=function(t){var e=t.separator,r=void 0===e?" : ":e,n=t.contentStyle,i=t.itemStyle,o=void 0===i?{}:i,c=t.labelStyle,u=t.payload,s=t.formatter,l=t.itemSorter,f=t.wrapperClassName,p=t.labelClassName,d=t.label,h=t.labelFormatter,y=t.accessibilityLayer,v=tY({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},void 0===n?{}:n),m=tY({margin:0},void 0===c?{}:c),b=!Z()(d),g=b?d:"",x=(0,A.A)("recharts-default-tooltip",f),w=(0,A.A)("recharts-tooltip-label",p);return b&&h&&null!=u&&(g=h(d,u)),a().createElement("div",tH({className:x,style:v},void 0!==y&&y?{role:"status","aria-live":"assertive"}:{}),a().createElement("p",{className:w,style:m},a().isValidElement(g)?g:"".concat(g)),function(){if(u&&u.length){var t=(l?tL()(u,l):u).map(function(t,e){if("none"===t.type)return null;var n=tY({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},o),i=t.formatter||s||tZ,c=t.value,l=t.name,f=c,p=l;if(i&&null!=f&&null!=p){var d=i(c,l,t,e,u);if(Array.isArray(d)){var h=function(t){if(Array.isArray(t))return t}(d)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,s=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw i}}return c}}(d,2)||function(t,e){if(t){if("string"==typeof t)return tV(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tV(t,e)}}(d,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();f=h[0],p=h[1]}else f=d}return a().createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(e),style:n},U(p)?a().createElement("span",{className:"recharts-tooltip-item-name"},p):null,U(p)?a().createElement("span",{className:"recharts-tooltip-item-separator"},r):null,a().createElement("span",{className:"recharts-tooltip-item-value"},f),a().createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))});return a().createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null}())};function tQ(t){return(tQ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function t0(t,e,r){var n;return(n=function(t,e){if("object"!=tQ(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tQ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==tQ(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var t1="recharts-tooltip-wrapper",t2={visibility:"hidden"};function t5(t){var e=t.allowEscapeViewBox,r=t.coordinate,n=t.key,i=t.offsetTopLeft,o=t.position,a=t.reverseDirection,c=t.tooltipDimension,u=t.viewBox,s=t.viewBoxDimension;if(o&&z(o[n]))return o[n];var l=r[n]-c-i,f=r[n]+i;return e[n]?a[n]?l:f:a[n]?l<u[n]?Math.max(f,u[n]):Math.max(l,u[n]):f+c>u[n]+s?Math.max(l,u[n]):Math.max(f,u[n])}function t4(t){return(t4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function t3(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function t6(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?t3(Object(r),!0).forEach(function(e){et(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):t3(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function t8(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(t8=function(){return!!t})()}function t7(t){return(t7=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function t9(t,e){return(t9=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function et(t,e,r){return(e=ee(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ee(t){var e=function(t,e){if("object"!=t4(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=t4(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==t4(e)?e:e+""}var er=function(t){var e;function r(){var t,e,n;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");for(var i=arguments.length,o=Array(i),a=0;a<i;a++)o[a]=arguments[a];return e=r,n=[].concat(o),e=t7(e),et(t=function(t,e){if(e&&("object"===t4(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,t8()?Reflect.construct(e,n||[],t7(this).constructor):e.apply(this,n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),et(t,"handleKeyDown",function(e){if("Escape"===e.key){var r,n,i,o;t.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(r=null==(n=t.props.coordinate)?void 0:n.x)?r:0,y:null!=(i=null==(o=t.props.coordinate)?void 0:o.y)?i:0}})}}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&t9(r,t),e=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();(Math.abs(t.width-this.state.lastBoundingBox.width)>1||Math.abs(t.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:t.width,height:t.height}})}else(-1!==this.state.lastBoundingBox.width||-1!==this.state.lastBoundingBox.height)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var t,e;this.props.active&&this.updateBBox(),this.state.dismissed&&((null==(t=this.props.coordinate)?void 0:t.x)!==this.state.dismissedAtCoordinate.x||(null==(e=this.props.coordinate)?void 0:e.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var t,e,r,n,i,o,c,u,s,l,f,p,d,h,y,v,m,b,g,x=this,w=this.props,O=w.active,j=w.allowEscapeViewBox,S=w.animationDuration,P=w.animationEasing,E=w.children,k=w.coordinate,_=w.hasPayload,N=w.isAnimationActive,M=w.offset,T=w.position,C=w.reverseDirection,D=w.useTranslate3d,I=w.viewBox,B=w.wrapperStyle,R=(p=(t={allowEscapeViewBox:j,coordinate:k,offsetTopLeft:M,position:T,reverseDirection:C,tooltipBox:this.state.lastBoundingBox,useTranslate3d:D,viewBox:I}).allowEscapeViewBox,d=t.coordinate,h=t.offsetTopLeft,y=t.position,v=t.reverseDirection,m=t.tooltipBox,b=t.useTranslate3d,g=t.viewBox,m.height>0&&m.width>0&&d?(r=(e={translateX:l=t5({allowEscapeViewBox:p,coordinate:d,key:"x",offsetTopLeft:h,position:y,reverseDirection:v,tooltipDimension:m.width,viewBox:g,viewBoxDimension:g.width}),translateY:f=t5({allowEscapeViewBox:p,coordinate:d,key:"y",offsetTopLeft:h,position:y,reverseDirection:v,tooltipDimension:m.height,viewBox:g,viewBoxDimension:g.height}),useTranslate3d:b}).translateX,n=e.translateY,s={transform:e.useTranslate3d?"translate3d(".concat(r,"px, ").concat(n,"px, 0)"):"translate(".concat(r,"px, ").concat(n,"px)")}):s=t2,{cssProperties:s,cssClasses:(o=(i={translateX:l,translateY:f,coordinate:d}).coordinate,c=i.translateX,u=i.translateY,(0,A.A)(t1,t0(t0(t0(t0({},"".concat(t1,"-right"),z(c)&&o&&z(o.x)&&c>=o.x),"".concat(t1,"-left"),z(c)&&o&&z(o.x)&&c<o.x),"".concat(t1,"-bottom"),z(u)&&o&&z(o.y)&&u>=o.y),"".concat(t1,"-top"),z(u)&&o&&z(o.y)&&u<o.y)))}),L=R.cssClasses,U=R.cssProperties,F=t6(t6({transition:N&&O?"transform ".concat(S,"ms ").concat(P):void 0},U),{},{pointerEvents:"none",visibility:!this.state.dismissed&&O&&_?"visible":"hidden",position:"absolute",top:0,left:0},B);return a().createElement("div",{tabIndex:-1,className:L,style:F,ref:function(t){x.wrapperNode=t}},E)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ee(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(o.PureComponent),en={isSsr:!0,get:function(t){return en[t]},set:function(t,e){if("string"==typeof t)en[t]=e;else{var r=Object.keys(t);r&&r.length&&r.forEach(function(e){en[e]=t[e]})}}},ei=r(36315),eo=r.n(ei);function ea(t,e,r){return!0===e?eo()(t,r):Q()(e)?eo()(t,e):t}function ec(t){return(ec="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function eu(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function es(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eu(Object(r),!0).forEach(function(e){ed(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eu(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function el(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(el=function(){return!!t})()}function ef(t){return(ef=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function ep(t,e){return(ep=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ed(t,e,r){return(e=eh(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function eh(t){var e=function(t,e){if("object"!=ec(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ec(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ec(e)?e:e+""}function ey(t){return t.dataKey}var ev=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=ef(t),function(t,e){if(e&&("object"===ec(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,el()?Reflect.construct(t,e||[],ef(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&ep(r,t),e=[{key:"render",value:function(){var t,e=this,r=this.props,n=r.active,i=r.allowEscapeViewBox,o=r.animationDuration,c=r.animationEasing,u=r.content,s=r.coordinate,l=r.filterNull,f=r.isAnimationActive,p=r.offset,d=r.payload,h=r.payloadUniqBy,y=r.position,v=r.reverseDirection,m=r.useTranslate3d,b=r.viewBox,g=r.wrapperStyle,x=null!=d?d:[];l&&x.length&&(x=ea(d.filter(function(t){return null!=t.value&&(!0!==t.hide||e.props.includeHidden)}),h,ey));var w=x.length>0;return a().createElement(er,{allowEscapeViewBox:i,animationDuration:o,animationEasing:c,isAnimationActive:f,active:n,coordinate:s,hasPayload:w,offset:p,position:y,reverseDirection:v,useTranslate3d:m,viewBox:b,wrapperStyle:g},(t=es(es({},this.props),{},{payload:x}),a().isValidElement(u)?a().cloneElement(u,t):"function"==typeof u?a().createElement(u,t):a().createElement(tJ,t)))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,eh(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(o.PureComponent);ed(ev,"displayName","Tooltip"),ed(ev,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!en.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var em=r(69433),eb=r.n(em);let eg=Math.cos,ex=Math.sin,ew=Math.sqrt,eO=Math.PI,ej=2*eO,eS={draw(t,e){let r=ew(e/eO);t.moveTo(r,0),t.arc(0,0,r,0,ej)}},eP=ew(1/3),eA=2*eP,eE=ex(eO/10)/ex(7*eO/10),ek=ex(ej/10)*eE,e_=-eg(ej/10)*eE,eN=ew(3),eM=ew(3)/2,eT=1/ew(12),eC=(eT/2+1)*3;function eD(t){return function(){return t}}let eI=Math.PI,eB=2*eI,eR=eB-1e-6;function eL(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=arguments[e]+t[e]}class ez{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?eL:function(t){let e=Math.floor(t);if(!(e>=0))throw Error(`invalid digits: ${t}`);if(e>15)return eL;let r=10**e;return function(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=Math.round(arguments[e]*r)/r+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,r,n){this._append`Q${+t},${+e},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(t,e,r,n,i,o){this._append`C${+t},${+e},${+r},${+n},${this._x1=+i},${this._y1=+o}`}arcTo(t,e,r,n,i){if(t*=1,e*=1,r*=1,n*=1,(i*=1)<0)throw Error(`negative radius: ${i}`);let o=this._x1,a=this._y1,c=r-t,u=n-e,s=o-t,l=a-e,f=s*s+l*l;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(f>1e-6)if(Math.abs(l*c-u*s)>1e-6&&i){let p=r-o,d=n-a,h=c*c+u*u,y=Math.sqrt(h),v=Math.sqrt(f),m=i*Math.tan((eI-Math.acos((h+f-(p*p+d*d))/(2*y*v)))/2),b=m/v,g=m/y;Math.abs(b-1)>1e-6&&this._append`L${t+b*s},${e+b*l}`,this._append`A${i},${i},0,0,${+(l*p>s*d)},${this._x1=t+g*c},${this._y1=e+g*u}`}else this._append`L${this._x1=t},${this._y1=e}`}arc(t,e,r,n,i,o){if(t*=1,e*=1,r*=1,o=!!o,r<0)throw Error(`negative radius: ${r}`);let a=r*Math.cos(n),c=r*Math.sin(n),u=t+a,s=e+c,l=1^o,f=o?n-i:i-n;null===this._x1?this._append`M${u},${s}`:(Math.abs(this._x1-u)>1e-6||Math.abs(this._y1-s)>1e-6)&&this._append`L${u},${s}`,r&&(f<0&&(f=f%eB+eB),f>eR?this._append`A${r},${r},0,1,${l},${t-a},${e-c}A${r},${r},0,1,${l},${this._x1=u},${this._y1=s}`:f>1e-6&&this._append`A${r},${r},0,${+(f>=eI)},${l},${this._x1=t+r*Math.cos(i)},${this._y1=e+r*Math.sin(i)}`)}rect(t,e,r,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function eU(t){let e=3;return t.digits=function(r){if(!arguments.length)return e;if(null==r)e=null;else{let t=Math.floor(r);if(!(t>=0))throw RangeError(`invalid digits: ${r}`);e=t}return t},()=>new ez(e)}function eF(t){return(eF="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}ez.prototype,ew(3),ew(3);var e$=["type","size","sizeType"];function eq(){return(eq=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function eW(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function eG(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eW(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=eF(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eF(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eF(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eW(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var eX={symbolCircle:eS,symbolCross:{draw(t,e){let r=ew(e/5)/2;t.moveTo(-3*r,-r),t.lineTo(-r,-r),t.lineTo(-r,-3*r),t.lineTo(r,-3*r),t.lineTo(r,-r),t.lineTo(3*r,-r),t.lineTo(3*r,r),t.lineTo(r,r),t.lineTo(r,3*r),t.lineTo(-r,3*r),t.lineTo(-r,r),t.lineTo(-3*r,r),t.closePath()}},symbolDiamond:{draw(t,e){let r=ew(e/eA),n=r*eP;t.moveTo(0,-r),t.lineTo(n,0),t.lineTo(0,r),t.lineTo(-n,0),t.closePath()}},symbolSquare:{draw(t,e){let r=ew(e),n=-r/2;t.rect(n,n,r,r)}},symbolStar:{draw(t,e){let r=ew(.8908130915292852*e),n=ek*r,i=e_*r;t.moveTo(0,-r),t.lineTo(n,i);for(let e=1;e<5;++e){let o=ej*e/5,a=eg(o),c=ex(o);t.lineTo(c*r,-a*r),t.lineTo(a*n-c*i,c*n+a*i)}t.closePath()}},symbolTriangle:{draw(t,e){let r=-ew(e/(3*eN));t.moveTo(0,2*r),t.lineTo(-eN*r,-r),t.lineTo(eN*r,-r),t.closePath()}},symbolWye:{draw(t,e){let r=ew(e/eC),n=r/2,i=r*eT,o=r*eT+r,a=-n;t.moveTo(n,i),t.lineTo(n,o),t.lineTo(a,o),t.lineTo(-.5*n-eM*i,eM*n+-.5*i),t.lineTo(-.5*n-eM*o,eM*n+-.5*o),t.lineTo(-.5*a-eM*o,eM*a+-.5*o),t.lineTo(-.5*n+eM*i,-.5*i-eM*n),t.lineTo(-.5*n+eM*o,-.5*o-eM*n),t.lineTo(-.5*a+eM*o,-.5*o-eM*a),t.closePath()}}},eH=Math.PI/180,eV=function(t,e,r){if("area"===e)return t;switch(r){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var n=18*eH;return 1.25*t*t*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},eK=function(t){var e,r=t.type,n=void 0===r?"circle":r,i=t.size,o=void 0===i?64:i,c=t.sizeType,u=void 0===c?"area":c,s=eG(eG({},function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,e$)),{},{type:n,size:o,sizeType:u}),l=s.className,f=s.cx,p=s.cy,d=tP(s,!0);return f===+f&&p===+p&&o===+o?a().createElement("path",eq({},d,{className:(0,A.A)("recharts-symbols",l),transform:"translate(".concat(f,", ").concat(p,")"),d:(e=eX["symbol".concat(eb()(n))]||eS,(function(t,e){let r=null,n=eU(i);function i(){let i;if(r||(r=i=n()),t.apply(this,arguments).draw(r,+e.apply(this,arguments)),i)return r=null,i+""||null}return t="function"==typeof t?t:eD(t||eS),e="function"==typeof e?e:eD(void 0===e?64:+e),i.type=function(e){return arguments.length?(t="function"==typeof e?e:eD(e),i):t},i.size=function(t){return arguments.length?(e="function"==typeof t?t:eD(+t),i):e},i.context=function(t){return arguments.length?(r=null==t?null:t,i):r},i})().type(e).size(eV(o,u,n))())})):null};function eY(t){return(eY="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function eZ(){return(eZ=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function eJ(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}eK.registerSymbol=function(t,e){eX["symbol".concat(eb()(t))]=e};function eQ(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(eQ=function(){return!!t})()}function e0(t){return(e0=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function e1(t,e){return(e1=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function e2(t,e,r){return(e=e5(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function e5(t){var e=function(t,e){if("object"!=eY(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eY(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eY(e)?e:e+""}var e4=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=e0(t),function(t,e){if(e&&("object"===eY(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,eQ()?Reflect.construct(t,e||[],e0(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&e1(r,t),e=[{key:"renderIcon",value:function(t){var e=this.props.inactiveColor,r=32/6,n=32/3,i=t.inactive?e:t.color;if("plainline"===t.type)return a().createElement("line",{strokeWidth:4,fill:"none",stroke:i,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===t.type)return a().createElement("path",{strokeWidth:4,fill:"none",stroke:i,d:"M0,".concat(16,"h").concat(n,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(2*n,",").concat(16,"\n            H").concat(32,"M").concat(2*n,",").concat(16,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(n,",").concat(16),className:"recharts-legend-icon"});if("rect"===t.type)return a().createElement("path",{stroke:"none",fill:i,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(a().isValidElement(t.legendIcon)){var o=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eJ(Object(r),!0).forEach(function(e){e2(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eJ(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({},t);return delete o.legendIcon,a().cloneElement(t.legendIcon,o)}return a().createElement(eK,{fill:i,cx:16,cy:16,size:32,sizeType:"diameter",type:t.type})}},{key:"renderItems",value:function(){var t=this,e=this.props,r=e.payload,n=e.iconSize,i=e.layout,o=e.formatter,c=e.inactiveColor,u={x:0,y:0,width:32,height:32},s={display:"horizontal"===i?"inline-block":"block",marginRight:10},l={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map(function(e,r){var i=e.formatter||o,f=(0,A.A)(e2(e2({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",e.inactive));if("none"===e.type)return null;var p=Q()(e.value)?null:e.value;K(!Q()(e.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var d=e.inactive?c:e.color;return a().createElement("li",eZ({className:f,style:s,key:"legend-item-".concat(r)},tl(t.props,e,r)),a().createElement(t$,{width:n,height:n,viewBox:u,style:l},t.renderIcon(e)),a().createElement("span",{className:"recharts-legend-item-text",style:{color:d}},i?i(p,e,r):p))})}},{key:"render",value:function(){var t=this.props,e=t.payload,r=t.layout,n=t.align;return e&&e.length?a().createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===r?n:"left"}},this.renderItems()):null}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,e5(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(o.PureComponent);function e3(t){return(e3="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}e2(e4,"displayName","Legend"),e2(e4,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var e6=["ref"];function e8(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function e7(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?e8(Object(r),!0).forEach(function(e){rn(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):e8(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function e9(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ri(n.key),n)}}function rt(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(rt=function(){return!!t})()}function re(t){return(re=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function rr(t,e){return(rr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function rn(t,e,r){return(e=ri(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ri(t){var e=function(t,e){if("object"!=e3(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=e3(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==e3(e)?e:e+""}function ro(t){return t.value}var ra=function(t){var e,r;function n(){var t,e,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var i=arguments.length,o=Array(i),a=0;a<i;a++)o[a]=arguments[a];return e=n,r=[].concat(o),e=re(e),rn(t=function(t,e){if(e&&("object"===e3(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,rt()?Reflect.construct(e,r||[],re(this).constructor):e.apply(this,r)),"lastBoundingBox",{width:-1,height:-1}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&rr(n,t),e=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();return t.height=this.wrapperNode.offsetHeight,t.width=this.wrapperNode.offsetWidth,t}return null}},{key:"updateBBox",value:function(){var t=this.props.onBBoxUpdate,e=this.getBBox();e?(Math.abs(e.width-this.lastBoundingBox.width)>1||Math.abs(e.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=e.width,this.lastBoundingBox.height=e.height,t&&t(e)):(-1!==this.lastBoundingBox.width||-1!==this.lastBoundingBox.height)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,t&&t(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?e7({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(t){var e,r,n=this.props,i=n.layout,o=n.align,a=n.verticalAlign,c=n.margin,u=n.chartWidth,s=n.chartHeight;return t&&(void 0!==t.left&&null!==t.left||void 0!==t.right&&null!==t.right)||(e="center"===o&&"vertical"===i?{left:((u||0)-this.getBBoxSnapshot().width)/2}:"right"===o?{right:c&&c.right||0}:{left:c&&c.left||0}),t&&(void 0!==t.top&&null!==t.top||void 0!==t.bottom&&null!==t.bottom)||(r="middle"===a?{top:((s||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:c&&c.bottom||0}:{top:c&&c.top||0}),e7(e7({},e),r)}},{key:"render",value:function(){var t=this,e=this.props,r=e.content,n=e.width,i=e.height,o=e.wrapperStyle,c=e.payloadUniqBy,u=e.payload,s=e7(e7({position:"absolute",width:n||"auto",height:i||"auto"},this.getDefaultPosition(o)),o);return a().createElement("div",{className:"recharts-legend-wrapper",style:s,ref:function(e){t.wrapperNode=e}},function(t,e){if(a().isValidElement(t))return a().cloneElement(t,e);if("function"==typeof t)return a().createElement(t,e);e.ref;var r=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(e,e6);return a().createElement(e4,r)}(r,e7(e7({},this.props),{},{payload:ea(u,c,ro)})))}}],r=[{key:"getWithHeight",value:function(t,e){var r=e7(e7({},this.defaultProps),t.props).layout;return"vertical"===r&&z(t.props.height)?{height:t.props.height}:"horizontal"===r?{width:t.props.width||e}:null}}],e&&e9(n.prototype,e),r&&e9(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(o.PureComponent);function rc(){return(rc=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}rn(ra,"displayName","Legend"),rn(ra,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var ru=function(t){var e=t.cx,r=t.cy,n=t.r,i=t.className,o=(0,A.A)("recharts-dot",i);return e===+e&&r===+r&&n===+n?a().createElement("circle",rc({},tP(t,!1),ts(t),{className:o,cx:e,cy:r,r:n})):null},rs=r(87955),rl=r.n(rs),rf=Object.getOwnPropertyNames,rp=Object.getOwnPropertySymbols,rd=Object.prototype.hasOwnProperty;function rh(t,e){return function(r,n,i){return t(r,n,i)&&e(r,n,i)}}function ry(t){return function(e,r,n){if(!e||!r||"object"!=typeof e||"object"!=typeof r)return t(e,r,n);var i=n.cache,o=i.get(e),a=i.get(r);if(o&&a)return o===r&&a===e;i.set(e,r),i.set(r,e);var c=t(e,r,n);return i.delete(e),i.delete(r),c}}function rv(t){return rf(t).concat(rp(t))}var rm=Object.hasOwn||function(t,e){return rd.call(t,e)};function rb(t,e){return t===e||!t&&!e&&t!=t&&e!=e}var rg=Object.getOwnPropertyDescriptor,rx=Object.keys;function rw(t,e,r){var n=t.length;if(e.length!==n)return!1;for(;n-- >0;)if(!r.equals(t[n],e[n],n,n,t,e,r))return!1;return!0}function rO(t,e){return rb(t.getTime(),e.getTime())}function rj(t,e){return t.name===e.name&&t.message===e.message&&t.cause===e.cause&&t.stack===e.stack}function rS(t,e){return t===e}function rP(t,e,r){var n,i,o=t.size;if(o!==e.size)return!1;if(!o)return!0;for(var a=Array(o),c=t.entries(),u=0;(n=c.next())&&!n.done;){for(var s=e.entries(),l=!1,f=0;(i=s.next())&&!i.done;){if(a[f]){f++;continue}var p=n.value,d=i.value;if(r.equals(p[0],d[0],u,f,t,e,r)&&r.equals(p[1],d[1],p[0],d[0],t,e,r)){l=a[f]=!0;break}f++}if(!l)return!1;u++}return!0}function rA(t,e,r){var n=rx(t),i=n.length;if(rx(e).length!==i)return!1;for(;i-- >0;)if(!rC(t,e,r,n[i]))return!1;return!0}function rE(t,e,r){var n,i,o,a=rv(t),c=a.length;if(rv(e).length!==c)return!1;for(;c-- >0;)if(!rC(t,e,r,n=a[c])||(i=rg(t,n),o=rg(e,n),(i||o)&&(!i||!o||i.configurable!==o.configurable||i.enumerable!==o.enumerable||i.writable!==o.writable)))return!1;return!0}function rk(t,e){return rb(t.valueOf(),e.valueOf())}function r_(t,e){return t.source===e.source&&t.flags===e.flags}function rN(t,e,r){var n,i,o=t.size;if(o!==e.size)return!1;if(!o)return!0;for(var a=Array(o),c=t.values();(n=c.next())&&!n.done;){for(var u=e.values(),s=!1,l=0;(i=u.next())&&!i.done;){if(!a[l]&&r.equals(n.value,i.value,n.value,i.value,t,e,r)){s=a[l]=!0;break}l++}if(!s)return!1}return!0}function rM(t,e){var r=t.length;if(e.length!==r)return!1;for(;r-- >0;)if(t[r]!==e[r])return!1;return!0}function rT(t,e){return t.hostname===e.hostname&&t.pathname===e.pathname&&t.protocol===e.protocol&&t.port===e.port&&t.hash===e.hash&&t.username===e.username&&t.password===e.password}function rC(t,e,r,n){return("_owner"===n||"__o"===n||"__v"===n)&&(!!t.$$typeof||!!e.$$typeof)||rm(e,n)&&r.equals(t[n],e[n],n,n,t,e,r)}var rD=Array.isArray,rI="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,rB=Object.assign,rR=Object.prototype.toString.call.bind(Object.prototype.toString),rL=rz();function rz(t){void 0===t&&(t={});var e,r,n,i,o,a,c,u,s,l,f,p,d,h=t.circular,y=t.createInternalComparator,v=t.createState,m=t.strict,b=(r=(e=function(t){var e=t.circular,r=t.createCustomConfig,n=t.strict,i={areArraysEqual:n?rE:rw,areDatesEqual:rO,areErrorsEqual:rj,areFunctionsEqual:rS,areMapsEqual:n?rh(rP,rE):rP,areNumbersEqual:rb,areObjectsEqual:n?rE:rA,arePrimitiveWrappersEqual:rk,areRegExpsEqual:r_,areSetsEqual:n?rh(rN,rE):rN,areTypedArraysEqual:n?rE:rM,areUrlsEqual:rT};if(r&&(i=rB({},i,r(i))),e){var o=ry(i.areArraysEqual),a=ry(i.areMapsEqual),c=ry(i.areObjectsEqual),u=ry(i.areSetsEqual);i=rB({},i,{areArraysEqual:o,areMapsEqual:a,areObjectsEqual:c,areSetsEqual:u})}return i}(t)).areArraysEqual,n=e.areDatesEqual,i=e.areErrorsEqual,o=e.areFunctionsEqual,a=e.areMapsEqual,c=e.areNumbersEqual,u=e.areObjectsEqual,s=e.arePrimitiveWrappersEqual,l=e.areRegExpsEqual,f=e.areSetsEqual,p=e.areTypedArraysEqual,d=e.areUrlsEqual,function(t,e,h){if(t===e)return!0;if(null==t||null==e)return!1;var y=typeof t;if(y!==typeof e)return!1;if("object"!==y)return"number"===y?c(t,e,h):"function"===y&&o(t,e,h);var v=t.constructor;if(v!==e.constructor)return!1;if(v===Object)return u(t,e,h);if(rD(t))return r(t,e,h);if(null!=rI&&rI(t))return p(t,e,h);if(v===Date)return n(t,e,h);if(v===RegExp)return l(t,e,h);if(v===Map)return a(t,e,h);if(v===Set)return f(t,e,h);var m=rR(t);return"[object Date]"===m?n(t,e,h):"[object RegExp]"===m?l(t,e,h):"[object Map]"===m?a(t,e,h):"[object Set]"===m?f(t,e,h):"[object Object]"===m?"function"!=typeof t.then&&"function"!=typeof e.then&&u(t,e,h):"[object URL]"===m?d(t,e,h):"[object Error]"===m?i(t,e,h):"[object Arguments]"===m?u(t,e,h):("[object Boolean]"===m||"[object Number]"===m||"[object String]"===m)&&s(t,e,h)}),g=y?y(b):function(t,e,r,n,i,o,a){return b(t,e,a)};return function(t){var e=t.circular,r=t.comparator,n=t.createState,i=t.equals,o=t.strict;if(n)return function(t,a){var c=n(),u=c.cache;return r(t,a,{cache:void 0===u?e?new WeakMap:void 0:u,equals:i,meta:c.meta,strict:o})};if(e)return function(t,e){return r(t,e,{cache:new WeakMap,equals:i,meta:void 0,strict:o})};var a={cache:void 0,equals:i,meta:void 0,strict:o};return function(t,e){return r(t,e,a)}}({circular:void 0!==h&&h,comparator:b,createState:v,equals:g,strict:void 0!==m&&m})}function rU(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(i){if(r<0&&(r=i),i-r>e)t(i),r=-1;else{var o;o=n,"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(o)}})}function rF(t){return(rF="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function r$(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function rq(t){return(rq="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rW(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function rG(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?rW(Object(r),!0).forEach(function(e){rX(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rW(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function rX(t,e,r){var n;return(n=function(t,e){if("object"!==rq(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==rq(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===rq(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}rz({strict:!0}),rz({circular:!0}),rz({circular:!0,strict:!0}),rz({createInternalComparator:function(){return rb}}),rz({strict:!0,createInternalComparator:function(){return rb}}),rz({circular:!0,createInternalComparator:function(){return rb}}),rz({circular:!0,createInternalComparator:function(){return rb},strict:!0});var rH=function(t){return t},rV=function(t,e){return Object.keys(e).reduce(function(r,n){return rG(rG({},r),{},rX({},n,t(n,e[n])))},{})},rK=function(t,e,r){return t.map(function(t){return"".concat(t.replace(/([A-Z])/g,function(t){return"-".concat(t.toLowerCase())})," ").concat(e,"ms ").concat(r)}).join(",")},rY=function(t,e,r,n,i,o,a,c){};function rZ(t,e){if(t){if("string"==typeof t)return rJ(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rJ(t,e)}}function rJ(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var rQ=function(t,e){return[0,3*t,3*e-6*t,3*t-3*e+1]},r0=function(t,e){return t.map(function(t,r){return t*Math.pow(e,r)}).reduce(function(t,e){return t+e})},r1=function(t,e){return function(r){return r0(rQ(t,e),r)}},r2=function(){for(var t,e,r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];var o=n[0],a=n[1],c=n[2],u=n[3];if(1===n.length)switch(n[0]){case"linear":o=0,a=0,c=1,u=1;break;case"ease":o=.25,a=.1,c=.25,u=1;break;case"ease-in":o=.42,a=0,c=1,u=1;break;case"ease-out":o=.42,a=0,c=.58,u=1;break;case"ease-in-out":o=0,a=0,c=.58,u=1;break;default:var s=n[0].split("(");if("cubic-bezier"===s[0]&&4===s[1].split(")")[0].split(",").length){var l,f=function(t){if(Array.isArray(t))return t}(l=s[1].split(")")[0].split(",").map(function(t){return parseFloat(t)}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,s=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw i}}return c}}(l,4)||rZ(l,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();o=f[0],a=f[1],c=f[2],u=f[3]}else rY(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",n)}rY([o,c,a,u].every(function(t){return"number"==typeof t&&t>=0&&t<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",n);var p=r1(o,c),d=r1(a,u),h=(t=o,e=c,function(r){var n;return r0([].concat(function(t){if(Array.isArray(t))return rJ(t)}(n=rQ(t,e).map(function(t,e){return t*e}).slice(1))||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(n)||rZ(n)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[0]),r)}),y=function(t){for(var e=t>1?1:t,r=e,n=0;n<8;++n){var i,o=p(r)-e,a=h(r);if(1e-4>Math.abs(o-e)||a<1e-4)break;r=(i=r-o/a)>1?1:i<0?0:i}return d(r)};return y.isStepper=!1,y},r5=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiff,r=void 0===e?100:e,n=t.damping,i=void 0===n?8:n,o=t.dt,a=void 0===o?17:o,c=function(t,e,n){var o=n+(-(t-e)*r-n*i)*a/1e3,c=n*a/1e3+t;return 1e-4>Math.abs(c-e)&&1e-4>Math.abs(o)?[e,0]:[c,o]};return c.isStepper=!0,c.dt=a,c},r4=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return r2(n);case"spring":return r5();default:if("cubic-bezier"===n.split("(")[0])return r2(n);rY(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",e)}return"function"==typeof n?n:(rY(!1,"[configEasing]: first argument type should be function or string, instead received %s",e),null)};function r3(t){return(r3="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function r6(t){return function(t){if(Array.isArray(t))return ne(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||nt(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r8(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function r7(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?r8(Object(r),!0).forEach(function(e){r9(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):r8(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function r9(t,e,r){var n;return(n=function(t,e){if("object"!==r3(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==r3(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===r3(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function nt(t,e){if(t){if("string"==typeof t)return ne(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ne(t,e)}}function ne(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var nr=function(t,e,r){return t+(e-t)*r},nn=function(t){return t.from!==t.to},ni=function t(e,r,n){var i=rV(function(t,r){if(nn(r)){var n,i=function(t){if(Array.isArray(t))return t}(n=e(r.from,r.to,r.velocity))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,s=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw i}}return c}}(n,2)||nt(n,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),o=i[0],a=i[1];return r7(r7({},r),{},{from:o,velocity:a})}return r},r);return n<1?rV(function(t,e){return nn(e)?r7(r7({},e),{},{velocity:nr(e.velocity,i[t].velocity,n),from:nr(e.from,i[t].from,n)}):e},r):t(e,i,n-1)};let no=function(t,e,r,n,i){var o,a,c=[Object.keys(t),Object.keys(e)].reduce(function(t,e){return t.filter(function(t){return e.includes(t)})}),u=c.reduce(function(r,n){return r7(r7({},r),{},r9({},n,[t[n],e[n]]))},{}),s=c.reduce(function(r,n){return r7(r7({},r),{},r9({},n,{from:t[n],velocity:0,to:e[n]}))},{}),l=-1,f=function(){return null};return f=r.isStepper?function(n){o||(o=n);var a=(n-o)/r.dt;s=ni(r,s,a),i(r7(r7(r7({},t),e),rV(function(t,e){return e.from},s))),o=n,Object.values(s).filter(nn).length&&(l=requestAnimationFrame(f))}:function(o){a||(a=o);var c=(o-a)/n,s=rV(function(t,e){return nr.apply(void 0,r6(e).concat([r(c)]))},u);if(i(r7(r7(r7({},t),e),s)),c<1)l=requestAnimationFrame(f);else{var p=rV(function(t,e){return nr.apply(void 0,r6(e).concat([r(1)]))},u);i(r7(r7(r7({},t),e),p))}},function(){return requestAnimationFrame(f),function(){cancelAnimationFrame(l)}}};function na(t){return(na="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var nc=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function nu(t){return function(t){if(Array.isArray(t))return ns(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return ns(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ns(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ns(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nl(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nf(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nl(Object(r),!0).forEach(function(e){np(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nl(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function np(t,e,r){return(e=nd(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function nd(t){var e=function(t,e){if("object"!==na(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==na(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===na(e)?e:String(e)}function nh(t,e){return(nh=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ny(t,e){if(e&&("object"===na(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return nv(t)}function nv(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function nm(t){return(nm=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var nb=function(t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");i.prototype=Object.create(t&&t.prototype,{constructor:{value:i,writable:!0,configurable:!0}}),Object.defineProperty(i,"prototype",{writable:!1}),t&&nh(i,t);var e,r,n=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,r=nm(i);return t=e?Reflect.construct(r,arguments,nm(this).constructor):r.apply(this,arguments),ny(this,t)});function i(t,e){if(!(this instanceof i))throw TypeError("Cannot call a class as a function");var r=n.call(this,t,e),o=r.props,a=o.isActive,c=o.attributeName,u=o.from,s=o.to,l=o.steps,f=o.children,p=o.duration;if(r.handleStyleChange=r.handleStyleChange.bind(nv(r)),r.changeStyle=r.changeStyle.bind(nv(r)),!a||p<=0)return r.state={style:{}},"function"==typeof f&&(r.state={style:s}),ny(r);if(l&&l.length)r.state={style:l[0].style};else if(u){if("function"==typeof f)return r.state={style:u},ny(r);r.state={style:c?np({},c,u):u}}else r.state={style:{}};return r}return r=[{key:"componentDidMount",value:function(){var t=this.props,e=t.isActive,r=t.canBegin;this.mounted=!0,e&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(t){var e=this.props,r=e.isActive,n=e.canBegin,i=e.attributeName,o=e.shouldReAnimate,a=e.to,c=e.from,u=this.state.style;if(n){if(!r){var s={style:i?np({},i,a):a};this.state&&u&&(i&&u[i]!==a||!i&&u!==a)&&this.setState(s);return}if(!rL(t.to,a)||!t.canBegin||!t.isActive){var l=!t.canBegin||!t.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var f=l||o?c:t.to;if(this.state&&u){var p={style:i?np({},i,f):f};(i&&u[i]!==f||!i&&u!==f)&&this.setState(p)}this.runAnimation(nf(nf({},this.props),{},{from:f,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var t=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}},{key:"handleStyleChange",value:function(t){this.changeStyle(t)}},{key:"changeStyle",value:function(t){this.mounted&&this.setState({style:t})}},{key:"runJSAnimation",value:function(t){var e=this,r=t.from,n=t.to,i=t.duration,o=t.easing,a=t.begin,c=t.onAnimationEnd,u=t.onAnimationStart,s=no(r,n,r4(o),i,this.changeStyle);this.manager.start([u,a,function(){e.stopJSAnimation=s()},i,c])}},{key:"runStepAnimation",value:function(t){var e=this,r=t.steps,n=t.begin,i=t.onAnimationStart,o=r[0],a=o.style,c=o.duration;return this.manager.start([i].concat(nu(r.reduce(function(t,n,i){if(0===i)return t;var o=n.duration,a=n.easing,c=void 0===a?"ease":a,u=n.style,s=n.properties,l=n.onAnimationEnd,f=i>0?r[i-1]:n,p=s||Object.keys(u);if("function"==typeof c||"spring"===c)return[].concat(nu(t),[e.runJSAnimation.bind(e,{from:f.style,to:u,duration:o,easing:c}),o]);var d=rK(p,o,c),h=nf(nf(nf({},f.style),u),{},{transition:d});return[].concat(nu(t),[h,o,l]).filter(rH)},[a,Math.max(void 0===c?0:c,n)])),[t.onAnimationEnd]))}},{key:"runAnimation",value:function(t){this.manager||(this.manager=(r=function(){return null},n=!1,i=function t(e){if(!n){if(Array.isArray(e)){if(!e.length)return;var i=function(t){if(Array.isArray(t))return t}(e)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(e)||function(t,e){if(t){if("string"==typeof t)return r$(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return r$(t,e)}}(e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),o=i[0],a=i.slice(1);return"number"==typeof o?void rU(t.bind(null,a),o):(t(o),void rU(t.bind(null,a)))}"object"===rF(e)&&r(e),"function"==typeof e&&e()}},{stop:function(){n=!0},start:function(t){n=!1,i(t)},subscribe:function(t){return r=t,function(){r=function(){return null}}}}));var e,r,n,i,o=t.begin,a=t.duration,c=t.attributeName,u=t.to,s=t.easing,l=t.onAnimationStart,f=t.onAnimationEnd,p=t.steps,d=t.children,h=this.manager;if(this.unSubscribe=h.subscribe(this.handleStyleChange),"function"==typeof s||"function"==typeof d||"spring"===s)return void this.runJSAnimation(t);if(p.length>1)return void this.runStepAnimation(t);var y=c?np({},c,u):u,v=rK(Object.keys(y),a,s);h.start([l,o,nf(nf({},y),{},{transition:v}),a,f])}},{key:"render",value:function(){var t=this.props,e=t.children,r=(t.begin,t.duration),n=(t.attributeName,t.easing,t.isActive),i=(t.steps,t.from,t.to,t.canBegin,t.onAnimationEnd,t.shouldReAnimate,t.onAnimationReStart,function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,nc)),c=o.Children.count(e),u=this.state.style;if("function"==typeof e)return e(u);if(!n||0===c||r<=0)return e;var s=function(t){var e=t.props,r=e.style,n=e.className;return(0,o.cloneElement)(t,nf(nf({},i),{},{style:nf(nf({},void 0===r?{}:r),u),className:n}))};return 1===c?s(o.Children.only(e)):a().createElement("div",null,o.Children.map(e,function(t){return s(t)}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,nd(n.key),n)}}(i.prototype,r),Object.defineProperty(i,"prototype",{writable:!1}),i}(o.PureComponent);function ng(t){return(ng="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nx(){return(nx=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function nw(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nO(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nj(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nO(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=ng(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ng(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ng(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nO(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}nb.displayName="Animate",nb.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},nb.propTypes={from:rl().oneOfType([rl().object,rl().string]),to:rl().oneOfType([rl().object,rl().string]),attributeName:rl().string,duration:rl().number,begin:rl().number,easing:rl().oneOfType([rl().string,rl().func]),steps:rl().arrayOf(rl().shape({duration:rl().number.isRequired,style:rl().object.isRequired,easing:rl().oneOfType([rl().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),rl().func]),properties:rl().arrayOf("string"),onAnimationEnd:rl().func})),children:rl().oneOfType([rl().node,rl().func]),isActive:rl().bool,canBegin:rl().bool,onAnimationEnd:rl().func,shouldReAnimate:rl().bool,onAnimationStart:rl().func,onAnimationReStart:rl().func};var nS=function(t,e,r,n,i){var o,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),c=n>=0?1:-1,u=r>=0?1:-1,s=+(n>=0&&r>=0||n<0&&r<0);if(a>0&&i instanceof Array){for(var l=[0,0,0,0],f=0;f<4;f++)l[f]=i[f]>a?a:i[f];o="M".concat(t,",").concat(e+c*l[0]),l[0]>0&&(o+="A ".concat(l[0],",").concat(l[0],",0,0,").concat(s,",").concat(t+u*l[0],",").concat(e)),o+="L ".concat(t+r-u*l[1],",").concat(e),l[1]>0&&(o+="A ".concat(l[1],",").concat(l[1],",0,0,").concat(s,",\n        ").concat(t+r,",").concat(e+c*l[1])),o+="L ".concat(t+r,",").concat(e+n-c*l[2]),l[2]>0&&(o+="A ".concat(l[2],",").concat(l[2],",0,0,").concat(s,",\n        ").concat(t+r-u*l[2],",").concat(e+n)),o+="L ".concat(t+u*l[3],",").concat(e+n),l[3]>0&&(o+="A ".concat(l[3],",").concat(l[3],",0,0,").concat(s,",\n        ").concat(t,",").concat(e+n-c*l[3])),o+="Z"}else if(a>0&&i===+i&&i>0){var p=Math.min(a,i);o="M ".concat(t,",").concat(e+c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(s,",").concat(t+u*p,",").concat(e,"\n            L ").concat(t+r-u*p,",").concat(e,"\n            A ").concat(p,",").concat(p,",0,0,").concat(s,",").concat(t+r,",").concat(e+c*p,"\n            L ").concat(t+r,",").concat(e+n-c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(s,",").concat(t+r-u*p,",").concat(e+n,"\n            L ").concat(t+u*p,",").concat(e+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(s,",").concat(t,",").concat(e+n-c*p," Z")}else o="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return o},nP=function(t,e){if(!t||!e)return!1;var r=t.x,n=t.y,i=e.x,o=e.y,a=e.width,c=e.height;if(Math.abs(a)>0&&Math.abs(c)>0){var u=Math.min(i,i+a),s=Math.max(i,i+a),l=Math.min(o,o+c),f=Math.max(o,o+c);return r>=u&&r<=s&&n>=l&&n<=f}return!1},nA={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},nE=function(t){var e,r=nj(nj({},nA),t),n=(0,o.useRef)(),i=function(t){if(Array.isArray(t))return t}(e=(0,o.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,s=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw i}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return nw(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nw(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),c=i[0],u=i[1];(0,o.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var t=n.current.getTotalLength();t&&u(t)}catch(t){}},[]);var s=r.x,l=r.y,f=r.width,p=r.height,d=r.radius,h=r.className,y=r.animationEasing,v=r.animationDuration,m=r.animationBegin,b=r.isAnimationActive,g=r.isUpdateAnimationActive;if(s!==+s||l!==+l||f!==+f||p!==+p||0===f||0===p)return null;var x=(0,A.A)("recharts-rectangle",h);return g?a().createElement(nb,{canBegin:c>0,from:{width:f,height:p,x:s,y:l},to:{width:f,height:p,x:s,y:l},duration:v,animationEasing:y,isActive:g},function(t){var e=t.width,i=t.height,o=t.x,u=t.y;return a().createElement(nb,{canBegin:c>0,from:"0px ".concat(-1===c?1:c,"px"),to:"".concat(c,"px 0px"),attributeName:"strokeDasharray",begin:m,duration:v,isActive:b,easing:y},a().createElement("path",nx({},tP(r,!0),{className:x,d:nS(o,u,e,i,d),ref:n})))}):a().createElement("path",nx({},tP(r,!0),{className:x,d:nS(s,l,f,p,d)}))};function nk(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function n_(t,e){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof e?this.interpolator(e):this.range(e)}return this}class nN extends Map{constructor(t,e=nT){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(let[e,r]of t)this.set(e,r)}get(t){return super.get(nM(this,t))}has(t){return super.has(nM(this,t))}set(t,e){return super.set(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):(t.set(n,r),r)}(this,t),e)}delete(t){return super.delete(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)&&(r=t.get(n),t.delete(n)),r}(this,t))}}function nM({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):r}function nT(t){return null!==t&&"object"==typeof t?t.valueOf():t}let nC=Symbol("implicit");function nD(){var t=new nN,e=[],r=[],n=nC;function i(i){let o=t.get(i);if(void 0===o){if(n!==nC)return n;t.set(i,o=e.push(i)-1)}return r[o%r.length]}return i.domain=function(r){if(!arguments.length)return e.slice();for(let n of(e=[],t=new nN,r))t.has(n)||t.set(n,e.push(n)-1);return i},i.range=function(t){return arguments.length?(r=Array.from(t),i):r.slice()},i.unknown=function(t){return arguments.length?(n=t,i):n},i.copy=function(){return nD(e,r).unknown(n)},nk.apply(i,arguments),i}function nI(){var t,e,r=nD().unknown(void 0),n=r.domain,i=r.range,o=0,a=1,c=!1,u=0,s=0,l=.5;function f(){var r=n().length,f=a<o,p=f?a:o,d=f?o:a;t=(d-p)/Math.max(1,r-u+2*s),c&&(t=Math.floor(t)),p+=(d-p-t*(r-u))*l,e=t*(1-u),c&&(p=Math.round(p),e=Math.round(e));var h=(function(t,e,r){t*=1,e*=1,r=(i=arguments.length)<2?(e=t,t=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((e-t)/r)),o=Array(i);++n<i;)o[n]=t+n*r;return o})(r).map(function(e){return p+t*e});return i(f?h.reverse():h)}return delete r.unknown,r.domain=function(t){return arguments.length?(n(t),f()):n()},r.range=function(t){return arguments.length?([o,a]=t,o*=1,a*=1,f()):[o,a]},r.rangeRound=function(t){return[o,a]=t,o*=1,a*=1,c=!0,f()},r.bandwidth=function(){return e},r.step=function(){return t},r.round=function(t){return arguments.length?(c=!!t,f()):c},r.padding=function(t){return arguments.length?(u=Math.min(1,s=+t),f()):u},r.paddingInner=function(t){return arguments.length?(u=Math.min(1,t),f()):u},r.paddingOuter=function(t){return arguments.length?(s=+t,f()):s},r.align=function(t){return arguments.length?(l=Math.max(0,Math.min(1,t)),f()):l},r.copy=function(){return nI(n(),[o,a]).round(c).paddingInner(u).paddingOuter(s).align(l)},nk.apply(f(),arguments)}function nB(){return function t(e){var r=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return t(r())},e}(nI.apply(null,arguments).paddingInner(1))}function nR(t){return(nR="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nL(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nz(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nL(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=nR(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nR(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nR(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nL(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function nU(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var nF={widthCache:{},cacheCount:0},n$={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},nq="recharts_measurement_span",nW=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==t||en.isSsr)return{width:0,height:0};var n=(Object.keys(e=nz({},r)).forEach(function(t){e[t]||delete e[t]}),e),i=JSON.stringify({text:t,copyStyle:n});if(nF.widthCache[i])return nF.widthCache[i];try{var o=document.getElementById(nq);o||((o=document.createElement("span")).setAttribute("id",nq),o.setAttribute("aria-hidden","true"),document.body.appendChild(o));var a=nz(nz({},n$),n);Object.assign(o.style,a),o.textContent="".concat(t);var c=o.getBoundingClientRect(),u={width:c.width,height:c.height};return nF.widthCache[i]=u,++nF.cacheCount>2e3&&(nF.cacheCount=0,nF.widthCache={}),u}catch(t){return{width:0,height:0}}};function nG(t){return(nG="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nX(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,s=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw i}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return nH(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nH(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nH(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nV(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,function(t){var e=function(t,e){if("object"!=nG(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nG(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nG(e)?e:e+""}(n.key),n)}}var nK=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nY=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nZ=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,nJ=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,nQ={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},n0=Object.keys(nQ),n1=function(){var t,e;function r(t,e){if(!(this instanceof r))throw TypeError("Cannot call a class as a function");this.num=t,this.unit=e,this.num=t,this.unit=e,Number.isNaN(t)&&(this.unit=""),""===e||nZ.test(e)||(this.num=NaN,this.unit=""),n0.includes(e)&&(this.num=t*nQ[e],this.unit="px")}return t=[{key:"add",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num+t.num,this.unit)}},{key:"subtract",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num-t.num,this.unit)}},{key:"multiply",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num*t.num,this.unit||t.unit)}},{key:"divide",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num/t.num,this.unit||t.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],e=[{key:"parse",value:function(t){var e,n=nX(null!=(e=nJ.exec(t))?e:[],3),i=n[1],o=n[2];return new r(parseFloat(i),null!=o?o:"")}}],t&&nV(r.prototype,t),e&&nV(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();function n2(t){if(t.includes("NaN"))return"NaN";for(var e=t;e.includes("*")||e.includes("/");){var r,n=nX(null!=(r=nK.exec(e))?r:[],4),i=n[1],o=n[2],a=n[3],c=n1.parse(null!=i?i:""),u=n1.parse(null!=a?a:""),s="*"===o?c.multiply(u):c.divide(u);if(s.isNaN())return"NaN";e=e.replace(nK,s.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var l,f=nX(null!=(l=nY.exec(e))?l:[],4),p=f[1],d=f[2],h=f[3],y=n1.parse(null!=p?p:""),v=n1.parse(null!=h?h:""),m="+"===d?y.add(v):y.subtract(v);if(m.isNaN())return"NaN";e=e.replace(nY,m.toString())}return e}var n5=/\(([^()]*)\)/;function n4(t){var e=function(t){try{var e;return e=t.replace(/\s+/g,""),e=function(t){for(var e=t;e.includes("(");){var r=nX(n5.exec(e),2)[1];e=e.replace(n5,n2(r))}return e}(e),e=n2(e)}catch(t){return"NaN"}}(t.slice(5,-1));return"NaN"===e?"":e}var n3=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],n6=["dx","dy","angle","className","breakAll"];function n8(){return(n8=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function n7(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function n9(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,s=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw i}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return it(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return it(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function it(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var ie=/[ \f\n\r\t\v\u2028\u2029]+/,ir=function(t){var e=t.children,r=t.breakAll,n=t.style;try{var i=[];Z()(e)||(i=r?e.toString().split(""):e.toString().split(ie));var o=i.map(function(t){return{word:t,width:nW(t,n).width}}),a=r?0:nW("\xa0",n).width;return{wordsWithComputedWidth:o,spaceWidth:a}}catch(t){return null}},ii=function(t,e,r,n,i){var o,a=t.maxLines,c=t.children,u=t.style,s=t.breakAll,l=z(a),f=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.reduce(function(t,e){var o=e.word,a=e.width,c=t[t.length-1];return c&&(null==n||i||c.width+a+r<Number(n))?(c.words.push(o),c.width+=a+r):t.push({words:[o],width:a}),t},[])},p=f(e);if(!l)return p;for(var d=function(t){var e=f(ir({breakAll:s,style:u,children:c.slice(0,t)+"…"}).wordsWithComputedWidth);return[e.length>a||e.reduce(function(t,e){return t.width>e.width?t:e}).width>Number(n),e]},h=0,y=c.length-1,v=0;h<=y&&v<=c.length-1;){var m=Math.floor((h+y)/2),b=n9(d(m-1),2),g=b[0],x=b[1],w=n9(d(m),1)[0];if(g||w||(h=m+1),g&&w&&(y=m-1),!g&&w){o=x;break}v++}return o||p},io=function(t){return[{words:Z()(t)?[]:t.toString().split(ie)}]},ia=function(t){var e=t.width,r=t.scaleToFit,n=t.children,i=t.style,o=t.breakAll,a=t.maxLines;if((e||r)&&!en.isSsr){var c=ir({breakAll:o,children:n,style:i});if(!c)return io(n);var u=c.wordsWithComputedWidth,s=c.spaceWidth;return ii({breakAll:o,children:n,maxLines:a,style:i},u,s,e,r)}return io(n)},ic="#808080",iu=function(t){var e,r=t.x,n=void 0===r?0:r,i=t.y,c=void 0===i?0:i,u=t.lineHeight,s=void 0===u?"1em":u,l=t.capHeight,f=void 0===l?"0.71em":l,p=t.scaleToFit,d=void 0!==p&&p,h=t.textAnchor,y=t.verticalAnchor,v=t.fill,m=void 0===v?ic:v,b=n7(t,n3),g=(0,o.useMemo)(function(){return ia({breakAll:b.breakAll,children:b.children,maxLines:b.maxLines,scaleToFit:d,style:b.style,width:b.width})},[b.breakAll,b.children,b.maxLines,d,b.style,b.width]),x=b.dx,w=b.dy,O=b.angle,j=b.className,S=b.breakAll,P=n7(b,n6);if(!U(n)||!U(c))return null;var E=n+(z(x)?x:0),k=c+(z(w)?w:0);switch(void 0===y?"end":y){case"start":e=n4("calc(".concat(f,")"));break;case"middle":e=n4("calc(".concat((g.length-1)/2," * -").concat(s," + (").concat(f," / 2))"));break;default:e=n4("calc(".concat(g.length-1," * -").concat(s,")"))}var _=[];if(d){var N=g[0].width,M=b.width;_.push("scale(".concat((z(M)?M/N:1)/N,")"))}return O&&_.push("rotate(".concat(O,", ").concat(E,", ").concat(k,")")),_.length&&(P.transform=_.join(" ")),a().createElement("text",n8({},tP(P,!0),{x:E,y:k,className:(0,A.A)("recharts-text",j),textAnchor:void 0===h?"start":h,fill:m.includes("url")?ic:m}),g.map(function(t,r){var n=t.words.join(S?"":" ");return a().createElement("tspan",{x:E,dy:0===r?e:s,key:"".concat(n,"-").concat(r)},n)}))};let is=Math.sqrt(50),il=Math.sqrt(10),ip=Math.sqrt(2);function id(t,e,r){let n,i,o,a=(e-t)/Math.max(0,r),c=Math.floor(Math.log10(a)),u=a/Math.pow(10,c),s=u>=is?10:u>=il?5:u>=ip?2:1;return(c<0?(n=Math.round(t*(o=Math.pow(10,-c)/s)),i=Math.round(e*o),n/o<t&&++n,i/o>e&&--i,o=-o):(n=Math.round(t/(o=Math.pow(10,c)*s)),i=Math.round(e/o),n*o<t&&++n,i*o>e&&--i),i<n&&.5<=r&&r<2)?id(t,e,2*r):[n,i,o]}function ih(t,e,r){if(e*=1,t*=1,!((r*=1)>0))return[];if(t===e)return[t];let n=e<t,[i,o,a]=n?id(e,t,r):id(t,e,r);if(!(o>=i))return[];let c=o-i+1,u=Array(c);if(n)if(a<0)for(let t=0;t<c;++t)u[t]=-((o-t)/a);else for(let t=0;t<c;++t)u[t]=(o-t)*a;else if(a<0)for(let t=0;t<c;++t)u[t]=-((i+t)/a);else for(let t=0;t<c;++t)u[t]=(i+t)*a;return u}function iy(t,e,r){return id(t*=1,e*=1,r*=1)[2]}function iv(t,e,r){e*=1,t*=1,r*=1;let n=e<t,i=n?iy(e,t,r):iy(t,e,r);return(n?-1:1)*(i<0?-(1/i):i)}function im(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function ib(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function ig(t){let e,r,n;function i(t,n,o=0,a=t.length){if(o<a){if(0!==e(n,n))return a;do{let e=o+a>>>1;0>r(t[e],n)?o=e+1:a=e}while(o<a)}return o}return 2!==t.length?(e=im,r=(e,r)=>im(t(e),r),n=(e,r)=>t(e)-r):(e=t===im||t===ib?t:ix,r=t,n=t),{left:i,center:function(t,e,r=0,o=t.length){let a=i(t,e,r,o-1);return a>r&&n(t[a-1],e)>-n(t[a],e)?a-1:a},right:function(t,n,i=0,o=t.length){if(i<o){if(0!==e(n,n))return o;do{let e=i+o>>>1;0>=r(t[e],n)?i=e+1:o=e}while(i<o)}return i}}}function ix(){return 0}function iw(t){return null===t?NaN:+t}let iO=ig(im),ij=iO.right;function iS(t,e,r){t.prototype=e.prototype=r,r.constructor=t}function iP(t,e){var r=Object.create(t.prototype);for(var n in e)r[n]=e[n];return r}function iA(){}iO.left,ig(iw).center;var iE="\\s*([+-]?\\d+)\\s*",ik="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",i_="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",iN=/^#([0-9a-f]{3,8})$/,iM=RegExp(`^rgb\\(${iE},${iE},${iE}\\)$`),iT=RegExp(`^rgb\\(${i_},${i_},${i_}\\)$`),iC=RegExp(`^rgba\\(${iE},${iE},${iE},${ik}\\)$`),iD=RegExp(`^rgba\\(${i_},${i_},${i_},${ik}\\)$`),iI=RegExp(`^hsl\\(${ik},${i_},${i_}\\)$`),iB=RegExp(`^hsla\\(${ik},${i_},${i_},${ik}\\)$`),iR={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function iL(){return this.rgb().formatHex()}function iz(){return this.rgb().formatRgb()}function iU(t){var e,r;return t=(t+"").trim().toLowerCase(),(e=iN.exec(t))?(r=e[1].length,e=parseInt(e[1],16),6===r?iF(e):3===r?new iW(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===r?i$(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===r?i$(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=iM.exec(t))?new iW(e[1],e[2],e[3],1):(e=iT.exec(t))?new iW(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=iC.exec(t))?i$(e[1],e[2],e[3],e[4]):(e=iD.exec(t))?i$(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=iI.exec(t))?iY(e[1],e[2]/100,e[3]/100,1):(e=iB.exec(t))?iY(e[1],e[2]/100,e[3]/100,e[4]):iR.hasOwnProperty(t)?iF(iR[t]):"transparent"===t?new iW(NaN,NaN,NaN,0):null}function iF(t){return new iW(t>>16&255,t>>8&255,255&t,1)}function i$(t,e,r,n){return n<=0&&(t=e=r=NaN),new iW(t,e,r,n)}function iq(t,e,r,n){var i;return 1==arguments.length?((i=t)instanceof iA||(i=iU(i)),i)?new iW((i=i.rgb()).r,i.g,i.b,i.opacity):new iW:new iW(t,e,r,null==n?1:n)}function iW(t,e,r,n){this.r=+t,this.g=+e,this.b=+r,this.opacity=+n}function iG(){return`#${iK(this.r)}${iK(this.g)}${iK(this.b)}`}function iX(){let t=iH(this.opacity);return`${1===t?"rgb(":"rgba("}${iV(this.r)}, ${iV(this.g)}, ${iV(this.b)}${1===t?")":`, ${t})`}`}function iH(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function iV(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function iK(t){return((t=iV(t))<16?"0":"")+t.toString(16)}function iY(t,e,r,n){return n<=0?t=e=r=NaN:r<=0||r>=1?t=e=NaN:e<=0&&(t=NaN),new iJ(t,e,r,n)}function iZ(t){if(t instanceof iJ)return new iJ(t.h,t.s,t.l,t.opacity);if(t instanceof iA||(t=iU(t)),!t)return new iJ;if(t instanceof iJ)return t;var e=(t=t.rgb()).r/255,r=t.g/255,n=t.b/255,i=Math.min(e,r,n),o=Math.max(e,r,n),a=NaN,c=o-i,u=(o+i)/2;return c?(a=e===o?(r-n)/c+(r<n)*6:r===o?(n-e)/c+2:(e-r)/c+4,c/=u<.5?o+i:2-o-i,a*=60):c=u>0&&u<1?0:a,new iJ(a,c,u,t.opacity)}function iJ(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}function iQ(t){return(t=(t||0)%360)<0?t+360:t}function i0(t){return Math.max(0,Math.min(1,t||0))}function i1(t,e,r){return(t<60?e+(r-e)*t/60:t<180?r:t<240?e+(r-e)*(240-t)/60:e)*255}function i2(t,e,r,n,i){var o=t*t,a=o*t;return((1-3*t+3*o-a)*e+(4-6*o+3*a)*r+(1+3*t+3*o-3*a)*n+a*i)/6}iS(iA,iU,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:iL,formatHex:iL,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return iZ(this).formatHsl()},formatRgb:iz,toString:iz}),iS(iW,iq,iP(iA,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new iW(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new iW(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new iW(iV(this.r),iV(this.g),iV(this.b),iH(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:iG,formatHex:iG,formatHex8:function(){return`#${iK(this.r)}${iK(this.g)}${iK(this.b)}${iK((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:iX,toString:iX})),iS(iJ,function(t,e,r,n){return 1==arguments.length?iZ(t):new iJ(t,e,r,null==n?1:n)},iP(iA,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new iJ(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new iJ(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*e,i=2*r-n;return new iW(i1(t>=240?t-240:t+120,i,n),i1(t,i,n),i1(t<120?t+240:t-120,i,n),this.opacity)},clamp(){return new iJ(iQ(this.h),i0(this.s),i0(this.l),iH(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=iH(this.opacity);return`${1===t?"hsl(":"hsla("}${iQ(this.h)}, ${100*i0(this.s)}%, ${100*i0(this.l)}%${1===t?")":`, ${t})`}`}}));let i5=t=>()=>t;function i4(t,e){var r,n,i=e-t;return i?(r=t,n=i,function(t){return r+t*n}):i5(isNaN(t)?e:t)}let i3=function t(e){var r,n=1==(r=+e)?i4:function(t,e){var n,i,o;return e-t?(n=t,i=e,n=Math.pow(n,o=r),i=Math.pow(i,o)-n,o=1/o,function(t){return Math.pow(n+t*i,o)}):i5(isNaN(t)?e:t)};function i(t,e){var r=n((t=iq(t)).r,(e=iq(e)).r),i=n(t.g,e.g),o=n(t.b,e.b),a=i4(t.opacity,e.opacity);return function(e){return t.r=r(e),t.g=i(e),t.b=o(e),t.opacity=a(e),t+""}}return i.gamma=t,i}(1);function i6(t){return function(e){var r,n,i=e.length,o=Array(i),a=Array(i),c=Array(i);for(r=0;r<i;++r)n=iq(e[r]),o[r]=n.r||0,a[r]=n.g||0,c[r]=n.b||0;return o=t(o),a=t(a),c=t(c),n.opacity=1,function(t){return n.r=o(t),n.g=a(t),n.b=c(t),n+""}}}i6(function(t){var e=t.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,e-1):Math.floor(r*e),i=t[n],o=t[n+1],a=n>0?t[n-1]:2*i-o,c=n<e-1?t[n+2]:2*o-i;return i2((r-n/e)*e,a,i,o,c)}}),i6(function(t){var e=t.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*e),i=t[(n+e-1)%e],o=t[n%e],a=t[(n+1)%e],c=t[(n+2)%e];return i2((r-n/e)*e,i,o,a,c)}});function i8(t,e){return t*=1,e*=1,function(r){return t*(1-r)+e*r}}var i7=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,i9=RegExp(i7.source,"g");function ot(t,e){var r,n,i=typeof e;return null==e||"boolean"===i?i5(e):("number"===i?i8:"string"===i?(n=iU(e))?(e=n,i3):function(t,e){var r,n,i,o,a,c=i7.lastIndex=i9.lastIndex=0,u=-1,s=[],l=[];for(t+="",e+="";(i=i7.exec(t))&&(o=i9.exec(e));)(a=o.index)>c&&(a=e.slice(c,a),s[u]?s[u]+=a:s[++u]=a),(i=i[0])===(o=o[0])?s[u]?s[u]+=o:s[++u]=o:(s[++u]=null,l.push({i:u,x:i8(i,o)})),c=i9.lastIndex;return c<e.length&&(a=e.slice(c),s[u]?s[u]+=a:s[++u]=a),s.length<2?l[0]?(r=l[0].x,function(t){return r(t)+""}):(n=e,function(){return n}):(e=l.length,function(t){for(var r,n=0;n<e;++n)s[(r=l[n]).i]=r.x(t);return s.join("")})}:e instanceof iU?i3:e instanceof Date?function(t,e){var r=new Date;return t*=1,e*=1,function(n){return r.setTime(t*(1-n)+e*n),r}}:!ArrayBuffer.isView(r=e)||r instanceof DataView?Array.isArray(e)?function(t,e){var r,n=e?e.length:0,i=t?Math.min(n,t.length):0,o=Array(i),a=Array(n);for(r=0;r<i;++r)o[r]=ot(t[r],e[r]);for(;r<n;++r)a[r]=e[r];return function(t){for(r=0;r<i;++r)a[r]=o[r](t);return a}}:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?function(t,e){var r,n={},i={};for(r in(null===t||"object"!=typeof t)&&(t={}),(null===e||"object"!=typeof e)&&(e={}),e)r in t?n[r]=ot(t[r],e[r]):i[r]=e[r];return function(t){for(r in n)i[r]=n[r](t);return i}}:i8:function(t,e){e||(e=[]);var r,n=t?Math.min(e.length,t.length):0,i=e.slice();return function(o){for(r=0;r<n;++r)i[r]=t[r]*(1-o)+e[r]*o;return i}})(t,e)}function oe(t,e){return t*=1,e*=1,function(r){return Math.round(t*(1-r)+e*r)}}function or(t){return+t}var on=[0,1];function oi(t){return t}function oo(t,e){var r;return(e-=t*=1)?function(r){return(r-t)/e}:(r=isNaN(e)?NaN:.5,function(){return r})}function oa(t,e,r){var n=t[0],i=t[1],o=e[0],a=e[1];return i<n?(n=oo(i,n),o=r(a,o)):(n=oo(n,i),o=r(o,a)),function(t){return o(n(t))}}function oc(t,e,r){var n=Math.min(t.length,e.length)-1,i=Array(n),o=Array(n),a=-1;for(t[n]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++a<n;)i[a]=oo(t[a],t[a+1]),o[a]=r(e[a],e[a+1]);return function(e){var r=ij(t,e,1,n)-1;return o[r](i[r](e))}}function ou(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function os(){var t,e,r,n,i,o,a=on,c=on,u=ot,s=oi;function l(){var t,e,r,u=Math.min(a.length,c.length);return s!==oi&&(t=a[0],e=a[u-1],t>e&&(r=t,t=e,e=r),s=function(r){return Math.max(t,Math.min(e,r))}),n=u>2?oc:oa,i=o=null,f}function f(e){return null==e||isNaN(e*=1)?r:(i||(i=n(a.map(t),c,u)))(t(s(e)))}return f.invert=function(r){return s(e((o||(o=n(c,a.map(t),i8)))(r)))},f.domain=function(t){return arguments.length?(a=Array.from(t,or),l()):a.slice()},f.range=function(t){return arguments.length?(c=Array.from(t),l()):c.slice()},f.rangeRound=function(t){return c=Array.from(t),u=oe,l()},f.clamp=function(t){return arguments.length?(s=!!t||oi,l()):s!==oi},f.interpolate=function(t){return arguments.length?(u=t,l()):u},f.unknown=function(t){return arguments.length?(r=t,f):r},function(r,n){return t=r,e=n,l()}}function ol(){return os()(oi,oi)}var of=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function op(t){var e;if(!(e=of.exec(t)))throw Error("invalid format: "+t);return new od({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function od(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function oh(t,e){if((r=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function oy(t){return(t=oh(Math.abs(t)))?t[1]:NaN}function ov(t,e){var r=oh(t,e);if(!r)return t+"";var n=r[0],i=r[1];return i<0?"0."+Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+Array(i-n.length+2).join("0")}op.prototype=od.prototype,od.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let om={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>ov(100*t,e),r:ov,s:function(t,e){var r=oh(t,e);if(!r)return t+"";var n=r[0],i=r[1],o=i-(cX=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,a=n.length;return o===a?n:o>a?n+Array(o-a+1).join("0"):o>0?n.slice(0,o)+"."+n.slice(o):"0."+Array(1-o).join("0")+oh(t,Math.max(0,e+o-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function ob(t){return t}var og=Array.prototype.map,ox=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function ow(t,e,r,n){var i,o,a,c=iv(t,e,r);switch((n=op(null==n?",f":n)).type){case"s":var u=Math.max(Math.abs(t),Math.abs(e));return null!=n.precision||isNaN(a=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(oy(u)/3)))-oy(Math.abs(c))))||(n.precision=a),cK(n,u);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(a=Math.max(0,oy(Math.abs(Math.max(Math.abs(t),Math.abs(e)))-(i=Math.abs(i=c)))-oy(i))+1)||(n.precision=a-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(a=Math.max(0,-oy(Math.abs(c))))||(n.precision=a-("%"===n.type)*2)}return cV(n)}function oO(t){var e=t.domain;return t.ticks=function(t){var r=e();return ih(r[0],r[r.length-1],null==t?10:t)},t.tickFormat=function(t,r){var n=e();return ow(n[0],n[n.length-1],null==t?10:t,r)},t.nice=function(r){null==r&&(r=10);var n,i,o=e(),a=0,c=o.length-1,u=o[a],s=o[c],l=10;for(s<u&&(i=u,u=s,s=i,i=a,a=c,c=i);l-- >0;){if((i=iy(u,s,r))===n)return o[a]=u,o[c]=s,e(o);if(i>0)u=Math.floor(u/i)*i,s=Math.ceil(s/i)*i;else if(i<0)u=Math.ceil(u*i)/i,s=Math.floor(s*i)/i;else break;n=i}return t},t}function oj(){var t=ol();return t.copy=function(){return ou(t,oj())},nk.apply(t,arguments),oO(t)}function oS(t,e){t=t.slice();var r,n=0,i=t.length-1,o=t[n],a=t[i];return a<o&&(r=n,n=i,i=r,r=o,o=a,a=r),t[n]=e.floor(o),t[i]=e.ceil(a),t}function oP(t){return Math.log(t)}function oA(t){return Math.exp(t)}function oE(t){return-Math.log(-t)}function ok(t){return-Math.exp(-t)}function o_(t){return isFinite(t)?+("1e"+t):t<0?0:t}function oN(t){return(e,r)=>-t(-e,r)}function oM(t){let e,r,n=t(oP,oA),i=n.domain,o=10;function a(){var a,c;return e=(a=o)===Math.E?Math.log:10===a&&Math.log10||2===a&&Math.log2||(a=Math.log(a),t=>Math.log(t)/a),r=10===(c=o)?o_:c===Math.E?Math.exp:t=>Math.pow(c,t),i()[0]<0?(e=oN(e),r=oN(r),t(oE,ok)):t(oP,oA),n}return n.base=function(t){return arguments.length?(o=+t,a()):o},n.domain=function(t){return arguments.length?(i(t),a()):i()},n.ticks=t=>{let n,a,c=i(),u=c[0],s=c[c.length-1],l=s<u;l&&([u,s]=[s,u]);let f=e(u),p=e(s),d=null==t?10:+t,h=[];if(!(o%1)&&p-f<d){if(f=Math.floor(f),p=Math.ceil(p),u>0){for(;f<=p;++f)for(n=1;n<o;++n)if(!((a=f<0?n/r(-f):n*r(f))<u)){if(a>s)break;h.push(a)}}else for(;f<=p;++f)for(n=o-1;n>=1;--n)if(!((a=f>0?n/r(-f):n*r(f))<u)){if(a>s)break;h.push(a)}2*h.length<d&&(h=ih(u,s,d))}else h=ih(f,p,Math.min(p-f,d)).map(r);return l?h.reverse():h},n.tickFormat=(t,i)=>{if(null==t&&(t=10),null==i&&(i=10===o?"s":","),"function"!=typeof i&&(o%1||null!=(i=op(i)).precision||(i.trim=!0),i=cV(i)),t===1/0)return i;let a=Math.max(1,o*t/n.ticks().length);return t=>{let n=t/r(Math.round(e(t)));return n*o<o-.5&&(n*=o),n<=a?i(t):""}},n.nice=()=>i(oS(i(),{floor:t=>r(Math.floor(e(t))),ceil:t=>r(Math.ceil(e(t)))})),n}function oT(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function oC(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function oD(t){var e=1,r=t(oT(1),oC(e));return r.constant=function(r){return arguments.length?t(oT(e=+r),oC(e)):e},oO(r)}function oI(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function oB(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function oR(t){return t<0?-t*t:t*t}function oL(t){var e=t(oi,oi),r=1;return e.exponent=function(e){return arguments.length?1==(r=+e)?t(oi,oi):.5===r?t(oB,oR):t(oI(r),oI(1/r)):r},oO(e)}function oz(){var t=oL(os());return t.copy=function(){return ou(t,oz()).exponent(t.exponent())},nk.apply(t,arguments),t}function oU(){return oz.apply(null,arguments).exponent(.5)}function oF(t){return Math.sign(t)*t*t}function o$(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r<e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let i of t)null!=(i=e(i,++n,t))&&(r<i||void 0===r&&i>=i)&&(r=i)}return r}function oq(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r>e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let i of t)null!=(i=e(i,++n,t))&&(r>i||void 0===r&&i>=i)&&(r=i)}return r}cV=(cH=function(t){var e,r,n,i=void 0===t.grouping||void 0===t.thousands?ob:(e=og.call(t.grouping,Number),r=t.thousands+"",function(t,n){for(var i=t.length,o=[],a=0,c=e[0],u=0;i>0&&c>0&&(u+c+1>n&&(c=Math.max(1,n-u)),o.push(t.substring(i-=c,i+c)),!((u+=c+1)>n));)c=e[a=(a+1)%e.length];return o.reverse().join(r)}),o=void 0===t.currency?"":t.currency[0]+"",a=void 0===t.currency?"":t.currency[1]+"",c=void 0===t.decimal?".":t.decimal+"",u=void 0===t.numerals?ob:(n=og.call(t.numerals,String),function(t){return t.replace(/[0-9]/g,function(t){return n[+t]})}),s=void 0===t.percent?"%":t.percent+"",l=void 0===t.minus?"−":t.minus+"",f=void 0===t.nan?"NaN":t.nan+"";function p(t){var e=(t=op(t)).fill,r=t.align,n=t.sign,p=t.symbol,d=t.zero,h=t.width,y=t.comma,v=t.precision,m=t.trim,b=t.type;"n"===b?(y=!0,b="g"):om[b]||(void 0===v&&(v=12),m=!0,b="g"),(d||"0"===e&&"="===r)&&(d=!0,e="0",r="=");var g="$"===p?o:"#"===p&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",x="$"===p?a:/[%p]/.test(b)?s:"",w=om[b],O=/[defgprs%]/.test(b);function j(t){var o,a,s,p=g,j=x;if("c"===b)j=w(t)+j,t="";else{var S=(t*=1)<0||1/t<0;if(t=isNaN(t)?f:w(Math.abs(t),v),m&&(t=function(t){t:for(var e,r=t.length,n=1,i=-1;n<r;++n)switch(t[n]){case".":i=e=n;break;case"0":0===i&&(i=n),e=n;break;default:if(!+t[n])break t;i>0&&(i=0)}return i>0?t.slice(0,i)+t.slice(e+1):t}(t)),S&&0==+t&&"+"!==n&&(S=!1),p=(S?"("===n?n:l:"-"===n||"("===n?"":n)+p,j=("s"===b?ox[8+cX/3]:"")+j+(S&&"("===n?")":""),O){for(o=-1,a=t.length;++o<a;)if(48>(s=t.charCodeAt(o))||s>57){j=(46===s?c+t.slice(o+1):t.slice(o))+j,t=t.slice(0,o);break}}}y&&!d&&(t=i(t,1/0));var P=p.length+t.length+j.length,A=P<h?Array(h-P+1).join(e):"";switch(y&&d&&(t=i(A+t,A.length?h-j.length:1/0),A=""),r){case"<":t=p+t+j+A;break;case"=":t=p+A+t+j;break;case"^":t=A.slice(0,P=A.length>>1)+p+t+j+A.slice(P);break;default:t=A+p+t+j}return u(t)}return v=void 0===v?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),j.toString=function(){return t+""},j}return{format:p,formatPrefix:function(t,e){var r=p(((t=op(t)).type="f",t)),n=3*Math.max(-8,Math.min(8,Math.floor(oy(e)/3))),i=Math.pow(10,-n),o=ox[8+n/3];return function(t){return r(i*t)+o}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,cK=cH.formatPrefix;function oW(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:+(t>e))}function oG(t,e,r){let n=t[e];t[e]=t[r],t[r]=n}let oX=new Date,oH=new Date;function oV(t,e,r,n){function i(e){return t(e=0==arguments.length?new Date:new Date(+e)),e}return i.floor=e=>(t(e=new Date(+e)),e),i.ceil=r=>(t(r=new Date(r-1)),e(r,1),t(r),r),i.round=t=>{let e=i(t),r=i.ceil(t);return t-e<r-t?e:r},i.offset=(t,r)=>(e(t=new Date(+t),null==r?1:Math.floor(r)),t),i.range=(r,n,o)=>{let a,c=[];if(r=i.ceil(r),o=null==o?1:Math.floor(o),!(r<n)||!(o>0))return c;do c.push(a=new Date(+r)),e(r,o),t(r);while(a<r&&r<n);return c},i.filter=r=>oV(e=>{if(e>=e)for(;t(e),!r(e);)e.setTime(e-1)},(t,n)=>{if(t>=t)if(n<0)for(;++n<=0;)for(;e(t,-1),!r(t););else for(;--n>=0;)for(;e(t,1),!r(t););}),r&&(i.count=(e,n)=>(oX.setTime(+e),oH.setTime(+n),t(oX),t(oH),Math.floor(r(oX,oH))),i.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?i.filter(n?e=>n(e)%t==0:e=>i.count(0,e)%t==0):i:null),i}let oK=oV(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);oK.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?oV(e=>{e.setTime(Math.floor(e/t)*t)},(e,r)=>{e.setTime(+e+r*t)},(e,r)=>(r-e)/t):oK:null,oK.range;let oY=oV(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+1e3*e)},(t,e)=>(e-t)/1e3,t=>t.getUTCSeconds());oY.range;let oZ=oV(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds())},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getMinutes());oZ.range;let oJ=oV(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getUTCMinutes());oJ.range;let oQ=oV(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds()-6e4*t.getMinutes())},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getHours());oQ.range;let o0=oV(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getUTCHours());o0.range;let o1=oV(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/864e5,t=>t.getDate()-1);o1.range;let o2=oV(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>t.getUTCDate()-1);o2.range;let o5=oV(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>Math.floor(t/864e5));function o4(t){return oV(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(t,e)=>{t.setDate(t.getDate()+7*e)},(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/6048e5)}o5.range;let o3=o4(0),o6=o4(1),o8=o4(2),o7=o4(3),o9=o4(4),at=o4(5),ae=o4(6);function ar(t){return oV(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)},(t,e)=>(e-t)/6048e5)}o3.range,o6.range,o8.range,o7.range,o9.range,at.range,ae.range;let an=ar(0),ai=ar(1),ao=ar(2),aa=ar(3),ac=ar(4),au=ar(5),as=ar(6);an.range,ai.range,ao.range,aa.range,ac.range,au.range,as.range;let al=oV(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+(e.getFullYear()-t.getFullYear())*12,t=>t.getMonth());al.range;let af=oV(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+(e.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());af.range;let ap=oV(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());ap.every=t=>isFinite(t=Math.floor(t))&&t>0?oV(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,r)=>{e.setFullYear(e.getFullYear()+r*t)}):null,ap.range;let ad=oV(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());function ah(t,e,r,n,i,o){let a=[[oY,1,1e3],[oY,5,5e3],[oY,15,15e3],[oY,30,3e4],[o,1,6e4],[o,5,3e5],[o,15,9e5],[o,30,18e5],[i,1,36e5],[i,3,108e5],[i,6,216e5],[i,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[e,1,2592e6],[e,3,7776e6],[t,1,31536e6]];function c(e,r,n){let i=Math.abs(r-e)/n,o=ig(([,,t])=>t).right(a,i);if(o===a.length)return t.every(iv(e/31536e6,r/31536e6,n));if(0===o)return oK.every(Math.max(iv(e,r,n),1));let[c,u]=a[i/a[o-1][2]<a[o][2]/i?o-1:o];return c.every(u)}return[function(t,e,r){let n=e<t;n&&([t,e]=[e,t]);let i=r&&"function"==typeof r.range?r:c(t,e,r),o=i?i.range(t,+e+1):[];return n?o.reverse():o},c]}ad.every=t=>isFinite(t=Math.floor(t))&&t>0?oV(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,r)=>{e.setUTCFullYear(e.getUTCFullYear()+r*t)}):null,ad.range;let[ay,av]=ah(ad,af,an,o5,o0,oJ),[am,ab]=ah(ap,al,o3,o1,oQ,oZ);function ag(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function ax(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function aw(t,e,r){return{y:t,m:e,d:r,H:0,M:0,S:0,L:0}}var aO={"-":"",_:" ",0:"0"},aj=/^\s*\d+/,aS=/^%/,aP=/[\\^$*+?|[\]().{}]/g;function aA(t,e,r){var n=t<0?"-":"",i=(n?-t:t)+"",o=i.length;return n+(o<r?Array(r-o+1).join(e)+i:i)}function aE(t){return t.replace(aP,"\\$&")}function ak(t){return RegExp("^(?:"+t.map(aE).join("|")+")","i")}function a_(t){return new Map(t.map((t,e)=>[t.toLowerCase(),e]))}function aN(t,e,r){var n=aj.exec(e.slice(r,r+1));return n?(t.w=+n[0],r+n[0].length):-1}function aM(t,e,r){var n=aj.exec(e.slice(r,r+1));return n?(t.u=+n[0],r+n[0].length):-1}function aT(t,e,r){var n=aj.exec(e.slice(r,r+2));return n?(t.U=+n[0],r+n[0].length):-1}function aC(t,e,r){var n=aj.exec(e.slice(r,r+2));return n?(t.V=+n[0],r+n[0].length):-1}function aD(t,e,r){var n=aj.exec(e.slice(r,r+2));return n?(t.W=+n[0],r+n[0].length):-1}function aI(t,e,r){var n=aj.exec(e.slice(r,r+4));return n?(t.y=+n[0],r+n[0].length):-1}function aB(t,e,r){var n=aj.exec(e.slice(r,r+2));return n?(t.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function aR(t,e,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(r,r+6));return n?(t.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function aL(t,e,r){var n=aj.exec(e.slice(r,r+1));return n?(t.q=3*n[0]-3,r+n[0].length):-1}function az(t,e,r){var n=aj.exec(e.slice(r,r+2));return n?(t.m=n[0]-1,r+n[0].length):-1}function aU(t,e,r){var n=aj.exec(e.slice(r,r+2));return n?(t.d=+n[0],r+n[0].length):-1}function aF(t,e,r){var n=aj.exec(e.slice(r,r+3));return n?(t.m=0,t.d=+n[0],r+n[0].length):-1}function a$(t,e,r){var n=aj.exec(e.slice(r,r+2));return n?(t.H=+n[0],r+n[0].length):-1}function aq(t,e,r){var n=aj.exec(e.slice(r,r+2));return n?(t.M=+n[0],r+n[0].length):-1}function aW(t,e,r){var n=aj.exec(e.slice(r,r+2));return n?(t.S=+n[0],r+n[0].length):-1}function aG(t,e,r){var n=aj.exec(e.slice(r,r+3));return n?(t.L=+n[0],r+n[0].length):-1}function aX(t,e,r){var n=aj.exec(e.slice(r,r+6));return n?(t.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function aH(t,e,r){var n=aS.exec(e.slice(r,r+1));return n?r+n[0].length:-1}function aV(t,e,r){var n=aj.exec(e.slice(r));return n?(t.Q=+n[0],r+n[0].length):-1}function aK(t,e,r){var n=aj.exec(e.slice(r));return n?(t.s=+n[0],r+n[0].length):-1}function aY(t,e){return aA(t.getDate(),e,2)}function aZ(t,e){return aA(t.getHours(),e,2)}function aJ(t,e){return aA(t.getHours()%12||12,e,2)}function aQ(t,e){return aA(1+o1.count(ap(t),t),e,3)}function a0(t,e){return aA(t.getMilliseconds(),e,3)}function a1(t,e){return a0(t,e)+"000"}function a2(t,e){return aA(t.getMonth()+1,e,2)}function a5(t,e){return aA(t.getMinutes(),e,2)}function a4(t,e){return aA(t.getSeconds(),e,2)}function a3(t){var e=t.getDay();return 0===e?7:e}function a6(t,e){return aA(o3.count(ap(t)-1,t),e,2)}function a8(t){var e=t.getDay();return e>=4||0===e?o9(t):o9.ceil(t)}function a7(t,e){return t=a8(t),aA(o9.count(ap(t),t)+(4===ap(t).getDay()),e,2)}function a9(t){return t.getDay()}function ct(t,e){return aA(o6.count(ap(t)-1,t),e,2)}function ce(t,e){return aA(t.getFullYear()%100,e,2)}function cr(t,e){return aA((t=a8(t)).getFullYear()%100,e,2)}function cn(t,e){return aA(t.getFullYear()%1e4,e,4)}function ci(t,e){var r=t.getDay();return aA((t=r>=4||0===r?o9(t):o9.ceil(t)).getFullYear()%1e4,e,4)}function co(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+aA(e/60|0,"0",2)+aA(e%60,"0",2)}function ca(t,e){return aA(t.getUTCDate(),e,2)}function cc(t,e){return aA(t.getUTCHours(),e,2)}function cu(t,e){return aA(t.getUTCHours()%12||12,e,2)}function cs(t,e){return aA(1+o2.count(ad(t),t),e,3)}function cl(t,e){return aA(t.getUTCMilliseconds(),e,3)}function cf(t,e){return cl(t,e)+"000"}function cp(t,e){return aA(t.getUTCMonth()+1,e,2)}function cd(t,e){return aA(t.getUTCMinutes(),e,2)}function ch(t,e){return aA(t.getUTCSeconds(),e,2)}function cy(t){var e=t.getUTCDay();return 0===e?7:e}function cv(t,e){return aA(an.count(ad(t)-1,t),e,2)}function cm(t){var e=t.getUTCDay();return e>=4||0===e?ac(t):ac.ceil(t)}function cb(t,e){return t=cm(t),aA(ac.count(ad(t),t)+(4===ad(t).getUTCDay()),e,2)}function cg(t){return t.getUTCDay()}function cx(t,e){return aA(ai.count(ad(t)-1,t),e,2)}function cw(t,e){return aA(t.getUTCFullYear()%100,e,2)}function cO(t,e){return aA((t=cm(t)).getUTCFullYear()%100,e,2)}function cj(t,e){return aA(t.getUTCFullYear()%1e4,e,4)}function cS(t,e){var r=t.getUTCDay();return aA((t=r>=4||0===r?ac(t):ac.ceil(t)).getUTCFullYear()%1e4,e,4)}function cP(){return"+0000"}function cA(){return"%"}function cE(t){return+t}function ck(t){return Math.floor(t/1e3)}function c_(t){return new Date(t)}function cN(t){return t instanceof Date?+t:+new Date(+t)}function cM(t,e,r,n,i,o,a,c,u,s){var l=ol(),f=l.invert,p=l.domain,d=s(".%L"),h=s(":%S"),y=s("%I:%M"),v=s("%I %p"),m=s("%a %d"),b=s("%b %d"),g=s("%B"),x=s("%Y");function w(t){return(u(t)<t?d:c(t)<t?h:a(t)<t?y:o(t)<t?v:n(t)<t?i(t)<t?m:b:r(t)<t?g:x)(t)}return l.invert=function(t){return new Date(f(t))},l.domain=function(t){return arguments.length?p(Array.from(t,cN)):p().map(c_)},l.ticks=function(e){var r=p();return t(r[0],r[r.length-1],null==e?10:e)},l.tickFormat=function(t,e){return null==e?w:s(e)},l.nice=function(t){var r=p();return t&&"function"==typeof t.range||(t=e(r[0],r[r.length-1],null==t?10:t)),t?p(oS(r,t)):l},l.copy=function(){return ou(l,cM(t,e,r,n,i,o,a,c,u,s))},l}function cT(){return nk.apply(cM(am,ab,ap,al,o3,o1,oQ,oZ,oY,cZ).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function cC(){return nk.apply(cM(ay,av,ad,af,an,o2,o0,oJ,oY,cJ).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function cD(){var t,e,r,n,i,o=0,a=1,c=oi,u=!1;function s(e){return null==e||isNaN(e*=1)?i:c(0===r?.5:(e=(n(e)-t)*r,u?Math.max(0,Math.min(1,e)):e))}function l(t){return function(e){var r,n;return arguments.length?([r,n]=e,c=t(r,n),s):[c(0),c(1)]}}return s.domain=function(i){return arguments.length?([o,a]=i,t=n(o*=1),e=n(a*=1),r=t===e?0:1/(e-t),s):[o,a]},s.clamp=function(t){return arguments.length?(u=!!t,s):u},s.interpolator=function(t){return arguments.length?(c=t,s):c},s.range=l(ot),s.rangeRound=l(oe),s.unknown=function(t){return arguments.length?(i=t,s):i},function(i){return n=i,t=i(o),e=i(a),r=t===e?0:1/(e-t),s}}function cI(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function cB(){var t=oL(cD());return t.copy=function(){return cI(t,cB()).exponent(t.exponent())},n_.apply(t,arguments)}function cR(){return cB.apply(null,arguments).exponent(.5)}function cL(){var t,e,r,n,i,o,a,c=0,u=.5,s=1,l=1,f=oi,p=!1;function d(t){return isNaN(t*=1)?a:(t=.5+((t=+o(t))-e)*(l*t<l*e?n:i),f(p?Math.max(0,Math.min(1,t)):t))}function h(t){return function(e){var r,n,i;return arguments.length?([r,n,i]=e,f=function(t,e){void 0===e&&(e=t,t=ot);for(var r=0,n=e.length-1,i=e[0],o=Array(n<0?0:n);r<n;)o[r]=t(i,i=e[++r]);return function(t){var e=Math.max(0,Math.min(n-1,Math.floor(t*=n)));return o[e](t-e)}}(t,[r,n,i]),d):[f(0),f(.5),f(1)]}}return d.domain=function(a){return arguments.length?([c,u,s]=a,t=o(c*=1),e=o(u*=1),r=o(s*=1),n=t===e?0:.5/(e-t),i=e===r?0:.5/(r-e),l=e<t?-1:1,d):[c,u,s]},d.clamp=function(t){return arguments.length?(p=!!t,d):p},d.interpolator=function(t){return arguments.length?(f=t,d):f},d.range=h(ot),d.rangeRound=h(oe),d.unknown=function(t){return arguments.length?(a=t,d):a},function(a){return o=a,t=a(c),e=a(u),r=a(s),n=t===e?0:.5/(e-t),i=e===r?0:.5/(r-e),l=e<t?-1:1,d}}function cz(){var t=oL(cL());return t.copy=function(){return cI(t,cz()).exponent(t.exponent())},n_.apply(t,arguments)}function cU(){return cz.apply(null,arguments).exponent(.5)}function cF(t,e){if((i=t.length)>1)for(var r,n,i,o=1,a=t[e[0]],c=a.length;o<i;++o)for(n=a,a=t[e[o]],r=0;r<c;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}function c$(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function cq(t){for(var e=t.length,r=Array(e);--e>=0;)r[e]=e;return r}function cW(t,e){return t[e]}function cG(t){let e=[];return e.key=t,e}cZ=(cY=function(t){var e=t.dateTime,r=t.date,n=t.time,i=t.periods,o=t.days,a=t.shortDays,c=t.months,u=t.shortMonths,s=ak(i),l=a_(i),f=ak(o),p=a_(o),d=ak(a),h=a_(a),y=ak(c),v=a_(c),m=ak(u),b=a_(u),g={a:function(t){return a[t.getDay()]},A:function(t){return o[t.getDay()]},b:function(t){return u[t.getMonth()]},B:function(t){return c[t.getMonth()]},c:null,d:aY,e:aY,f:a1,g:cr,G:ci,H:aZ,I:aJ,j:aQ,L:a0,m:a2,M:a5,p:function(t){return i[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:cE,s:ck,S:a4,u:a3,U:a6,V:a7,w:a9,W:ct,x:null,X:null,y:ce,Y:cn,Z:co,"%":cA},x={a:function(t){return a[t.getUTCDay()]},A:function(t){return o[t.getUTCDay()]},b:function(t){return u[t.getUTCMonth()]},B:function(t){return c[t.getUTCMonth()]},c:null,d:ca,e:ca,f:cf,g:cO,G:cS,H:cc,I:cu,j:cs,L:cl,m:cp,M:cd,p:function(t){return i[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:cE,s:ck,S:ch,u:cy,U:cv,V:cb,w:cg,W:cx,x:null,X:null,y:cw,Y:cj,Z:cP,"%":cA},w={a:function(t,e,r){var n=d.exec(e.slice(r));return n?(t.w=h.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(t,e,r){var n=f.exec(e.slice(r));return n?(t.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(t,e,r){var n=m.exec(e.slice(r));return n?(t.m=b.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(t,e,r){var n=y.exec(e.slice(r));return n?(t.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(t,r,n){return S(t,e,r,n)},d:aU,e:aU,f:aX,g:aB,G:aI,H:a$,I:a$,j:aF,L:aG,m:az,M:aq,p:function(t,e,r){var n=s.exec(e.slice(r));return n?(t.p=l.get(n[0].toLowerCase()),r+n[0].length):-1},q:aL,Q:aV,s:aK,S:aW,u:aM,U:aT,V:aC,w:aN,W:aD,x:function(t,e,n){return S(t,r,e,n)},X:function(t,e,r){return S(t,n,e,r)},y:aB,Y:aI,Z:aR,"%":aH};function O(t,e){return function(r){var n,i,o,a=[],c=-1,u=0,s=t.length;for(r instanceof Date||(r=new Date(+r));++c<s;)37===t.charCodeAt(c)&&(a.push(t.slice(u,c)),null!=(i=aO[n=t.charAt(++c)])?n=t.charAt(++c):i="e"===n?" ":"0",(o=e[n])&&(n=o(r,i)),a.push(n),u=c+1);return a.push(t.slice(u,c)),a.join("")}}function j(t,e){return function(r){var n,i,o=aw(1900,void 0,1);if(S(o,t,r+="",0)!=r.length)return null;if("Q"in o)return new Date(o.Q);if("s"in o)return new Date(1e3*o.s+("L"in o?o.L:0));if(!e||"Z"in o||(o.Z=0),"p"in o&&(o.H=o.H%12+12*o.p),void 0===o.m&&(o.m="q"in o?o.q:0),"V"in o){if(o.V<1||o.V>53)return null;"w"in o||(o.w=1),"Z"in o?(n=(i=(n=ax(aw(o.y,0,1))).getUTCDay())>4||0===i?ai.ceil(n):ai(n),n=o2.offset(n,(o.V-1)*7),o.y=n.getUTCFullYear(),o.m=n.getUTCMonth(),o.d=n.getUTCDate()+(o.w+6)%7):(n=(i=(n=ag(aw(o.y,0,1))).getDay())>4||0===i?o6.ceil(n):o6(n),n=o1.offset(n,(o.V-1)*7),o.y=n.getFullYear(),o.m=n.getMonth(),o.d=n.getDate()+(o.w+6)%7)}else("W"in o||"U"in o)&&("w"in o||(o.w="u"in o?o.u%7:+("W"in o)),i="Z"in o?ax(aw(o.y,0,1)).getUTCDay():ag(aw(o.y,0,1)).getDay(),o.m=0,o.d="W"in o?(o.w+6)%7+7*o.W-(i+5)%7:o.w+7*o.U-(i+6)%7);return"Z"in o?(o.H+=o.Z/100|0,o.M+=o.Z%100,ax(o)):ag(o)}}function S(t,e,r,n){for(var i,o,a=0,c=e.length,u=r.length;a<c;){if(n>=u)return -1;if(37===(i=e.charCodeAt(a++))){if(!(o=w[(i=e.charAt(a++))in aO?e.charAt(a++):i])||(n=o(t,r,n))<0)return -1}else if(i!=r.charCodeAt(n++))return -1}return n}return g.x=O(r,g),g.X=O(n,g),g.c=O(e,g),x.x=O(r,x),x.X=O(n,x),x.c=O(e,x),{format:function(t){var e=O(t+="",g);return e.toString=function(){return t},e},parse:function(t){var e=j(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=O(t+="",x);return e.toString=function(){return t},e},utcParse:function(t){var e=j(t+="",!0);return e.toString=function(){return t},e}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,cY.parse,cJ=cY.utcFormat,cY.utcParse,Array.prototype.slice;var cX,cH,cV,cK,cY,cZ,cJ,cQ,c0,c1=r(90453),c2=r.n(c1),c5=r(15883),c4=r.n(c5),c3=r(21592),c6=r.n(c3),c8=r(71967),c7=r.n(c8),c9=!0,ut="[DecimalError] ",ue=ut+"Invalid argument: ",ur=ut+"Exponent out of range: ",un=Math.floor,ui=Math.pow,uo=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,ua=un(1286742750677284.5),uc={};function uu(t,e){var r,n,i,o,a,c,u,s,l=t.constructor,f=l.precision;if(!t.s||!e.s)return e.s||(e=new l(t)),c9?ub(e,f):e;if(u=t.d,s=e.d,a=t.e,i=e.e,u=u.slice(),o=a-i){for(o<0?(n=u,o=-o,c=s.length):(n=s,i=a,c=u.length),o>(c=(a=Math.ceil(f/7))>c?a+1:c+1)&&(o=c,n.length=1),n.reverse();o--;)n.push(0);n.reverse()}for((c=u.length)-(o=s.length)<0&&(o=c,n=s,s=u,u=n),r=0;o;)r=(u[--o]=u[o]+s[o]+r)/1e7|0,u[o]%=1e7;for(r&&(u.unshift(r),++i),c=u.length;0==u[--c];)u.pop();return e.d=u,e.e=i,c9?ub(e,f):e}function us(t,e,r){if(t!==~~t||t<e||t>r)throw Error(ue+t)}function ul(t){var e,r,n,i=t.length-1,o="",a=t[0];if(i>0){for(o+=a,e=1;e<i;e++)(r=7-(n=t[e]+"").length)&&(o+=uy(r)),o+=n;(r=7-(n=(a=t[e])+"").length)&&(o+=uy(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return o+a}uc.absoluteValue=uc.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},uc.comparedTo=uc.cmp=function(t){var e,r,n,i;if(t=new this.constructor(t),this.s!==t.s)return this.s||-t.s;if(this.e!==t.e)return this.e>t.e^this.s<0?1:-1;for(e=0,r=(n=this.d.length)<(i=t.d.length)?n:i;e<r;++e)if(this.d[e]!==t.d[e])return this.d[e]>t.d[e]^this.s<0?1:-1;return n===i?0:n>i^this.s<0?1:-1},uc.decimalPlaces=uc.dp=function(){var t=this.d.length-1,e=(t-this.e)*7;if(t=this.d[t])for(;t%10==0;t/=10)e--;return e<0?0:e},uc.dividedBy=uc.div=function(t){return uf(this,new this.constructor(t))},uc.dividedToIntegerBy=uc.idiv=function(t){var e=this.constructor;return ub(uf(this,new e(t),0,1),e.precision)},uc.equals=uc.eq=function(t){return!this.cmp(t)},uc.exponent=function(){return ud(this)},uc.greaterThan=uc.gt=function(t){return this.cmp(t)>0},uc.greaterThanOrEqualTo=uc.gte=function(t){return this.cmp(t)>=0},uc.isInteger=uc.isint=function(){return this.e>this.d.length-2},uc.isNegative=uc.isneg=function(){return this.s<0},uc.isPositive=uc.ispos=function(){return this.s>0},uc.isZero=function(){return 0===this.s},uc.lessThan=uc.lt=function(t){return 0>this.cmp(t)},uc.lessThanOrEqualTo=uc.lte=function(t){return 1>this.cmp(t)},uc.logarithm=uc.log=function(t){var e,r=this.constructor,n=r.precision,i=n+5;if(void 0===t)t=new r(10);else if((t=new r(t)).s<1||t.eq(c0))throw Error(ut+"NaN");if(this.s<1)throw Error(ut+(this.s?"NaN":"-Infinity"));return this.eq(c0)?new r(0):(c9=!1,e=uf(uv(this,i),uv(t,i),i),c9=!0,ub(e,n))},uc.minus=uc.sub=function(t){return t=new this.constructor(t),this.s==t.s?ug(this,t):uu(this,(t.s=-t.s,t))},uc.modulo=uc.mod=function(t){var e,r=this.constructor,n=r.precision;if(!(t=new r(t)).s)throw Error(ut+"NaN");return this.s?(c9=!1,e=uf(this,t,0,1).times(t),c9=!0,this.minus(e)):ub(new r(this),n)},uc.naturalExponential=uc.exp=function(){return up(this)},uc.naturalLogarithm=uc.ln=function(){return uv(this)},uc.negated=uc.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},uc.plus=uc.add=function(t){return t=new this.constructor(t),this.s==t.s?uu(this,t):ug(this,(t.s=-t.s,t))},uc.precision=uc.sd=function(t){var e,r,n;if(void 0!==t&&!!t!==t&&1!==t&&0!==t)throw Error(ue+t);if(e=ud(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return t&&e>r?e:r},uc.squareRoot=uc.sqrt=function(){var t,e,r,n,i,o,a,c=this.constructor;if(this.s<1){if(!this.s)return new c(0);throw Error(ut+"NaN")}for(t=ud(this),c9=!1,0==(i=Math.sqrt(+this))||i==1/0?(((e=ul(this.d)).length+t)%2==0&&(e+="0"),i=Math.sqrt(e),t=un((t+1)/2)-(t<0||t%2),n=new c(e=i==1/0?"5e"+t:(e=i.toExponential()).slice(0,e.indexOf("e")+1)+t)):n=new c(i.toString()),i=a=(r=c.precision)+3;;)if(n=(o=n).plus(uf(this,o,a+2)).times(.5),ul(o.d).slice(0,a)===(e=ul(n.d)).slice(0,a)){if(e=e.slice(a-3,a+1),i==a&&"4999"==e){if(ub(o,r+1,0),o.times(o).eq(this)){n=o;break}}else if("9999"!=e)break;a+=4}return c9=!0,ub(n,r)},uc.times=uc.mul=function(t){var e,r,n,i,o,a,c,u,s,l=this.constructor,f=this.d,p=(t=new l(t)).d;if(!this.s||!t.s)return new l(0);for(t.s*=this.s,r=this.e+t.e,(u=f.length)<(s=p.length)&&(o=f,f=p,p=o,a=u,u=s,s=a),o=[],n=a=u+s;n--;)o.push(0);for(n=s;--n>=0;){for(e=0,i=u+n;i>n;)c=o[i]+p[n]*f[i-n-1]+e,o[i--]=c%1e7|0,e=c/1e7|0;o[i]=(o[i]+e)%1e7|0}for(;!o[--a];)o.pop();return e?++r:o.shift(),t.d=o,t.e=r,c9?ub(t,l.precision):t},uc.toDecimalPlaces=uc.todp=function(t,e){var r=this,n=r.constructor;return(r=new n(r),void 0===t)?r:(us(t,0,1e9),void 0===e?e=n.rounding:us(e,0,8),ub(r,t+ud(r)+1,e))},uc.toExponential=function(t,e){var r,n=this,i=n.constructor;return void 0===t?r=ux(n,!0):(us(t,0,1e9),void 0===e?e=i.rounding:us(e,0,8),r=ux(n=ub(new i(n),t+1,e),!0,t+1)),r},uc.toFixed=function(t,e){var r,n,i=this.constructor;return void 0===t?ux(this):(us(t,0,1e9),void 0===e?e=i.rounding:us(e,0,8),r=ux((n=ub(new i(this),t+ud(this)+1,e)).abs(),!1,t+ud(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},uc.toInteger=uc.toint=function(){var t=this.constructor;return ub(new t(this),ud(this)+1,t.rounding)},uc.toNumber=function(){return+this},uc.toPower=uc.pow=function(t){var e,r,n,i,o,a,c=this,u=c.constructor,s=+(t=new u(t));if(!t.s)return new u(c0);if(!(c=new u(c)).s){if(t.s<1)throw Error(ut+"Infinity");return c}if(c.eq(c0))return c;if(n=u.precision,t.eq(c0))return ub(c,n);if(a=(e=t.e)>=(r=t.d.length-1),o=c.s,a){if((r=s<0?-s:s)<=0x1fffffffffffff){for(i=new u(c0),e=Math.ceil(n/7+4),c9=!1;r%2&&uw((i=i.times(c)).d,e),0!==(r=un(r/2));)uw((c=c.times(c)).d,e);return c9=!0,t.s<0?new u(c0).div(i):ub(i,n)}}else if(o<0)throw Error(ut+"NaN");return o=o<0&&1&t.d[Math.max(e,r)]?-1:1,c.s=1,c9=!1,i=t.times(uv(c,n+12)),c9=!0,(i=up(i)).s=o,i},uc.toPrecision=function(t,e){var r,n,i=this,o=i.constructor;return void 0===t?(r=ud(i),n=ux(i,r<=o.toExpNeg||r>=o.toExpPos)):(us(t,1,1e9),void 0===e?e=o.rounding:us(e,0,8),r=ud(i=ub(new o(i),t,e)),n=ux(i,t<=r||r<=o.toExpNeg,t)),n},uc.toSignificantDigits=uc.tosd=function(t,e){var r=this.constructor;return void 0===t?(t=r.precision,e=r.rounding):(us(t,1,1e9),void 0===e?e=r.rounding:us(e,0,8)),ub(new r(this),t,e)},uc.toString=uc.valueOf=uc.val=uc.toJSON=uc[Symbol.for("nodejs.util.inspect.custom")]=function(){var t=ud(this),e=this.constructor;return ux(this,t<=e.toExpNeg||t>=e.toExpPos)};var uf=function(){function t(t,e){var r,n=0,i=t.length;for(t=t.slice();i--;)r=t[i]*e+n,t[i]=r%1e7|0,n=r/1e7|0;return n&&t.unshift(n),t}function e(t,e,r,n){var i,o;if(r!=n)o=r>n?1:-1;else for(i=o=0;i<r;i++)if(t[i]!=e[i]){o=t[i]>e[i]?1:-1;break}return o}function r(t,e,r){for(var n=0;r--;)t[r]-=n,n=+(t[r]<e[r]),t[r]=1e7*n+t[r]-e[r];for(;!t[0]&&t.length>1;)t.shift()}return function(n,i,o,a){var c,u,s,l,f,p,d,h,y,v,m,b,g,x,w,O,j,S,P=n.constructor,A=n.s==i.s?1:-1,E=n.d,k=i.d;if(!n.s)return new P(n);if(!i.s)throw Error(ut+"Division by zero");for(s=0,u=n.e-i.e,j=k.length,w=E.length,h=(d=new P(A)).d=[];k[s]==(E[s]||0);)++s;if(k[s]>(E[s]||0)&&--u,(b=null==o?o=P.precision:a?o+(ud(n)-ud(i))+1:o)<0)return new P(0);if(b=b/7+2|0,s=0,1==j)for(l=0,k=k[0],b++;(s<w||l)&&b--;s++)g=1e7*l+(E[s]||0),h[s]=g/k|0,l=g%k|0;else{for((l=1e7/(k[0]+1)|0)>1&&(k=t(k,l),E=t(E,l),j=k.length,w=E.length),x=j,v=(y=E.slice(0,j)).length;v<j;)y[v++]=0;(S=k.slice()).unshift(0),O=k[0],k[1]>=1e7/2&&++O;do l=0,(c=e(k,y,j,v))<0?(m=y[0],j!=v&&(m=1e7*m+(y[1]||0)),(l=m/O|0)>1?(l>=1e7&&(l=1e7-1),p=(f=t(k,l)).length,v=y.length,1==(c=e(f,y,p,v))&&(l--,r(f,j<p?S:k,p))):(0==l&&(c=l=1),f=k.slice()),(p=f.length)<v&&f.unshift(0),r(y,f,v),-1==c&&(v=y.length,(c=e(k,y,j,v))<1&&(l++,r(y,j<v?S:k,v))),v=y.length):0===c&&(l++,y=[0]),h[s++]=l,c&&y[0]?y[v++]=E[x]||0:(y=[E[x]],v=1);while((x++<w||void 0!==y[0])&&b--)}return h[0]||h.shift(),d.e=u,ub(d,a?o+ud(d)+1:o)}}();function up(t,e){var r,n,i,o,a,c=0,u=0,s=t.constructor,l=s.precision;if(ud(t)>16)throw Error(ur+ud(t));if(!t.s)return new s(c0);for(null==e?(c9=!1,a=l):a=e,o=new s(.03125);t.abs().gte(.1);)t=t.times(o),u+=5;for(a+=Math.log(ui(2,u))/Math.LN10*2+5|0,r=n=i=new s(c0),s.precision=a;;){if(n=ub(n.times(t),a),r=r.times(++c),ul((o=i.plus(uf(n,r,a))).d).slice(0,a)===ul(i.d).slice(0,a)){for(;u--;)i=ub(i.times(i),a);return s.precision=l,null==e?(c9=!0,ub(i,l)):i}i=o}}function ud(t){for(var e=7*t.e,r=t.d[0];r>=10;r/=10)e++;return e}function uh(t,e,r){if(e>t.LN10.sd())throw c9=!0,r&&(t.precision=r),Error(ut+"LN10 precision limit exceeded");return ub(new t(t.LN10),e)}function uy(t){for(var e="";t--;)e+="0";return e}function uv(t,e){var r,n,i,o,a,c,u,s,l,f=1,p=t,d=p.d,h=p.constructor,y=h.precision;if(p.s<1)throw Error(ut+(p.s?"NaN":"-Infinity"));if(p.eq(c0))return new h(0);if(null==e?(c9=!1,s=y):s=e,p.eq(10))return null==e&&(c9=!0),uh(h,s);if(h.precision=s+=10,n=(r=ul(d)).charAt(0),!(15e14>Math.abs(o=ud(p))))return u=uh(h,s+2,y).times(o+""),p=uv(new h(n+"."+r.slice(1)),s-10).plus(u),h.precision=y,null==e?(c9=!0,ub(p,y)):p;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=ul((p=p.times(t)).d)).charAt(0),f++;for(o=ud(p),n>1?(p=new h("0."+r),o++):p=new h(n+"."+r.slice(1)),c=a=p=uf(p.minus(c0),p.plus(c0),s),l=ub(p.times(p),s),i=3;;){if(a=ub(a.times(l),s),ul((u=c.plus(uf(a,new h(i),s))).d).slice(0,s)===ul(c.d).slice(0,s))return c=c.times(2),0!==o&&(c=c.plus(uh(h,s+2,y).times(o+""))),c=uf(c,new h(f),s),h.precision=y,null==e?(c9=!0,ub(c,y)):c;c=u,i+=2}}function um(t,e){var r,n,i;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;48===e.charCodeAt(n);)++n;for(i=e.length;48===e.charCodeAt(i-1);)--i;if(e=e.slice(n,i)){if(i-=n,t.e=un((r=r-n-1)/7),t.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&t.d.push(+e.slice(0,n)),i-=7;n<i;)t.d.push(+e.slice(n,n+=7));n=7-(e=e.slice(n)).length}else n-=i;for(;n--;)e+="0";if(t.d.push(+e),c9&&(t.e>ua||t.e<-ua))throw Error(ur+r)}else t.s=0,t.e=0,t.d=[0];return t}function ub(t,e,r){var n,i,o,a,c,u,s,l,f=t.d;for(a=1,o=f[0];o>=10;o/=10)a++;if((n=e-a)<0)n+=7,i=e,s=f[l=0];else{if((l=Math.ceil((n+1)/7))>=(o=f.length))return t;for(a=1,s=o=f[l];o>=10;o/=10)a++;n%=7,i=n-7+a}if(void 0!==r&&(c=s/(o=ui(10,a-i-1))%10|0,u=e<0||void 0!==f[l+1]||s%o,u=r<4?(c||u)&&(0==r||r==(t.s<0?3:2)):c>5||5==c&&(4==r||u||6==r&&(n>0?i>0?s/ui(10,a-i):0:f[l-1])%10&1||r==(t.s<0?8:7))),e<1||!f[0])return u?(o=ud(t),f.length=1,e=e-o-1,f[0]=ui(10,(7-e%7)%7),t.e=un(-e/7)||0):(f.length=1,f[0]=t.e=t.s=0),t;if(0==n?(f.length=l,o=1,l--):(f.length=l+1,o=ui(10,7-n),f[l]=i>0?(s/ui(10,a-i)%ui(10,i)|0)*o:0),u)for(;;)if(0==l){1e7==(f[0]+=o)&&(f[0]=1,++t.e);break}else{if(f[l]+=o,1e7!=f[l])break;f[l--]=0,o=1}for(n=f.length;0===f[--n];)f.pop();if(c9&&(t.e>ua||t.e<-ua))throw Error(ur+ud(t));return t}function ug(t,e){var r,n,i,o,a,c,u,s,l,f,p=t.constructor,d=p.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new p(t),c9?ub(e,d):e;if(u=t.d,f=e.d,n=e.e,s=t.e,u=u.slice(),a=s-n){for((l=a<0)?(r=u,a=-a,c=f.length):(r=f,n=s,c=u.length),a>(i=Math.max(Math.ceil(d/7),c)+2)&&(a=i,r.length=1),r.reverse(),i=a;i--;)r.push(0);r.reverse()}else{for((l=(i=u.length)<(c=f.length))&&(c=i),i=0;i<c;i++)if(u[i]!=f[i]){l=u[i]<f[i];break}a=0}for(l&&(r=u,u=f,f=r,e.s=-e.s),c=u.length,i=f.length-c;i>0;--i)u[c++]=0;for(i=f.length;i>a;){if(u[--i]<f[i]){for(o=i;o&&0===u[--o];)u[o]=1e7-1;--u[o],u[i]+=1e7}u[i]-=f[i]}for(;0===u[--c];)u.pop();for(;0===u[0];u.shift())--n;return u[0]?(e.d=u,e.e=n,c9?ub(e,d):e):new p(0)}function ux(t,e,r){var n,i=ud(t),o=ul(t.d),a=o.length;return e?(r&&(n=r-a)>0?o=o.charAt(0)+"."+o.slice(1)+uy(n):a>1&&(o=o.charAt(0)+"."+o.slice(1)),o=o+(i<0?"e":"e+")+i):i<0?(o="0."+uy(-i-1)+o,r&&(n=r-a)>0&&(o+=uy(n))):i>=a?(o+=uy(i+1-a),r&&(n=r-i-1)>0&&(o=o+"."+uy(n))):((n=i+1)<a&&(o=o.slice(0,n)+"."+o.slice(n)),r&&(n=r-a)>0&&(i+1===a&&(o+="."),o+=uy(n))),t.s<0?"-"+o:o}function uw(t,e){if(t.length>e)return t.length=e,!0}function uO(t){if(!t||"object"!=typeof t)throw Error(ut+"Object expected");var e,r,n,i=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<i.length;e+=3)if(void 0!==(n=t[r=i[e]]))if(un(n)===n&&n>=i[e+1]&&n<=i[e+2])this[r]=n;else throw Error(ue+r+": "+n);if(void 0!==(n=t[r="LN10"]))if(n==Math.LN10)this[r]=new this(n);else throw Error(ue+r+": "+n);return this}var cQ=function t(e){var r,n,i;function o(t){if(!(this instanceof o))return new o(t);if(this.constructor=o,t instanceof o){this.s=t.s,this.e=t.e,this.d=(t=t.d)?t.slice():t;return}if("number"==typeof t){if(0*t!=0)throw Error(ue+t);if(t>0)this.s=1;else if(t<0)t=-t,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(t===~~t&&t<1e7){this.e=0,this.d=[t];return}return um(this,t.toString())}if("string"!=typeof t)throw Error(ue+t);if(45===t.charCodeAt(0)?(t=t.slice(1),this.s=-1):this.s=1,uo.test(t))um(this,t);else throw Error(ue+t)}if(o.prototype=uc,o.ROUND_UP=0,o.ROUND_DOWN=1,o.ROUND_CEIL=2,o.ROUND_FLOOR=3,o.ROUND_HALF_UP=4,o.ROUND_HALF_DOWN=5,o.ROUND_HALF_EVEN=6,o.ROUND_HALF_CEIL=7,o.ROUND_HALF_FLOOR=8,o.clone=t,o.config=o.set=uO,void 0===e&&(e={}),e)for(r=0,i=["precision","rounding","toExpNeg","toExpPos","LN10"];r<i.length;)e.hasOwnProperty(n=i[r++])||(e[n]=this[n]);return o.config(e),o}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});c0=new cQ(1);let uj=cQ;function uS(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var uP=function(t){return t},uA={},uE=function(t){return t===uA},uk=function(t){return function e(){return 0==arguments.length||1==arguments.length&&uE(arguments.length<=0?void 0:arguments[0])?e:t.apply(void 0,arguments)}},u_=function(t){return function t(e,r){return 1===e?r:uk(function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];var a=i.filter(function(t){return t!==uA}).length;return a>=e?r.apply(void 0,i):t(e-a,uk(function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];var o=i.map(function(t){return uE(t)?e.shift():t});return r.apply(void 0,((function(t){if(Array.isArray(t))return uS(t)})(o)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(o)||function(t,e){if(t){if("string"==typeof t)return uS(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uS(t,e)}}(o)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(e))}))})}(t.length,t)},uN=function(t,e){for(var r=[],n=t;n<e;++n)r[n-t]=n;return r},uM=u_(function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map(function(t){return e[t]}).map(t)}),uT=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];if(!e.length)return uP;var n=e.reverse(),i=n[0],o=n.slice(1);return function(){return o.reduce(function(t,e){return e(t)},i.apply(void 0,arguments))}},uC=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},uD=function(t){var e=null,r=null;return function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];return e&&i.every(function(t,r){return t===e[r]})?r:(e=i,r=t.apply(void 0,i))}};u_(function(t,e,r){var n=+t;return n+r*(e-n)}),u_(function(t,e,r){var n=e-t;return(r-t)/(n=n||1/0)}),u_(function(t,e,r){var n=e-t;return Math.max(0,Math.min(1,(r-t)/(n=n||1/0)))});let uI={rangeStep:function(t,e,r){for(var n=new uj(t),i=0,o=[];n.lt(e)&&i<1e5;)o.push(n.toNumber()),n=n.add(r),i++;return o},getDigitCount:function(t){var e;return 0===t?1:Math.floor(new uj(t).abs().log(10).toNumber())+1}};function uB(t){return function(t){if(Array.isArray(t))return uz(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||uL(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function uR(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t)){var r=[],n=!0,i=!1,o=void 0;try{for(var a,c=t[Symbol.iterator]();!(n=(a=c.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(t){i=!0,o=t}finally{try{n||null==c.return||c.return()}finally{if(i)throw o}}return r}}(t,e)||uL(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function uL(t,e){if(t){if("string"==typeof t)return uz(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uz(t,e)}}function uz(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function uU(t){var e=uR(t,2),r=e[0],n=e[1],i=r,o=n;return r>n&&(i=n,o=r),[i,o]}function uF(t,e,r){if(t.lte(0))return new uj(0);var n=uI.getDigitCount(t.toNumber()),i=new uj(10).pow(n),o=t.div(i),a=1!==n?.05:.1,c=new uj(Math.ceil(o.div(a).toNumber())).add(r).mul(a).mul(i);return e?c:new uj(Math.ceil(c))}function u$(t,e,r){var n=1,i=new uj(t);if(!i.isint()&&r){var o=Math.abs(t);o<1?(n=new uj(10).pow(uI.getDigitCount(t)-1),i=new uj(Math.floor(i.div(n).toNumber())).mul(n)):o>1&&(i=new uj(Math.floor(t)))}else 0===t?i=new uj(Math.floor((e-1)/2)):r||(i=new uj(Math.floor(t)));var a=Math.floor((e-1)/2);return uT(uM(function(t){return i.add(new uj(t-a).mul(n)).toNumber()}),uN)(0,e)}var uq=uD(function(t){var e=uR(t,2),r=e[0],n=e[1],i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(i,2),c=uR(uU([r,n]),2),u=c[0],s=c[1];if(u===-1/0||s===1/0){var l=s===1/0?[u].concat(uB(uN(0,i-1).map(function(){return 1/0}))):[].concat(uB(uN(0,i-1).map(function(){return-1/0})),[s]);return r>n?uC(l):l}if(u===s)return u$(u,i,o);var f=function t(e,r,n,i){var o,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((r-e)/(n-1)))return{step:new uj(0),tickMin:new uj(0),tickMax:new uj(0)};var c=uF(new uj(r).sub(e).div(n-1),i,a),u=Math.ceil((o=e<=0&&r>=0?new uj(0):(o=new uj(e).add(r).div(2)).sub(new uj(o).mod(c))).sub(e).div(c).toNumber()),s=Math.ceil(new uj(r).sub(o).div(c).toNumber()),l=u+s+1;return l>n?t(e,r,n,i,a+1):(l<n&&(s=r>0?s+(n-l):s,u=r>0?u:u+(n-l)),{step:c,tickMin:o.sub(new uj(u).mul(c)),tickMax:o.add(new uj(s).mul(c))})}(u,s,a,o),p=f.step,d=f.tickMin,h=f.tickMax,y=uI.rangeStep(d,h.add(new uj(.1).mul(p)),p);return r>n?uC(y):y});uD(function(t){var e=uR(t,2),r=e[0],n=e[1],i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(i,2),c=uR(uU([r,n]),2),u=c[0],s=c[1];if(u===-1/0||s===1/0)return[r,n];if(u===s)return u$(u,i,o);var l=uF(new uj(s).sub(u).div(a-1),o,0),f=uT(uM(function(t){return new uj(u).add(new uj(t).mul(l)).toNumber()}),uN)(0,a).filter(function(t){return t>=u&&t<=s});return r>n?uC(f):f});var uW=uD(function(t,e){var r=uR(t,2),n=r[0],i=r[1],o=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=uR(uU([n,i]),2),c=a[0],u=a[1];if(c===-1/0||u===1/0)return[n,i];if(c===u)return[c];var s=Math.max(e,2),l=uF(new uj(u).sub(c).div(s-1),o,0),f=[].concat(uB(uI.rangeStep(new uj(c),new uj(u).sub(new uj(.99).mul(l)),l)),[u]);return n>i?uC(f):f}),uG=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function uX(t){return(uX="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function uH(){return(uH=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function uV(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function uK(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(uK=function(){return!!t})()}function uY(t){return(uY=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function uZ(t,e){return(uZ=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function uJ(t,e,r){return(e=uQ(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function uQ(t){var e=function(t,e){if("object"!=uX(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=uX(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==uX(e)?e:e+""}var u0=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=uY(t),function(t,e){if(e&&("object"===uX(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,uK()?Reflect.construct(t,e||[],uY(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&uZ(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.offset,r=t.layout,n=t.width,i=t.dataKey,o=t.data,c=t.dataPointFormatter,u=t.xAxis,s=t.yAxis,l=tP(function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,uG),!1);"x"===this.props.direction&&"number"!==u.type&&tz(!1);var f=o.map(function(t){var o,f,p=c(t,i),d=p.x,h=p.y,y=p.value,v=p.errorVal;if(!v)return null;var m=[];if(Array.isArray(v)){var b=function(t){if(Array.isArray(t))return t}(v)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,s=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw i}}return c}}(v,2)||function(t,e){if(t){if("string"==typeof t)return uV(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uV(t,e)}}(v,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();o=b[0],f=b[1]}else o=f=v;if("vertical"===r){var g=u.scale,x=h+e,w=x+n,O=x-n,j=g(y-o),S=g(y+f);m.push({x1:S,y1:w,x2:S,y2:O}),m.push({x1:j,y1:x,x2:S,y2:x}),m.push({x1:j,y1:w,x2:j,y2:O})}else if("horizontal"===r){var P=s.scale,A=d+e,E=A-n,k=A+n,_=P(y-o),N=P(y+f);m.push({x1:E,y1:N,x2:k,y2:N}),m.push({x1:A,y1:_,x2:A,y2:N}),m.push({x1:E,y1:_,x2:k,y2:_})}return a().createElement(tG,uH({className:"recharts-errorBar",key:"bar-".concat(m.map(function(t){return"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))},l),m.map(function(t){return a().createElement("line",uH({},t,{key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))}))});return a().createElement(tG,{className:"recharts-errorBars"},f)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,uQ(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function u1(t){return(u1="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function u2(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function u5(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u2(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=u1(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u1(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==u1(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):u2(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}uJ(u0,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),uJ(u0,"displayName","ErrorBar");var u4=function(t){var e,r=t.children,n=t.formattedGraphicalItems,i=t.legendWidth,o=t.legendContent,a=tw(r,ra);if(!a)return null;var c=ra.defaultProps,u=void 0!==c?u5(u5({},c),a.props):{};return e=a.props&&a.props.payload?a.props&&a.props.payload:"children"===o?(n||[]).reduce(function(t,e){var r=e.item,n=e.props,i=n.sectors||n.data||[];return t.concat(i.map(function(t){return{type:a.props.iconType||r.props.legendType,value:t.name,color:t.fill,payload:t}}))},[]):(n||[]).map(function(t){var e=t.item,r=e.type.defaultProps,n=void 0!==r?u5(u5({},r),e.props):{},i=n.dataKey,o=n.name,a=n.legendType;return{inactive:n.hide,dataKey:i,type:u.iconType||a||"square",color:si(e),value:o||i,payload:n}}),u5(u5(u5({},u),ra.getWithHeight(a,i)),{},{payload:e,item:a})};function u3(t){return(u3="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function u6(t){return function(t){if(Array.isArray(t))return u8(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return u8(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return u8(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u8(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function u7(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function u9(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u7(Object(r),!0).forEach(function(e){st(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):u7(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function st(t,e,r){var n;return(n=function(t,e){if("object"!=u3(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u3(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==u3(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function se(t,e,r){return Z()(t)||Z()(e)?r:U(e)?D()(t,e,r):Q()(e)?e(t):r}function sr(t,e,r,n){var i=c6()(t,function(t){return se(t,e)});if("number"===r){var o=i.filter(function(t){return z(t)||parseFloat(t)});return o.length?[c4()(o),c2()(o)]:[1/0,-1/0]}return(n?i.filter(function(t){return!Z()(t)}):i).map(function(t){return U(t)||t instanceof Date?t:""})}var sn=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,i=arguments.length>3?arguments[3]:void 0,o=-1,a=null!=(e=null==r?void 0:r.length)?e:0;if(a<=1)return 0;if(i&&"angleAxis"===i.axisType&&1e-6>=Math.abs(Math.abs(i.range[1]-i.range[0])-360))for(var c=i.range,u=0;u<a;u++){var s=u>0?n[u-1].coordinate:n[a-1].coordinate,l=n[u].coordinate,f=u>=a-1?n[0].coordinate:n[u+1].coordinate,p=void 0;if(R(l-s)!==R(f-l)){var d=[];if(R(f-l)===R(c[1]-c[0])){p=f;var h=l+c[1]-c[0];d[0]=Math.min(h,(h+s)/2),d[1]=Math.max(h,(h+s)/2)}else{p=s;var y=f+c[1]-c[0];d[0]=Math.min(l,(y+l)/2),d[1]=Math.max(l,(y+l)/2)}var v=[Math.min(l,(p+l)/2),Math.max(l,(p+l)/2)];if(t>v[0]&&t<=v[1]||t>=d[0]&&t<=d[1]){o=n[u].index;break}}else{var m=Math.min(s,f),b=Math.max(s,f);if(t>(m+l)/2&&t<=(b+l)/2){o=n[u].index;break}}}else for(var g=0;g<a;g++)if(0===g&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g>0&&g<a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g===a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2){o=r[g].index;break}return o},si=function(t){var e,r,n=t.type.displayName,i=null!=(e=t.type)&&e.defaultProps?u9(u9({},t.type.defaultProps),t.props):t.props,o=i.stroke,a=i.fill;switch(n){case"Line":r=o;break;case"Area":case"Radar":r=o&&"none"!==o?o:a;break;default:r=a}return r},so=function(t){var e=t.barSize,r=t.totalSize,n=t.stackGroups,i=void 0===n?{}:n;if(!i)return{};for(var o={},a=Object.keys(i),c=0,u=a.length;c<u;c++)for(var s=i[a[c]].stackGroups,l=Object.keys(s),f=0,p=l.length;f<p;f++){var d=s[l[f]],h=d.items,y=d.cateAxisId,v=h.filter(function(t){return tv(t.type).indexOf("Bar")>=0});if(v&&v.length){var m=v[0].type.defaultProps,b=void 0!==m?u9(u9({},m),v[0].props):v[0].props,g=b.barSize,x=b[y];o[x]||(o[x]=[]);var w=Z()(g)?e:g;o[x].push({item:v[0],stackList:v.slice(1),barSize:Z()(w)?void 0:q(w,r,0)})}}return o},sa=function(t){var e,r=t.barGap,n=t.barCategoryGap,i=t.bandSize,o=t.sizeList,a=void 0===o?[]:o,c=t.maxBarSize,u=a.length;if(u<1)return null;var s=q(r,i,0,!0),l=[];if(a[0].barSize===+a[0].barSize){var f=!1,p=i/u,d=a.reduce(function(t,e){return t+e.barSize||0},0);(d+=(u-1)*s)>=i&&(d-=(u-1)*s,s=0),d>=i&&p>0&&(f=!0,p*=.9,d=u*p);var h={offset:((i-d)/2|0)-s,size:0};e=a.reduce(function(t,e){var r={item:e.item,position:{offset:h.offset+h.size+s,size:f?p:e.barSize}},n=[].concat(u6(t),[r]);return h=n[n.length-1].position,e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:h})}),n},l)}else{var y=q(n,i,0,!0);i-2*y-(u-1)*s<=0&&(s=0);var v=(i-2*y-(u-1)*s)/u;v>1&&(v>>=0);var m=c===+c?Math.min(v,c):v;e=a.reduce(function(t,e,r){var n=[].concat(u6(t),[{item:e.item,position:{offset:y+(v+s)*r+(v-m)/2,size:m}}]);return e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:n[n.length-1].position})}),n},l)}return e},sc=function(t,e,r,n){var i=r.children,o=r.width,a=r.margin,c=u4({children:i,legendWidth:o-(a.left||0)-(a.right||0)});if(c){var u=n||{},s=u.width,l=u.height,f=c.align,p=c.verticalAlign,d=c.layout;if(("vertical"===d||"horizontal"===d&&"middle"===p)&&"center"!==f&&z(t[f]))return u9(u9({},t),{},st({},f,t[f]+(s||0)));if(("horizontal"===d||"vertical"===d&&"center"===f)&&"middle"!==p&&z(t[p]))return u9(u9({},t),{},st({},p,t[p]+(l||0)))}return t},su=function(t,e,r,n,i){var o=tx(e.props.children,u0).filter(function(t){var e;return e=t.props.direction,!!Z()(i)||("horizontal"===n?"yAxis"===i:"vertical"===n||"x"===e?"xAxis"===i:"y"!==e||"yAxis"===i)});if(o&&o.length){var a=o.map(function(t){return t.props.dataKey});return t.reduce(function(t,e){var n=se(e,r);if(Z()(n))return t;var i=Array.isArray(n)?[c4()(n),c2()(n)]:[n,n],o=a.reduce(function(t,r){var n=se(e,r,0),o=i[0]-Math.abs(Array.isArray(n)?n[0]:n),a=i[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(o,t[0]),Math.max(a,t[1])]},[1/0,-1/0]);return[Math.min(o[0],t[0]),Math.max(o[1],t[1])]},[1/0,-1/0])}return null},ss=function(t,e,r,n,i){var o=e.map(function(e){return su(t,e,r,i,n)}).filter(function(t){return!Z()(t)});return o&&o.length?o.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]):null},sl=function(t,e,r,n,i){var o=e.map(function(e){var o=e.props.dataKey;return"number"===r&&o&&su(t,e,o,n)||sr(t,o,r,i)});if("number"===r)return o.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]);var a={};return o.reduce(function(t,e){for(var r=0,n=e.length;r<n;r++)a[e[r]]||(a[e[r]]=!0,t.push(e[r]));return t},[])},sf=function(t,e){return"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e},sp=function(t,e,r,n){if(n)return t.map(function(t){return t.coordinate});var i,o,a=t.map(function(t){return t.coordinate===e&&(i=!0),t.coordinate===r&&(o=!0),t.coordinate});return i||a.push(e),o||a.push(r),a},sd=function(t,e,r){if(!t)return null;var n=t.scale,i=t.duplicateDomain,o=t.type,a=t.range,c="scaleBand"===t.realScaleType?n.bandwidth()/2:2,u=(e||r)&&"category"===o&&n.bandwidth?n.bandwidth()/c:0;return(u="angleAxis"===t.axisType&&(null==a?void 0:a.length)>=2?2*R(a[0]-a[1])*u:u,e&&(t.ticks||t.niceTicks))?(t.ticks||t.niceTicks).map(function(t){return{coordinate:n(i?i.indexOf(t):t)+u,value:t,offset:u}}).filter(function(t){return!T()(t.coordinate)}):t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(t,e){return{coordinate:n(t)+u,value:t,index:e,offset:u}}):n.ticks&&!r?n.ticks(t.tickCount).map(function(t){return{coordinate:n(t)+u,value:t,offset:u}}):n.domain().map(function(t,e){return{coordinate:n(t)+u,value:i?i[t]:t,index:e,offset:u}})},sh=new WeakMap,sy=function(t,e){if("function"!=typeof e)return t;sh.has(t)||sh.set(t,new WeakMap);var r=sh.get(t);if(r.has(e))return r.get(e);var n=function(){t.apply(void 0,arguments),e.apply(void 0,arguments)};return r.set(e,n),n},sv=function(t,e,r){var i=t.scale,o=t.type,a=t.layout,c=t.axisType;if("auto"===i)return"radial"===a&&"radiusAxis"===c?{scale:nI(),realScaleType:"band"}:"radial"===a&&"angleAxis"===c?{scale:oj(),realScaleType:"linear"}:"category"===o&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!r)?{scale:nB(),realScaleType:"point"}:"category"===o?{scale:nI(),realScaleType:"band"}:{scale:oj(),realScaleType:"linear"};if(N()(i)){var u="scale".concat(eb()(i));return{scale:(n[u]||nB)(),realScaleType:n[u]?u:"point"}}return Q()(i)?{scale:i}:{scale:nB(),realScaleType:"point"}},sm=function(t){var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),i=Math.min(n[0],n[1])-1e-4,o=Math.max(n[0],n[1])+1e-4,a=t(e[0]),c=t(e[r-1]);(a<i||a>o||c<i||c>o)&&t.domain([e[0],e[r-1]])}},sb=function(t,e){if(!t)return null;for(var r=0,n=t.length;r<n;r++)if(t[r].item===e)return t[r].position;return null},sg=function(t,e){if(!e||2!==e.length||!z(e[0])||!z(e[1]))return t;var r=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]),i=[t[0],t[1]];return(!z(t[0])||t[0]<r)&&(i[0]=r),(!z(t[1])||t[1]>n)&&(i[1]=n),i[0]>n&&(i[0]=n),i[1]<r&&(i[1]=r),i},sx={sign:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var i=0,o=0,a=0;a<e;++a){var c=T()(t[a][r][1])?t[a][r][0]:t[a][r][1];c>=0?(t[a][r][0]=i,t[a][r][1]=i+c,i=t[a][r][1]):(t[a][r][0]=o,t[a][r][1]=o+c,o=t[a][r][1])}},expand:function(t,e){if((n=t.length)>0){for(var r,n,i,o=0,a=t[0].length;o<a;++o){for(i=r=0;r<n;++r)i+=t[r][o][1]||0;if(i)for(r=0;r<n;++r)t[r][o][1]/=i}cF(t,e)}},none:cF,silhouette:function(t,e){if((r=t.length)>0){for(var r,n=0,i=t[e[0]],o=i.length;n<o;++n){for(var a=0,c=0;a<r;++a)c+=t[a][n][1]||0;i[n][1]+=i[n][0]=-c/2}cF(t,e)}},wiggle:function(t,e){if((i=t.length)>0&&(n=(r=t[e[0]]).length)>0){for(var r,n,i,o=0,a=1;a<n;++a){for(var c=0,u=0,s=0;c<i;++c){for(var l=t[e[c]],f=l[a][1]||0,p=(f-(l[a-1][1]||0))/2,d=0;d<c;++d){var h=t[e[d]];p+=(h[a][1]||0)-(h[a-1][1]||0)}u+=f,s+=p*f}r[a-1][1]+=r[a-1][0]=o,u&&(o-=s/u)}r[a-1][1]+=r[a-1][0]=o,cF(t,e)}},positive:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var i=0,o=0;o<e;++o){var a=T()(t[o][r][1])?t[o][r][0]:t[o][r][1];a>=0?(t[o][r][0]=i,t[o][r][1]=i+a,i=t[o][r][1]):(t[o][r][0]=0,t[o][r][1]=0)}}},sw=function(t,e,r){var n=e.map(function(t){return t.props.dataKey}),i=sx[r];return(function(){var t=eD([]),e=cq,r=cF,n=cW;function i(i){var o,a,c=Array.from(t.apply(this,arguments),cG),u=c.length,s=-1;for(let t of i)for(o=0,++s;o<u;++o)(c[o][s]=[0,+n(t,c[o].key,s,i)]).data=t;for(o=0,a=c$(e(c));o<u;++o)c[a[o]].index=o;return r(c,a),c}return i.keys=function(e){return arguments.length?(t="function"==typeof e?e:eD(Array.from(e)),i):t},i.value=function(t){return arguments.length?(n="function"==typeof t?t:eD(+t),i):n},i.order=function(t){return arguments.length?(e=null==t?cq:"function"==typeof t?t:eD(Array.from(t)),i):e},i.offset=function(t){return arguments.length?(r=null==t?cF:t,i):r},i})().keys(n).value(function(t,e){return+se(t,e,0)}).order(cq).offset(i)(t)},sO=function(t,e,r,n,i,o){if(!t)return null;var a=(o?e.reverse():e).reduce(function(t,e){var i,o=null!=(i=e.type)&&i.defaultProps?u9(u9({},e.type.defaultProps),e.props):e.props,a=o.stackId;if(o.hide)return t;var c=o[r],u=t[c]||{hasStack:!1,stackGroups:{}};if(U(a)){var s=u.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};s.items.push(e),u.hasStack=!0,u.stackGroups[a]=s}else u.stackGroups[$("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[e]};return u9(u9({},t),{},st({},c,u))},{});return Object.keys(a).reduce(function(e,o){var c=a[o];return c.hasStack&&(c.stackGroups=Object.keys(c.stackGroups).reduce(function(e,o){var a=c.stackGroups[o];return u9(u9({},e),{},st({},o,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:sw(t,a.items,i)}))},{})),u9(u9({},e),{},st({},o,c))},{})},sj=function(t,e){var r=e.realScaleType,n=e.type,i=e.tickCount,o=e.originalDomain,a=e.allowDecimals,c=r||e.scale;if("auto"!==c&&"linear"!==c)return null;if(i&&"number"===n&&o&&("auto"===o[0]||"auto"===o[1])){var u=t.domain();if(!u.length)return null;var s=uq(u,i,a);return t.domain([c4()(s),c2()(s)]),{niceTicks:s}}return i&&"number"===n?{niceTicks:uW(t.domain(),i,a)}:null};function sS(t){var e=t.axis,r=t.ticks,n=t.bandSize,i=t.entry,o=t.index,a=t.dataKey;if("category"===e.type){if(!e.allowDuplicatedCategory&&e.dataKey&&!Z()(i[e.dataKey])){var c=H(r,"value",i[e.dataKey]);if(c)return c.coordinate+n/2}return r[o]?r[o].coordinate+n/2:null}var u=se(i,Z()(a)?e.dataKey:a);return Z()(u)?null:e.scale(u)}var sP=function(t){var e=t.axis,r=t.ticks,n=t.offset,i=t.bandSize,o=t.entry,a=t.index;if("category"===e.type)return r[a]?r[a].coordinate+n:null;var c=se(o,e.dataKey,e.domain[a]);return Z()(c)?null:e.scale(c)-i/2+n},sA=function(t){var e=t.numericAxis,r=e.scale.domain();if("number"===e.type){var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]);return n<=0&&i>=0?0:i<0?i:n}return r[0]},sE=function(t,e){var r,n=(null!=(r=t.type)&&r.defaultProps?u9(u9({},t.type.defaultProps),t.props):t.props).stackId;if(U(n)){var i=e[n];if(i){var o=i.items.indexOf(t);return o>=0?i.stackedData[o]:null}}return null},sk=function(t,e,r){return Object.keys(t).reduce(function(n,i){var o=t[i].stackedData.reduce(function(t,n){var i=n.slice(e,r+1).reduce(function(t,e){return[c4()(e.concat([t[0]]).filter(z)),c2()(e.concat([t[1]]).filter(z))]},[1/0,-1/0]);return[Math.min(t[0],i[0]),Math.max(t[1],i[1])]},[1/0,-1/0]);return[Math.min(o[0],n[0]),Math.max(o[1],n[1])]},[1/0,-1/0]).map(function(t){return t===1/0||t===-1/0?0:t})},s_=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,sN=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,sM=function(t,e,r){if(Q()(t))return t(e,r);if(!Array.isArray(t))return e;var n=[];if(z(t[0]))n[0]=r?t[0]:Math.min(t[0],e[0]);else if(s_.test(t[0])){var i=+s_.exec(t[0])[1];n[0]=e[0]-i}else Q()(t[0])?n[0]=t[0](e[0]):n[0]=e[0];if(z(t[1]))n[1]=r?t[1]:Math.max(t[1],e[1]);else if(sN.test(t[1])){var o=+sN.exec(t[1])[1];n[1]=e[1]+o}else Q()(t[1])?n[1]=t[1](e[1]):n[1]=e[1];return n},sT=function(t,e,r){if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var i=tL()(e,function(t){return t.coordinate}),o=1/0,a=1,c=i.length;a<c;a++){var u=i[a],s=i[a-1];o=Math.min((u.coordinate||0)-(s.coordinate||0),o)}return o===1/0?0:o}return r?void 0:0},sC=function(t,e,r){return!t||!t.length||c7()(t,D()(r,"type.defaultProps.domain"))?e:t},sD=function(t,e){var r=t.type.defaultProps?u9(u9({},t.type.defaultProps),t.props):t.props,n=r.dataKey,i=r.name,o=r.unit,a=r.formatter,c=r.tooltipType,u=r.chartType,s=r.hide;return u9(u9({},tP(t,!1)),{},{dataKey:n,unit:o,formatter:a,name:i||n,color:si(t),value:se(e,n),type:c,payload:e,chartType:u,hide:s})};function sI(t){return(sI="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sB(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sR(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sB(Object(r),!0).forEach(function(e){sL(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sB(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function sL(t,e,r){var n;return(n=function(t,e){if("object"!=sI(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sI(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==sI(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var sz=["Webkit","Moz","O","ms"],sU=function(t,e){if(!t)return null;var r=t.replace(/(\w)/,function(t){return t.toUpperCase()}),n=sz.reduce(function(t,n){return sR(sR({},t),{},sL({},n+r,e))},{});return n[t]=e,n};function sF(t){return(sF="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s$(){return(s$=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sq(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sW(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sq(Object(r),!0).forEach(function(e){sK(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sq(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function sG(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,sY(n.key),n)}}function sX(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(sX=function(){return!!t})()}function sH(t){return(sH=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function sV(t,e){return(sV=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function sK(t,e,r){return(e=sY(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function sY(t){var e=function(t,e){if("object"!=sF(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sF(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sF(e)?e:e+""}var sZ=function(t){var e=t.data,r=t.startIndex,n=t.endIndex,i=t.x,o=t.width,a=t.travellerWidth;if(!e||!e.length)return{};var c=e.length,u=nB().domain(tB()(0,c)).range([i,i+o-a]),s=u.domain().map(function(t){return u(t)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:u(r),endX:u(n),scale:u,scaleValues:s}},sJ=function(t){return t.changedTouches&&!!t.changedTouches.length},sQ=function(t){var e,r;function n(t){var e,r,i;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return r=n,i=[t],r=sH(r),sK(e=function(t,e){if(e&&("object"===sF(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,sX()?Reflect.construct(r,i||[],sH(this).constructor):r.apply(this,i)),"handleDrag",function(t){e.leaveTimer&&(clearTimeout(e.leaveTimer),e.leaveTimer=null),e.state.isTravellerMoving?e.handleTravellerMove(t):e.state.isSlideMoving&&e.handleSlideDrag(t)}),sK(e,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&e.handleDrag(t.changedTouches[0])}),sK(e,"handleDragEnd",function(){e.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var t=e.props,r=t.endIndex,n=t.onDragEnd,i=t.startIndex;null==n||n({endIndex:r,startIndex:i})}),e.detachDragEndListener()}),sK(e,"handleLeaveWrapper",function(){(e.state.isTravellerMoving||e.state.isSlideMoving)&&(e.leaveTimer=window.setTimeout(e.handleDragEnd,e.props.leaveTimeOut))}),sK(e,"handleEnterSlideOrTraveller",function(){e.setState({isTextActive:!0})}),sK(e,"handleLeaveSlideOrTraveller",function(){e.setState({isTextActive:!1})}),sK(e,"handleSlideDragStart",function(t){var r=sJ(t)?t.changedTouches[0]:t;e.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:r.pageX}),e.attachDragEndListener()}),e.travellerDragStartHandlers={startX:e.handleTravellerDragStart.bind(e,"startX"),endX:e.handleTravellerDragStart.bind(e,"endX")},e.state={},e}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&sV(n,t),e=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var e=t.startX,r=t.endX,i=this.state.scaleValues,o=this.props,a=o.gap,c=o.data.length-1,u=Math.min(e,r),s=Math.max(e,r),l=n.getIndexInRange(i,u),f=n.getIndexInRange(i,s);return{startIndex:l-l%a,endIndex:f===c?c:f-f%a}}},{key:"getTextOfTick",value:function(t){var e=this.props,r=e.data,n=e.tickFormatter,i=e.dataKey,o=se(r[t],i,t);return Q()(n)?n(o,t):o}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(t){var e=this.state,r=e.slideMoveStartX,n=e.startX,i=e.endX,o=this.props,a=o.x,c=o.width,u=o.travellerWidth,s=o.startIndex,l=o.endIndex,f=o.onChange,p=t.pageX-r;p>0?p=Math.min(p,a+c-u-i,a+c-u-n):p<0&&(p=Math.max(p,a-n,a-i));var d=this.getIndex({startX:n+p,endX:i+p});(d.startIndex!==s||d.endIndex!==l)&&f&&f(d),this.setState({startX:n+p,endX:i+p,slideMoveStartX:t.pageX})}},{key:"handleTravellerDragStart",value:function(t,e){var r=sJ(e)?e.changedTouches[0]:e;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:t,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(t){var e=this.state,r=e.brushMoveStartX,n=e.movingTravellerId,i=e.endX,o=e.startX,a=this.state[n],c=this.props,u=c.x,s=c.width,l=c.travellerWidth,f=c.onChange,p=c.gap,d=c.data,h={startX:this.state.startX,endX:this.state.endX},y=t.pageX-r;y>0?y=Math.min(y,u+s-l-a):y<0&&(y=Math.max(y,u-a)),h[n]=a+y;var v=this.getIndex(h),m=v.startIndex,b=v.endIndex,g=function(){var t=d.length-1;return"startX"===n&&(i>o?m%p==0:b%p==0)||!!(i<o)&&b===t||"endX"===n&&(i>o?b%p==0:m%p==0)||!!(i>o)&&b===t};this.setState(sK(sK({},n,a+y),"brushMoveStartX",t.pageX),function(){f&&g()&&f(v)})}},{key:"handleTravellerMoveKeyboard",value:function(t,e){var r=this,n=this.state,i=n.scaleValues,o=n.startX,a=n.endX,c=this.state[e],u=i.indexOf(c);if(-1!==u){var s=u+t;if(-1!==s&&!(s>=i.length)){var l=i[s];"startX"===e&&l>=a||"endX"===e&&l<=o||this.setState(sK({},e,l),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,i=t.height,o=t.fill,c=t.stroke;return a().createElement("rect",{stroke:c,fill:o,x:e,y:r,width:n,height:i})}},{key:"renderPanorama",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,i=t.height,c=t.data,u=t.children,s=t.padding,l=o.Children.only(u);return l?a().cloneElement(l,{x:e,y:r,width:n,height:i,margin:s,compact:!0,data:c}):null}},{key:"renderTravellerLayer",value:function(t,e){var r,i,o=this,c=this.props,u=c.y,s=c.travellerWidth,l=c.height,f=c.traveller,p=c.ariaLabel,d=c.data,h=c.startIndex,y=c.endIndex,v=Math.max(t,this.props.x),m=sW(sW({},tP(this.props,!1)),{},{x:v,y:u,width:s,height:l}),b=p||"Min value: ".concat(null==(r=d[h])?void 0:r.name,", Max value: ").concat(null==(i=d[y])?void 0:i.name);return a().createElement(tG,{tabIndex:0,role:"slider","aria-label":b,"aria-valuenow":t,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[e],onTouchStart:this.travellerDragStartHandlers[e],onKeyDown:function(t){["ArrowLeft","ArrowRight"].includes(t.key)&&(t.preventDefault(),t.stopPropagation(),o.handleTravellerMoveKeyboard("ArrowRight"===t.key?1:-1,e))},onFocus:function(){o.setState({isTravellerFocused:!0})},onBlur:function(){o.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},n.renderTraveller(f,m))}},{key:"renderSlide",value:function(t,e){var r=this.props,n=r.y,i=r.height,o=r.stroke,c=r.travellerWidth,u=Math.min(t,e)+c,s=Math.max(Math.abs(e-t)-c,0);return a().createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:o,fillOpacity:.2,x:u,y:n,width:s,height:i})}},{key:"renderText",value:function(){var t=this.props,e=t.startIndex,r=t.endIndex,n=t.y,i=t.height,o=t.travellerWidth,c=t.stroke,u=this.state,s=u.startX,l=u.endX,f={pointerEvents:"none",fill:c};return a().createElement(tG,{className:"recharts-brush-texts"},a().createElement(iu,s$({textAnchor:"end",verticalAnchor:"middle",x:Math.min(s,l)-5,y:n+i/2},f),this.getTextOfTick(e)),a().createElement(iu,s$({textAnchor:"start",verticalAnchor:"middle",x:Math.max(s,l)+o+5,y:n+i/2},f),this.getTextOfTick(r)))}},{key:"render",value:function(){var t=this.props,e=t.data,r=t.className,n=t.children,i=t.x,o=t.y,c=t.width,u=t.height,s=t.alwaysShowText,l=this.state,f=l.startX,p=l.endX,d=l.isTextActive,h=l.isSlideMoving,y=l.isTravellerMoving,v=l.isTravellerFocused;if(!e||!e.length||!z(i)||!z(o)||!z(c)||!z(u)||c<=0||u<=0)return null;var m=(0,A.A)("recharts-brush",r),b=1===a().Children.count(n),g=sU("userSelect","none");return a().createElement(tG,{className:m,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:g},this.renderBackground(),b&&this.renderPanorama(),this.renderSlide(f,p),this.renderTravellerLayer(f,"startX"),this.renderTravellerLayer(p,"endX"),(d||h||y||v||s)&&this.renderText())}}],r=[{key:"renderDefaultTraveller",value:function(t){var e=t.x,r=t.y,n=t.width,i=t.height,o=t.stroke,c=Math.floor(r+i/2)-1;return a().createElement(a().Fragment,null,a().createElement("rect",{x:e,y:r,width:n,height:i,fill:o,stroke:"none"}),a().createElement("line",{x1:e+1,y1:c,x2:e+n-1,y2:c,fill:"none",stroke:"#fff"}),a().createElement("line",{x1:e+1,y1:c+2,x2:e+n-1,y2:c+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,e){var r;return a().isValidElement(t)?a().cloneElement(t,e):Q()(t)?t(e):n.renderDefaultTraveller(e)}},{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,i=t.x,o=t.travellerWidth,a=t.updateId,c=t.startIndex,u=t.endIndex;if(r!==e.prevData||a!==e.prevUpdateId)return sW({prevData:r,prevTravellerWidth:o,prevUpdateId:a,prevX:i,prevWidth:n},r&&r.length?sZ({data:r,width:n,x:i,travellerWidth:o,startIndex:c,endIndex:u}):{scale:null,scaleValues:null});if(e.scale&&(n!==e.prevWidth||i!==e.prevX||o!==e.prevTravellerWidth)){e.scale.range([i,i+n-o]);var s=e.scale.domain().map(function(t){return e.scale(t)});return{prevData:r,prevTravellerWidth:o,prevUpdateId:a,prevX:i,prevWidth:n,startX:e.scale(t.startIndex),endX:e.scale(t.endIndex),scaleValues:s}}return null}},{key:"getIndexInRange",value:function(t,e){for(var r=t.length,n=0,i=r-1;i-n>1;){var o=Math.floor((n+i)/2);t[o]>e?i=o:n=o}return e>=t[i]?i:n}}],e&&sG(n.prototype,e),r&&sG(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(o.PureComponent);function s0(t){return(s0="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s1(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function s2(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s1(Object(r),!0).forEach(function(e){(function(t,e,r){var n;(n=function(t,e){if("object"!=s0(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s0(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==s0(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r})(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s1(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}sK(sQ,"displayName","Brush"),sK(sQ,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var s5=Math.PI/180,s4=function(t,e,r,n){return{x:t+Math.cos(-s5*n)*r,y:e+Math.sin(-s5*n)*r}},s3=function(t,e){var r=t.x,n=t.y;return Math.sqrt(Math.pow(r-e.x,2)+Math.pow(n-e.y,2))},s6=function(t,e){var r=t.x,n=t.y,i=e.cx,o=e.cy,a=s3({x:r,y:n},{x:i,y:o});if(a<=0)return{radius:a};var c=Math.acos((r-i)/a);return n>o&&(c=2*Math.PI-c),{radius:a,angle:180*c/Math.PI,angleInRadian:c}},s8=function(t){var e=t.startAngle,r=t.endAngle,n=Math.min(Math.floor(e/360),Math.floor(r/360));return{startAngle:e-360*n,endAngle:r-360*n}},s7=function(t,e){var r,n=s6({x:t.x,y:t.y},e),i=n.radius,o=n.angle,a=e.innerRadius,c=e.outerRadius;if(i<a||i>c)return!1;if(0===i)return!0;var u=s8(e),s=u.startAngle,l=u.endAngle,f=o;if(s<=l){for(;f>l;)f-=360;for(;f<s;)f+=360;r=f>=s&&f<=l}else{for(;f>s;)f-=360;for(;f<l;)f+=360;r=f>=l&&f<=s}return r?s2(s2({},e),{},{radius:i,angle:f+360*Math.min(Math.floor(e.startAngle/360),Math.floor(e.endAngle/360))}):null};function s9(t){return(s9="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var lt=["offset"];function le(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function lr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ln(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lr(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=s9(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s9(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s9(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lr(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function li(){return(li=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var lo=function(t){var e=t.value,r=t.formatter,n=Z()(t.children)?e:t.children;return Q()(r)?r(n):n},la=function(t,e,r){var n,i,o=t.position,c=t.viewBox,u=t.offset,s=t.className,l=c.cx,f=c.cy,p=c.innerRadius,d=c.outerRadius,h=c.startAngle,y=c.endAngle,v=c.clockWise,m=(p+d)/2,b=R(y-h)*Math.min(Math.abs(y-h),360),g=b>=0?1:-1;"insideStart"===o?(n=h+g*u,i=v):"insideEnd"===o?(n=y-g*u,i=!v):"end"===o&&(n=y+g*u,i=v),i=b<=0?i:!i;var x=s4(l,f,m,n),w=s4(l,f,m,n+(i?1:-1)*359),O="M".concat(x.x,",").concat(x.y,"\n    A").concat(m,",").concat(m,",0,1,").concat(+!i,",\n    ").concat(w.x,",").concat(w.y),j=Z()(t.id)?$("recharts-radial-line-"):t.id;return a().createElement("text",li({},r,{dominantBaseline:"central",className:(0,A.A)("recharts-radial-bar-label",s)}),a().createElement("defs",null,a().createElement("path",{id:j,d:O})),a().createElement("textPath",{xlinkHref:"#".concat(j)},e))},lc=function(t){var e=t.viewBox,r=t.offset,n=t.position,i=e.cx,o=e.cy,a=e.innerRadius,c=e.outerRadius,u=(e.startAngle+e.endAngle)/2;if("outside"===n){var s=s4(i,o,c+r,u),l=s.x;return{x:l,y:s.y,textAnchor:l>=i?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:i,y:o,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:i,y:o,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:i,y:o,textAnchor:"middle",verticalAnchor:"end"};var f=s4(i,o,(a+c)/2,u);return{x:f.x,y:f.y,textAnchor:"middle",verticalAnchor:"middle"}},lu=function(t){var e=t.viewBox,r=t.parentViewBox,n=t.offset,i=t.position,o=e.x,a=e.y,c=e.width,u=e.height,s=u>=0?1:-1,l=s*n,f=s>0?"end":"start",p=s>0?"start":"end",d=c>=0?1:-1,h=d*n,y=d>0?"end":"start",v=d>0?"start":"end";if("top"===i)return ln(ln({},{x:o+c/2,y:a-s*n,textAnchor:"middle",verticalAnchor:f}),r?{height:Math.max(a-r.y,0),width:c}:{});if("bottom"===i)return ln(ln({},{x:o+c/2,y:a+u+l,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(r.y+r.height-(a+u),0),width:c}:{});if("left"===i){var m={x:o-h,y:a+u/2,textAnchor:y,verticalAnchor:"middle"};return ln(ln({},m),r?{width:Math.max(m.x-r.x,0),height:u}:{})}if("right"===i){var b={x:o+c+h,y:a+u/2,textAnchor:v,verticalAnchor:"middle"};return ln(ln({},b),r?{width:Math.max(r.x+r.width-b.x,0),height:u}:{})}var g=r?{width:c,height:u}:{};return"insideLeft"===i?ln({x:o+h,y:a+u/2,textAnchor:v,verticalAnchor:"middle"},g):"insideRight"===i?ln({x:o+c-h,y:a+u/2,textAnchor:y,verticalAnchor:"middle"},g):"insideTop"===i?ln({x:o+c/2,y:a+l,textAnchor:"middle",verticalAnchor:p},g):"insideBottom"===i?ln({x:o+c/2,y:a+u-l,textAnchor:"middle",verticalAnchor:f},g):"insideTopLeft"===i?ln({x:o+h,y:a+l,textAnchor:v,verticalAnchor:p},g):"insideTopRight"===i?ln({x:o+c-h,y:a+l,textAnchor:y,verticalAnchor:p},g):"insideBottomLeft"===i?ln({x:o+h,y:a+u-l,textAnchor:v,verticalAnchor:f},g):"insideBottomRight"===i?ln({x:o+c-h,y:a+u-l,textAnchor:y,verticalAnchor:f},g):te()(i)&&(z(i.x)||L(i.x))&&(z(i.y)||L(i.y))?ln({x:o+q(i.x,c),y:a+q(i.y,u),textAnchor:"end",verticalAnchor:"end"},g):ln({x:o+c/2,y:a+u/2,textAnchor:"middle",verticalAnchor:"middle"},g)};function ls(t){var e,r=t.offset,n=ln({offset:void 0===r?5:r},function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,lt)),i=n.viewBox,c=n.position,u=n.value,s=n.children,l=n.content,f=n.className,p=n.textBreakAll;if(!i||Z()(u)&&Z()(s)&&!(0,o.isValidElement)(l)&&!Q()(l))return null;if((0,o.isValidElement)(l))return(0,o.cloneElement)(l,n);if(Q()(l)){if(e=(0,o.createElement)(l,n),(0,o.isValidElement)(e))return e}else e=lo(n);var d="cx"in i&&z(i.cx),h=tP(n,!0);if(d&&("insideStart"===c||"insideEnd"===c||"end"===c))return la(n,e,h);var y=d?lc(n):lu(n);return a().createElement(iu,li({className:(0,A.A)("recharts-label",void 0===f?"":f)},h,y,{breakAll:p}),e)}ls.displayName="Label";var ll=function(t){var e=t.cx,r=t.cy,n=t.angle,i=t.startAngle,o=t.endAngle,a=t.r,c=t.radius,u=t.innerRadius,s=t.outerRadius,l=t.x,f=t.y,p=t.top,d=t.left,h=t.width,y=t.height,v=t.clockWise,m=t.labelViewBox;if(m)return m;if(z(h)&&z(y)){if(z(l)&&z(f))return{x:l,y:f,width:h,height:y};if(z(p)&&z(d))return{x:p,y:d,width:h,height:y}}return z(l)&&z(f)?{x:l,y:f,width:0,height:0}:z(e)&&z(r)?{cx:e,cy:r,startAngle:i||n||0,endAngle:o||n||0,innerRadius:u||0,outerRadius:s||c||a||0,clockWise:v}:t.viewBox?t.viewBox:{}};ls.parseViewBox=ll,ls.renderCallByParent=function(t,e){var r,n,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&i&&!t.label)return null;var c=t.children,u=ll(t),s=tx(c,ls).map(function(t,r){return(0,o.cloneElement)(t,{viewBox:e||u,key:"label-".concat(r)})});if(!i)return s;return[(r=t.label,n=e||u,!r?null:!0===r?a().createElement(ls,{key:"label-implicit",viewBox:n}):U(r)?a().createElement(ls,{key:"label-implicit",viewBox:n,value:r}):(0,o.isValidElement)(r)?r.type===ls?(0,o.cloneElement)(r,{key:"label-implicit",viewBox:n}):a().createElement(ls,{key:"label-implicit",content:r,viewBox:n}):Q()(r)?a().createElement(ls,{key:"label-implicit",content:r,viewBox:n}):te()(r)?a().createElement(ls,li({viewBox:n},r,{key:"label-implicit"})):null)].concat(function(t){if(Array.isArray(t))return le(t)}(s)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(s)||function(t,e){if(t){if("string"==typeof t)return le(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return le(t,e)}}(s)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())};var lf=function(t,e){var r=t.alwaysShow,n=t.ifOverflow;return r&&(n="extendDomain"),n===e},lp=r(69691),ld=r.n(lp),lh=r(47212),ly=r.n(lh),lv=function(t){return null};lv.displayName="Cell";var lm=r(5359),lb=r.n(lm);function lg(t){return(lg="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var lx=["valueAccessor"],lw=["data","dataKey","clockWise","id","textBreakAll"];function lO(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function lj(){return(lj=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function lS(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lP(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lS(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=lg(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lg(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==lg(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lS(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function lA(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var lE=function(t){return Array.isArray(t.value)?lb()(t.value):t.value};function lk(t){var e=t.valueAccessor,r=void 0===e?lE:e,n=lA(t,lx),i=n.data,o=n.dataKey,c=n.clockWise,u=n.id,s=n.textBreakAll,l=lA(n,lw);return i&&i.length?a().createElement(tG,{className:"recharts-label-list"},i.map(function(t,e){var n=Z()(o)?r(t,e):se(t&&t.payload,o),i=Z()(u)?{}:{id:"".concat(u,"-").concat(e)};return a().createElement(ls,lj({},tP(t,!0),l,i,{parentViewBox:t.parentViewBox,value:n,textBreakAll:s,viewBox:ls.parseViewBox(Z()(c)?t:lP(lP({},t),{},{clockWise:c})),key:"label-".concat(e),index:e}))})):null}lk.displayName="LabelList",lk.renderCallByParent=function(t,e){var r,n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&n&&!t.label)return null;var i=tx(t.children,lk).map(function(t,r){return(0,o.cloneElement)(t,{data:e,key:"labelList-".concat(r)})});return n?[(r=t.label,!r?null:!0===r?a().createElement(lk,{key:"labelList-implicit",data:e}):a().isValidElement(r)||Q()(r)?a().createElement(lk,{key:"labelList-implicit",data:e,content:r}):te()(r)?a().createElement(lk,lj({data:e},r,{key:"labelList-implicit"})):null)].concat(function(t){if(Array.isArray(t))return lO(t)}(i)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(i)||function(t,e){if(t){if("string"==typeof t)return lO(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return lO(t,e)}}(i)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):i};var l_=r(38404),lN=r.n(l_),lM=r(98451),lT=r.n(lM);function lC(t){return(lC="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lD(){return(lD=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function lI(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function lB(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lR(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lB(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=lC(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lC(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==lC(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lB(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var lL=function(t,e,r,n,i){var o,a=r-n;return"M ".concat(t,",").concat(e)+"L ".concat(t+r,",").concat(e)+"L ".concat(t+r-a/2,",").concat(e+i)+"L ".concat(t+r-a/2-n,",").concat(e+i)+"L ".concat(t,",").concat(e," Z")},lz={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},lU=function(t){var e,r=lR(lR({},lz),t),n=(0,o.useRef)(),i=function(t){if(Array.isArray(t))return t}(e=(0,o.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,s=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw i}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return lI(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return lI(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),c=i[0],u=i[1];(0,o.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var t=n.current.getTotalLength();t&&u(t)}catch(t){}},[]);var s=r.x,l=r.y,f=r.upperWidth,p=r.lowerWidth,d=r.height,h=r.className,y=r.animationEasing,v=r.animationDuration,m=r.animationBegin,b=r.isUpdateAnimationActive;if(s!==+s||l!==+l||f!==+f||p!==+p||d!==+d||0===f&&0===p||0===d)return null;var g=(0,A.A)("recharts-trapezoid",h);return b?a().createElement(nb,{canBegin:c>0,from:{upperWidth:0,lowerWidth:0,height:d,x:s,y:l},to:{upperWidth:f,lowerWidth:p,height:d,x:s,y:l},duration:v,animationEasing:y,isActive:b},function(t){var e=t.upperWidth,i=t.lowerWidth,o=t.height,u=t.x,s=t.y;return a().createElement(nb,{canBegin:c>0,from:"0px ".concat(-1===c?1:c,"px"),to:"".concat(c,"px 0px"),attributeName:"strokeDasharray",begin:m,duration:v,easing:y},a().createElement("path",lD({},tP(r,!0),{className:g,d:lL(u,s,e,i,o),ref:n})))}):a().createElement("g",null,a().createElement("path",lD({},tP(r,!0),{className:g,d:lL(s,l,f,p,d)})))};function lF(t){return(lF="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l$(){return(l$=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function lq(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lW(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lq(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=lF(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lF(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==lF(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lq(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var lG=function(t){var e=t.cx,r=t.cy,n=t.radius,i=t.angle,o=t.sign,a=t.isExternal,c=t.cornerRadius,u=t.cornerIsExternal,s=c*(a?1:-1)+n,l=Math.asin(c/s)/s5,f=u?i:i+o*l;return{center:s4(e,r,s,f),circleTangency:s4(e,r,n,f),lineTangency:s4(e,r,s*Math.cos(l*s5),u?i-o*l:i),theta:l}},lX=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,i=t.outerRadius,o=t.startAngle,a=t.endAngle,c=R(a-o)*Math.min(Math.abs(a-o),359.999),u=o+c,s=s4(e,r,i,o),l=s4(e,r,i,u),f="M ".concat(s.x,",").concat(s.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(c)>180),",").concat(+(o>u),",\n    ").concat(l.x,",").concat(l.y,"\n  ");if(n>0){var p=s4(e,r,n,o),d=s4(e,r,n,u);f+="L ".concat(d.x,",").concat(d.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(c)>180),",").concat(+(o<=u),",\n            ").concat(p.x,",").concat(p.y," Z")}else f+="L ".concat(e,",").concat(r," Z");return f},lH=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,i=t.outerRadius,o=t.cornerRadius,a=t.forceCornerRadius,c=t.cornerIsExternal,u=t.startAngle,s=t.endAngle,l=R(s-u),f=lG({cx:e,cy:r,radius:i,angle:u,sign:l,cornerRadius:o,cornerIsExternal:c}),p=f.circleTangency,d=f.lineTangency,h=f.theta,y=lG({cx:e,cy:r,radius:i,angle:s,sign:-l,cornerRadius:o,cornerIsExternal:c}),v=y.circleTangency,m=y.lineTangency,b=y.theta,g=c?Math.abs(u-s):Math.abs(u-s)-h-b;if(g<0)return a?"M ".concat(d.x,",").concat(d.y,"\n        a").concat(o,",").concat(o,",0,0,1,").concat(2*o,",0\n        a").concat(o,",").concat(o,",0,0,1,").concat(-(2*o),",0\n      "):lX({cx:e,cy:r,innerRadius:n,outerRadius:i,startAngle:u,endAngle:s});var x="M ".concat(d.x,",").concat(d.y,"\n    A").concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(g>180),",").concat(+(l<0),",").concat(v.x,",").concat(v.y,"\n    A").concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(m.x,",").concat(m.y,"\n  ");if(n>0){var w=lG({cx:e,cy:r,radius:n,angle:u,sign:l,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),O=w.circleTangency,j=w.lineTangency,S=w.theta,P=lG({cx:e,cy:r,radius:n,angle:s,sign:-l,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),A=P.circleTangency,E=P.lineTangency,k=P.theta,_=c?Math.abs(u-s):Math.abs(u-s)-S-k;if(_<0&&0===o)return"".concat(x,"L").concat(e,",").concat(r,"Z");x+="L".concat(E.x,",").concat(E.y,"\n      A").concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(A.x,",").concat(A.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(_>180),",").concat(+(l>0),",").concat(O.x,",").concat(O.y,"\n      A").concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(j.x,",").concat(j.y,"Z")}else x+="L".concat(e,",").concat(r,"Z");return x},lV={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},lK=function(t){var e,r=lW(lW({},lV),t),n=r.cx,i=r.cy,o=r.innerRadius,c=r.outerRadius,u=r.cornerRadius,s=r.forceCornerRadius,l=r.cornerIsExternal,f=r.startAngle,p=r.endAngle,d=r.className;if(c<o||f===p)return null;var h=(0,A.A)("recharts-sector",d),y=c-o,v=q(u,y,0,!0);return e=v>0&&360>Math.abs(f-p)?lH({cx:n,cy:i,innerRadius:o,outerRadius:c,cornerRadius:Math.min(v,y/2),forceCornerRadius:s,cornerIsExternal:l,startAngle:f,endAngle:p}):lX({cx:n,cy:i,innerRadius:o,outerRadius:c,startAngle:f,endAngle:p}),a().createElement("path",l$({},tP(r,!0),{className:h,d:e,role:"img"}))},lY=["option","shapeType","propTransformer","activeClassName","isActive"];function lZ(t){return(lZ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lJ(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lQ(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lJ(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=lZ(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lZ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==lZ(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lJ(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function l0(t){var e=t.shapeType,r=t.elementProps;switch(e){case"rectangle":return a().createElement(nE,r);case"trapezoid":return a().createElement(lU,r);case"sector":return a().createElement(lK,r);case"symbols":if("symbols"===e)return a().createElement(eK,r);break;default:return null}}function l1(t){var e,r=t.option,n=t.shapeType,i=t.propTransformer,c=t.activeClassName,u=t.isActive,s=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,lY);if((0,o.isValidElement)(r))e=(0,o.cloneElement)(r,lQ(lQ({},s),(0,o.isValidElement)(r)?r.props:r));else if(Q()(r))e=r(s);else if(lN()(r)&&!lT()(r)){var l=(void 0===i?function(t,e){return lQ(lQ({},e),t)}:i)(r,s);e=a().createElement(l0,{shapeType:n,elementProps:l})}else e=a().createElement(l0,{shapeType:n,elementProps:s});return u?a().createElement(tG,{className:void 0===c?"recharts-active-shape":c},e):e}function l2(t,e){return null!=e&&"trapezoids"in t.props}function l5(t,e){return null!=e&&"sectors"in t.props}function l4(t,e){return null!=e&&"points"in t.props}function l3(t,e){var r,n,i=t.x===(null==e||null==(r=e.labelViewBox)?void 0:r.x)||t.x===e.x,o=t.y===(null==e||null==(n=e.labelViewBox)?void 0:n.y)||t.y===e.y;return i&&o}function l6(t,e){var r=t.endAngle===e.endAngle,n=t.startAngle===e.startAngle;return r&&n}function l8(t,e){var r=t.x===e.x,n=t.y===e.y,i=t.z===e.z;return r&&n&&i}var l7=["x","y"];function l9(t){return(l9="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ft(){return(ft=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function fe(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fr(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fe(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=l9(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=l9(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==l9(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fe(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fn(t,e){var r=t.x,n=t.y,i=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,l7),o=parseInt("".concat(r),10),a=parseInt("".concat(n),10),c=parseInt("".concat(e.height||i.height),10),u=parseInt("".concat(e.width||i.width),10);return fr(fr(fr(fr(fr({},e),i),o?{x:o}:{}),a?{y:a}:{}),{},{height:c,width:u,name:e.name,radius:e.radius})}function fi(t){return a().createElement(l1,ft({shapeType:"rectangle",propTransformer:fn,activeClassName:"recharts-active-bar"},t))}var fo=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"==typeof t)return t;var i="number"==typeof r;return i?t(r,n):(i||tz(!1),e)}},fa=["value","background"];function fc(t){return(fc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fu(){return(fu=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function fs(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fl(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fs(Object(r),!0).forEach(function(e){fy(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fs(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ff(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fv(n.key),n)}}function fp(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(fp=function(){return!!t})()}function fd(t){return(fd=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function fh(t,e){return(fh=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function fy(t,e,r){return(e=fv(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fv(t){var e=function(t,e){if("object"!=fc(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fc(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fc(e)?e:e+""}var fm=function(t){var e,r;function n(){var t,e,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var i=arguments.length,o=Array(i),a=0;a<i;a++)o[a]=arguments[a];return e=n,r=[].concat(o),e=fd(e),fy(t=function(t,e){if(e&&("object"===fc(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,fp()?Reflect.construct(e,r||[],fd(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!1}),fy(t,"id",$("recharts-bar-")),fy(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),e&&e()}),fy(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),e&&e()}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&fh(n,t),e=[{key:"renderRectanglesStatically",value:function(t){var e=this,r=this.props,n=r.shape,i=r.dataKey,o=r.activeIndex,c=r.activeBar,u=tP(this.props,!1);return t&&t.map(function(t,r){var s=r===o,l=fl(fl(fl({},u),t),{},{isActive:s,option:s?c:n,index:r,dataKey:i,onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd});return a().createElement(tG,fu({className:"recharts-bar-rectangle"},tl(e.props,t,r),{key:"rectangle-".concat(null==t?void 0:t.x,"-").concat(null==t?void 0:t.y,"-").concat(null==t?void 0:t.value,"-").concat(r)}),a().createElement(fi,l))})}},{key:"renderRectanglesWithAnimation",value:function(){var t=this,e=this.props,r=e.data,n=e.layout,i=e.isAnimationActive,o=e.animationBegin,c=e.animationDuration,u=e.animationEasing,s=e.animationId,l=this.state.prevData;return a().createElement(nb,{begin:o,duration:c,isActive:i,easing:u,from:{t:0},to:{t:1},key:"bar-".concat(s),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(e){var i=e.t,o=r.map(function(t,e){var r=l&&l[e];if(r){var o=X(r.x,t.x),a=X(r.y,t.y),c=X(r.width,t.width),u=X(r.height,t.height);return fl(fl({},t),{},{x:o(i),y:a(i),width:c(i),height:u(i)})}if("horizontal"===n){var s=X(0,t.height)(i);return fl(fl({},t),{},{y:t.y+t.height-s,height:s})}var f=X(0,t.width)(i);return fl(fl({},t),{},{width:f})});return a().createElement(tG,null,t.renderRectanglesStatically(o))})}},{key:"renderRectangles",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return r&&e&&e.length&&(!n||!c7()(n,e))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(e)}},{key:"renderBackground",value:function(){var t=this,e=this.props,r=e.data,n=e.dataKey,i=e.activeIndex,o=tP(this.props.background,!1);return r.map(function(e,r){e.value;var c=e.background,u=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(e,fa);if(!c)return null;var s=fl(fl(fl(fl(fl({},u),{},{fill:"#eee"},c),o),tl(t.props,e,r)),{},{onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd,dataKey:n,index:r,className:"recharts-bar-background-rectangle"});return a().createElement(fi,fu({key:"background-bar-".concat(r),option:t.props.background,isActive:r===i},s))})}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.data,i=r.xAxis,o=r.yAxis,c=r.layout,u=tx(r.children,u0);if(!u)return null;var s="vertical"===c?n[0].height/2:n[0].width/2,l=function(t,e){var r=Array.isArray(t.value)?t.value[1]:t.value;return{x:t.x,y:t.y,value:r,errorVal:se(t,e)}};return a().createElement(tG,{clipPath:t?"url(#clipPath-".concat(e,")"):null},u.map(function(t){return a().cloneElement(t,{key:"error-bar-".concat(e,"-").concat(t.props.dataKey),data:n,xAxis:i,yAxis:o,layout:c,offset:s,dataPointFormatter:l})}))}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.data,n=t.className,i=t.xAxis,o=t.yAxis,c=t.left,u=t.top,s=t.width,l=t.height,f=t.isAnimationActive,p=t.background,d=t.id;if(e||!r||!r.length)return null;var h=this.state.isAnimationFinished,y=(0,A.A)("recharts-bar",n),v=i&&i.allowDataOverflow,m=o&&o.allowDataOverflow,b=v||m,g=Z()(d)?this.id:d;return a().createElement(tG,{className:y},v||m?a().createElement("defs",null,a().createElement("clipPath",{id:"clipPath-".concat(g)},a().createElement("rect",{x:v?c:c-s/2,y:m?u:u-l/2,width:v?s:2*s,height:m?l:2*l}))):null,a().createElement(tG,{className:"recharts-bar-rectangles",clipPath:b?"url(#clipPath-".concat(g,")"):null},p?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(b,g),(!f||h)&&lk.renderCallByParent(this.props,r))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}}],e&&ff(n.prototype,e),r&&ff(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(o.PureComponent);function fb(t){return(fb="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fg(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fj(n.key),n)}}function fx(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fw(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fx(Object(r),!0).forEach(function(e){fO(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fx(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fO(t,e,r){return(e=fj(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fj(t){var e=function(t,e){if("object"!=fb(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fb(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fb(e)?e:e+""}fy(fm,"displayName","Bar"),fy(fm,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!en.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),fy(fm,"getComposedData",function(t){var e=t.props,r=t.item,n=t.barPosition,i=t.bandSize,o=t.xAxis,a=t.yAxis,c=t.xAxisTicks,u=t.yAxisTicks,s=t.stackedData,l=t.dataStartIndex,f=t.displayedData,p=t.offset,d=sb(n,r);if(!d)return null;var h=e.layout,y=r.type.defaultProps,v=void 0!==y?fl(fl({},y),r.props):r.props,m=v.dataKey,b=v.children,g=v.minPointSize,x="horizontal"===h?a:o,w=s?x.scale.domain():null,O=sA({numericAxis:x}),j=tx(b,lv),S=f.map(function(t,e){s?f=sg(s[l+e],w):Array.isArray(f=se(t,m))||(f=[O,f]);var n=fo(g,fm.defaultProps.minPointSize)(f[1],e);if("horizontal"===h){var f,p,y,v,b,x,S,P=[a.scale(f[0]),a.scale(f[1])],A=P[0],E=P[1];p=sP({axis:o,ticks:c,bandSize:i,offset:d.offset,entry:t,index:e}),y=null!=(S=null!=E?E:A)?S:void 0,v=d.size;var k=A-E;if(b=Number.isNaN(k)?0:k,x={x:p,y:a.y,width:v,height:a.height},Math.abs(n)>0&&Math.abs(b)<Math.abs(n)){var _=R(b||n)*(Math.abs(n)-Math.abs(b));y-=_,b+=_}}else{var N=[o.scale(f[0]),o.scale(f[1])],M=N[0],T=N[1];if(p=M,y=sP({axis:a,ticks:u,bandSize:i,offset:d.offset,entry:t,index:e}),v=T-M,b=d.size,x={x:o.x,y:y,width:o.width,height:b},Math.abs(n)>0&&Math.abs(v)<Math.abs(n)){var C=R(v||n)*(Math.abs(n)-Math.abs(v));v+=C}}return fl(fl(fl({},t),{},{x:p,y:y,width:v,height:b,value:s?f:f[1],payload:t,background:x},j&&j[e]&&j[e].props),{},{tooltipPayload:[sD(r,t)],tooltipPosition:{x:p+v/2,y:y+b/2}})});return fl({data:S,layout:h},p)});var fS=function(t,e){var r=t.x,n=t.y,i=e.x,o=e.y;return{x:Math.min(r,i),y:Math.min(n,o),width:Math.abs(i-r),height:Math.abs(o-n)}},fP=function(){var t,e;function r(t){if(!(this instanceof r))throw TypeError("Cannot call a class as a function");this.scale=t}return t=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.bandAware,n=e.position;if(void 0!==t){if(n)switch(n){case"start":default:return this.scale(t);case"middle":var i=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+i;case"end":var o=this.bandwidth?this.bandwidth():0;return this.scale(t)+o}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}},{key:"isInRange",value:function(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}}],e=[{key:"create",value:function(t){return new r(t)}}],t&&fg(r.prototype,t),e&&fg(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();fO(fP,"EPS",1e-4);var fA=function(t){var e=Object.keys(t).reduce(function(e,r){return fw(fw({},e),{},fO({},r,fP.create(t[r])))},{});return fw(fw({},e),{},{apply:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,i=r.position;return ld()(t,function(t,r){return e[r].apply(t,{bandAware:n,position:i})})},isInRange:function(t){return ly()(t,function(t,r){return e[r].isInRange(t)})}})},fE=function(t){var e=t.width,r=t.height,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=(n%180+180)%180*Math.PI/180,o=Math.atan(r/e);return Math.abs(i>o&&i<Math.PI-o?r/Math.sin(i):e/Math.cos(i))};function fk(){return(fk=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function f_(t){return(f_="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fN(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fM(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fN(Object(r),!0).forEach(function(e){fI(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fN(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fT(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(fT=function(){return!!t})()}function fC(t){return(fC=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function fD(t,e){return(fD=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function fI(t,e,r){return(e=fB(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fB(t){var e=function(t,e){if("object"!=f_(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f_(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f_(e)?e:e+""}var fR=function(t){var e=t.x,r=t.y,n=t.xAxis,i=t.yAxis,o=fA({x:n.scale,y:i.scale}),a=o.apply({x:e,y:r},{bandAware:!0});return lf(t,"discard")&&!o.isInRange(a)?null:a},fL=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=fC(t),function(t,e){if(e&&("object"===f_(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,fT()?Reflect.construct(t,e||[],fC(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&fD(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x,n=t.y,i=t.r,o=t.alwaysShow,c=t.clipPathId,u=U(e),s=U(n);if(K(void 0===o,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!u||!s)return null;var l=fR(this.props);if(!l)return null;var f=l.x,p=l.y,d=this.props,h=d.shape,y=d.className,v=fM(fM({clipPath:lf(this.props,"hidden")?"url(#".concat(c,")"):void 0},tP(this.props,!0)),{},{cx:f,cy:p});return a().createElement(tG,{className:(0,A.A)("recharts-reference-dot",y)},r.renderDot(h,v),ls.renderCallByParent(this.props,{x:f-i,y:p-i,width:2*i,height:2*i}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fB(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);fI(fL,"displayName","ReferenceDot"),fI(fL,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),fI(fL,"renderDot",function(t,e){var r;return a().isValidElement(t)?a().cloneElement(t,e):Q()(t)?t(e):a().createElement(ru,fk({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"}))});var fz=r(67367),fU=r.n(fz),fF=r(22964),f$=r.n(fF),fq=r(86451),fW=r.n(fq)()(function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}},function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")}),fG=(0,o.createContext)(void 0),fX=(0,o.createContext)(void 0),fH=(0,o.createContext)(void 0),fV=(0,o.createContext)({}),fK=(0,o.createContext)(void 0),fY=(0,o.createContext)(0),fZ=(0,o.createContext)(0),fJ=function(t){var e=t.state,r=e.xAxisMap,n=e.yAxisMap,i=e.offset,o=t.clipPathId,c=t.children,u=t.width,s=t.height,l=fW(i);return a().createElement(fG.Provider,{value:r},a().createElement(fX.Provider,{value:n},a().createElement(fV.Provider,{value:i},a().createElement(fH.Provider,{value:l},a().createElement(fK.Provider,{value:o},a().createElement(fY.Provider,{value:s},a().createElement(fZ.Provider,{value:u},c)))))))},fQ=function(t){var e=(0,o.useContext)(fG);null==e&&tz(!1);var r=e[t];return null==r&&tz(!1),r},f0=function(){var t=(0,o.useContext)(fX);return f$()(t,function(t){return ly()(t.domain,Number.isFinite)})||W(t)},f1=function(t){var e=(0,o.useContext)(fX);null==e&&tz(!1);var r=e[t];return null==r&&tz(!1),r},f2=function(){return(0,o.useContext)(fZ)},f5=function(){return(0,o.useContext)(fY)};function f4(t){return(f4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function f3(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(f3=function(){return!!t})()}function f6(t){return(f6=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function f8(t,e){return(f8=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function f7(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function f9(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f7(Object(r),!0).forEach(function(e){pt(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f7(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function pt(t,e,r){return(e=pe(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function pe(t){var e=function(t,e){if("object"!=f4(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f4(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f4(e)?e:e+""}function pr(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function pn(){return(pn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var pi=function(t,e){var r;return a().isValidElement(t)?a().cloneElement(t,e):Q()(t)?t(e):a().createElement("line",pn({},e,{className:"recharts-reference-line-line"}))},po=function(t,e,r,n,i,o,a,c,u){var s=i.x,l=i.y,f=i.width,p=i.height;if(r){var d=u.y,h=t.y.apply(d,{position:o});if(lf(u,"discard")&&!t.y.isInRange(h))return null;var y=[{x:s+f,y:h},{x:s,y:h}];return"left"===c?y.reverse():y}if(e){var v=u.x,m=t.x.apply(v,{position:o});if(lf(u,"discard")&&!t.x.isInRange(m))return null;var b=[{x:m,y:l+p},{x:m,y:l}];return"top"===a?b.reverse():b}if(n){var g=u.segment.map(function(e){return t.apply(e,{position:o})});return lf(u,"discard")&&fU()(g,function(e){return!t.isInRange(e)})?null:g}return null};function pa(t){var e,r=t.x,n=t.y,i=t.segment,c=t.xAxisId,u=t.yAxisId,s=t.shape,l=t.className,f=t.alwaysShow,p=(0,o.useContext)(fK),d=fQ(c),h=f1(u),y=(0,o.useContext)(fH);if(!p||!y)return null;K(void 0===f,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var v=po(fA({x:d.scale,y:h.scale}),U(r),U(n),i&&2===i.length,y,t.position,d.orientation,h.orientation,t);if(!v)return null;var m=function(t){if(Array.isArray(t))return t}(v)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,s=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw i}}return c}}(v,2)||function(t,e){if(t){if("string"==typeof t)return pr(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return pr(t,e)}}(v,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),b=m[0],g=b.x,x=b.y,w=m[1],O=w.x,j=w.y,S=f9(f9({clipPath:lf(t,"hidden")?"url(#".concat(p,")"):void 0},tP(t,!0)),{},{x1:g,y1:x,x2:O,y2:j});return a().createElement(tG,{className:(0,A.A)("recharts-reference-line",l)},pi(s,S),ls.renderCallByParent(t,fS({x:(e={x1:g,y1:x,x2:O,y2:j}).x1,y:e.y1},{x:e.x2,y:e.y2})))}var pc=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=f6(t),function(t,e){if(e&&("object"===f4(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,f3()?Reflect.construct(t,e||[],f6(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&f8(r,t),e=[{key:"render",value:function(){return a().createElement(pa,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,pe(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function pu(){return(pu=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function ps(t){return(ps="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function pl(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function pf(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pl(Object(r),!0).forEach(function(e){py(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pl(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}pt(pc,"displayName","ReferenceLine"),pt(pc,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function pp(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(pp=function(){return!!t})()}function pd(t){return(pd=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function ph(t,e){return(ph=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function py(t,e,r){return(e=pv(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function pv(t){var e=function(t,e){if("object"!=ps(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ps(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ps(e)?e:e+""}var pm=function(t,e,r,n,i){var o=i.x1,a=i.x2,c=i.y1,u=i.y2,s=i.xAxis,l=i.yAxis;if(!s||!l)return null;var f=fA({x:s.scale,y:l.scale}),p={x:t?f.x.apply(o,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(c,{position:"start"}):f.y.rangeMin},d={x:e?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(u,{position:"end"}):f.y.rangeMax};return!lf(i,"discard")||f.isInRange(p)&&f.isInRange(d)?fS(p,d):null},pb=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=pd(t),function(t,e){if(e&&("object"===ps(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,pp()?Reflect.construct(t,e||[],pd(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&ph(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x1,n=t.x2,i=t.y1,o=t.y2,c=t.className,u=t.alwaysShow,s=t.clipPathId;K(void 0===u,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var l=U(e),f=U(n),p=U(i),d=U(o),h=this.props.shape;if(!l&&!f&&!p&&!d&&!h)return null;var y=pm(l,f,p,d,this.props);if(!y&&!h)return null;var v=lf(this.props,"hidden")?"url(#".concat(s,")"):void 0;return a().createElement(tG,{className:(0,A.A)("recharts-reference-area",c)},r.renderRect(h,pf(pf({clipPath:v},tP(this.props,!0)),y)),ls.renderCallByParent(this.props,y))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,pv(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function pg(t){return function(t){if(Array.isArray(t))return px(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return px(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return px(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function px(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}py(pb,"displayName","ReferenceArea"),py(pb,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),py(pb,"renderRect",function(t,e){var r;return a().isValidElement(t)?a().cloneElement(t,e):Q()(t)?t(e):a().createElement(nE,pu({},e,{className:"recharts-reference-area-rect"}))});var pw=function(t,e,r,n,i){var o=tx(t,pc),a=tx(t,fL),c=[].concat(pg(o),pg(a)),u=tx(t,pb),s="".concat(n,"Id"),l=n[0],f=e;if(c.length&&(f=c.reduce(function(t,e){if(e.props[s]===r&&lf(e.props,"extendDomain")&&z(e.props[l])){var n=e.props[l];return[Math.min(t[0],n),Math.max(t[1],n)]}return t},f)),u.length){var p="".concat(l,"1"),d="".concat(l,"2");f=u.reduce(function(t,e){if(e.props[s]===r&&lf(e.props,"extendDomain")&&z(e.props[p])&&z(e.props[d])){var n=e.props[p],i=e.props[d];return[Math.min(t[0],n,i),Math.max(t[1],n,i)]}return t},f)}return i&&i.length&&(f=i.reduce(function(t,e){return z(e)?[Math.min(t[0],e),Math.max(t[1],e)]:t},f)),f},pO=r(11117),pj=new(r.n(pO)()),pS="recharts.syncMouseEvents";function pP(t){return(pP="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function pA(t,e,r){return(e=pE(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function pE(t){var e=function(t,e){if("object"!=pP(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pP(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pP(e)?e:e+""}var pk=function(){var t,e;return t=function t(){if(!(this instanceof t))throw TypeError("Cannot call a class as a function");pA(this,"activeIndex",0),pA(this,"coordinateList",[]),pA(this,"layout","horizontal")},e=[{key:"setDetails",value:function(t){var e,r=t.coordinateList,n=void 0===r?null:r,i=t.container,o=void 0===i?null:i,a=t.layout,c=void 0===a?null:a,u=t.offset,s=void 0===u?null:u,l=t.mouseHandlerCallback,f=void 0===l?null:l;this.coordinateList=null!=(e=null!=n?n:this.coordinateList)?e:[],this.container=null!=o?o:this.container,this.layout=null!=c?c:this.layout,this.offset=null!=s?s:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(t){if(0!==this.coordinateList.length)switch(t.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(t){this.activeIndex=t}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout&&0!==this.coordinateList.length){var t,e,r=this.container.getBoundingClientRect(),n=r.x,i=r.y,o=r.height,a=this.coordinateList[this.activeIndex].coordinate,c=(null==(t=window)?void 0:t.scrollX)||0,u=(null==(e=window)?void 0:e.scrollY)||0,s=i+this.offset.top+o/2+u;this.mouseHandlerCallback({pageX:n+a+c,pageY:s})}}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,pE(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}();function p_(){}function pN(t,e,r){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+r)/6)}function pM(t){this._context=t}function pT(t){this._context=t}function pC(t){this._context=t}pM.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:pN(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:pN(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},pT.prototype={areaStart:p_,areaEnd:p_,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:pN(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},pC.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+t)/6,n=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:pN(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};class pD{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function pI(t){this._context=t}function pB(t){this._context=t}function pR(t){return new pB(t)}pI.prototype={areaStart:p_,areaEnd:p_,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t*=1,e*=1,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}};function pL(t,e,r){var n=t._x1-t._x0,i=e-t._x1,o=(t._y1-t._y0)/(n||i<0&&-0),a=(r-t._y1)/(i||n<0&&-0);return((o<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(o),Math.abs(a),.5*Math.abs((o*i+a*n)/(n+i)))||0}function pz(t,e){var r=t._x1-t._x0;return r?(3*(t._y1-t._y0)/r-e)/2:e}function pU(t,e,r){var n=t._x0,i=t._y0,o=t._x1,a=t._y1,c=(o-n)/3;t._context.bezierCurveTo(n+c,i+c*e,o-c,a-c*r,o,a)}function pF(t){this._context=t}function p$(t){this._context=new pq(t)}function pq(t){this._context=t}function pW(t){this._context=t}function pG(t){var e,r,n=t.length-1,i=Array(n),o=Array(n),a=Array(n);for(i[0]=0,o[0]=2,a[0]=t[0]+2*t[1],e=1;e<n-1;++e)i[e]=1,o[e]=4,a[e]=4*t[e]+2*t[e+1];for(i[n-1]=2,o[n-1]=7,a[n-1]=8*t[n-1]+t[n],e=1;e<n;++e)r=i[e]/o[e-1],o[e]-=r,a[e]-=r*a[e-1];for(i[n-1]=a[n-1]/o[n-1],e=n-2;e>=0;--e)i[e]=(a[e]-i[e+1])/o[e];for(e=0,o[n-1]=(t[n]+i[n-1])/2;e<n-1;++e)o[e]=2*t[e+1]-i[e+1];return[i,o]}function pX(t,e){this._context=t,this._t=e}function pH(t){return t[0]}function pV(t){return t[1]}function pK(t,e){var r=eD(!0),n=null,i=pR,o=null,a=eU(c);function c(c){var u,s,l,f=(c=c$(c)).length,p=!1;for(null==n&&(o=i(l=a())),u=0;u<=f;++u)!(u<f&&r(s=c[u],u,c))===p&&((p=!p)?o.lineStart():o.lineEnd()),p&&o.point(+t(s,u,c),+e(s,u,c));if(l)return o=null,l+""||null}return t="function"==typeof t?t:void 0===t?pH:eD(t),e="function"==typeof e?e:void 0===e?pV:eD(e),c.x=function(e){return arguments.length?(t="function"==typeof e?e:eD(+e),c):t},c.y=function(t){return arguments.length?(e="function"==typeof t?t:eD(+t),c):e},c.defined=function(t){return arguments.length?(r="function"==typeof t?t:eD(!!t),c):r},c.curve=function(t){return arguments.length?(i=t,null!=n&&(o=i(n)),c):i},c.context=function(t){return arguments.length?(null==t?n=o=null:o=i(n=t),c):n},c}function pY(t,e,r){var n=null,i=eD(!0),o=null,a=pR,c=null,u=eU(s);function s(s){var l,f,p,d,h,y=(s=c$(s)).length,v=!1,m=Array(y),b=Array(y);for(null==o&&(c=a(h=u())),l=0;l<=y;++l){if(!(l<y&&i(d=s[l],l,s))===v)if(v=!v)f=l,c.areaStart(),c.lineStart();else{for(c.lineEnd(),c.lineStart(),p=l-1;p>=f;--p)c.point(m[p],b[p]);c.lineEnd(),c.areaEnd()}v&&(m[l]=+t(d,l,s),b[l]=+e(d,l,s),c.point(n?+n(d,l,s):m[l],r?+r(d,l,s):b[l]))}if(h)return c=null,h+""||null}function l(){return pK().defined(i).curve(a).context(o)}return t="function"==typeof t?t:void 0===t?pH:eD(+t),e="function"==typeof e?e:void 0===e?eD(0):eD(+e),r="function"==typeof r?r:void 0===r?pV:eD(+r),s.x=function(e){return arguments.length?(t="function"==typeof e?e:eD(+e),n=null,s):t},s.x0=function(e){return arguments.length?(t="function"==typeof e?e:eD(+e),s):t},s.x1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:eD(+t),s):n},s.y=function(t){return arguments.length?(e="function"==typeof t?t:eD(+t),r=null,s):e},s.y0=function(t){return arguments.length?(e="function"==typeof t?t:eD(+t),s):e},s.y1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:eD(+t),s):r},s.lineX0=s.lineY0=function(){return l().x(t).y(e)},s.lineY1=function(){return l().x(t).y(r)},s.lineX1=function(){return l().x(n).y(e)},s.defined=function(t){return arguments.length?(i="function"==typeof t?t:eD(!!t),s):i},s.curve=function(t){return arguments.length?(a=t,null!=o&&(c=a(o)),s):a},s.context=function(t){return arguments.length?(null==t?o=c=null:c=a(o=t),s):o},s}function pZ(t){return(pZ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function pJ(){return(pJ=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function pQ(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p0(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pQ(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=pZ(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pZ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pZ(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pQ(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}pB.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}},pF.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:pU(this,this._t0,pz(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var r=NaN;if(e*=1,(t*=1)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,pU(this,pz(this,r=pL(this,t,e)),r);break;default:pU(this,this._t0,r=pL(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=r}}},(p$.prototype=Object.create(pF.prototype)).point=function(t,e){pF.prototype.point.call(this,e,t)},pq.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,r,n,i,o){this._context.bezierCurveTo(e,t,n,r,o,i)}},pW.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,r=t.length;if(r)if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===r)this._context.lineTo(t[1],e[1]);else for(var n=pG(t),i=pG(e),o=0,a=1;a<r;++o,++a)this._context.bezierCurveTo(n[0][o],i[0][o],n[1][o],i[1][o],t[a],e[a]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},pX.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var r=this._x*(1-this._t)+t*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,e)}}this._x=t,this._y=e}};var p1={curveBasisClosed:function(t){return new pT(t)},curveBasisOpen:function(t){return new pC(t)},curveBasis:function(t){return new pM(t)},curveBumpX:function(t){return new pD(t,!0)},curveBumpY:function(t){return new pD(t,!1)},curveLinearClosed:function(t){return new pI(t)},curveLinear:pR,curveMonotoneX:function(t){return new pF(t)},curveMonotoneY:function(t){return new p$(t)},curveNatural:function(t){return new pW(t)},curveStep:function(t){return new pX(t,.5)},curveStepAfter:function(t){return new pX(t,1)},curveStepBefore:function(t){return new pX(t,0)}},p2=function(t){return t.x===+t.x&&t.y===+t.y},p5=function(t){return t.x},p4=function(t){return t.y},p3=function(t,e){if(Q()(t))return t;var r="curve".concat(eb()(t));return("curveMonotone"===r||"curveBump"===r)&&e?p1["".concat(r).concat("vertical"===e?"Y":"X")]:p1[r]||pR},p6=function(t){var e,r=t.type,n=t.points,i=void 0===n?[]:n,o=t.baseLine,a=t.layout,c=t.connectNulls,u=void 0!==c&&c,s=p3(void 0===r?"linear":r,a),l=u?i.filter(function(t){return p2(t)}):i;if(Array.isArray(o)){var f=u?o.filter(function(t){return p2(t)}):o,p=l.map(function(t,e){return p0(p0({},t),{},{base:f[e]})});return(e="vertical"===a?pY().y(p4).x1(p5).x0(function(t){return t.base.x}):pY().x(p5).y1(p4).y0(function(t){return t.base.y})).defined(p2).curve(s),e(p)}return(e="vertical"===a&&z(o)?pY().y(p4).x1(p5).x0(o):z(o)?pY().x(p5).y1(p4).y0(o):pK().x(p5).y(p4)).defined(p2).curve(s),e(l)},p8=function(t){var e=t.className,r=t.points,n=t.path,i=t.pathRef;if((!r||!r.length)&&!n)return null;var o=r&&r.length?p6(t):n;return a().createElement("path",pJ({},tP(t,!1),ts(t),{className:(0,A.A)("recharts-curve",e),d:o,ref:i}))};function p7(t){return(p7="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var p9=["x","y","top","left","width","height","className"];function dt(){return(dt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function de(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}var dr=function(t){var e=t.x,r=void 0===e?0:e,n=t.y,i=void 0===n?0:n,o=t.top,c=void 0===o?0:o,u=t.left,s=void 0===u?0:u,l=t.width,f=void 0===l?0:l,p=t.height,d=void 0===p?0:p,h=t.className,y=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?de(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=p7(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=p7(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==p7(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):de(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({x:r,y:i,top:c,left:s,width:f,height:d},function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,p9));return z(r)&&z(i)&&z(f)&&z(d)&&z(c)&&z(s)?a().createElement("path",dt({},tP(y,!0),{className:(0,A.A)("recharts-cross",h),d:"M".concat(r,",").concat(c,"v").concat(d,"M").concat(s,",").concat(i,"h").concat(f)})):null};function dn(t){var e=t.cx,r=t.cy,n=t.radius,i=t.startAngle,o=t.endAngle;return{points:[s4(e,r,n,i),s4(e,r,n,o)],cx:e,cy:r,radius:n,startAngle:i,endAngle:o}}function di(t){return(di="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function da(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function dc(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?da(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=di(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=di(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==di(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):da(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function du(t){var e,r,n,i,a=t.element,c=t.tooltipEventType,u=t.isActive,s=t.activeCoordinate,l=t.activePayload,f=t.offset,p=t.activeTooltipIndex,d=t.tooltipAxisBandSize,h=t.layout,y=t.chartName,v=null!=(r=a.props.cursor)?r:null==(n=a.type.defaultProps)?void 0:n.cursor;if(!a||!v||!u||!s||"ScatterChart"!==y&&"axis"!==c)return null;var m=p8;if("ScatterChart"===y)i=s,m=dr;else if("BarChart"===y)e=d/2,i={stroke:"none",fill:"#ccc",x:"horizontal"===h?s.x-e:f.left+.5,y:"horizontal"===h?f.top+.5:s.y-e,width:"horizontal"===h?d:f.width-1,height:"horizontal"===h?f.height-1:d},m=nE;else if("radial"===h){var b=dn(s),g=b.cx,x=b.cy,w=b.radius;i={cx:g,cy:x,startAngle:b.startAngle,endAngle:b.endAngle,innerRadius:w,outerRadius:w},m=lK}else i={points:function(t,e,r){var n,i,o,a;if("horizontal"===t)o=n=e.x,i=r.top,a=r.top+r.height;else if("vertical"===t)a=i=e.y,n=r.left,o=r.left+r.width;else if(null!=e.cx&&null!=e.cy)if("centric"!==t)return dn(e);else{var c=e.cx,u=e.cy,s=e.innerRadius,l=e.outerRadius,f=e.angle,p=s4(c,u,s,f),d=s4(c,u,l,f);n=p.x,i=p.y,o=d.x,a=d.y}return[{x:n,y:i},{x:o,y:a}]}(h,s,f)},m=p8;var O=dc(dc(dc(dc({stroke:"#ccc",pointerEvents:"none"},f),i),tP(v,!1)),{},{payload:l,payloadIndex:p,className:(0,A.A)("recharts-tooltip-cursor",v.className)});return(0,o.isValidElement)(v)?(0,o.cloneElement)(v,O):(0,o.createElement)(m,O)}var ds=["item"],dl=["children","className","width","height","style","compact","title","desc"];function df(t){return(df="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function dp(){return(dp=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function dd(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,s=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw i}}return c}}(t,e)||dg(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function dh(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function dy(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(dy=function(){return!!t})()}function dv(t){return(dv=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function dm(t,e){return(dm=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function db(t){return function(t){if(Array.isArray(t))return dx(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||dg(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function dg(t,e){if(t){if("string"==typeof t)return dx(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return dx(t,e)}}function dx(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function dw(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function dO(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?dw(Object(r),!0).forEach(function(e){dj(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):dw(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function dj(t,e,r){return(e=dS(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function dS(t){var e=function(t,e){if("object"!=df(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=df(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==df(e)?e:e+""}var dP={xAxis:["bottom","top"],yAxis:["left","right"]},dA={width:"100%",height:"100%"},dE={x:0,y:0};function dk(t){return t}var d_=function(t,e,r,n){var i=e.find(function(t){return t&&t.index===r});if(i){if("horizontal"===t)return{x:i.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:i.coordinate};if("centric"===t){var o=i.coordinate,a=n.radius;return dO(dO(dO({},n),s4(n.cx,n.cy,a,o)),{},{angle:o,radius:a})}var c=i.coordinate,u=n.angle;return dO(dO(dO({},n),s4(n.cx,n.cy,c,u)),{},{angle:u,radius:c})}return dE},dN=function(t,e){var r=e.graphicalItems,n=e.dataStartIndex,i=e.dataEndIndex,o=(null!=r?r:[]).reduce(function(t,e){var r=e.props.data;return r&&r.length?[].concat(db(t),db(r)):t},[]);return o.length>0?o:t&&t.length&&z(n)&&z(i)?t.slice(n,i+1):[]};function dM(t){return"number"===t?[0,"auto"]:void 0}var dT=function(t,e,r,n){var i=t.graphicalItems,o=t.tooltipAxis,a=dN(e,t);return r<0||!i||!i.length||r>=a.length?null:i.reduce(function(i,c){var u,s,l=null!=(u=c.props.data)?u:e;return(l&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=r&&(l=l.slice(t.dataStartIndex,t.dataEndIndex+1)),s=o.dataKey&&!o.allowDuplicatedCategory?H(void 0===l?a:l,o.dataKey,n):l&&l[r]||a[r])?[].concat(db(i),[sD(c,s)]):i},[])},dC=function(t,e,r,n){var i=n||{x:t.chartX,y:t.chartY},o="horizontal"===r?i.x:"vertical"===r?i.y:"centric"===r?i.angle:i.radius,a=t.orderedTooltipTicks,c=t.tooltipAxis,u=t.tooltipTicks,s=sn(o,a,u,c);if(s>=0&&u){var l=u[s]&&u[s].value,f=dT(t,e,s,l),p=d_(r,a,s,i);return{activeTooltipIndex:s,activeLabel:l,activePayload:f,activeCoordinate:p}}return null},dD=function(t,e){var r=e.axes,n=e.graphicalItems,i=e.axisType,o=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,s=t.layout,l=t.children,f=t.stackOffset,p=sf(s,i);return r.reduce(function(e,r){var d=void 0!==r.type.defaultProps?dO(dO({},r.type.defaultProps),r.props):r.props,h=d.type,y=d.dataKey,v=d.allowDataOverflow,m=d.allowDuplicatedCategory,b=d.scale,g=d.ticks,x=d.includeHidden,w=d[o];if(e[w])return e;var O=dN(t.data,{graphicalItems:n.filter(function(t){var e;return(o in t.props?t.props[o]:null==(e=t.type.defaultProps)?void 0:e[o])===w}),dataStartIndex:c,dataEndIndex:u}),j=O.length;(function(t,e,r){if("number"===r&&!0===e&&Array.isArray(t)){var n=null==t?void 0:t[0],i=null==t?void 0:t[1];if(n&&i&&z(n)&&z(i))return!0}return!1})(d.domain,v,h)&&(A=sM(d.domain,null,v),p&&("number"===h||"auto"!==b)&&(k=sr(O,y,"category")));var S=dM(h);if(!A||0===A.length){var P,A,E,k,_,N=null!=(_=d.domain)?_:S;if(y){if(A=sr(O,y,h),"category"===h&&p){var M=G(A);m&&M?(E=A,A=tB()(0,j)):m||(A=sC(N,A,r).reduce(function(t,e){return t.indexOf(e)>=0?t:[].concat(db(t),[e])},[]))}else if("category"===h)A=m?A.filter(function(t){return""!==t&&!Z()(t)}):sC(N,A,r).reduce(function(t,e){return t.indexOf(e)>=0||""===e||Z()(e)?t:[].concat(db(t),[e])},[]);else if("number"===h){var T=ss(O,n.filter(function(t){var e,r,n=o in t.props?t.props[o]:null==(e=t.type.defaultProps)?void 0:e[o],i="hide"in t.props?t.props.hide:null==(r=t.type.defaultProps)?void 0:r.hide;return n===w&&(x||!i)}),y,i,s);T&&(A=T)}p&&("number"===h||"auto"!==b)&&(k=sr(O,y,"category"))}else A=p?tB()(0,j):a&&a[w]&&a[w].hasStack&&"number"===h?"expand"===f?[0,1]:sk(a[w].stackGroups,c,u):sl(O,n.filter(function(t){var e=o in t.props?t.props[o]:t.type.defaultProps[o],r="hide"in t.props?t.props.hide:t.type.defaultProps.hide;return e===w&&(x||!r)}),h,s,!0);"number"===h?(A=pw(l,A,w,i,g),N&&(A=sM(N,A,v))):"category"===h&&N&&A.every(function(t){return N.indexOf(t)>=0})&&(A=N)}return dO(dO({},e),{},dj({},w,dO(dO({},d),{},{axisType:i,domain:A,categoricalDomain:k,duplicateDomain:E,originalDomain:null!=(P=d.domain)?P:S,isCategorical:p,layout:s})))},{})},dI=function(t,e){var r=e.graphicalItems,n=e.Axis,i=e.axisType,o=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,s=t.layout,l=t.children,f=dN(t.data,{graphicalItems:r,dataStartIndex:c,dataEndIndex:u}),p=f.length,d=sf(s,i),h=-1;return r.reduce(function(t,e){var y,v=(void 0!==e.type.defaultProps?dO(dO({},e.type.defaultProps),e.props):e.props)[o],m=dM("number");return t[v]?t:(h++,y=d?tB()(0,p):a&&a[v]&&a[v].hasStack?pw(l,y=sk(a[v].stackGroups,c,u),v,i):pw(l,y=sM(m,sl(f,r.filter(function(t){var e,r,n=o in t.props?t.props[o]:null==(e=t.type.defaultProps)?void 0:e[o],i="hide"in t.props?t.props.hide:null==(r=t.type.defaultProps)?void 0:r.hide;return n===v&&!i}),"number",s),n.defaultProps.allowDataOverflow),v,i),dO(dO({},t),{},dj({},v,dO(dO({axisType:i},n.defaultProps),{},{hide:!0,orientation:D()(dP,"".concat(i,".").concat(h%2),null),domain:y,originalDomain:m,isCategorical:d,layout:s}))))},{})},dB=function(t,e){var r=e.axisType,n=void 0===r?"xAxis":r,i=e.AxisComp,o=e.graphicalItems,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,s=t.children,l="".concat(n,"Id"),f=tx(s,i),p={};return f&&f.length?p=dD(t,{axes:f,graphicalItems:o,axisType:n,axisIdKey:l,stackGroups:a,dataStartIndex:c,dataEndIndex:u}):o&&o.length&&(p=dI(t,{Axis:i,graphicalItems:o,axisType:n,axisIdKey:l,stackGroups:a,dataStartIndex:c,dataEndIndex:u})),p},dR=function(t){var e=W(t),r=sd(e,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:tL()(r,function(t){return t.coordinate}),tooltipAxis:e,tooltipAxisBandSize:sT(e,r)}},dL=function(t){var e=t.children,r=t.defaultShowTooltip,n=tw(e,sQ),i=0,o=0;return t.data&&0!==t.data.length&&(o=t.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(i=n.props.startIndex),n.props.endIndex>=0&&(o=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:i,dataEndIndex:o,activeTooltipIndex:-1,isTooltipActive:!!r}},dz=function(t){return"horizontal"===t?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===t?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===t?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},dU=function(t,e){var r=t.props,n=t.graphicalItems,i=t.xAxisMap,o=void 0===i?{}:i,a=t.yAxisMap,c=void 0===a?{}:a,u=r.width,s=r.height,l=r.children,f=r.margin||{},p=tw(l,sQ),d=tw(l,ra),h=Object.keys(c).reduce(function(t,e){var r=c[e],n=r.orientation;return r.mirror||r.hide?t:dO(dO({},t),{},dj({},n,t[n]+r.width))},{left:f.left||0,right:f.right||0}),y=Object.keys(o).reduce(function(t,e){var r=o[e],n=r.orientation;return r.mirror||r.hide?t:dO(dO({},t),{},dj({},n,D()(t,"".concat(n))+r.height))},{top:f.top||0,bottom:f.bottom||0}),v=dO(dO({},y),h),m=v.bottom;p&&(v.bottom+=p.props.height||sQ.defaultProps.height),d&&e&&(v=sc(v,n,r,e));var b=u-v.left-v.right,g=s-v.top-v.bottom;return dO(dO({brushBottom:m},v),{},{width:Math.max(b,0),height:Math.max(g,0)})},dF=["type","layout","connectNulls","ref"],d$=["key"];function dq(t){return(dq="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function dW(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function dG(){return(dG=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function dX(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function dH(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?dX(Object(r),!0).forEach(function(e){d0(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):dX(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function dV(t){return function(t){if(Array.isArray(t))return dK(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return dK(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return dK(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function dK(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function dY(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,d1(n.key),n)}}function dZ(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(dZ=function(){return!!t})()}function dJ(t){return(dJ=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function dQ(t,e){return(dQ=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function d0(t,e,r){return(e=d1(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function d1(t){var e=function(t,e){if("object"!=dq(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dq(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dq(e)?e:e+""}var d2=function(t){var e,r;function n(){var t,e,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var i=arguments.length,o=Array(i),a=0;a<i;a++)o[a]=arguments[a];return e=n,r=[].concat(o),e=dJ(e),d0(t=function(t,e){if(e&&("object"===dq(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,dZ()?Reflect.construct(e,r||[],dJ(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!0,totalLength:0}),d0(t,"generateSimpleStrokeDasharray",function(t,e){return"".concat(e,"px ").concat(t-e,"px")}),d0(t,"getStrokeDasharray",function(e,r,i){var o=i.reduce(function(t,e){return t+e});if(!o)return t.generateSimpleStrokeDasharray(r,e);for(var a=Math.floor(e/o),c=e%o,u=r-e,s=[],l=0,f=0;l<i.length;f+=i[l],++l)if(f+i[l]>c){s=[].concat(dV(i.slice(0,l)),[c-f]);break}var p=s.length%2==0?[0,u]:[u];return[].concat(dV(n.repeat(i,a)),dV(s),p).map(function(t){return"".concat(t,"px")}).join(", ")}),d0(t,"id",$("recharts-line-")),d0(t,"pathRef",function(e){t.mainCurve=e}),d0(t,"handleAnimationEnd",function(){t.setState({isAnimationFinished:!0}),t.props.onAnimationEnd&&t.props.onAnimationEnd()}),d0(t,"handleAnimationStart",function(){t.setState({isAnimationFinished:!1}),t.props.onAnimationStart&&t.props.onAnimationStart()}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&dQ(n,t),e=[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var t=this.getTotalLength();this.setState({totalLength:t})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var t=this.getTotalLength();t!==this.state.totalLength&&this.setState({totalLength:t})}}},{key:"getTotalLength",value:function(){var t=this.mainCurve;try{return t&&t.getTotalLength&&t.getTotalLength()||0}catch(t){return 0}}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.points,i=r.xAxis,o=r.yAxis,c=r.layout,u=tx(r.children,u0);if(!u)return null;var s=function(t,e){return{x:t.x,y:t.y,value:t.value,errorVal:se(t.payload,e)}};return a().createElement(tG,{clipPath:t?"url(#clipPath-".concat(e,")"):null},u.map(function(t){return a().cloneElement(t,{key:"bar-".concat(t.props.dataKey),data:n,xAxis:i,yAxis:o,layout:c,dataPointFormatter:s})}))}},{key:"renderDots",value:function(t,e,r){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var i=this.props,o=i.dot,c=i.points,u=i.dataKey,s=tP(this.props,!1),l=tP(o,!0),f=c.map(function(t,e){var r=dH(dH(dH({key:"dot-".concat(e),r:3},s),l),{},{index:e,cx:t.x,cy:t.y,value:t.value,dataKey:u,payload:t.payload,points:c});return n.renderDotItem(o,r)}),p={clipPath:t?"url(#clipPath-".concat(e?"":"dots-").concat(r,")"):null};return a().createElement(tG,dG({className:"recharts-line-dots",key:"dots"},p),f)}},{key:"renderCurveStatically",value:function(t,e,r,n){var i=this.props,o=i.type,c=i.layout,u=i.connectNulls,s=dH(dH(dH({},tP((i.ref,dW(i,dF)),!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:e?"url(#clipPath-".concat(r,")"):null,points:t},n),{},{type:o,layout:c,connectNulls:u});return a().createElement(p8,dG({},s,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(t,e){var r=this,n=this.props,i=n.points,o=n.strokeDasharray,c=n.isAnimationActive,u=n.animationBegin,s=n.animationDuration,l=n.animationEasing,f=n.animationId,p=n.animateNewValues,d=n.width,h=n.height,y=this.state,v=y.prevPoints,m=y.totalLength;return a().createElement(nb,{begin:u,duration:s,isActive:c,easing:l,from:{t:0},to:{t:1},key:"line-".concat(f),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(n){var a,c=n.t;if(v){var u=v.length/i.length,s=i.map(function(t,e){var r=Math.floor(e*u);if(v[r]){var n=v[r],i=X(n.x,t.x),o=X(n.y,t.y);return dH(dH({},t),{},{x:i(c),y:o(c)})}if(p){var a=X(2*d,t.x),s=X(h/2,t.y);return dH(dH({},t),{},{x:a(c),y:s(c)})}return dH(dH({},t),{},{x:t.x,y:t.y})});return r.renderCurveStatically(s,t,e)}var l=X(0,m)(c);if(o){var f="".concat(o).split(/[,\s]+/gim).map(function(t){return parseFloat(t)});a=r.getStrokeDasharray(l,m,f)}else a=r.generateSimpleStrokeDasharray(m,l);return r.renderCurveStatically(i,t,e,{strokeDasharray:a})})}},{key:"renderCurve",value:function(t,e){var r=this.props,n=r.points,i=r.isAnimationActive,o=this.state,a=o.prevPoints,c=o.totalLength;return i&&n&&n.length&&(!a&&c>0||!c7()(a,n))?this.renderCurveWithAnimation(t,e):this.renderCurveStatically(n,t,e)}},{key:"render",value:function(){var t,e=this.props,r=e.hide,n=e.dot,i=e.points,o=e.className,c=e.xAxis,u=e.yAxis,s=e.top,l=e.left,f=e.width,p=e.height,d=e.isAnimationActive,h=e.id;if(r||!i||!i.length)return null;var y=this.state.isAnimationFinished,v=1===i.length,m=(0,A.A)("recharts-line",o),b=c&&c.allowDataOverflow,g=u&&u.allowDataOverflow,x=b||g,w=Z()(h)?this.id:h,O=null!=(t=tP(n,!1))?t:{r:3,strokeWidth:2},j=O.r,S=O.strokeWidth,P=(n&&"object"===th(n)&&"clipDot"in n?n:{}).clipDot,E=void 0===P||P,k=2*(void 0===j?3:j)+(void 0===S?2:S);return a().createElement(tG,{className:m},b||g?a().createElement("defs",null,a().createElement("clipPath",{id:"clipPath-".concat(w)},a().createElement("rect",{x:b?l:l-f/2,y:g?s:s-p/2,width:b?f:2*f,height:g?p:2*p})),!E&&a().createElement("clipPath",{id:"clipPath-dots-".concat(w)},a().createElement("rect",{x:l-k/2,y:s-k/2,width:f+k,height:p+k}))):null,!v&&this.renderCurve(x,w),this.renderErrorBar(x,w),(v||n)&&this.renderDots(x,E,w),(!d||y)&&lk.renderCallByParent(this.props,i))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,prevPoints:e.curPoints}:t.points!==e.curPoints?{curPoints:t.points}:null}},{key:"repeat",value:function(t,e){for(var r=t.length%2!=0?[].concat(dV(t),[0]):t,n=[],i=0;i<e;++i)n=[].concat(dV(n),dV(r));return n}},{key:"renderDotItem",value:function(t,e){var r;if(a().isValidElement(t))r=a().cloneElement(t,e);else if(Q()(t))r=t(e);else{var n=e.key,i=dW(e,d$),o=(0,A.A)("recharts-line-dot","boolean"!=typeof t?t.className:"");r=a().createElement(ru,dG({key:n},i,{className:o}))}return r}}],e&&dY(n.prototype,e),r&&dY(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(o.PureComponent);function d5(t,e,r){if(e<1)return[];if(1===e&&void 0===r)return t;for(var n=[],i=0;i<t.length;i+=e)if(void 0!==r&&!0!==r(t[i]))return;else n.push(t[i]);return n}function d4(t,e,r,n,i){if(t*e<t*n||t*e>t*i)return!1;var o=r();return t*(e-t*o/2-n)>=0&&t*(e+t*o/2-i)<=0}function d3(t){return(d3="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function d6(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function d8(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?d6(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=d3(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=d3(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==d3(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):d6(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function d7(t,e,r){var n,i,o,a,c,u=t.tick,s=t.ticks,l=t.viewBox,f=t.minTickGap,p=t.orientation,d=t.interval,h=t.tickFormatter,y=t.unit,v=t.angle;if(!s||!s.length||!u)return[];if(z(d)||en.isSsr)return d5(s,("number"==typeof d&&z(d)?d:0)+1);var m=[],b="top"===p||"bottom"===p?"width":"height",g=y&&"width"===b?nW(y,{fontSize:e,letterSpacing:r}):{width:0,height:0},x=function(t,n){var i,o=Q()(h)?h(t.value,n):t.value;return"width"===b?(i=nW(o,{fontSize:e,letterSpacing:r}),fE({width:i.width+g.width,height:i.height+g.height},v)):nW(o,{fontSize:e,letterSpacing:r})[b]},w=s.length>=2?R(s[1].coordinate-s[0].coordinate):1,O=(n="width"===b,i=l.x,o=l.y,a=l.width,c=l.height,1===w?{start:n?i:o,end:n?i+a:o+c}:{start:n?i+a:o+c,end:n?i:o});return"equidistantPreserveStart"===d?function(t,e,r,n,i){for(var o,a=(n||[]).slice(),c=e.start,u=e.end,s=0,l=1,f=c;l<=a.length;)if(o=function(){var e,o=null==n?void 0:n[s];if(void 0===o)return{v:d5(n,l)};var a=s,p=function(){return void 0===e&&(e=r(o,a)),e},d=o.coordinate,h=0===s||d4(t,d,p,f,u);h||(s=0,f=c,l+=1),h&&(f=d+t*(p()/2+i),s+=l)}())return o.v;return[]}(w,O,x,s,f):("preserveStart"===d||"preserveStartEnd"===d?function(t,e,r,n,i,o){var a=(n||[]).slice(),c=a.length,u=e.start,s=e.end;if(o){var l=n[c-1],f=r(l,c-1),p=t*(l.coordinate+t*f/2-s);a[c-1]=l=d8(d8({},l),{},{tickCoord:p>0?l.coordinate-p*t:l.coordinate}),d4(t,l.tickCoord,function(){return f},u,s)&&(s=l.tickCoord-t*(f/2+i),a[c-1]=d8(d8({},l),{},{isShow:!0}))}for(var d=o?c-1:c,h=function(e){var n,o=a[e],c=function(){return void 0===n&&(n=r(o,e)),n};if(0===e){var l=t*(o.coordinate-t*c()/2-u);a[e]=o=d8(d8({},o),{},{tickCoord:l<0?o.coordinate-l*t:o.coordinate})}else a[e]=o=d8(d8({},o),{},{tickCoord:o.coordinate});d4(t,o.tickCoord,c,u,s)&&(u=o.tickCoord+t*(c()/2+i),a[e]=d8(d8({},o),{},{isShow:!0}))},y=0;y<d;y++)h(y);return a}(w,O,x,s,f,"preserveStartEnd"===d):function(t,e,r,n,i){for(var o=(n||[]).slice(),a=o.length,c=e.start,u=e.end,s=function(e){var n,s=o[e],l=function(){return void 0===n&&(n=r(s,e)),n};if(e===a-1){var f=t*(s.coordinate+t*l()/2-u);o[e]=s=d8(d8({},s),{},{tickCoord:f>0?s.coordinate-f*t:s.coordinate})}else o[e]=s=d8(d8({},s),{},{tickCoord:s.coordinate});d4(t,s.tickCoord,l,c,u)&&(u=s.tickCoord-t*(l()/2+i),o[e]=d8(d8({},s),{},{isShow:!0}))},l=a-1;l>=0;l--)s(l);return o}(w,O,x,s,f)).filter(function(t){return t.isShow})}d0(d2,"displayName","Line"),d0(d2,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!en.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1}),d0(d2,"getComposedData",function(t){var e=t.props,r=t.xAxis,n=t.yAxis,i=t.xAxisTicks,o=t.yAxisTicks,a=t.dataKey,c=t.bandSize,u=t.displayedData,s=t.offset,l=e.layout;return dH({points:u.map(function(t,e){var u=se(t,a);return"horizontal"===l?{x:sS({axis:r,ticks:i,bandSize:c,entry:t,index:e}),y:Z()(u)?null:n.scale(u),value:u,payload:t}:{x:Z()(u)?null:r.scale(u),y:sS({axis:n,ticks:o,bandSize:c,entry:t,index:e}),value:u,payload:t}}),layout:l},s)});var d9=["viewBox"],ht=["viewBox"],he=["ticks"];function hr(t){return(hr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function hn(){return(hn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function hi(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ho(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hi(Object(r),!0).forEach(function(e){hf(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hi(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ha(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function hc(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,hp(n.key),n)}}function hu(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(hu=function(){return!!t})()}function hs(t){return(hs=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function hl(t,e){return(hl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function hf(t,e,r){return(e=hp(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function hp(t){var e=function(t,e){if("object"!=hr(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hr(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hr(e)?e:e+""}var hd=function(t){var e,r;function n(t){var e,r,i;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return r=n,i=[t],r=hs(r),(e=function(t,e){if(e&&("object"===hr(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,hu()?Reflect.construct(r,i||[],hs(this).constructor):r.apply(this,i))).state={fontSize:"",letterSpacing:""},e}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&hl(n,t),e=[{key:"shouldComponentUpdate",value:function(t,e){var r=t.viewBox,n=ha(t,d9),i=this.props,o=i.viewBox,a=ha(i,ht);return!tn(r,o)||!tn(n,a)||!tn(e,this.state)}},{key:"componentDidMount",value:function(){var t=this.layerReference;if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];e&&this.setState({fontSize:window.getComputedStyle(e).fontSize,letterSpacing:window.getComputedStyle(e).letterSpacing})}}},{key:"getTickLineCoord",value:function(t){var e,r,n,i,o,a,c=this.props,u=c.x,s=c.y,l=c.width,f=c.height,p=c.orientation,d=c.tickSize,h=c.mirror,y=c.tickMargin,v=h?-1:1,m=t.tickSize||d,b=z(t.tickCoord)?t.tickCoord:t.coordinate;switch(p){case"top":e=r=t.coordinate,a=(n=(i=s+!h*f)-v*m)-v*y,o=b;break;case"left":n=i=t.coordinate,o=(e=(r=u+!h*l)-v*m)-v*y,a=b;break;case"right":n=i=t.coordinate,o=(e=(r=u+h*l)+v*m)+v*y,a=b;break;default:e=r=t.coordinate,a=(n=(i=s+h*f)+v*m)+v*y,o=b}return{line:{x1:e,y1:n,x2:r,y2:i},tick:{x:o,y:a}}}},{key:"getTickTextAnchor",value:function(){var t,e=this.props,r=e.orientation,n=e.mirror;switch(r){case"left":t=n?"start":"end";break;case"right":t=n?"end":"start";break;default:t="middle"}return t}},{key:"getTickVerticalAnchor",value:function(){var t=this.props,e=t.orientation,r=t.mirror,n="end";switch(e){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,i=t.height,o=t.orientation,c=t.mirror,u=t.axisLine,s=ho(ho(ho({},tP(this.props,!1)),tP(u,!1)),{},{fill:"none"});if("top"===o||"bottom"===o){var l=+("top"===o&&!c||"bottom"===o&&c);s=ho(ho({},s),{},{x1:e,y1:r+l*i,x2:e+n,y2:r+l*i})}else{var f=+("left"===o&&!c||"right"===o&&c);s=ho(ho({},s),{},{x1:e+f*n,y1:r,x2:e+f*n,y2:r+i})}return a().createElement("line",hn({},s,{className:(0,A.A)("recharts-cartesian-axis-line",D()(u,"className"))}))}},{key:"renderTicks",value:function(t,e,r){var i=this,o=this.props,c=o.tickLine,u=o.stroke,s=o.tick,l=o.tickFormatter,f=o.unit,p=d7(ho(ho({},this.props),{},{ticks:t}),e,r),d=this.getTickTextAnchor(),h=this.getTickVerticalAnchor(),y=tP(this.props,!1),v=tP(s,!1),m=ho(ho({},y),{},{fill:"none"},tP(c,!1)),b=p.map(function(t,e){var r=i.getTickLineCoord(t),o=r.line,b=r.tick,g=ho(ho(ho(ho({textAnchor:d,verticalAnchor:h},y),{},{stroke:"none",fill:u},v),b),{},{index:e,payload:t,visibleTicksCount:p.length,tickFormatter:l});return a().createElement(tG,hn({className:"recharts-cartesian-axis-tick",key:"tick-".concat(t.value,"-").concat(t.coordinate,"-").concat(t.tickCoord)},tl(i.props,t,e)),c&&a().createElement("line",hn({},m,o,{className:(0,A.A)("recharts-cartesian-axis-tick-line",D()(c,"className"))})),s&&n.renderTickItem(s,g,"".concat(Q()(l)?l(t.value,e):t.value).concat(f||"")))});return a().createElement("g",{className:"recharts-cartesian-axis-ticks"},b)}},{key:"render",value:function(){var t=this,e=this.props,r=e.axisLine,n=e.width,i=e.height,o=e.ticksGenerator,c=e.className;if(e.hide)return null;var u=this.props,s=u.ticks,l=ha(u,he),f=s;return(Q()(o)&&(f=o(s&&s.length>0?this.props:l)),n<=0||i<=0||!f||!f.length)?null:a().createElement(tG,{className:(0,A.A)("recharts-cartesian-axis",c),ref:function(e){t.layerReference=e}},r&&this.renderAxisLine(),this.renderTicks(f,this.state.fontSize,this.state.letterSpacing),ls.renderCallByParent(this.props))}}],r=[{key:"renderTickItem",value:function(t,e,r){var n;return a().isValidElement(t)?a().cloneElement(t,e):Q()(t)?t(e):a().createElement(iu,hn({},e,{className:"recharts-cartesian-axis-tick-value"}),r)}}],e&&hc(n.prototype,e),r&&hc(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(o.Component);function hh(t){return(hh="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}hf(hd,"displayName","CartesianAxis"),hf(hd,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});function hy(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(hy=function(){return!!t})()}function hv(t){return(hv=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function hm(t,e){return(hm=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function hb(t,e,r){return(e=hg(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function hg(t){var e=function(t,e){if("object"!=hh(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hh(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hh(e)?e:e+""}function hx(){return(hx=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function hw(t){var e=t.xAxisId,r=f2(),n=f5(),i=fQ(e);return null==i?null:a().createElement(hd,hx({},i,{className:(0,A.A)("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(t){return sd(t,!0)}}))}var hO=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=hv(t),function(t,e){if(e&&("object"===hh(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,hy()?Reflect.construct(t,e||[],hv(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&hm(r,t),e=[{key:"render",value:function(){return a().createElement(hw,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,hg(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function hj(t){return(hj="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}hb(hO,"displayName","XAxis"),hb(hO,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function hS(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(hS=function(){return!!t})()}function hP(t){return(hP=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function hA(t,e){return(hA=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function hE(t,e,r){return(e=hk(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function hk(t){var e=function(t,e){if("object"!=hj(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hj(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hj(e)?e:e+""}function h_(){return(h_=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var hN=function(t){var e=t.yAxisId,r=f2(),n=f5(),i=f1(e);return null==i?null:a().createElement(hd,h_({},i,{className:(0,A.A)("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(t){return sd(t,!0)}}))},hM=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=hP(t),function(t,e){if(e&&("object"===hj(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,hS()?Reflect.construct(t,e||[],hP(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&hA(r,t),e=[{key:"render",value:function(){return a().createElement(hN,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,hk(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);hE(hM,"displayName","YAxis"),hE(hM,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});var hT=function(t){var e=t.chartName,r=t.GraphicalChild,n=t.defaultTooltipEventType,i=void 0===n?"axis":n,c=t.validateTooltipEventTypes,u=void 0===c?["axis"]:c,s=t.axisComponents,l=t.legendContent,f=t.formatAxisMap,p=t.defaultProps,d=function(t,e){var r=e.graphicalItems,n=e.stackGroups,i=e.offset,o=e.updateId,a=e.dataStartIndex,c=e.dataEndIndex,u=t.barSize,l=t.layout,f=t.barGap,p=t.barCategoryGap,d=t.maxBarSize,h=dz(l),y=h.numericAxisName,v=h.cateAxisName,m=!!r&&!!r.length&&r.some(function(t){var e=tv(t&&t.type);return e&&e.indexOf("Bar")>=0}),b=[];return r.forEach(function(r,h){var g=dN(t.data,{graphicalItems:[r],dataStartIndex:a,dataEndIndex:c}),x=void 0!==r.type.defaultProps?dO(dO({},r.type.defaultProps),r.props):r.props,w=x.dataKey,O=x.maxBarSize,j=x["".concat(y,"Id")],S=x["".concat(v,"Id")],P=s.reduce(function(t,r){var n=e["".concat(r.axisType,"Map")],i=x["".concat(r.axisType,"Id")];n&&n[i]||"zAxis"===r.axisType||tz(!1);var o=n[i];return dO(dO({},t),{},dj(dj({},r.axisType,o),"".concat(r.axisType,"Ticks"),sd(o)))},{}),A=P[v],E=P["".concat(v,"Ticks")],k=n&&n[j]&&n[j].hasStack&&sE(r,n[j].stackGroups),_=tv(r.type).indexOf("Bar")>=0,N=sT(A,E),M=[],T=m&&so({barSize:u,stackGroups:n,totalSize:"xAxis"===v?P[v].width:"yAxis"===v?P[v].height:void 0});if(_){var C,D,I=Z()(O)?d:O,B=null!=(C=null!=(D=sT(A,E,!0))?D:I)?C:0;M=sa({barGap:f,barCategoryGap:p,bandSize:B!==N?B:N,sizeList:T[S],maxBarSize:I}),B!==N&&(M=M.map(function(t){return dO(dO({},t),{},{position:dO(dO({},t.position),{},{offset:t.position.offset-B/2})})}))}var R=r&&r.type&&r.type.getComposedData;R&&b.push({props:dO(dO({},R(dO(dO({},P),{},{displayedData:g,props:t,dataKey:w,item:r,bandSize:N,barPosition:M,offset:i,stackedData:k,layout:l,dataStartIndex:a,dataEndIndex:c}))),{},dj(dj(dj({key:r.key||"item-".concat(h)},y,P[y]),v,P[v]),"animationId",o)),childIndex:tg(t.children).indexOf(r),item:r})}),b},h=function(t,n){var i=t.props,o=t.dataStartIndex,a=t.dataEndIndex,c=t.updateId;if(!tO({props:i}))return null;var u=i.children,l=i.layout,p=i.stackOffset,h=i.data,y=i.reverseStackOrder,v=dz(l),m=v.numericAxisName,b=v.cateAxisName,g=tx(u,r),x=sO(h,g,"".concat(m,"Id"),"".concat(b,"Id"),p,y),w=s.reduce(function(t,e){var r="".concat(e.axisType,"Map");return dO(dO({},t),{},dj({},r,dB(i,dO(dO({},e),{},{graphicalItems:g,stackGroups:e.axisType===m&&x,dataStartIndex:o,dataEndIndex:a}))))},{}),O=dU(dO(dO({},w),{},{props:i,graphicalItems:g}),null==n?void 0:n.legendBBox);Object.keys(w).forEach(function(t){w[t]=f(i,w[t],O,t.replace("Map",""),e)});var j=dR(w["".concat(b,"Map")]),S=d(i,dO(dO({},w),{},{dataStartIndex:o,dataEndIndex:a,updateId:c,graphicalItems:g,stackGroups:x,offset:O}));return dO(dO({formattedGraphicalItems:S,graphicalItems:g,offset:O,stackGroups:x},j),w)},y=function(t){var r;function n(t){var r,i,c,u,s;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return u=n,s=[t],u=dv(u),dj(c=function(t,e){if(e&&("object"===df(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,dy()?Reflect.construct(u,s||[],dv(this).constructor):u.apply(this,s)),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),dj(c,"accessibilityManager",new pk),dj(c,"handleLegendBBoxUpdate",function(t){if(t){var e=c.state,r=e.dataStartIndex,n=e.dataEndIndex,i=e.updateId;c.setState(dO({legendBBox:t},h({props:c.props,dataStartIndex:r,dataEndIndex:n,updateId:i},dO(dO({},c.state),{},{legendBBox:t}))))}}),dj(c,"handleReceiveSyncEvent",function(t,e,r){c.props.syncId===t&&(r!==c.eventEmitterSymbol||"function"==typeof c.props.syncMethod)&&c.applySyncEvent(e)}),dj(c,"handleBrushChange",function(t){var e=t.startIndex,r=t.endIndex;if(e!==c.state.dataStartIndex||r!==c.state.dataEndIndex){var n=c.state.updateId;c.setState(function(){return dO({dataStartIndex:e,dataEndIndex:r},h({props:c.props,dataStartIndex:e,dataEndIndex:r,updateId:n},c.state))}),c.triggerSyncEvent({dataStartIndex:e,dataEndIndex:r})}}),dj(c,"handleMouseEnter",function(t){var e=c.getMouseInfo(t);if(e){var r=dO(dO({},e),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseEnter;Q()(n)&&n(r,t)}}),dj(c,"triggeredAfterMouseMove",function(t){var e=c.getMouseInfo(t),r=e?dO(dO({},e),{},{isTooltipActive:!0}):{isTooltipActive:!1};c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseMove;Q()(n)&&n(r,t)}),dj(c,"handleItemMouseEnter",function(t){c.setState(function(){return{isTooltipActive:!0,activeItem:t,activePayload:t.tooltipPayload,activeCoordinate:t.tooltipPosition||{x:t.cx,y:t.cy}}})}),dj(c,"handleItemMouseLeave",function(){c.setState(function(){return{isTooltipActive:!1}})}),dj(c,"handleMouseMove",function(t){t.persist(),c.throttleTriggeredAfterMouseMove(t)}),dj(c,"handleMouseLeave",function(t){c.throttleTriggeredAfterMouseMove.cancel();var e={isTooltipActive:!1};c.setState(e),c.triggerSyncEvent(e);var r=c.props.onMouseLeave;Q()(r)&&r(e,t)}),dj(c,"handleOuterEvent",function(t){var e,r,n=t_(t),i=D()(c.props,"".concat(n));n&&Q()(i)&&i(null!=(e=/.*touch.*/i.test(n)?c.getMouseInfo(t.changedTouches[0]):c.getMouseInfo(t))?e:{},t)}),dj(c,"handleClick",function(t){var e=c.getMouseInfo(t);if(e){var r=dO(dO({},e),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onClick;Q()(n)&&n(r,t)}}),dj(c,"handleMouseDown",function(t){var e=c.props.onMouseDown;Q()(e)&&e(c.getMouseInfo(t),t)}),dj(c,"handleMouseUp",function(t){var e=c.props.onMouseUp;Q()(e)&&e(c.getMouseInfo(t),t)}),dj(c,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.throttleTriggeredAfterMouseMove(t.changedTouches[0])}),dj(c,"handleTouchStart",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.handleMouseDown(t.changedTouches[0])}),dj(c,"handleTouchEnd",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.handleMouseUp(t.changedTouches[0])}),dj(c,"handleDoubleClick",function(t){var e=c.props.onDoubleClick;Q()(e)&&e(c.getMouseInfo(t),t)}),dj(c,"handleContextMenu",function(t){var e=c.props.onContextMenu;Q()(e)&&e(c.getMouseInfo(t),t)}),dj(c,"triggerSyncEvent",function(t){void 0!==c.props.syncId&&pj.emit(pS,c.props.syncId,t,c.eventEmitterSymbol)}),dj(c,"applySyncEvent",function(t){var e=c.props,r=e.layout,n=e.syncMethod,i=c.state.updateId,o=t.dataStartIndex,a=t.dataEndIndex;if(void 0!==t.dataStartIndex||void 0!==t.dataEndIndex)c.setState(dO({dataStartIndex:o,dataEndIndex:a},h({props:c.props,dataStartIndex:o,dataEndIndex:a,updateId:i},c.state)));else if(void 0!==t.activeTooltipIndex){var u=t.chartX,s=t.chartY,l=t.activeTooltipIndex,f=c.state,p=f.offset,d=f.tooltipTicks;if(!p)return;if("function"==typeof n)l=n(d,t);else if("value"===n){l=-1;for(var y=0;y<d.length;y++)if(d[y].value===t.activeLabel){l=y;break}}var v=dO(dO({},p),{},{x:p.left,y:p.top}),m=Math.min(u,v.x+v.width),b=Math.min(s,v.y+v.height),g=d[l]&&d[l].value,x=dT(c.state,c.props.data,l),w=d[l]?{x:"horizontal"===r?d[l].coordinate:m,y:"horizontal"===r?b:d[l].coordinate}:dE;c.setState(dO(dO({},t),{},{activeLabel:g,activeCoordinate:w,activePayload:x,activeTooltipIndex:l}))}else c.setState(t)}),dj(c,"renderCursor",function(t){var r,n=c.state,i=n.isTooltipActive,o=n.activeCoordinate,u=n.activePayload,s=n.offset,l=n.activeTooltipIndex,f=n.tooltipAxisBandSize,p=c.getTooltipEventType(),d=null!=(r=t.props.active)?r:i,h=c.props.layout,y=t.key||"_recharts-cursor";return a().createElement(du,{key:y,activeCoordinate:o,activePayload:u,activeTooltipIndex:l,chartName:e,element:t,isActive:d,layout:h,offset:s,tooltipAxisBandSize:f,tooltipEventType:p})}),dj(c,"renderPolarAxis",function(t,e,r){var n=D()(t,"type.axisType"),i=D()(c.state,"".concat(n,"Map")),a=t.type.defaultProps,u=void 0!==a?dO(dO({},a),t.props):t.props,s=i&&i[u["".concat(n,"Id")]];return(0,o.cloneElement)(t,dO(dO({},s),{},{className:(0,A.A)(n,s.className),key:t.key||"".concat(e,"-").concat(r),ticks:sd(s,!0)}))}),dj(c,"renderPolarGrid",function(t){var e=t.props,r=e.radialLines,n=e.polarAngles,i=e.polarRadius,a=c.state,u=a.radiusAxisMap,s=a.angleAxisMap,l=W(u),f=W(s),p=f.cx,d=f.cy,h=f.innerRadius,y=f.outerRadius;return(0,o.cloneElement)(t,{polarAngles:Array.isArray(n)?n:sd(f,!0).map(function(t){return t.coordinate}),polarRadius:Array.isArray(i)?i:sd(l,!0).map(function(t){return t.coordinate}),cx:p,cy:d,innerRadius:h,outerRadius:y,key:t.key||"polar-grid",radialLines:r})}),dj(c,"renderLegend",function(){var t=c.state.formattedGraphicalItems,e=c.props,r=e.children,n=e.width,i=e.height,a=c.props.margin||{},u=u4({children:r,formattedGraphicalItems:t,legendWidth:n-(a.left||0)-(a.right||0),legendContent:l});if(!u)return null;var s=u.item,f=dh(u,ds);return(0,o.cloneElement)(s,dO(dO({},f),{},{chartWidth:n,chartHeight:i,margin:a,onBBoxUpdate:c.handleLegendBBoxUpdate}))}),dj(c,"renderTooltip",function(){var t,e=c.props,r=e.children,n=e.accessibilityLayer,i=tw(r,ev);if(!i)return null;var a=c.state,u=a.isTooltipActive,s=a.activeCoordinate,l=a.activePayload,f=a.activeLabel,p=a.offset,d=null!=(t=i.props.active)?t:u;return(0,o.cloneElement)(i,{viewBox:dO(dO({},p),{},{x:p.left,y:p.top}),active:d,label:f,payload:d?l:[],coordinate:s,accessibilityLayer:n})}),dj(c,"renderBrush",function(t){var e=c.props,r=e.margin,n=e.data,i=c.state,a=i.offset,u=i.dataStartIndex,s=i.dataEndIndex,l=i.updateId;return(0,o.cloneElement)(t,{key:t.key||"_recharts-brush",onChange:sy(c.handleBrushChange,t.props.onChange),data:n,x:z(t.props.x)?t.props.x:a.left,y:z(t.props.y)?t.props.y:a.top+a.height+a.brushBottom-(r.bottom||0),width:z(t.props.width)?t.props.width:a.width,startIndex:u,endIndex:s,updateId:"brush-".concat(l)})}),dj(c,"renderReferenceElement",function(t,e,r){if(!t)return null;var n=c.clipPathId,i=c.state,a=i.xAxisMap,u=i.yAxisMap,s=i.offset,l=t.type.defaultProps||{},f=t.props,p=f.xAxisId,d=void 0===p?l.xAxisId:p,h=f.yAxisId,y=void 0===h?l.yAxisId:h;return(0,o.cloneElement)(t,{key:t.key||"".concat(e,"-").concat(r),xAxis:a[d],yAxis:u[y],viewBox:{x:s.left,y:s.top,width:s.width,height:s.height},clipPathId:n})}),dj(c,"renderActivePoints",function(t){var e=t.item,r=t.activePoint,i=t.basePoint,o=t.childIndex,a=t.isRange,c=[],u=e.props.key,s=void 0!==e.item.type.defaultProps?dO(dO({},e.item.type.defaultProps),e.item.props):e.item.props,l=s.activeDot,f=dO(dO({index:o,dataKey:s.dataKey,cx:r.x,cy:r.y,r:4,fill:si(e.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},tP(l,!1)),ts(l));return c.push(n.renderActiveDot(l,f,"".concat(u,"-activePoint-").concat(o))),i?c.push(n.renderActiveDot(l,dO(dO({},f),{},{cx:i.x,cy:i.y}),"".concat(u,"-basePoint-").concat(o))):a&&c.push(null),c}),dj(c,"renderGraphicChild",function(t,e,r){var n=c.filterFormatItem(t,e,r);if(!n)return null;var i=c.getTooltipEventType(),a=c.state,u=a.isTooltipActive,s=a.tooltipAxis,l=a.activeTooltipIndex,f=a.activeLabel,p=tw(c.props.children,ev),d=n.props,h=d.points,y=d.isRange,v=d.baseLine,m=void 0!==n.item.type.defaultProps?dO(dO({},n.item.type.defaultProps),n.item.props):n.item.props,b=m.activeDot,g=m.hide,x=m.activeBar,w=m.activeShape,O=!!(!g&&u&&p&&(b||x||w)),j={};"axis"!==i&&p&&"click"===p.props.trigger?j={onClick:sy(c.handleItemMouseEnter,t.props.onClick)}:"axis"!==i&&(j={onMouseLeave:sy(c.handleItemMouseLeave,t.props.onMouseLeave),onMouseEnter:sy(c.handleItemMouseEnter,t.props.onMouseEnter)});var S=(0,o.cloneElement)(t,dO(dO({},n.props),j));if(O)if(l>=0){if(s.dataKey&&!s.allowDuplicatedCategory){var P="function"==typeof s.dataKey?function(t){return"function"==typeof s.dataKey?s.dataKey(t.payload):null}:"payload.".concat(s.dataKey.toString());E=H(h,P,f),k=y&&v&&H(v,P,f)}else E=null==h?void 0:h[l],k=y&&v&&v[l];if(w||x){var A=void 0!==t.props.activeIndex?t.props.activeIndex:l;return[(0,o.cloneElement)(t,dO(dO(dO({},n.props),j),{},{activeIndex:A})),null,null]}if(!Z()(E))return[S].concat(db(c.renderActivePoints({item:n,activePoint:E,basePoint:k,childIndex:l,isRange:y})))}else{var E,k,_,N=(null!=(_=c.getItemByXY(c.state.activeCoordinate))?_:{graphicalItem:S}).graphicalItem,M=N.item,T=void 0===M?t:M,C=N.childIndex,D=dO(dO(dO({},n.props),j),{},{activeIndex:C});return[(0,o.cloneElement)(T,D),null,null]}return y?[S,null,null]:[S,null]}),dj(c,"renderCustomized",function(t,e,r){return(0,o.cloneElement)(t,dO(dO({key:"recharts-customized-".concat(r)},c.props),c.state))}),dj(c,"renderMap",{CartesianGrid:{handler:dk,once:!0},ReferenceArea:{handler:c.renderReferenceElement},ReferenceLine:{handler:dk},ReferenceDot:{handler:c.renderReferenceElement},XAxis:{handler:dk},YAxis:{handler:dk},Brush:{handler:c.renderBrush,once:!0},Bar:{handler:c.renderGraphicChild},Line:{handler:c.renderGraphicChild},Area:{handler:c.renderGraphicChild},Radar:{handler:c.renderGraphicChild},RadialBar:{handler:c.renderGraphicChild},Scatter:{handler:c.renderGraphicChild},Pie:{handler:c.renderGraphicChild},Funnel:{handler:c.renderGraphicChild},Tooltip:{handler:c.renderCursor,once:!0},PolarGrid:{handler:c.renderPolarGrid,once:!0},PolarAngleAxis:{handler:c.renderPolarAxis},PolarRadiusAxis:{handler:c.renderPolarAxis},Customized:{handler:c.renderCustomized}}),c.clipPathId="".concat(null!=(r=t.id)?r:$("recharts"),"-clip"),c.throttleTriggeredAfterMouseMove=k()(c.triggeredAfterMouseMove,null!=(i=t.throttleDelay)?i:1e3/60),c.state={},c}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&dm(n,t),r=[{key:"componentDidMount",value:function(){var t,e;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!=(t=this.props.margin.left)?t:0,top:null!=(e=this.props.margin.top)?e:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var t=this.props,e=t.children,r=t.data,n=t.height,i=t.layout,o=tw(e,ev);if(o){var a=o.props.defaultIndex;if("number"==typeof a&&!(a<0)&&!(a>this.state.tooltipTicks.length-1)){var c=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,u=dT(this.state,r,a,c),s=this.state.tooltipTicks[a].coordinate,l=(this.state.offset.top+n)/2,f="horizontal"===i?{x:s,y:l}:{y:s,x:l},p=this.state.formattedGraphicalItems.find(function(t){return"Scatter"===t.item.type.name});p&&(f=dO(dO({},f),p.props.points[a].tooltipPosition),u=p.props.points[a].tooltipPayload);var d={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:c,activePayload:u,activeCoordinate:f};this.setState(d),this.renderCursor(o),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(t,e){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==e.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==t.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==t.margin){var r,n;this.accessibilityManager.setDetails({offset:{left:null!=(r=this.props.margin.left)?r:0,top:null!=(n=this.props.margin.top)?n:0}})}return null}},{key:"componentDidUpdate",value:function(t){tA([tw(t.children,ev)],[tw(this.props.children,ev)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var t=tw(this.props.children,ev);if(t&&"boolean"==typeof t.props.shared){var e=t.props.shared?"axis":"item";return u.indexOf(e)>=0?e:i}return i}},{key:"getMouseInfo",value:function(t){if(!this.container)return null;var e=this.container,r=e.getBoundingClientRect(),n={top:r.top+window.scrollY-document.documentElement.clientTop,left:r.left+window.scrollX-document.documentElement.clientLeft},i={chartX:Math.round(t.pageX-n.left),chartY:Math.round(t.pageY-n.top)},o=r.width/e.offsetWidth||1,a=this.inRange(i.chartX,i.chartY,o);if(!a)return null;var c=this.state,u=c.xAxisMap,s=c.yAxisMap,l=this.getTooltipEventType(),f=dC(this.state,this.props.data,this.props.layout,a);if("axis"!==l&&u&&s){var p=W(u).scale,d=W(s).scale,h=p&&p.invert?p.invert(i.chartX):null,y=d&&d.invert?d.invert(i.chartY):null;return dO(dO({},i),{},{xValue:h,yValue:y},f)}return f?dO(dO({},i),f):null}},{key:"inRange",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,i=t/r,o=e/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return i>=a.left&&i<=a.left+a.width&&o>=a.top&&o<=a.top+a.height?{x:i,y:o}:null}var c=this.state,u=c.angleAxisMap,s=c.radiusAxisMap;return u&&s?s7({x:i,y:o},W(u)):null}},{key:"parseEventsOfWrapper",value:function(){var t=this.props.children,e=this.getTooltipEventType(),r=tw(t,ev),n={};return r&&"axis"===e&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),dO(dO({},ts(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){pj.on(pS,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){pj.removeListener(pS,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(t,e,r){for(var n=this.state.formattedGraphicalItems,i=0,o=n.length;i<o;i++){var a=n[i];if(a.item===t||a.props.key===t.key||e===tv(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var t=this.clipPathId,e=this.state.offset,r=e.left,n=e.top,i=e.height,o=e.width;return a().createElement("defs",null,a().createElement("clipPath",{id:t},a().createElement("rect",{x:r,y:n,height:i,width:o})))}},{key:"getXScales",value:function(){var t=this.state.xAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=dd(e,2),n=r[0],i=r[1];return dO(dO({},t),{},dj({},n,i.scale))},{}):null}},{key:"getYScales",value:function(){var t=this.state.yAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=dd(e,2),n=r[0],i=r[1];return dO(dO({},t),{},dj({},n,i.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(t){var e;return null==(e=this.state.xAxisMap)||null==(e=e[t])?void 0:e.scale}},{key:"getYScaleByAxisId",value:function(t){var e;return null==(e=this.state.yAxisMap)||null==(e=e[t])?void 0:e.scale}},{key:"getItemByXY",value:function(t){var e=this.state,r=e.formattedGraphicalItems,n=e.activeItem;if(r&&r.length)for(var i=0,o=r.length;i<o;i++){var a=r[i],c=a.props,u=a.item,s=void 0!==u.type.defaultProps?dO(dO({},u.type.defaultProps),u.props):u.props,l=tv(u.type);if("Bar"===l){var f=(c.data||[]).find(function(e){return nP(t,e)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===l){var p=(c.data||[]).find(function(e){return s7(t,e)});if(p)return{graphicalItem:a,payload:p}}else if(l2(a,n)||l5(a,n)||l4(a,n)){var d=function(t){var e,r,n,i=t.activeTooltipItem,o=t.graphicalItem,a=t.itemData,c=(l2(o,i)?e="trapezoids":l5(o,i)?e="sectors":l4(o,i)&&(e="points"),e),u=l2(o,i)?null==(r=i.tooltipPayload)||null==(r=r[0])||null==(r=r.payload)?void 0:r.payload:l5(o,i)?null==(n=i.tooltipPayload)||null==(n=n[0])||null==(n=n.payload)?void 0:n.payload:l4(o,i)?i.payload:{},s=a.filter(function(t,e){var r=c7()(u,t),n=o.props[c].filter(function(t){var e;return(l2(o,i)?e=l3:l5(o,i)?e=l6:l4(o,i)&&(e=l8),e)(t,i)}),a=o.props[c].indexOf(n[n.length-1]);return r&&e===a});return a.indexOf(s[s.length-1])}({graphicalItem:a,activeTooltipItem:n,itemData:s.data}),h=void 0===s.activeIndex?d:s.activeIndex;return{graphicalItem:dO(dO({},a),{},{childIndex:h}),payload:l4(a,n)?s.data[d]:a.props.data[d]}}}return null}},{key:"render",value:function(){var t,e,r=this;if(!tO(this))return null;var n=this.props,i=n.children,o=n.className,c=n.width,u=n.height,s=n.style,l=n.compact,f=n.title,p=n.desc,d=tP(dh(n,dl),!1);if(l)return a().createElement(fJ,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},a().createElement(t$,dp({},d,{width:c,height:u,title:f,desc:p}),this.renderClipPath(),tk(i,this.renderMap)));this.props.accessibilityLayer&&(d.tabIndex=null!=(t=this.props.tabIndex)?t:0,d.role=null!=(e=this.props.role)?e:"application",d.onKeyDown=function(t){r.accessibilityManager.keyboardEvent(t)},d.onFocus=function(){r.accessibilityManager.focus()});var h=this.parseEventsOfWrapper();return a().createElement(fJ,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},a().createElement("div",dp({className:(0,A.A)("recharts-wrapper",o),style:dO({position:"relative",cursor:"default",width:c,height:u},s)},h,{ref:function(t){r.container=t}}),a().createElement(t$,dp({},d,{width:c,height:u,title:f,desc:p,style:dA}),this.renderClipPath(),tk(i,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,dS(n.key),n)}}(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(o.Component);dj(y,"displayName",e),dj(y,"defaultProps",dO({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},p)),dj(y,"getDerivedStateFromProps",function(t,e){var r=t.dataKey,n=t.data,i=t.children,o=t.width,a=t.height,c=t.layout,u=t.stackOffset,s=t.margin,l=e.dataStartIndex,f=e.dataEndIndex;if(void 0===e.updateId){var p=dL(t);return dO(dO(dO({},p),{},{updateId:0},h(dO(dO({props:t},p),{},{updateId:0}),e)),{},{prevDataKey:r,prevData:n,prevWidth:o,prevHeight:a,prevLayout:c,prevStackOffset:u,prevMargin:s,prevChildren:i})}if(r!==e.prevDataKey||n!==e.prevData||o!==e.prevWidth||a!==e.prevHeight||c!==e.prevLayout||u!==e.prevStackOffset||!tn(s,e.prevMargin)){var d=dL(t),y={chartX:e.chartX,chartY:e.chartY,isTooltipActive:e.isTooltipActive},v=dO(dO({},dC(e,n,c)),{},{updateId:e.updateId+1}),m=dO(dO(dO({},d),y),v);return dO(dO(dO({},m),h(dO({props:t},m),e)),{},{prevDataKey:r,prevData:n,prevWidth:o,prevHeight:a,prevLayout:c,prevStackOffset:u,prevMargin:s,prevChildren:i})}if(!tA(i,e.prevChildren)){var b,g,x,w,O=tw(i,sQ),j=O&&null!=(b=null==(g=O.props)?void 0:g.startIndex)?b:l,S=O&&null!=(x=null==(w=O.props)?void 0:w.endIndex)?x:f,P=Z()(n)||j!==l||S!==f?e.updateId+1:e.updateId;return dO(dO({updateId:P},h(dO(dO({props:t},e),{},{updateId:P,dataStartIndex:j,dataEndIndex:S}),e)),{},{prevChildren:i,dataStartIndex:j,dataEndIndex:S})}return null}),dj(y,"renderActiveDot",function(t,e,r){var n;return n=(0,o.isValidElement)(t)?(0,o.cloneElement)(t,e):Q()(t)?t(e):a().createElement(ru,e),a().createElement(tG,{className:"recharts-active-dot",key:r},n)});var v=(0,o.forwardRef)(function(t,e){return a().createElement(y,dp({},t,{ref:e}))});return v.displayName=y.displayName,v}({chartName:"LineChart",GraphicalChild:d2,axisComponents:[{axisType:"xAxis",AxisComp:hO},{axisType:"yAxis",AxisComp:hM}],formatAxisMap:function(t,e,r,n,i){var o=t.width,a=t.height,c=t.layout,u=t.children,s=Object.keys(e),l={left:r.left,leftMirror:r.left,right:o-r.right,rightMirror:o-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},f=!!tw(u,fm);return s.reduce(function(o,a){var u,s,p,d,h,y=e[a],v=y.orientation,m=y.domain,b=y.padding,g=void 0===b?{}:b,x=y.mirror,w=y.reversed,O="".concat(v).concat(x?"Mirror":"");if("number"===y.type&&("gap"===y.padding||"no-gap"===y.padding)){var j=m[1]-m[0],S=1/0,P=y.categoricalDomain.sort(V);if(P.forEach(function(t,e){e>0&&(S=Math.min((t||0)-(P[e-1]||0),S))}),Number.isFinite(S)){var A=S/j,E="vertical"===y.layout?r.height:r.width;if("gap"===y.padding&&(u=A*E/2),"no-gap"===y.padding){var k=q(t.barCategoryGap,A*E),_=A*E/2;u=_-k-(_-k)/E*k}}}s="xAxis"===n?[r.left+(g.left||0)+(u||0),r.left+r.width-(g.right||0)-(u||0)]:"yAxis"===n?"horizontal"===c?[r.top+r.height-(g.bottom||0),r.top+(g.top||0)]:[r.top+(g.top||0)+(u||0),r.top+r.height-(g.bottom||0)-(u||0)]:y.range,w&&(s=[s[1],s[0]]);var N=sv(y,i,f),M=N.scale,T=N.realScaleType;M.domain(m).range(s),sm(M);var C=sj(M,fw(fw({},y),{},{realScaleType:T}));"xAxis"===n?(h="top"===v&&!x||"bottom"===v&&x,p=r.left,d=l[O]-h*y.height):"yAxis"===n&&(h="left"===v&&!x||"right"===v&&x,p=l[O]-h*y.width,d=r.top);var D=fw(fw(fw({},y),C),{},{realScaleType:T,x:p,y:d,scale:M,width:"xAxis"===n?r.width:y.width,height:"yAxis"===n?r.height:y.height});return D.bandSize=sT(D,C),y.hide||"xAxis"!==n?y.hide||(l[O]+=(h?-1:1)*D.width):l[O]+=(h?-1:1)*D.height,fw(fw({},o),{},fO({},a,D))},{})}}),hC=["x1","y1","x2","y2","key"],hD=["offset"];function hI(t){return(hI="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function hB(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hR(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hB(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=hI(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hI(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hI(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hB(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function hL(){return(hL=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function hz(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var hU=function(t){var e=t.fill;if(!e||"none"===e)return null;var r=t.fillOpacity,n=t.x,i=t.y,o=t.width,c=t.height,u=t.ry;return a().createElement("rect",{x:n,y:i,ry:u,width:o,height:c,stroke:"none",fill:e,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function hF(t,e){var r;if(a().isValidElement(t))r=a().cloneElement(t,e);else if(Q()(t))r=t(e);else{var n=e.x1,i=e.y1,o=e.x2,c=e.y2,u=e.key,s=tP(hz(e,hC),!1),l=(s.offset,hz(s,hD));r=a().createElement("line",hL({},l,{x1:n,y1:i,x2:o,y2:c,fill:"none",key:u}))}return r}function h$(t){var e=t.x,r=t.width,n=t.horizontal,i=void 0===n||n,o=t.horizontalPoints;if(!i||!o||!o.length)return null;var c=o.map(function(n,o){return hF(i,hR(hR({},t),{},{x1:e,y1:n,x2:e+r,y2:n,key:"line-".concat(o),index:o}))});return a().createElement("g",{className:"recharts-cartesian-grid-horizontal"},c)}function hq(t){var e=t.y,r=t.height,n=t.vertical,i=void 0===n||n,o=t.verticalPoints;if(!i||!o||!o.length)return null;var c=o.map(function(n,o){return hF(i,hR(hR({},t),{},{x1:n,y1:e,x2:n,y2:e+r,key:"line-".concat(o),index:o}))});return a().createElement("g",{className:"recharts-cartesian-grid-vertical"},c)}function hW(t){var e=t.horizontalFill,r=t.fillOpacity,n=t.x,i=t.y,o=t.width,c=t.height,u=t.horizontalPoints,s=t.horizontal;if(!(void 0===s||s)||!e||!e.length)return null;var l=u.map(function(t){return Math.round(t+i-i)}).sort(function(t,e){return t-e});i!==l[0]&&l.unshift(0);var f=l.map(function(t,u){var s=l[u+1]?l[u+1]-t:i+c-t;if(s<=0)return null;var f=u%e.length;return a().createElement("rect",{key:"react-".concat(u),y:t,x:n,height:s,width:o,stroke:"none",fill:e[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return a().createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function hG(t){var e=t.vertical,r=t.verticalFill,n=t.fillOpacity,i=t.x,o=t.y,c=t.width,u=t.height,s=t.verticalPoints;if(!(void 0===e||e)||!r||!r.length)return null;var l=s.map(function(t){return Math.round(t+i-i)}).sort(function(t,e){return t-e});i!==l[0]&&l.unshift(0);var f=l.map(function(t,e){var s=l[e+1]?l[e+1]-t:i+c-t;if(s<=0)return null;var f=e%r.length;return a().createElement("rect",{key:"react-".concat(e),x:t,y:o,width:s,height:u,stroke:"none",fill:r[f],fillOpacity:n,className:"recharts-cartesian-grid-bg"})});return a().createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var hX=function(t,e){var r=t.xAxis,n=t.width,i=t.height,o=t.offset;return sp(d7(hR(hR(hR({},hd.defaultProps),r),{},{ticks:sd(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),o.left,o.left+o.width,e)},hH=function(t,e){var r=t.yAxis,n=t.width,i=t.height,o=t.offset;return sp(d7(hR(hR(hR({},hd.defaultProps),r),{},{ticks:sd(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),o.top,o.top+o.height,e)},hV={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function hK(t){var e,r,n,i,c,u,s=f2(),l=f5(),f=(0,o.useContext)(fV),p=hR(hR({},t),{},{stroke:null!=(e=t.stroke)?e:hV.stroke,fill:null!=(r=t.fill)?r:hV.fill,horizontal:null!=(n=t.horizontal)?n:hV.horizontal,horizontalFill:null!=(i=t.horizontalFill)?i:hV.horizontalFill,vertical:null!=(c=t.vertical)?c:hV.vertical,verticalFill:null!=(u=t.verticalFill)?u:hV.verticalFill,x:z(t.x)?t.x:f.left,y:z(t.y)?t.y:f.top,width:z(t.width)?t.width:f.width,height:z(t.height)?t.height:f.height}),d=p.x,h=p.y,y=p.width,v=p.height,m=p.syncWithTicks,b=p.horizontalValues,g=p.verticalValues,x=W((0,o.useContext)(fG)),w=f0();if(!z(y)||y<=0||!z(v)||v<=0||!z(d)||d!==+d||!z(h)||h!==+h)return null;var O=p.verticalCoordinatesGenerator||hX,j=p.horizontalCoordinatesGenerator||hH,S=p.horizontalPoints,P=p.verticalPoints;if((!S||!S.length)&&Q()(j)){var A=b&&b.length,E=j({yAxis:w?hR(hR({},w),{},{ticks:A?b:w.ticks}):void 0,width:s,height:l,offset:f},!!A||m);K(Array.isArray(E),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(hI(E),"]")),Array.isArray(E)&&(S=E)}if((!P||!P.length)&&Q()(O)){var k=g&&g.length,_=O({xAxis:x?hR(hR({},x),{},{ticks:k?g:x.ticks}):void 0,width:s,height:l,offset:f},!!k||m);K(Array.isArray(_),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(hI(_),"]")),Array.isArray(_)&&(P=_)}return a().createElement("g",{className:"recharts-cartesian-grid"},a().createElement(hU,{fill:p.fill,fillOpacity:p.fillOpacity,x:p.x,y:p.y,width:p.width,height:p.height,ry:p.ry}),a().createElement(h$,hL({},p,{offset:f,horizontalPoints:S,xAxis:x,yAxis:w})),a().createElement(hq,hL({},p,{offset:f,verticalPoints:P,xAxis:x,yAxis:w})),a().createElement(hW,hL({},p,{horizontalPoints:S})),a().createElement(hG,hL({},p,{verticalPoints:P})))}function hY(){let[t,e]=(0,o.useState)("avanzamento"),[r,n]=(0,o.useState)("month"),[a,A]=(0,o.useState)(null),[E,k]=(0,o.useState)(null),[_,N]=(0,o.useState)(null),[M,T]=(0,o.useState)(null),[C,D]=(0,o.useState)(!0),[I,B]=(0,o.useState)(""),{user:R,isLoading:L}=(0,l.A)(),{cantiereId:z,cantiere:U,isValidCantiere:F,isLoading:$,error:q}=(0,f.jV)();return(0,i.jsx)(p.u,{children:(0,i.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100",children:(0,i.jsx)("div",{className:"max-w-[90%] mx-auto py-6 space-y-6",children:C||L?(0,i.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(d.A,{className:"h-6 w-6 animate-spin"}),(0,i.jsx)("span",{children:"Caricamento report..."})]})}):I?(0,i.jsxs)("div",{className:"p-6 border border-amber-200 rounded-lg bg-amber-50",children:[(0,i.jsxs)("div",{className:"flex items-center mb-4",children:[(0,i.jsx)(h.A,{className:"h-5 w-5 text-amber-600 mr-2"}),(0,i.jsx)("span",{className:"text-amber-800 font-medium",children:I.includes("Nessun cantiere selezionato")||I.includes("Cantiere non selezionato")?"Cantiere non selezionato":I.includes("timeout")||I.includes("Timeout")?"Timeout API":"Errore caricamento report"})]}),(0,i.jsx)("p",{className:"text-amber-700 mb-4",children:I}),I.includes("timeout")||I.includes("Timeout")?(0,i.jsx)("div",{className:"mb-4 p-3 bg-blue-50 border border-blue-200 rounded",children:(0,i.jsxs)("p",{className:"text-blue-800 text-sm",children:["\uD83D\uDCA1 ",(0,i.jsx)("strong",{children:"Suggerimento:"})," Le API stanno impiegando pi\xf9 tempo del previsto. Prova ad aggiornare la pagina o riprova tra qualche minuto."]})}):null,(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsx)(u.$,{onClick:()=>window.location.href="/cantieri",className:"bg-amber-600 hover:bg-amber-700 text-white",children:"Gestisci Cantieri"}),(0,i.jsxs)(u.$,{variant:"outline",onClick:()=>{F&&z?(console.log("\uD83C\uDFD7️ ReportsPage: Refresh report per cantiere:",z),D(!0),B(""),T(null),k(null),N(null),A(null),loadAllReports()):console.warn("\uD83C\uDFD7️ ReportsPage: Impossibile fare refresh, nessun cantiere valido")},className:"border-amber-600 text-amber-700 hover:bg-amber-100",children:[(0,i.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Riprova"]})]})]}):(0,i.jsxs)(s.tU,{value:t,onValueChange:e,className:"w-full",children:[(0,i.jsx)("div",{className:"bg-white rounded-lg border border-slate-200 shadow-sm p-1 mb-6 mt-6",children:(0,i.jsxs)("div",{className:"grid grid-cols-4 gap-1 h-auto bg-transparent p-0",children:[(0,i.jsxs)("button",{onClick:()=>e("avanzamento"),className:`admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 border border-transparent ${"avanzamento"===t?"bg-blue-50 text-blue-700 border-blue-200 shadow-sm":"hover:bg-slate-50"}`,"data-state":"avanzamento"===t?"active":"inactive",children:[(0,i.jsx)(v.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{className:"font-medium",children:"Avanzamento"})]}),(0,i.jsxs)("button",{onClick:()=>e("boq"),className:`admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 border border-transparent ${"boq"===t?"bg-blue-50 text-blue-700 border-blue-200 shadow-sm":"hover:bg-slate-50"}`,"data-state":"boq"===t?"active":"inactive",children:[(0,i.jsx)(m.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{className:"font-medium",children:"BOQ"})]}),(0,i.jsxs)("button",{onClick:()=>e("bobine"),className:`admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 border border-transparent ${"bobine"===t?"bg-blue-50 text-blue-700 border-blue-200 shadow-sm":"hover:bg-slate-50"}`,"data-state":"bobine"===t?"active":"inactive",children:[(0,i.jsx)(b.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{className:"font-medium",children:"Bobine"})]}),(0,i.jsxs)("button",{onClick:()=>e("produttivita"),className:`admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 border border-transparent ${"produttivita"===t?"bg-blue-50 text-blue-700 border-blue-200 shadow-sm":"hover:bg-slate-50"}`,"data-state":"produttivita"===t?"active":"inactive",children:[(0,i.jsx)(g.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{className:"font-medium",children:"Produttivit\xe0"})]})]})}),(0,i.jsx)(s.av,{value:"avanzamento",className:"space-y-6",children:M?.content?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,i.jsx)("div",{className:"p-2 bg-blue-50 rounded-lg",children:(0,i.jsx)(v.A,{className:"h-5 w-5 text-blue-600"})}),(0,i.jsxs)("div",{className:"text-right",children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:M.content.metri_totali?.toLocaleString()||0}),(0,i.jsx)("div",{className:"text-xs text-blue-600 font-medium",children:"metri"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-sm font-medium text-gray-600 mb-1",children:"Metri Totali"}),(0,i.jsxs)("p",{className:"text-xs text-gray-500",children:[M.content.totale_cavi||0," cavi totali"]})]})]}),(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,i.jsx)("div",{className:"p-2 bg-green-50 rounded-lg",children:(0,i.jsx)(x.A,{className:"h-5 w-5 text-green-600"})}),(0,i.jsxs)("div",{className:"text-right",children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:M.content.metri_posati?.toLocaleString()||0}),(0,i.jsx)("div",{className:"text-xs text-green-600 font-medium",children:"metri"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-sm font-medium text-gray-600 mb-2",children:"Metri Posati"}),(0,i.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mb-2",children:(0,i.jsx)("div",{className:"bg-green-500 h-2 rounded-full transition-all duration-300",style:{width:`${Math.min(M.content.percentuale_avanzamento||0,100)}%`}})}),(0,i.jsxs)("p",{className:"text-xs text-gray-500",children:[M.content.percentuale_avanzamento?.toFixed(1)||0,"% completato"]})]})]}),(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,i.jsx)("div",{className:"p-2 bg-purple-50 rounded-lg",children:(0,i.jsx)(w.A,{className:"h-5 w-5 text-purple-600"})}),(0,i.jsxs)("div",{className:"text-right",children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:M.content.media_giornaliera?.toFixed(1)||0}),(0,i.jsx)("div",{className:"text-xs text-purple-600 font-medium",children:"m/giorno"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-sm font-medium text-gray-600 mb-1",children:"Media Giornaliera"}),(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)(O.A,{className:"h-3 w-3 text-purple-500 mr-1"}),(0,i.jsxs)("span",{className:"text-xs text-gray-500",children:[M.content.giorni_lavorativi_effettivi||0," giorni attivi"]})]})]})]}),(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,i.jsx)("div",{className:"p-2 bg-orange-50 rounded-lg",children:(0,i.jsx)(j.A,{className:"h-5 w-5 text-orange-600"})}),(0,i.jsxs)("div",{className:"text-right",children:[(0,i.jsx)("div",{className:"text-lg font-bold text-gray-900",children:M.content.data_completamento?new Date(M.content.data_completamento).toLocaleDateString("it-IT"):"Da calcolare"}),(0,i.jsx)("div",{className:"text-xs text-orange-600 font-medium",children:"stima"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-sm font-medium text-gray-600 mb-1",children:"Completamento"}),(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)(S.A,{className:"h-3 w-3 text-orange-500 mr-1"}),(0,i.jsxs)("span",{className:"text-xs text-gray-500",children:[M.content.giorni_stimati||0," giorni rimanenti"]})]})]})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,i.jsxs)("div",{className:"mb-6",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:"Posa Recente"}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:"Ultimi 10 giorni di attivit\xe0"})]}),(0,i.jsx)("div",{className:"h-80",children:(0,i.jsx)(tD,{width:"100%",height:"100%",children:(0,i.jsxs)(hT,{data:[...M.content.posa_recente||[]].reverse(),children:[(0,i.jsx)(hK,{strokeDasharray:"3 3",stroke:"#f1f5f9"}),(0,i.jsx)(hO,{dataKey:"data",tick:{fontSize:12,fill:"#64748b"},axisLine:{stroke:"#e2e8f0"}}),(0,i.jsx)(hM,{tick:{fontSize:12,fill:"#64748b"},axisLine:{stroke:"#e2e8f0"}}),(0,i.jsx)(ev,{contentStyle:{backgroundColor:"white",border:"1px solid #e2e8f0",borderRadius:"8px",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1)",fontSize:"14px"},labelStyle:{color:"#374151",fontWeight:"500"}}),(0,i.jsx)(d2,{type:"monotone",dataKey:"metri",stroke:"#3b82f6",strokeWidth:3,dot:{fill:"#3b82f6",strokeWidth:2,r:4},activeDot:{r:6,stroke:"#3b82f6",strokeWidth:2,fill:"white"},name:"Metri Posati"})]})})})]}),(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,i.jsxs)("div",{className:"mb-6",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:"Stato Cavi"}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:"Distribuzione per stato di avanzamento"})]}),(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[(0,i.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),(0,i.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Cavi Posati"})]}),(0,i.jsx)("span",{className:"text-lg font-bold text-green-700",children:M.content.cavi_posati||0})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[(0,i.jsx)("div",{className:"w-3 h-3 bg-gray-400 rounded-full"}),(0,i.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Cavi Rimanenti"})]}),(0,i.jsx)("span",{className:"text-lg font-bold text-gray-700",children:M.content.cavi_rimanenti||0})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[(0,i.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded-full"}),(0,i.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Percentuale Completamento"})]}),(0,i.jsxs)("span",{className:"text-lg font-bold text-blue-700",children:[M.content.percentuale_cavi?.toFixed(1)||0,"%"]})]})]})]})]})]}):(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center",children:[(0,i.jsx)(v.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-700 mb-2",children:"Nessun Dato Disponibile"}),(0,i.jsx)("p",{className:"text-gray-500",children:"Nessun dato di avanzamento disponibile per questo cantiere"})]})}),(0,i.jsx)(s.av,{value:"boq",className:"space-y-6",children:E?.error?(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-amber-200 p-12 text-center",children:[(0,i.jsx)(h.A,{className:"h-12 w-12 mx-auto mb-4 text-amber-500"}),(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-700 mb-2",children:"API BOQ Temporaneamente Non Disponibile"}),(0,i.jsx)("p",{className:"text-gray-500 mb-4",children:"Il servizio BOQ sta riscontrando problemi di performance."}),(0,i.jsx)("p",{className:"text-sm text-gray-400",children:"Stiamo lavorando per risolvere il problema."})]}):E?.content?(0,i.jsxs)(i.Fragment,{children:[E.content.metri_orfani&&E.content.metri_orfani.metri_orfani_totali>0&&(0,i.jsxs)(c.Zp,{className:"border-red-200 bg-red-50",children:[(0,i.jsx)(c.aR,{className:"pb-3",children:(0,i.jsxs)(c.ZB,{className:"text-red-700 flex items-center",children:[(0,i.jsx)(P.A,{className:"h-5 w-5 mr-2"}),"\uD83D\uDEA8 METRI POSATI SENZA TRACCIABILIT\xc0 BOBINA"]})}),(0,i.jsxs)(c.Wu,{children:[(0,i.jsxs)("p",{className:"text-red-800 font-medium mb-2",children:[(0,i.jsxs)("strong",{children:[E.content.metri_orfani.metri_orfani_totali,"m"]})," installati con BOBINA_VUOTA (",E.content.metri_orfani.num_cavi_orfani," cavi)"]}),(0,i.jsx)("div",{className:"text-sm text-red-700 space-y-1",children:Array.isArray(E.content.metri_orfani.dettaglio_per_categoria)?E.content.metri_orfani.dettaglio_per_categoria.map((t,e)=>(0,i.jsxs)("div",{children:["• ",(0,i.jsxs)("strong",{children:[t.tipologia," ",t.formazione]}),": ",t.metri_orfani,"m (",t.num_cavi," cavi)"]},e)):(0,i.jsx)("div",{children:"Dettaglio metri orfani non disponibile"})}),(0,i.jsx)("div",{className:"mt-3 p-3 bg-amber-50 border border-amber-200 rounded",children:(0,i.jsxs)("p",{className:"text-amber-800 text-sm",children:["⚠️ ",(0,i.jsx)("strong",{children:"NOTA:"})," I metri orfani NON sono inclusi nel calcolo acquisti. Prima di acquistare, verificare se questi metri possono essere associati a bobine esistenti."]})})]})]}),E.content.riepilogo&&(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,i.jsx)("div",{className:"p-2 bg-red-50 rounded-lg",children:(0,i.jsx)(P.A,{className:"h-5 w-5 text-red-600"})}),(0,i.jsxs)("div",{className:"text-right",children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:E.content.riepilogo.totale_metri_mancanti?.toLocaleString()||0}),(0,i.jsx)("div",{className:"text-xs text-red-600 font-medium",children:"metri"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-sm font-medium text-gray-600 mb-1",children:"Metri da Acquistare"}),(0,i.jsx)("p",{className:"text-xs text-gray-500",children:"per completamento progetto"})]})]}),(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,i.jsx)("div",{className:"p-2 bg-green-50 rounded-lg",children:(0,i.jsx)(b.A,{className:"h-5 w-5 text-green-600"})}),(0,i.jsxs)("div",{className:"text-right",children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:E.content.riepilogo.totale_metri_residui?.toLocaleString()||0}),(0,i.jsx)("div",{className:"text-xs text-green-600 font-medium",children:"metri"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-sm font-medium text-gray-600 mb-1",children:"Metri Residui"}),(0,i.jsx)("p",{className:"text-xs text-gray-500",children:"disponibili in magazzino"})]})]}),(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,i.jsx)("div",{className:"p-2 bg-purple-50 rounded-lg",children:(0,i.jsx)(x.A,{className:"h-5 w-5 text-purple-600"})}),(0,i.jsxs)("div",{className:"text-right",children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:E.content.riepilogo.percentuale_completamento?.toFixed(1)||0}),(0,i.jsx)("div",{className:"text-xs text-purple-600 font-medium",children:"%"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-sm font-medium text-gray-600 mb-1",children:"Completamento"}),(0,i.jsx)("p",{className:"text-xs text-gray-500",children:"progetto completato"})]})]}),(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,i.jsx)("div",{className:"p-2 bg-orange-50 rounded-lg",children:(0,i.jsx)(m.A,{className:"h-5 w-5 text-orange-600"})}),(0,i.jsxs)("div",{className:"text-right",children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:E.content.riepilogo.categorie_necessitano_acquisto||0}),(0,i.jsx)("div",{className:"text-xs text-orange-600 font-medium",children:"categorie"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-sm font-medium text-gray-600 mb-1",children:"Categorie"}),(0,i.jsx)("p",{className:"text-xs text-gray-500",children:"necessitano acquisto"})]})]})]}),(0,i.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:(0,i.jsx)("div",{className:"overflow-x-auto",children:(0,i.jsxs)("table",{className:"w-full",children:[(0,i.jsx)("thead",{className:"bg-gray-50",children:(0,i.jsxs)("tr",{children:[(0,i.jsx)("th",{className:"text-left p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tipologia"}),(0,i.jsx)("th",{className:"text-left p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Formazione"}),(0,i.jsx)("th",{className:"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Cavi"}),(0,i.jsx)("th",{className:"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Metri Teorici"}),(0,i.jsx)("th",{className:"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Metri Posati"}),(0,i.jsx)("th",{className:"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Metri da Posare"}),(0,i.jsx)("th",{className:"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Bobine"}),(0,i.jsx)("th",{className:"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Metri Residui"}),(0,i.jsx)("th",{className:"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Metri Mancanti"}),(0,i.jsx)("th",{className:"text-center p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Acquisto"})]})}),(0,i.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:E.content.distinta_materiali?.map((t,e)=>(0,i.jsxs)("tr",{className:`hover:bg-gray-50 transition-colors duration-150 ${t.ha_bobina_vuota?"bg-red-50":""}`,children:[(0,i.jsx)("td",{className:"p-4 text-sm font-medium text-gray-900",children:t.tipologia}),(0,i.jsx)("td",{className:"p-4 text-sm text-gray-700",children:t.formazione}),(0,i.jsx)("td",{className:"p-4 text-sm text-gray-700 text-right",children:t.num_cavi}),(0,i.jsx)("td",{className:"p-4 text-sm text-gray-700 text-right",children:t.metri_teorici_totali?.toLocaleString()}),(0,i.jsx)("td",{className:"p-4 text-sm text-gray-700 text-right",children:t.metri_reali_posati?.toLocaleString()}),(0,i.jsx)("td",{className:"p-4 text-sm text-gray-700 text-right",children:t.metri_da_posare?.toLocaleString()}),(0,i.jsx)("td",{className:"p-4 text-sm text-gray-700 text-right",children:t.num_bobine}),(0,i.jsx)("td",{className:"p-4 text-sm text-gray-700 text-right",children:t.metri_residui?.toLocaleString()}),(0,i.jsx)("td",{className:"p-4 text-sm font-medium text-right",children:t.metri_mancanti>0?(0,i.jsxs)("span",{className:"text-red-600",children:[t.metri_mancanti?.toLocaleString(),"m"]}):(0,i.jsx)("span",{className:"text-green-600",children:"0m"})}),(0,i.jsx)("td",{className:"p-4 text-center",children:t.necessita_acquisto?(0,i.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800",children:"S\xec"}):(0,i.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:"No"})})]},e))})]})})}),(0,i.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:(0,i.jsx)("div",{className:"overflow-x-auto",children:(0,i.jsxs)("table",{className:"w-full",children:[(0,i.jsx)("thead",{className:"bg-gray-50",children:(0,i.jsxs)("tr",{children:[(0,i.jsx)("th",{className:"text-left p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tipologia"}),(0,i.jsx)("th",{className:"text-left p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Formazione"}),(0,i.jsx)("th",{className:"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Numero Bobine"}),(0,i.jsx)("th",{className:"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Metri Disponibili"})]})}),(0,i.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:E.content.bobine_per_tipo?.map((t,e)=>(0,i.jsxs)("tr",{className:"hover:bg-gray-50 transition-colors duration-150",children:[(0,i.jsx)("td",{className:"p-4 text-sm font-medium text-gray-900",children:t.tipologia}),(0,i.jsx)("td",{className:"p-4 text-sm text-gray-700",children:t.formazione}),(0,i.jsx)("td",{className:"p-4 text-sm text-gray-700 text-right",children:t.num_bobine}),(0,i.jsx)("td",{className:"p-4 text-sm font-medium text-right",children:(0,i.jsxs)("span",{className:t.metri_disponibili>0?"text-green-600":"text-red-600",children:[t.metri_disponibili?.toLocaleString(),"m"]})})]},e))})]})})})]}):(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center",children:[(0,i.jsx)(m.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-700 mb-2",children:"Nessun Dato Disponibile"}),(0,i.jsx)("p",{className:"text-gray-500",children:"Nessun dato BOQ disponibile per questo cantiere"})]})}),(0,i.jsx)(s.av,{value:"bobine",className:"space-y-6",children:_?.content?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,i.jsx)("div",{className:"p-2 bg-blue-50 rounded-lg",children:(0,i.jsx)(b.A,{className:"h-5 w-5 text-blue-600"})}),(0,i.jsxs)("div",{className:"text-right",children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:_.content.totale_bobine||0}),(0,i.jsx)("div",{className:"text-xs text-blue-600 font-medium",children:"bobine"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-sm font-medium text-gray-600 mb-1",children:"Totale Bobine"}),(0,i.jsx)("p",{className:"text-xs text-gray-500",children:"bobine nel cantiere"})]})]}),(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,i.jsx)("div",{className:"p-2 bg-green-50 rounded-lg",children:(0,i.jsx)(O.A,{className:"h-5 w-5 text-green-600"})}),(0,i.jsxs)("div",{className:"text-right",children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:_.content.bobine?.filter(t=>"In uso"===t.stato||"Disponibile"===t.stato).length||0}),(0,i.jsx)("div",{className:"text-xs text-green-600 font-medium",children:"attive"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-sm font-medium text-gray-600 mb-1",children:"Bobine Attive"}),(0,i.jsx)("p",{className:"text-xs text-gray-500",children:"disponibili/in uso"})]})]}),(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,i.jsx)("div",{className:"p-2 bg-purple-50 rounded-lg",children:(0,i.jsx)(w.A,{className:"h-5 w-5 text-purple-600"})}),(0,i.jsxs)("div",{className:"text-right",children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:_.content.bobine?.length>0?(_.content.bobine.reduce((t,e)=>t+(e.percentuale_utilizzo||0),0)/_.content.bobine.length).toFixed(1):0}),(0,i.jsx)("div",{className:"text-xs text-purple-600 font-medium",children:"%"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-sm font-medium text-gray-600 mb-1",children:"Utilizzo Medio"}),(0,i.jsx)("p",{className:"text-xs text-gray-500",children:"utilizzo medio"})]})]})]}),(0,i.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:(0,i.jsx)("div",{className:"overflow-x-auto",children:(0,i.jsxs)("table",{className:"w-full",children:[(0,i.jsx)("thead",{className:"bg-gray-50",children:(0,i.jsxs)("tr",{children:[(0,i.jsx)("th",{className:"text-left p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Codice"}),(0,i.jsx)("th",{className:"text-left p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tipologia"}),(0,i.jsx)("th",{className:"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Metri Totali"}),(0,i.jsx)("th",{className:"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Metri Utilizzati"}),(0,i.jsx)("th",{className:"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Metri Residui"}),(0,i.jsx)("th",{className:"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Utilizzo %"}),(0,i.jsx)("th",{className:"text-left p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Stato"}),(0,i.jsx)("th",{className:"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Cavi"})]})}),(0,i.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:_.content.bobine?.map((t,e)=>(0,i.jsxs)("tr",{className:"hover:bg-gray-50 transition-colors duration-150",children:[(0,i.jsx)("td",{className:"p-4 text-sm font-medium text-gray-900",children:t.codice}),(0,i.jsx)("td",{className:"p-4 text-sm text-gray-700",children:t.tipologia}),(0,i.jsx)("td",{className:"p-4 text-sm text-gray-700 text-right",children:t.metri_totali?.toLocaleString()}),(0,i.jsx)("td",{className:"p-4 text-sm text-gray-700 text-right",children:t.metri_utilizzati?.toLocaleString()}),(0,i.jsx)("td",{className:"p-4 text-sm text-gray-700 text-right",children:t.metri_residui?.toLocaleString()}),(0,i.jsx)("td",{className:"p-4 text-sm text-gray-700 text-right",children:(0,i.jsxs)("div",{className:"flex items-center justify-end gap-2",children:[(0,i.jsxs)("span",{className:"text-sm font-medium",children:[t.percentuale_utilizzo?.toFixed(1),"%"]}),(0,i.jsx)("div",{className:"w-16 bg-gray-200 rounded-full h-2",children:(0,i.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:`${Math.min(t.percentuale_utilizzo||0,100)}%`}})})]})}),(0,i.jsx)("td",{className:"p-4",children:(0,i.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${"Disponibile"===t.stato?"bg-green-100 text-green-800":"In uso"===t.stato?"bg-blue-100 text-blue-800":"Terminata"===t.stato?"bg-gray-100 text-gray-800":"Over"===t.stato?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:t.stato})}),(0,i.jsx)("td",{className:"p-4 text-sm text-gray-700 text-right",children:t.totale_cavi_associati||0})]},e))})]})})})]}):(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center",children:[(0,i.jsx)(b.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-700 mb-2",children:"Nessun Dato Disponibile"}),(0,i.jsx)("p",{className:"text-gray-500",children:"Nessun dato bobine disponibile per questo cantiere"})]})}),(0,i.jsx)(s.av,{value:"produttivita",className:"space-y-6",children:(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center",children:[(0,i.jsx)("div",{className:"p-4 bg-blue-50 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center",children:(0,i.jsx)(g.A,{className:"h-10 w-10 text-blue-600"})}),(0,i.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Produttivit\xe0"}),(0,i.jsx)("p",{className:"text-gray-600 mb-2",children:"Funzionalit\xe0 in fase di sviluppo"}),(0,i.jsx)("p",{className:"text-sm text-gray-500",children:"Includer\xe0 calcoli IAP, statistiche team e analisi performance"})]})})]})})})})}hK.displayName="CartesianGrid"},54765:(t,e,r)=>{var n=r(67554),i=r(32269);t.exports=function(t,e){var r=-1,o=i(t)?Array(t.length):[];return n(t,function(t,n,i){o[++r]=e(t,n,i)}),o}},55048:t=>{t.exports=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}},55511:t=>{"use strict";t.exports=require("crypto")},55591:t=>{"use strict";t.exports=require("https")},56506:(t,e,r)=>{var n=r(32269);t.exports=function(t,e){return function(r,i){if(null==r)return r;if(!n(r))return t(r,i);for(var o=r.length,a=e?o:-1,c=Object(r);(e?a--:++a<o)&&!1!==i(c[a],a,c););return r}}},57797:(t,e,r)=>{var n=r(67009);t.exports=function(t,e){for(var r=t.length;r--;)if(n(t[r][0],e))return r;return -1}},58141:(t,e,r)=>{t.exports=r(41547)(Object,"create")},58276:t=>{t.exports=function(t,e){return t.has(e)}},58744:(t,e,r)=>{var n=r(57797);t.exports=function(t,e){var r=this.__data__,i=n(r,t);return i<0?(++this.size,r.push([t,e])):r[i][1]=e,this}},59467:(t,e,r)=>{var n=r(35142),i=r(35163),o=r(40542),a=r(38428),c=r(69619),u=r(46436);t.exports=function(t,e,r){e=n(e,t);for(var s=-1,l=e.length,f=!1;++s<l;){var p=u(e[s]);if(!(f=null!=t&&r(t,p)))break;t=t[p]}return f||++s!=l?f:!!(l=null==t?0:t.length)&&c(l)&&a(p,l)&&(o(t)||i(t))}},59774:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t,n){r[++e]=[n,t]}),r}},61320:(t,e,r)=>{var n=r(8336);t.exports=function(t){return n(this,t).has(t)}},61548:(t,e,r)=>{var n=r(5231),i=r(52823),o=r(55048),a=r(12290),c=/^\[object .+?Constructor\]$/,u=Object.prototype,s=Function.prototype.toString,l=u.hasOwnProperty,f=RegExp("^"+s.call(l).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");t.exports=function(t){return!(!o(t)||i(t))&&(n(t)?f:c).test(a(t))}},61837:(t,e,r)=>{var n=r(21367),i=r(22),o=r(54765),a=r(40542);t.exports=function(t,e){return(a(t)?n:o)(t,i(e,3))}},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63866:(t,e,r)=>{var n=r(29395),i=r(40542),o=r(27467);t.exports=function(t){return"string"==typeof t||!i(t)&&o(t)&&"[object String]"==n(t)}},63979:(t,e,r)=>{var n=r(52599),i=r(6330),o=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols;t.exports=a?function(t){return null==t?[]:n(a(t=Object(t)),function(e){return o.call(t,e)})}:i},65551:(t,e,r)=>{"use strict";r.d(e,{i:()=>c});var n,i=r(43210),o=r(66156),a=(n||(n=r.t(i,2)))[" useInsertionEffect ".trim().toString()]||o.N;function c({prop:t,defaultProp:e,onChange:r=()=>{},caller:n}){let[o,c,u]=function({defaultProp:t,onChange:e}){let[r,n]=i.useState(t),o=i.useRef(r),c=i.useRef(e);return a(()=>{c.current=e},[e]),i.useEffect(()=>{o.current!==r&&(c.current?.(r),o.current=r)},[r,o]),[r,n,c]}({defaultProp:e,onChange:r}),s=void 0!==t,l=s?t:o;{let e=i.useRef(void 0!==t);i.useEffect(()=>{let t=e.current;if(t!==s){let e=s?"controlled":"uncontrolled";console.warn(`${n} is changing from ${t?"controlled":"uncontrolled"} to ${e}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}e.current=s},[s,n])}return[l,i.useCallback(e=>{if(s){let r="function"==typeof e?e(t):e;r!==t&&u.current?.(r)}else c(e)},[s,t,c,u])]}Symbol("RADIX:SYNC_STATE")},65662:t=>{t.exports=function(t,e){return function(r){return t(e(r))}}},65727:(t,e,r)=>{var n=r(81957);t.exports=function(t,e,r){for(var i=-1,o=t.criteria,a=e.criteria,c=o.length,u=r.length;++i<c;){var s=n(o[i],a[i]);if(s){if(i>=u)return s;return s*("desc"==r[i]?-1:1)}}return t.index-e.index}},65932:(t,e,r)=>{t.exports=r(65662)(Object.getPrototypeOf,Object)},65984:t=>{t.exports=function(t){return function(e,r,n){for(var i=-1,o=Object(e),a=n(e),c=a.length;c--;){var u=a[t?c:++i];if(!1===r(o[u],u,o))break}return e}}},66156:(t,e,r)=>{"use strict";r.d(e,{N:()=>i});var n=r(43210),i=globalThis?.document?n.useLayoutEffect:()=>{}},66354:(t,e,r)=>{var n=r(85244),i=Math.max;t.exports=function(t,e,r){return e=i(void 0===e?t.length-1:e,0),function(){for(var o=arguments,a=-1,c=i(o.length-e,0),u=Array(c);++a<c;)u[a]=o[e+a];a=-1;for(var s=Array(e+1);++a<e;)s[a]=o[a];return s[e]=r(u),n(t,this,s)}}},66400:t=>{var e=Date.now;t.exports=function(t){var r=0,n=0;return function(){var i=e(),o=16-(i-n);if(n=i,o>0){if(++r>=800)return arguments[0]}else r=0;return t.apply(void 0,arguments)}}},66713:(t,e,r)=>{var n=r(3105),i=r(34117),o=r(48385);t.exports=function(t){return i(t)?o(t):n(t)}},66837:(t,e,r)=>{var n=r(58141);t.exports=function(){this.__data__=n?n(null):{},this.size=0}},66930:(t,e,r)=>{var n=r(27669),i=r(28837),o=r(94388),a=r(35800),c=r(58744);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=i,u.prototype.get=o,u.prototype.has=a,u.prototype.set=c,t.exports=u},66992:(t,e)=>{"use strict";var r,n=Symbol.for("react.element"),i=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),u=Symbol.for("react.provider"),s=Symbol.for("react.context"),l=Symbol.for("react.server_context"),f=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),d=Symbol.for("react.suspense_list"),h=Symbol.for("react.memo"),y=Symbol.for("react.lazy");Symbol.for("react.offscreen");Symbol.for("react.module.reference"),e.isFragment=function(t){return function(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case n:switch(t=t.type){case o:case c:case a:case p:case d:return t;default:switch(t=t&&t.$$typeof){case l:case s:case f:case y:case h:case u:return t;default:return e}}case i:return e}}}(t)===o}},67009:t=>{t.exports=function(t,e){return t===e||t!=t&&e!=e}},67200:(t,e,r)=>{var n=r(66930),i=r(37575),o=r(75411),a=r(34746),c=r(25118),u=r(30854);function s(t){var e=this.__data__=new n(t);this.size=e.size}s.prototype.clear=i,s.prototype.delete=o,s.prototype.get=a,s.prototype.has=c,s.prototype.set=u,t.exports=s},67367:(t,e,r)=>{var n=r(99525),i=r(22),o=r(75847),a=r(40542),c=r(7383);t.exports=function(t,e,r){var u=a(t)?n:o;return r&&c(t,e,r)&&(e=void 0),u(t,i(e,3))}},67554:(t,e,r)=>{var n=r(99114);t.exports=r(56506)(n)},67619:(t,e,r)=>{var n=r(40542),i=r(49227),o=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.exports=function(t,e){if(n(t))return!1;var r=typeof t;return!!("number"==r||"symbol"==r||"boolean"==r||null==t||i(t))||a.test(t)||!o.test(t)||null!=e&&t in Object(e)}},69433:(t,e,r)=>{t.exports=r(5566)("toUpperCase")},69619:t=>{t.exports=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=0x1fffffffffffff}},69691:(t,e,r)=>{var n=r(41157),i=r(99114),o=r(22);t.exports=function(t,e){var r={};return e=o(e,3),i(t,function(t,i,o){n(r,i,e(t,i,o))}),r}},70151:(t,e,r)=>{var n=r(85718);t.exports=function(){return n.Date.now()}},70222:(t,e,r)=>{var n=r(79474),i=Object.prototype,o=i.hasOwnProperty,a=i.toString,c=n?n.toStringTag:void 0;t.exports=function(t){var e=o.call(t,c),r=t[c];try{t[c]=void 0;var n=!0}catch(t){}var i=a.call(t);return n&&(e?t[c]=r:delete t[c]),i}},70440:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>i});var n=r(31658);let i=async t=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await t.params,"favicon.ico")+""}]},70569:(t,e,r)=>{"use strict";function n(t,e,{checkForDefaultPrevented:r=!0}={}){return function(n){if(t?.(n),!1===r||!n.defaultPrevented)return e?.(n)}}r.d(e,{m:()=>n})},71960:t=>{t.exports=function(t,e,r){for(var n=-1,i=null==t?0:t.length;++n<i;)if(r(e,t[n]))return!0;return!1}},71967:(t,e,r)=>{var n=r(15871);t.exports=function(t,e){return n(t,e)}},72942:(t,e,r)=>{"use strict";r.d(e,{RG:()=>w,bL:()=>N,q7:()=>M});var n=r(43210),i=r(70569),o=r(9510),a=r(98599),c=r(11273),u=r(96963),s=r(14163),l=r(13495),f=r(65551),p=r(43),d=r(60687),h="rovingFocusGroup.onEntryFocus",y={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[m,b,g]=(0,o.N)(v),[x,w]=(0,c.A)(v,[g]),[O,j]=x(v),S=n.forwardRef((t,e)=>(0,d.jsx)(m.Provider,{scope:t.__scopeRovingFocusGroup,children:(0,d.jsx)(m.Slot,{scope:t.__scopeRovingFocusGroup,children:(0,d.jsx)(P,{...t,ref:e})})}));S.displayName=v;var P=n.forwardRef((t,e)=>{let{__scopeRovingFocusGroup:r,orientation:o,loop:c=!1,dir:u,currentTabStopId:m,defaultCurrentTabStopId:g,onCurrentTabStopIdChange:x,onEntryFocus:w,preventScrollOnEntryFocus:j=!1,...S}=t,P=n.useRef(null),A=(0,a.s)(e,P),E=(0,p.jH)(u),[k,N]=(0,f.i)({prop:m,defaultProp:g??null,onChange:x,caller:v}),[M,T]=n.useState(!1),C=(0,l.c)(w),D=b(r),I=n.useRef(!1),[B,R]=n.useState(0);return n.useEffect(()=>{let t=P.current;if(t)return t.addEventListener(h,C),()=>t.removeEventListener(h,C)},[C]),(0,d.jsx)(O,{scope:r,orientation:o,dir:E,loop:c,currentTabStopId:k,onItemFocus:n.useCallback(t=>N(t),[N]),onItemShiftTab:n.useCallback(()=>T(!0),[]),onFocusableItemAdd:n.useCallback(()=>R(t=>t+1),[]),onFocusableItemRemove:n.useCallback(()=>R(t=>t-1),[]),children:(0,d.jsx)(s.sG.div,{tabIndex:M||0===B?-1:0,"data-orientation":o,...S,ref:A,style:{outline:"none",...t.style},onMouseDown:(0,i.m)(t.onMouseDown,()=>{I.current=!0}),onFocus:(0,i.m)(t.onFocus,t=>{let e=!I.current;if(t.target===t.currentTarget&&e&&!M){let e=new CustomEvent(h,y);if(t.currentTarget.dispatchEvent(e),!e.defaultPrevented){let t=D().filter(t=>t.focusable);_([t.find(t=>t.active),t.find(t=>t.id===k),...t].filter(Boolean).map(t=>t.ref.current),j)}}I.current=!1}),onBlur:(0,i.m)(t.onBlur,()=>T(!1))})})}),A="RovingFocusGroupItem",E=n.forwardRef((t,e)=>{let{__scopeRovingFocusGroup:r,focusable:o=!0,active:a=!1,tabStopId:c,children:l,...f}=t,p=(0,u.B)(),h=c||p,y=j(A,r),v=y.currentTabStopId===h,g=b(r),{onFocusableItemAdd:x,onFocusableItemRemove:w,currentTabStopId:O}=y;return n.useEffect(()=>{if(o)return x(),()=>w()},[o,x,w]),(0,d.jsx)(m.ItemSlot,{scope:r,id:h,focusable:o,active:a,children:(0,d.jsx)(s.sG.span,{tabIndex:v?0:-1,"data-orientation":y.orientation,...f,ref:e,onMouseDown:(0,i.m)(t.onMouseDown,t=>{o?y.onItemFocus(h):t.preventDefault()}),onFocus:(0,i.m)(t.onFocus,()=>y.onItemFocus(h)),onKeyDown:(0,i.m)(t.onKeyDown,t=>{if("Tab"===t.key&&t.shiftKey)return void y.onItemShiftTab();if(t.target!==t.currentTarget)return;let e=function(t,e,r){var n;let i=(n=t.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===e&&["ArrowLeft","ArrowRight"].includes(i))&&!("horizontal"===e&&["ArrowUp","ArrowDown"].includes(i)))return k[i]}(t,y.orientation,y.dir);if(void 0!==e){if(t.metaKey||t.ctrlKey||t.altKey||t.shiftKey)return;t.preventDefault();let r=g().filter(t=>t.focusable).map(t=>t.ref.current);if("last"===e)r.reverse();else if("prev"===e||"next"===e){"prev"===e&&r.reverse();let n=r.indexOf(t.currentTarget);r=y.loop?function(t,e){return t.map((r,n)=>t[(e+n)%t.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>_(r))}}),children:"function"==typeof l?l({isCurrentTabStop:v,hasTabStop:null!=O}):l})})});E.displayName=A;var k={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function _(t,e=!1){let r=document.activeElement;for(let n of t)if(n===r||(n.focus({preventScroll:e}),document.activeElement!==r))return}var N=S,M=E},74075:t=>{"use strict";t.exports=require("zlib")},74610:t=>{t.exports=function(t,e,r){for(var n=r-1,i=t.length;++n<i;)if(t[n]===e)return n;return -1}},75254:(t,e,r)=>{var n=r(78418),i=r(93311),o=r(41132);t.exports=function(t){var e=i(t);return 1==e.length&&e[0][2]?o(e[0][0],e[0][1]):function(r){return r===t||n(r,t,e)}}},75411:t=>{t.exports=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r}},75847:(t,e,r)=>{var n=r(67554);t.exports=function(t,e){var r;return n(t,function(t,n,i){return!(r=e(t,n,i))}),!!r}},75900:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\reports\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\reports\\page.tsx","default")},77822:(t,e,r)=>{var n=r(93490);t.exports=function(t){return n(t)&&t!=+t}},77834:t=>{var e=Object.prototype;t.exports=function(t){var r=t&&t.constructor;return t===("function"==typeof r&&r.prototype||e)}},78122:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},78418:(t,e,r)=>{var n=r(67200),i=r(15871);t.exports=function(t,e,r,o){var a=r.length,c=a,u=!o;if(null==t)return!c;for(t=Object(t);a--;){var s=r[a];if(u&&s[2]?s[1]!==t[s[0]]:!(s[0]in t))return!1}for(;++a<c;){var l=(s=r[a])[0],f=t[l],p=s[1];if(u&&s[2]){if(void 0===f&&!(l in t))return!1}else{var d=new n;if(o)var h=o(f,p,l,t,e,d);if(!(void 0===h?i(p,f,3,o,d):h))return!1}}return!0}},79474:(t,e,r)=>{t.exports=r(85718).Symbol},79551:t=>{"use strict";t.exports=require("url")},80195:(t,e,r)=>{var n=r(79474),i=r(21367),o=r(40542),a=r(49227),c=1/0,u=n?n.prototype:void 0,s=u?u.toString:void 0;t.exports=function t(e){if("string"==typeof e)return e;if(o(e))return i(e,t)+"";if(a(e))return s?s.call(e):"";var r=e+"";return"0"==r&&1/e==-c?"-0":r}},80329:(t,e,r)=>{t=r.nmd(t);var n=r(85718),i=r(1944),o=e&&!e.nodeType&&e,a=o&&t&&!t.nodeType&&t,c=a&&a.exports===o?n.Buffer:void 0,u=c?c.isBuffer:void 0;t.exports=u||i},80458:(t,e,r)=>{var n=r(29395),i=r(69619),o=r(27467),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,t.exports=function(t){return o(t)&&i(t.length)&&!!a[n(t)]}},80704:(t,e,r)=>{var n=r(96678);t.exports=function(t,e){return!!(null==t?0:t.length)&&n(t,e,0)>-1}},81488:t=>{t.exports=function(t,e){return null!=t&&e in Object(t)}},81630:t=>{"use strict";t.exports=require("http")},81957:(t,e,r)=>{var n=r(49227);t.exports=function(t,e){if(t!==e){var r=void 0!==t,i=null===t,o=t==t,a=n(t),c=void 0!==e,u=null===e,s=e==e,l=n(e);if(!u&&!l&&!a&&t>e||a&&c&&s&&!u&&!l||i&&c&&s||!r&&s||!o)return 1;if(!i&&!a&&!l&&t<e||l&&r&&o&&!i&&!a||u&&r&&o||!c&&o||!s)return -1}return 0}},82038:(t,e,r)=>{var n=r(34821),i=r(35163),o=r(40542),a=r(80329),c=r(38428),u=r(10090),s=Object.prototype.hasOwnProperty;t.exports=function(t,e){var r=o(t),l=!r&&i(t),f=!r&&!l&&a(t),p=!r&&!l&&!f&&u(t),d=r||l||f||p,h=d?n(t.length,String):[],y=h.length;for(var v in t)(e||s.call(t,v))&&!(d&&("length"==v||f&&("offset"==v||"parent"==v)||p&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||c(v,y)))&&h.push(v);return h}},83997:t=>{"use strict";t.exports=require("tty")},84031:(t,e,r)=>{"use strict";var n=r(34452);function i(){}function o(){}o.resetWarningCache=i,t.exports=function(){function t(t,e,r,i,o,a){if(a!==n){var c=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}function e(){return t}t.isRequired=t;var r={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:o,resetWarningCache:i};return r.PropTypes=r,r}},84261:t=>{t.exports=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=!!e,e}},84482:(t,e,r)=>{var n=r(28977);t.exports=function(t){var e=n(t),r=e%1;return e==e?r?e-r:e:0}},84713:t=>{var e=Object.prototype.toString;t.exports=function(t){return e.call(t)}},85244:t=>{t.exports=function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}},85406:(t,e,r)=>{t.exports=r(85718)["__core-js_shared__"]},85450:(t,e,r)=>{var n=r(79474),i=r(35163),o=r(40542),a=n?n.isConcatSpreadable:void 0;t.exports=function(t){return o(t)||i(t)||!!(a&&t&&t[a])}},85718:(t,e,r)=>{var n=r(10663),i="object"==typeof self&&self&&self.Object===Object&&self;t.exports=n||i||Function("return this")()},85745:(t,e,r)=>{var n=r(86451);t.exports=function(t){var e=n(t,function(t){return 500===r.size&&r.clear(),t}),r=e.cache;return e}},85938:(t,e,r)=>{var n=r(42205),i=r(17518),o=r(46229),a=r(7383);t.exports=o(function(t,e){if(null==t)return[];var r=e.length;return r>1&&a(t,e[0],e[1])?e=[]:r>2&&a(e[0],e[1],e[2])&&(e=[e[0]]),i(t,n(e,1),[])})},86451:(t,e,r)=>{var n=r(95746);function i(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw TypeError("Expected a function");var r=function(){var n=arguments,i=e?e.apply(this,n):n[0],o=r.cache;if(o.has(i))return o.get(i);var a=t.apply(this,n);return r.cache=o.set(i,a)||o,a};return r.cache=new(i.Cache||n),r}i.Cache=n,t.exports=i},87270:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0}},87321:(t,e,r)=>{var n=r(98798),i=r(7383),o=r(28977);t.exports=function(t){return function(e,r,a){return a&&"number"!=typeof a&&i(e,r,a)&&(r=a=void 0),e=o(e),void 0===r?(r=e,e=0):r=o(r),a=void 0===a?e<r?1:-1:o(a),n(e,r,a,t)}}},87506:(t,e,r)=>{var n=r(66837),i=r(84261),o=r(89492),a=r(90200),c=r(39672);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=i,u.prototype.get=o,u.prototype.has=a,u.prototype.set=c,t.exports=u},87955:(t,e,r)=>{t.exports=r(84031)()},89167:(t,e,r)=>{t.exports=r(41547)(r(85718),"DataView")},89185:t=>{t.exports=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this}},89492:(t,e,r)=>{var n=r(58141),i=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;if(n){var r=e[t];return"__lodash_hash_undefined__"===r?void 0:r}return i.call(e,t)?e[t]:void 0}},89605:(t,e,r)=>{t.exports=r(65662)(Object.keys,Object)},89624:t=>{t.exports=function(t){return function(e){return t(e)}}},90200:(t,e,r)=>{var n=r(58141),i=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;return n?void 0!==e[t]:i.call(e,t)}},90453:(t,e,r)=>{var n=r(2984),i=r(99180),o=r(48169);t.exports=function(t){return t&&t.length?n(t,o,i):void 0}},90851:t=>{t.exports=function(t,e){return null==t?void 0:t[e]}},91290:t=>{t.exports=function(t,e,r,n){for(var i=t.length,o=r+(n?1:-1);n?o--:++o<i;)if(e(t[o],o,t))return o;return -1}},91928:(t,e,r)=>{var n=r(41547);t.exports=function(){try{var t=n(Object,"defineProperty");return t({},"",{}),t}catch(t){}}()},92311:(t,e,r)=>{Promise.resolve().then(r.bind(r,53425))},92662:(t,e,r)=>{var n=r(46328),i=r(80704),o=r(71960),a=r(58276),c=r(95308),u=r(2408);t.exports=function(t,e,r){var s=-1,l=i,f=t.length,p=!0,d=[],h=d;if(r)p=!1,l=o;else if(f>=200){var y=e?null:c(t);if(y)return u(y);p=!1,l=a,h=new n}else h=e?[]:d;e:for(;++s<f;){var v=t[s],m=e?e(v):v;if(v=r||0!==v?v:0,p&&m==m){for(var b=h.length;b--;)if(h[b]===m)continue e;e&&h.push(m),d.push(v)}else l(h,m,r)||(h!==d&&h.push(m),d.push(v))}return d}},93311:(t,e,r)=>{var n=r(34883),i=r(7651);t.exports=function(t){for(var e=i(t),r=e.length;r--;){var o=e[r],a=t[o];e[r]=[o,a,n(a)]}return e}},93490:(t,e,r)=>{var n=r(29395),i=r(27467);t.exports=function(t){return"number"==typeof t||i(t)&&"[object Number]"==n(t)}},93780:(t,e,r)=>{"use strict";t.exports=r(66992)},94388:(t,e,r)=>{var n=r(57797);t.exports=function(t){var e=this.__data__,r=n(e,t);return r<0?void 0:e[r][1]}},94735:t=>{"use strict";t.exports=require("events")},95308:(t,e,r)=>{var n=r(34772),i=r(36959),o=r(2408);t.exports=n&&1/o(new n([,-0]))[1]==1/0?function(t){return new n(t)}:i},95746:(t,e,r)=>{var n=r(15909),i=r(29205),o=r(29508),a=r(61320),c=r(19976);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=i,u.prototype.get=o,u.prototype.has=a,u.prototype.set=c,t.exports=u},96678:(t,e,r)=>{var n=r(91290),i=r(39774),o=r(74610);t.exports=function(t,e,r){return e==e?o(t,e,r):n(t,i,r)}},96963:(t,e,r)=>{"use strict";r.d(e,{B:()=>u});var n,i=r(43210),o=r(66156),a=(n||(n=r.t(i,2)))[" useId ".trim().toString()]||(()=>void 0),c=0;function u(t){let[e,r]=i.useState(a());return(0,o.N)(()=>{t||r(t=>t??String(c++))},[t]),t||(e?`radix-${e}`:"")}},97887:(t,e,r)=>{Promise.resolve().then(r.bind(r,75900))},98451:(t,e,r)=>{var n=r(29395),i=r(27467);t.exports=function(t){return!0===t||!1===t||i(t)&&"[object Boolean]"==n(t)}},98798:t=>{var e=Math.ceil,r=Math.max;t.exports=function(t,n,i,o){for(var a=-1,c=r(e((n-t)/(i||1)),0),u=Array(c);c--;)u[o?c:++a]=t,t+=i;return u}},99114:(t,e,r)=>{var n=r(12344),i=r(7651);t.exports=function(t,e){return t&&n(t,e,i)}},99180:t=>{t.exports=function(t,e){return t>e}},99525:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1}}};var e=require("../../webpack-runtime.js");e.C(t);var r=t=>e(e.s=t),n=e.X(0,[447,991,658,400,639,653],()=>r(45180));module.exports=n})();