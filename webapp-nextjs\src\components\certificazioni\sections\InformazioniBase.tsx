'use client'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { CertificazioneCavoCreate, StrumentoCertificato } from '@/types/certificazioni'

interface InformazioniBaseProps {
  formData: Partial<CertificazioneCavoCreate>
  cavi: any[]
  responsabili: any[]
  strumenti: StrumentoCertificato[]
  validationErrors: Record<string, string>
  isCavoLocked: boolean
  onInputChange: (field: string, value: any) => void
}

export function InformazioniBase({
  formData,
  cavi,
  responsabili,
  strumenti,
  validationErrors,
  isCavoLocked,
  onInputChange
}: InformazioniBaseProps) {
  return (
    <Card>
      <CardHeader className="pb-1 px-3 pt-2">
        <CardTitle className="text-sm font-semibold">Informazioni Base</CardTitle>
      </CardHeader>
      <CardContent className="space-y-2 pt-0 px-3 pb-2">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {/* Cavo */}
          <div className="space-y-1">
            <Label htmlFor="id_cavo" className="text-sm">Cavo *</Label>
            <Select
              value={formData.id_cavo}
              onValueChange={(value) => onInputChange('id_cavo', value)}
              disabled={isCavoLocked}
            >
              <SelectTrigger>
                <SelectValue placeholder="Seleziona cavo..." />
              </SelectTrigger>
              <SelectContent>
                {cavi.map((cavo) => (
                  <SelectItem key={cavo.id_cavo} value={cavo.id_cavo}>
                    {cavo.id_cavo} - {cavo.descrizione}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {validationErrors.id_cavo && (
              <p className="text-sm text-red-600">{validationErrors.id_cavo}</p>
            )}
          </div>

          {/* Operatore */}
          <div className="space-y-1">
            <Label htmlFor="id_operatore" className="text-sm">Operatore</Label>
            <Select
              value={formData.id_operatore?.toString() || ''}
              onValueChange={(value) => onInputChange('id_operatore', parseInt(value))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Seleziona operatore..." />
              </SelectTrigger>
              <SelectContent>
                {responsabili.map((resp) => (
                  <SelectItem key={resp.id_responsabile} value={resp.id_responsabile.toString()}>
                    {resp.nome} {resp.cognome}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Strumento */}
          <div className="space-y-1">
            <Label htmlFor="id_strumento" className="text-sm">Strumento</Label>
            <Select
              value={formData.id_strumento?.toString() || ''}
              onValueChange={(value) => {
                const strumento = strumenti.find(s => s.id_strumento === parseInt(value))
                onInputChange('id_strumento', parseInt(value))
                if (strumento) {
                  onInputChange('strumento_utilizzato', `${strumento.marca} ${strumento.modello}`)
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Seleziona strumento..." />
              </SelectTrigger>
              <SelectContent>
                {strumenti.map((strumento) => (
                  <SelectItem key={strumento.id_strumento} value={strumento.id_strumento.toString()}>
                    {strumento.marca} {strumento.modello} - {strumento.numero_serie}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Tipo Certificato */}
          <div className="space-y-1">
            <Label htmlFor="tipo_certificato" className="text-sm">Tipo Certificato</Label>
            <Select
              value={formData.tipo_certificato || 'SINGOLO'}
              onValueChange={(value) => onInputChange('tipo_certificato', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="SINGOLO">Singolo</SelectItem>
                <SelectItem value="GRUPPO">Gruppo</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
