(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6222],{381:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1368:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>A});var s=t(95155),r=t(12115),n=t(35695),i=t(66695),o=t(30285),l=t(62523),c=t(85057),d=t(85127),m=t(54165),u=t(40283),h=t(25731),x=t(51154),g=t(47924),p=t(84616),f=t(85339),v=t(23227),b=t(24357),j=t(78749),w=t(92657),N=t(72713),y=t(13717);let _=(0,t(19946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var z=t(32919),C=t(381),k=t(40646);function A(){let{user:e,isAuthenticated:a,isLoading:t}=(0,u.A)(),A=(0,n.useRouter)(),[P,M]=(0,r.useState)([]),[S,E]=(0,r.useState)(!0),[F,I]=(0,r.useState)(""),[J,$]=(0,r.useState)({}),[L,V]=(0,r.useState)(!1),[O,q]=(0,r.useState)(""),[D,G]=(0,r.useState)(!1),[B,R]=(0,r.useState)(!1),[T,Z]=(0,r.useState)(!1),[H,X]=(0,r.useState)(!1),[U,W]=(0,r.useState)(null),[Q,Y]=(0,r.useState)({commessa:"",descrizione:"",nome_cliente:"",indirizzo_cantiere:"",citta_cantiere:"",nazione_cantiere:"",password_cantiere:"",codice_univoco:""}),[K,ee]=(0,r.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[ea,et]=(0,r.useState)("change"),[es,er]=(0,r.useState)(""),[en,ei]=(0,r.useState)(!1),[eo,el]=(0,r.useState)(!1),[ec,ed]=(0,r.useState)({}),[em,eu]=(0,r.useState)({});(0,r.useEffect)(()=>{t||a||A.push("/login")},[a,t,A]),(0,r.useEffect)(()=>{a&&eh()},[a]);let eh=async()=>{try{E(!0);let e=await h._I.getCantieri();M(e),await ex(e)}catch(e){I("Errore nel caricamento dei cantieri")}finally{E(!1)}},ex=async e=>{try{V(!0);let a=e.map(async e=>{try{let a=await h._I.getCantiereStatistics(e.id_cantiere);return{id:e.id_cantiere,stats:a}}catch(a){return console.error("Errore nel caricamento statistiche cantiere ".concat(e.id_cantiere,":"),a),{id:e.id_cantiere,stats:{percentuale_avanzamento:0}}}}),t=(await Promise.all(a)).reduce((e,a)=>{let{id:t,stats:s}=a;return e[t]=s,e},{});$(t)}catch(e){console.error("Errore nel caricamento delle statistiche:",e)}finally{V(!1)}},eg=async()=>{try{await h._I.createCantiere(Q),G(!1),Y({commessa:"",descrizione:"",nome_cliente:"",indirizzo_cantiere:"",citta_cantiere:"",nazione_cantiere:"",password_cantiere:"",codice_univoco:""}),eh()}catch(e){I("Errore nella creazione del cantiere")}},ep=async()=>{if(U)try{await h._I.updateCantiere(U.id_cantiere,Q),R(!1),W(null),eh()}catch(e){I("Errore nella modifica del cantiere")}},ef=e=>{localStorage.setItem("selectedCantiereId",e.id_cantiere.toString()),localStorage.setItem("selectedCantiereName",e.commessa),A.push("/cantieri/".concat(e.id_cantiere))},ev=e=>{W(e),Y({commessa:e.commessa||"",descrizione:e.descrizione||"",nome_cliente:e.nome_cliente||"",indirizzo_cantiere:e.indirizzo_cantiere||"",citta_cantiere:e.citta_cantiere||"",nazione_cantiere:e.nazione_cantiere||"",password_cantiere:e.password_cantiere||"",codice_univoco:e.codice_univoco||""}),R(!0)},eb=async()=>{if(U){if(K.newPassword!==K.confirmPassword)return void I("Le password non coincidono");if(!K.currentPassword)return void I("Inserisci la password attuale per confermare il cambio");if(!K.newPassword||K.newPassword.length<6)return void I("La nuova password deve essere di almeno 6 caratteri");try{E(!0),I("");let e=await fetch("".concat("http://localhost:8001","/api/cantieri/").concat(U.id_cantiere,"/change-password"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))},body:JSON.stringify({password_attuale:K.currentPassword,password_nuova:K.newPassword,conferma_password:K.confirmPassword})});if(!e.ok){let a=await e.json();throw Error(a.detail||"Errore nel cambio password")}let a=await e.json();if(a.success)ee({currentPassword:"",newPassword:"",confirmPassword:""}),Z(!1),I(""),alert(a.message||"Password cambiata con successo");else throw Error(a.message||"Errore nel cambio password")}catch(e){I(e instanceof Error?e.message:"Errore nel cambio password")}finally{E(!1)}}},ej=async e=>{try{await navigator.clipboard.writeText(e)}catch(e){}},ew=async e=>{let a=e.id_cantiere;if(ec[a])ed(e=>({...e,[a]:!1})),eu(e=>({...e,[a]:""}));else if(em[a])ed(e=>({...e,[a]:!0}));else try{el(!0);let e=localStorage.getItem("token")||localStorage.getItem("access_token"),t=await fetch("".concat("http://localhost:8001","/api/cantieri/").concat(a,"/view-password"),{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)}});if(!t.ok){let e=await t.json();throw Error(e.detail||"Errore nel recupero password")}let s=await t.json();eu(e=>({...e,[a]:s.password_cantiere})),ed(e=>({...e,[a]:!0}))}catch(e){I(e instanceof Error?e.message:"Errore nel recupero password")}finally{el(!1)}},eN=P.filter(e=>{var a,t;return e.commessa.toLowerCase().includes(O.toLowerCase())||(null==(a=e.descrizione)?void 0:a.toLowerCase().includes(O.toLowerCase()))||(null==(t=e.nome_cliente)?void 0:t.toLowerCase().includes(O.toLowerCase()))});return t?(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,s.jsx)(x.A,{className:"h-8 w-8 animate-spin"})}):(0,s.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsx)("div",{className:"flex items-center gap-4",children:(0,s.jsxs)("div",{className:"relative w-80",children:[(0,s.jsx)(g.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(l.p,{placeholder:"Cerca per commessa, descrizione o cliente...",value:O,onChange:e=>q(e.target.value),className:"pl-8 w-full"})]})}),(0,s.jsxs)(m.lG,{open:D,onOpenChange:G,children:[(0,s.jsx)(m.zM,{asChild:!0,children:(0,s.jsxs)(o.$,{className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:[(0,s.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Nuovo Cantiere"]})}),(0,s.jsxs)(m.Cf,{className:"sm:max-w-[425px]",children:[(0,s.jsxs)(m.c7,{children:[(0,s.jsx)(m.L3,{children:"Crea Nuovo Cantiere"}),(0,s.jsx)(m.rr,{children:"Inserisci i dettagli del nuovo cantiere"})]}),(0,s.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(c.J,{htmlFor:"commessa",className:"text-right",children:"Commessa"}),(0,s.jsx)(l.p,{id:"commessa",value:Q.commessa,onChange:e=>Y({...Q,commessa:e.target.value}),className:"col-span-3"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(c.J,{htmlFor:"descrizione",className:"text-right",children:"Descrizione"}),(0,s.jsx)(l.p,{id:"descrizione",value:Q.descrizione,onChange:e=>Y({...Q,descrizione:e.target.value}),className:"col-span-3"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(c.J,{htmlFor:"nome_cliente",className:"text-right",children:"Cliente"}),(0,s.jsx)(l.p,{id:"nome_cliente",value:Q.nome_cliente,onChange:e=>Y({...Q,nome_cliente:e.target.value}),className:"col-span-3"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(c.J,{htmlFor:"password_cantiere",className:"text-right",children:"Password"}),(0,s.jsx)(l.p,{id:"password_cantiere",type:"password",value:Q.password_cantiere,onChange:e=>Y({...Q,password_cantiere:e.target.value}),className:"col-span-3"})]})]}),(0,s.jsx)(m.Es,{children:(0,s.jsx)(o.$,{onClick:eg,className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:"Crea Cantiere"})})]})]})]}),F&&(0,s.jsx)("div",{className:"mb-4 p-4 border border-red-200 rounded-lg bg-red-50",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(f.A,{className:"h-4 w-4 text-red-600 mr-2"}),(0,s.jsx)("span",{className:"text-red-800",children:F})]})}),S?(0,s.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,s.jsx)(x.A,{className:"h-8 w-8 animate-spin"})}):0===eN.length?(0,s.jsx)(i.Zp,{children:(0,s.jsxs)(i.Wu,{className:"flex flex-col items-center justify-center py-8",children:[(0,s.jsx)(v.A,{className:"h-12 w-12 text-muted-foreground mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Nessun cantiere trovato"}),(0,s.jsx)("p",{className:"text-muted-foreground text-center mb-4",children:O?"Nessun cantiere corrisponde ai criteri di ricerca":"Crea il tuo primo cantiere per iniziare"}),!O&&(0,s.jsxs)(o.$,{onClick:()=>G(!0),className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:[(0,s.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Crea Primo Cantiere"]})]})}):(0,s.jsx)(i.Zp,{children:(0,s.jsxs)(d.XI,{children:[(0,s.jsx)(d.A0,{children:(0,s.jsxs)(d.Hj,{className:"border-b border-gray-200",children:[(0,s.jsx)(d.nd,{className:"font-semibold text-gray-700",children:"Commessa"}),(0,s.jsx)(d.nd,{className:"font-semibold text-gray-700",children:"Descrizione"}),(0,s.jsx)(d.nd,{className:"font-semibold text-gray-700",children:"Cliente"}),(0,s.jsx)(d.nd,{className:"font-semibold text-gray-700",children:"Data Creazione"}),(0,s.jsx)(d.nd,{className:"font-semibold text-gray-700",children:"Codice Accesso"}),(0,s.jsx)(d.nd,{className:"font-semibold text-gray-700",children:"Password Cantiere"}),(0,s.jsx)(d.nd,{className:"font-semibold text-gray-700 w-32",children:"Avanzamento"}),(0,s.jsx)(d.nd,{className:"font-semibold text-gray-700 text-center",children:"Progresso %"}),(0,s.jsx)(d.nd,{className:"text-center font-semibold text-gray-700 w-48",children:"Azioni"})]})}),(0,s.jsx)(d.BF,{children:eN.map(e=>{var a,t,r,n,i,l,c,m,u,h;return(0,s.jsxs)(d.Hj,{className:"hover:bg-gray-50/50 transition-colors",children:[(0,s.jsx)(d.nA,{className:"font-semibold text-gray-900 py-4",children:e.commessa}),(0,s.jsx)(d.nA,{className:"text-gray-700 py-4",children:e.descrizione}),(0,s.jsx)(d.nA,{className:"text-gray-700 py-4",children:e.nome_cliente}),(0,s.jsx)(d.nA,{className:"text-gray-600 py-4",children:new Date(e.data_creazione).toLocaleDateString()}),(0,s.jsx)(d.nA,{className:"py-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("code",{className:"text-sm bg-blue-50 text-blue-700 px-3 py-1.5 rounded-md font-mono border border-blue-200",children:e.codice_univoco}),(0,s.jsx)(o.$,{size:"sm",variant:"ghost",className:"h-7 w-7 p-0 text-gray-400 hover:bg-gray-50 hover:text-gray-600 transition-colors",title:"Copia codice",onClick:()=>ej(e.codice_univoco),children:(0,s.jsx)(b.A,{className:"h-3 w-3"})})]})}),(0,s.jsx)(d.nA,{className:"py-4",children:(0,s.jsx)("div",{className:"flex items-center gap-3",children:(0,s.jsx)("div",{className:"flex items-center gap-2",children:ec[e.id_cantiere]&&em[e.id_cantiere]?(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("code",{className:"text-sm bg-green-50 text-green-700 px-2 py-1 rounded border border-green-200 font-mono",children:em[e.id_cantiere]}),(0,s.jsx)(o.$,{size:"sm",variant:"ghost",className:"h-7 w-7 p-0 text-green-600 hover:bg-green-50 hover:text-green-700 transition-colors",title:"Nascondi password",onClick:()=>ew(e),children:(0,s.jsx)(j.A,{className:"h-4 w-4"})})]}):(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,s.jsx)("span",{className:"text-sm text-gray-600 font-medium",children:"Configurata"})]}),(0,s.jsx)(o.$,{size:"sm",variant:"ghost",className:"h-7 w-7 p-0 text-blue-600 hover:bg-blue-50 hover:text-blue-700 transition-colors",title:"Mostra password",onClick:()=>ew(e),disabled:eo,children:eo?(0,s.jsx)(x.A,{className:"h-4 w-4 animate-spin"}):(0,s.jsx)(w.A,{className:"h-4 w-4"})})]})})})}),(0,s.jsx)(d.nA,{className:"py-4",children:(0,s.jsx)("div",{className:"flex items-center gap-2",children:L?(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(x.A,{className:"h-4 w-4 animate-spin text-gray-400"}),(0,s.jsx)("span",{className:"text-sm text-gray-500",children:"Caricamento..."})]}):(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{className:"flex-1 min-w-[120px]",children:(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3 shadow-inner",children:(0,s.jsx)("div",{className:"h-3 rounded-full transition-all duration-500 ease-out shadow-sm ".concat(((null==(a=J[e.id_cantiere])?void 0:a.percentuale_avanzamento)||0)>=90?"bg-gradient-to-r from-green-500 to-green-600":((null==(t=J[e.id_cantiere])?void 0:t.percentuale_avanzamento)||0)>=75?"bg-gradient-to-r from-blue-500 to-blue-600":((null==(r=J[e.id_cantiere])?void 0:r.percentuale_avanzamento)||0)>=50?"bg-gradient-to-r from-yellow-500 to-yellow-600":((null==(n=J[e.id_cantiere])?void 0:n.percentuale_avanzamento)||0)>=25?"bg-gradient-to-r from-orange-500 to-orange-600":"bg-gradient-to-r from-red-500 to-red-600"),style:{width:"".concat(Math.min((null==(i=J[e.id_cantiere])?void 0:i.percentuale_avanzamento)||0,100),"%")}})})})})})}),(0,s.jsx)(d.nA,{className:"py-4 text-center",children:L?(0,s.jsx)(x.A,{className:"h-4 w-4 animate-spin text-gray-400 mx-auto"}):(0,s.jsxs)("div",{className:"flex items-center justify-center gap-1",children:[(0,s.jsx)(N.A,{className:"h-4 w-4 text-gray-500"}),(0,s.jsxs)("span",{className:"text-sm font-semibold ".concat(((null==(l=J[e.id_cantiere])?void 0:l.percentuale_avanzamento)||0)>=90?"text-green-700":((null==(c=J[e.id_cantiere])?void 0:c.percentuale_avanzamento)||0)>=75?"text-blue-700":((null==(m=J[e.id_cantiere])?void 0:m.percentuale_avanzamento)||0)>=50?"text-yellow-700":((null==(u=J[e.id_cantiere])?void 0:u.percentuale_avanzamento)||0)>=25?"text-orange-700":"text-red-700"),children:[((null==(h=J[e.id_cantiere])?void 0:h.percentuale_avanzamento)||0).toFixed(1),"%"]})]})}),(0,s.jsx)(d.nA,{className:"text-center py-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsxs)(o.$,{size:"sm",variant:"outline",onClick:()=>ev(e),className:"h-9 px-3 text-gray-600 border-gray-200 hover:bg-gray-50 hover:text-gray-700 hover:border-gray-300 transition-all duration-200 ease-in-out",title:"Modifica dati cantiere",children:[(0,s.jsx)(y.A,{className:"h-4 w-4 mr-1.5"}),"Modifica"]}),(0,s.jsxs)(o.$,{size:"sm",onClick:()=>ef(e),className:"h-9 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium transition-colors duration-200 ease-in-out",title:"Accedi al cantiere",children:[(0,s.jsx)(_,{className:"h-4 w-4 mr-1.5"}),"Accedi"]})]})})]},e.id_cantiere)})})]})}),(0,s.jsx)(m.lG,{open:B,onOpenChange:R,children:(0,s.jsxs)(m.Cf,{className:"sm:max-w-[425px]",children:[(0,s.jsxs)(m.c7,{children:[(0,s.jsx)(m.L3,{children:"Modifica Cantiere"}),(0,s.jsx)(m.rr,{children:"Modifica i dettagli del cantiere selezionato"})]}),(0,s.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(c.J,{htmlFor:"edit-commessa",className:"text-right",children:"Commessa"}),(0,s.jsx)(l.p,{id:"edit-commessa",value:Q.commessa,onChange:e=>Y({...Q,commessa:e.target.value}),className:"col-span-3"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(c.J,{htmlFor:"edit-descrizione",className:"text-right",children:"Descrizione"}),(0,s.jsx)(l.p,{id:"edit-descrizione",value:Q.descrizione,onChange:e=>Y({...Q,descrizione:e.target.value}),className:"col-span-3"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(c.J,{htmlFor:"edit-nome_cliente",className:"text-right",children:"Cliente"}),(0,s.jsx)(l.p,{id:"edit-nome_cliente",value:Q.nome_cliente,onChange:e=>Y({...Q,nome_cliente:e.target.value}),className:"col-span-3"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(c.J,{htmlFor:"edit-indirizzo_cantiere",className:"text-right",children:"Indirizzo"}),(0,s.jsx)(l.p,{id:"edit-indirizzo_cantiere",value:Q.indirizzo_cantiere,onChange:e=>Y({...Q,indirizzo_cantiere:e.target.value}),className:"col-span-3"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(c.J,{htmlFor:"edit-citta_cantiere",className:"text-right",children:"Citt\xe0"}),(0,s.jsx)(l.p,{id:"edit-citta_cantiere",value:Q.citta_cantiere,onChange:e=>Y({...Q,citta_cantiere:e.target.value}),className:"col-span-3"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(c.J,{htmlFor:"edit-nazione_cantiere",className:"text-right",children:"Nazione"}),(0,s.jsx)(l.p,{id:"edit-nazione_cantiere",value:Q.nazione_cantiere,onChange:e=>Y({...Q,nazione_cantiere:e.target.value}),className:"col-span-3"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(c.J,{className:"text-right",children:"Password"}),(0,s.jsx)("div",{className:"col-span-3",children:(0,s.jsxs)(o.$,{type:"button",variant:"outline",onClick:()=>{Z(!0)},className:"w-full h-10 justify-start text-left bg-gray-50 hover:bg-gray-100 border-gray-200 hover:border-gray-300 transition-colors",children:[(0,s.jsx)(z.A,{className:"h-4 w-4 mr-2 text-gray-500"}),(0,s.jsx)("span",{className:"text-gray-700",children:"Modifica Password"})]})})]})]}),(0,s.jsxs)(m.Es,{children:[(0,s.jsx)(o.$,{onClick:()=>R(!1),className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:"Annulla"}),(0,s.jsx)(o.$,{onClick:ep,className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:"Salva Modifiche"})]})]})}),(0,s.jsx)(m.lG,{open:T,onOpenChange:e=>{Z(e),e||(ee({currentPassword:"",newPassword:"",confirmPassword:""}),I(""))},children:(0,s.jsxs)(m.Cf,{className:"sm:max-w-[600px]",children:[(0,s.jsxs)(m.c7,{children:[(0,s.jsxs)(m.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)(z.A,{className:"h-5 w-5"}),"Gestione Password - ",null==U?void 0:U.commessa]}),(0,s.jsx)(m.rr,{children:"Modifica la password di accesso al cantiere"})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("h3",{className:"text-lg font-medium flex items-center gap-2",children:[(0,s.jsx)(C.A,{className:"h-5 w-5"}),"Cambia Password"]}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Inserisci la password attuale e la nuova password"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"current-password-change",children:"Password Attuale"}),(0,s.jsx)(l.p,{id:"current-password-change",type:"password",placeholder:"Password attuale per conferma",value:K.currentPassword,onChange:e=>ee({...K,currentPassword:e.target.value})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"new-password",children:"Nuova Password"}),(0,s.jsx)(l.p,{id:"new-password",type:"password",placeholder:"Inserisci la nuova password",value:K.newPassword,onChange:e=>ee({...K,newPassword:e.target.value})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"confirm-password",children:"Conferma Nuova Password"}),(0,s.jsx)(l.p,{id:"confirm-password",type:"password",placeholder:"Conferma la nuova password",value:K.confirmPassword,onChange:e=>ee({...K,confirmPassword:e.target.value})})]}),(0,s.jsxs)(o.$,{onClick:eb,disabled:S||!K.currentPassword||!K.newPassword||!K.confirmPassword,className:"w-full relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:[S?(0,s.jsx)(x.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,s.jsx)(C.A,{className:"mr-2 h-4 w-4"}),"Cambia Password"]})]})]}),F&&(0,s.jsxs)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(f.A,{className:"h-5 w-5 text-red-600"}),(0,s.jsx)("span",{className:"font-medium text-red-800",children:"Errore"})]}),(0,s.jsx)("p",{className:"text-sm text-red-700 mt-1",children:F})]})]}),(0,s.jsx)(m.Es,{children:(0,s.jsx)(o.$,{variant:"outline",onClick:()=>Z(!1),children:"Chiudi"})})]})}),(0,s.jsx)(m.lG,{open:H,onOpenChange:e=>{X(e),e||(er(""),W(null),I(""))},children:(0,s.jsxs)(m.Cf,{className:"sm:max-w-[500px]",children:[(0,s.jsxs)(m.c7,{children:[(0,s.jsxs)(m.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)(w.A,{className:"h-5 w-5 text-green-600"}),"Password Cantiere - ",null==U?void 0:U.commessa]}),(0,s.jsxs)(m.rr,{children:["Password per l'accesso al cantiere con codice: ",(0,s.jsx)("code",{className:"bg-muted px-2 py-1 rounded",children:null==U?void 0:U.codice_univoco})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[es&&(0,s.jsxs)("div",{className:"p-4 bg-green-50 border border-green-200 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,s.jsx)(k.A,{className:"h-5 w-5 text-green-600"}),(0,s.jsx)("span",{className:"font-medium text-green-800",children:"Password del Cantiere"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("code",{className:"flex-1 text-lg font-mono bg-white p-3 rounded border border-green-300 text-center",children:es}),(0,s.jsx)(o.$,{size:"sm",variant:"outline",onClick:()=>ej(es),className:"text-green-600 hover:bg-green-50 border-green-300",title:"Copia password",children:(0,s.jsx)(b.A,{className:"h-4 w-4"})})]}),(0,s.jsxs)("p",{className:"text-sm text-green-700 mt-2",children:["Utilizza questa password insieme al codice univoco ",(0,s.jsx)("strong",{children:null==U?void 0:U.codice_univoco})," per accedere al cantiere."]})]}),F&&(0,s.jsxs)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(f.A,{className:"h-5 w-5 text-red-600"}),(0,s.jsx)("span",{className:"font-medium text-red-800",children:"Errore"})]}),(0,s.jsx)("p",{className:"text-sm text-red-700 mt-1",children:F})]})]}),(0,s.jsx)(m.Es,{children:(0,s.jsx)(o.$,{variant:"outline",onClick:()=>X(!1),children:"Chiudi"})})]})})]})}},13717:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},23227:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},24357:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},30285:(e,a,t)=>{"use strict";t.d(a,{$:()=>l});var s=t(95155);t(12115);var r=t(99708),n=t(74466),i=t(59434);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:a,variant:t,size:n,asChild:l=!1,...c}=e,d=l?r.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,i.cn)(o({variant:t,size:n,className:a})),...c})}},32919:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},35695:(e,a,t)=>{"use strict";var s=t(18999);t.o(s,"useParams")&&t.d(a,{useParams:function(){return s.useParams}}),t.o(s,"usePathname")&&t.d(a,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(a,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(a,{useSearchParams:function(){return s.useSearchParams}})},40321:(e,a,t)=>{Promise.resolve().then(t.bind(t,1368))},54165:(e,a,t)=>{"use strict";t.d(a,{Cf:()=>m,Es:()=>h,L3:()=>x,c7:()=>u,lG:()=>o,rr:()=>g,zM:()=>l});var s=t(95155);t(12115);var r=t(15452),n=t(54416),i=t(59434);function o(e){let{...a}=e;return(0,s.jsx)(r.bL,{"data-slot":"dialog",...a})}function l(e){let{...a}=e;return(0,s.jsx)(r.l9,{"data-slot":"dialog-trigger",...a})}function c(e){let{...a}=e;return(0,s.jsx)(r.ZL,{"data-slot":"dialog-portal",...a})}function d(e){let{className:a,...t}=e;return(0,s.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...t})}function m(e){let{className:a,children:t,showCloseButton:o=!0,...l}=e;return(0,s.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,s.jsx)(d,{}),(0,s.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...l,children:[t,o&&(0,s.jsxs)(r.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(n.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",a),...t})}function h(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...t})}function x(e){let{className:a,...t}=e;return(0,s.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",a),...t})}function g(e){let{className:a,...t}=e;return(0,s.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",a),...t})}},59434:(e,a,t)=>{"use strict";t.d(a,{cn:()=>n});var s=t(52596),r=t(39688);function n(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,r.QP)((0,s.$)(a))}},62523:(e,a,t)=>{"use strict";t.d(a,{p:()=>i});var s=t(95155),r=t(12115),n=t(59434);let i=r.forwardRef((e,a)=>{let{className:t,type:r,...i}=e;return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),ref:a,...i})});i.displayName="Input"},66695:(e,a,t)=>{"use strict";t.d(a,{BT:()=>l,Wu:()=>c,ZB:()=>o,Zp:()=>n,aR:()=>i});var s=t(95155);t(12115);var r=t(59434);function n(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...t})}function i(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...t})}function o(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",a),...t})}function l(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",a),...t})}function c(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",a),...t})}},72713:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},78749:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},84616:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},85057:(e,a,t)=>{"use strict";t.d(a,{J:()=>i});var s=t(95155);t(12115);var r=t(40968),n=t(59434);function i(e){let{className:a,...t}=e;return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...t})}},85127:(e,a,t)=>{"use strict";t.d(a,{A0:()=>i,BF:()=>o,Hj:()=>l,XI:()=>n,nA:()=>d,nd:()=>c});var s=t(95155);t(12115);var r=t(59434);function n(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm border-collapse",a),...t})})}function i(e){let{className:a,...t}=e;return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",a),...t})}function o(e){let{className:a,...t}=e;return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",a),...t})}function l(e){let{className:a,...t}=e;return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("data-[state=selected]:bg-muted border-b",a),...t})}function c(e){let{className:a,...t}=e;return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...t})}function d(e){let{className:a,...t}=e;return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...t})}},85339:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},92657:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var a=a=>e(e.s=a);e.O(0,[3455,3464,9816,6987,283,8441,1684,7358],()=>a(40321)),_N_E=e.O()}]);