{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/certificazioni/CertificazioneForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport { Input } from '@/components/ui/input'\nimport { Textarea } from '@/components/ui/textarea'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, Save, X, FileText, Settings, Cloud, Info } from 'lucide-react'\nimport { CertificazioneCavo, CertificazioneCavoCreate, StrumentoCertificato } from '@/types/certificazioni'\nimport { certificazioniApi, caviApi, responsabiliApi, cantieriApi } from '@/lib/api'\n\ninterface CertificazioneFormProps {\n  cantiereId: number\n  certificazione?: CertificazioneCavo | null\n  strumenti: StrumentoCertificato[]\n  preselectedCavoId?: string // Cavo preselezionato per certificazione singola\n  onSuccess: (certificazioneData?: any) => void\n  onCancel: () => void\n}\n\ninterface Cavo {\n  id_cavo: string\n  tipologia?: string\n  sezione?: string\n  lunghezza?: number\n  stato_installazione?: string\n}\n\ninterface Responsabile {\n  id_responsabile: number\n  nome_responsabile: string\n}\n\ninterface WeatherData {\n  temperature: number\n  humidity: number\n  pressure?: number\n  description?: string\n  city?: string\n  country?: string\n  timestamp?: string\n  isDemo?: boolean\n  source?: string\n}\n\nexport default function CertificazioneForm({\n  cantiereId,\n  certificazione,\n  strumenti,\n  preselectedCavoId,\n  onSuccess,\n  onCancel\n}: CertificazioneFormProps) {\n  const [formData, setFormData] = useState<CertificazioneCavoCreate>({\n    id_cavo: preselectedCavoId || '',\n    id_operatore: undefined,\n    strumento_utilizzato: '',\n    id_strumento: undefined,\n    lunghezza_misurata: undefined,\n    valore_continuita: '',\n    valore_isolamento: '',\n    valore_resistenza: '',\n    note: '',\n    tipo_certificato: 'SINGOLO',\n    stato_certificato: 'BOZZA',\n    designazione_funzionale: '',\n    tensione_nominale: '230V',\n    tensione_prova_isolamento: 500,\n    durata_prova_isolamento: 60,\n    valore_minimo_isolamento: 500,\n    temperatura_prova: 20,\n    umidita_prova: 50,\n    esito_complessivo: ''\n  })\n\n  const [cavi, setCavi] = useState<Cavo[]>([])\n  const [responsabili, setResponsabili] = useState<Responsabile[]>([])\n  const [weatherData, setWeatherData] = useState<WeatherData | null>(null)\n  const [isLoading, setIsLoading] = useState(false)\n  const [isSaving, setIsSaving] = useState(false)\n  const [isLoadingWeather, setIsLoadingWeather] = useState(false)\n  const [error, setError] = useState('')\n  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})\n  const [isWeatherOverride, setIsWeatherOverride] = useState(false)\n\n  const isEdit = !!certificazione\n  const isCavoLocked = isEdit || !!preselectedCavoId\n\n  // Carica dati iniziali solo una volta\n  useEffect(() => {\n    loadInitialData()\n    loadWeatherData()\n  }, [cantiereId]) // Solo quando cambia il cantiere\n\n  // Aggiorna form quando cambia la certificazione\n  useEffect(() => {\n    if (certificazione) {\n      setFormData({\n        id_cavo: certificazione.id_cavo,\n        id_operatore: certificazione.id_operatore,\n        strumento_utilizzato: certificazione.strumento_utilizzato || '',\n        id_strumento: certificazione.id_strumento,\n        lunghezza_misurata: certificazione.lunghezza_misurata,\n        valore_continuita: certificazione.valore_continuita || '',\n        valore_isolamento: certificazione.valore_isolamento || '',\n        valore_resistenza: certificazione.valore_resistenza || '',\n        note: certificazione.note || '',\n        tipo_certificato: certificazione.tipo_certificato || 'SINGOLO',\n        stato_certificato: certificazione.stato_certificato || 'BOZZA',\n        designazione_funzionale: certificazione.designazione_funzionale || '',\n        tensione_nominale: certificazione.tensione_nominale || '230V',\n        tensione_prova_isolamento: certificazione.tensione_prova_isolamento || 500,\n        durata_prova_isolamento: certificazione.durata_prova_isolamento || 60,\n        valore_minimo_isolamento: certificazione.valore_minimo_isolamento || 500,\n        temperatura_prova: certificazione.temperatura_prova || weatherData?.temperature || 20,\n        umidita_prova: certificazione.umidita_prova || weatherData?.humidity || 50,\n        esito_complessivo: certificazione.esito_complessivo || ''\n      })\n    }\n  }, [certificazione])\n\n  // Aggiorna temperatura e umidità quando arrivano i dati meteo\n  useEffect(() => {\n    if (weatherData && !certificazione) {\n      setFormData(prev => ({\n        ...prev,\n        temperatura_prova: weatherData.temperature || 20,\n        umidita_prova: weatherData.humidity || 50\n      }))\n    }\n  }, [weatherData, certificazione])\n\n  const loadInitialData = async () => {\n    try {\n      setIsLoading(true)\n      const [caviData, responsabiliData] = await Promise.all([\n        caviApi.getCavi(cantiereId),\n        responsabiliApi.getResponsabili(cantiereId)\n      ])\n      setCavi(caviData)\n      setResponsabili(responsabiliData)\n    } catch (error: any) {\n      setError('Errore durante il caricamento dei dati')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const loadWeatherData = async () => {\n    try {\n      setIsLoadingWeather(true)\n      const response = await cantieriApi.getWeatherData(cantiereId)\n      if (response.success && response.data) {\n        setWeatherData(response.data)\n        // I valori del form vengono aggiornati tramite useEffect separato\n      }\n    } catch (error: any) {\n      console.warn('Impossibile caricare dati meteorologici:', error)\n      // Non mostrare errore all'utente, usa valori predefiniti\n    } finally {\n      setIsLoadingWeather(false)\n    }\n  }\n\n  // Funzione per collegare automaticamente un cavo (CEI 64-8 compliance)\n  const collegaCavoAutomatico = async (cavoId: string, responsabile: string = 'cantiere') => {\n    try {\n      let partenzaCollegata = false\n      let arrivoCollegato = false\n\n      // Prova a collegare il lato partenza\n      try {\n        await caviApi.collegaCavo(cantiereId, cavoId, 'partenza', responsabile)\n        partenzaCollegata = true\n      } catch (error: any) {\n        if (error.response?.data?.detail?.includes('già collegato')) {\n          partenzaCollegata = true\n        } else {\n          throw error\n        }\n      }\n\n      // Prova a collegare il lato arrivo\n      try {\n        await caviApi.collegaCavo(cantiereId, cavoId, 'arrivo', responsabile)\n        arrivoCollegato = true\n      } catch (error: any) {\n        if (error.response?.data?.detail?.includes('già collegato')) {\n          arrivoCollegato = true\n        } else {\n          throw error\n        }\n      }\n\n      return partenzaCollegata && arrivoCollegato\n    } catch (error) {\n      console.error('Errore nel collegamento automatico:', error)\n      throw error\n    }\n  }\n\n  const validateForm = (): boolean => {\n    const errors: Record<string, string> = {}\n\n    if (!formData.id_cavo) {\n      errors.id_cavo = 'Seleziona un cavo'\n    }\n\n    if (!formData.valore_isolamento) {\n      errors.valore_isolamento = 'Inserisci il valore di isolamento'\n    } else if (parseFloat(formData.valore_isolamento) < 0) {\n      errors.valore_isolamento = 'Il valore deve essere positivo'\n    }\n\n    if (formData.tensione_prova_isolamento && formData.tensione_prova_isolamento < 0) {\n      errors.tensione_prova_isolamento = 'La tensione deve essere positiva'\n    }\n\n    if (formData.temperatura_prova && (formData.temperatura_prova < -40 || formData.temperatura_prova > 80)) {\n      errors.temperatura_prova = 'Temperatura deve essere tra -40°C e 80°C'\n    }\n\n    if (formData.umidita_prova && (formData.umidita_prova < 0 || formData.umidita_prova > 100)) {\n      errors.umidita_prova = 'Umidità deve essere tra 0% e 100%'\n    }\n\n    setValidationErrors(errors)\n    return Object.keys(errors).length === 0\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!validateForm()) {\n      return\n    }\n\n    try {\n      setIsSaving(true)\n      setError('')\n\n      // Verifica se il cavo è completamente collegato (CEI 64-8 compliance)\n      if (!isEdit && formData.id_cavo) {\n        const cavo = cavi.find(c => c.id_cavo === formData.id_cavo)\n        const isCompletelyConnected = cavo && (cavo.collegamenti || 0) === 3\n\n        if (!isCompletelyConnected) {\n          const statoCollegamenti = (cavo?.collegamenti || 0) === 0 ? 'Non collegato' :\n                                   (cavo?.collegamenti || 0) === 1 ? 'Solo partenza collegata' :\n                                   (cavo?.collegamenti || 0) === 2 ? 'Solo arrivo collegato' :\n                                   'Stato sconosciuto'\n\n          const conferma = window.confirm(\n            `ATTENZIONE: Il cavo ${formData.id_cavo} non risulta completamente collegato.\\n\\n` +\n            `Stato collegamenti: ${statoCollegamenti}\\n\\n` +\n            `Vuoi collegare automaticamente entrambi i lati del cavo e procedere con la certificazione CEI 64-8?\\n\\n` +\n            `(Il sistema collegherà automaticamente il cavo a \"cantiere\" su entrambi i lati)`\n          )\n\n          if (!conferma) {\n            setIsSaving(false)\n            return\n          }\n\n          // Collega automaticamente il cavo\n          try {\n            await collegaCavoAutomatico(formData.id_cavo, 'cantiere')\n            // Ricarica i cavi per aggiornare lo stato\n            const updatedCavi = await caviApi.getCavi(cantiereId)\n            setCavi(updatedCavi)\n          } catch (error: any) {\n            setError(`Errore nel collegamento automatico: ${error.response?.data?.detail || error.message}`)\n            setIsSaving(false)\n            return\n          }\n        }\n      }\n\n      // Determina automaticamente lo stato in base ai valori\n      const isolamento = parseFloat(formData.valore_isolamento || '0')\n      const statoAutomatico = isolamento >= (formData.valore_minimo_isolamento || 500) ? 'CONFORME' : 'NON_CONFORME'\n      \n      const dataToSubmit = {\n        ...formData,\n        stato_certificato: formData.stato_certificato === 'BOZZA' ? statoAutomatico : formData.stato_certificato,\n        esito_complessivo: isolamento >= (formData.valore_minimo_isolamento || 500) ? 'CONFORME' : 'NON CONFORME',\n        // Includi i dati meteorologici automatici\n        temperatura_prova: formData.temperatura_prova || weatherData?.temperature || 20,\n        umidita_prova: formData.umidita_prova || weatherData?.humidity || 50\n      }\n\n      if (isEdit && certificazione) {\n        await certificazioniApi.updateCertificazione(cantiereId, certificazione.id_certificazione, dataToSubmit)\n      } else {\n        await certificazioniApi.createCertificazione(cantiereId, dataToSubmit)\n      }\n\n      onSuccess(dataToSubmit)\n    } catch (error: any) {\n      setError(error.response?.data?.detail || 'Errore durante il salvataggio')\n    } finally {\n      setIsSaving(false)\n    }\n  }\n\n  const handleInputChange = (field: keyof CertificazioneCavoCreate, value: any) => {\n    setFormData(prev => ({ ...prev, [field]: value }))\n    \n    // Rimuovi errore di validazione se presente\n    if (validationErrors[field]) {\n      setValidationErrors(prev => {\n        const newErrors = { ...prev }\n        delete newErrors[field]\n        return newErrors\n      })\n    }\n  }\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center p-8\">\n        <div className=\"flex items-center gap-2\">\n          <Loader2 className=\"h-4 w-4 animate-spin\" />\n          Caricamento dati...\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"h-full w-full\">\n      {/* Header Compatto */}\n      <div className=\"flex items-center justify-between p-3 border-b bg-white\">\n        <div className=\"flex items-center gap-2\">\n          <FileText className=\"h-4 w-4 text-blue-600\" />\n          <h1 className=\"text-base font-semibold text-slate-900\">\n            {isEdit ? 'Modifica Certificazione' : 'Nuova Certificazione'}\n          </h1>\n        </div>\n\n        <Button onClick={handleSubmit} disabled={isSaving} size=\"sm\">\n          {isSaving ? <Loader2 className=\"h-4 w-4 animate-spin mr-2\" /> : <Save className=\"h-4 w-4 mr-2\" />}\n          {isEdit ? 'Aggiorna' : 'Salva'}\n        </Button>\n      </div>\n\n      {/* Contenuto in Grid Layout */}\n      <div className=\"p-3 h-[calc(100%-60px)] overflow-y-auto\">\n        {error && (\n          <Alert variant=\"destructive\" className=\"mb-2\">\n            <AlertCircle className=\"h-4 w-4\" />\n            <AlertDescription>{error}</AlertDescription>\n          </Alert>\n        )}\n\n        <form onSubmit={handleSubmit}>\n          {/* Layout a 2 colonne per ottimizzare spazio */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-3 h-full\">\n\n            {/* COLONNA SINISTRA */}\n            <div className=\"space-y-2\">\n              {/* Informazioni Base */}\n              <Card>\n                <CardHeader className=\"pb-1 px-3 pt-2\">\n                  <CardTitle className=\"text-sm font-semibold\">Informazioni Base</CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-2 pt-0 px-3 pb-2\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n              <div className=\"space-y-1\">\n                <Label htmlFor=\"id_cavo\" className=\"text-sm\">Cavo *</Label>\n                <Select\n                  value={formData.id_cavo}\n                  onValueChange={(value) => handleInputChange('id_cavo', value)}\n                  disabled={isCavoLocked}\n                >\n                  <SelectTrigger className={validationErrors.id_cavo ? 'border-red-500' : ''}>\n                    <SelectValue placeholder=\"Seleziona cavo...\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {cavi.map((cavo) => (\n                      <SelectItem key={cavo.id_cavo} value={cavo.id_cavo}>\n                        {cavo.id_cavo} - {cavo.tipologia} {cavo.sezione}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n                {validationErrors.id_cavo && (\n                  <p className=\"text-sm text-red-600\">{validationErrors.id_cavo}</p>\n                )}\n              </div>\n\n              <div className=\"space-y-1\">\n                <Label htmlFor=\"id_operatore\" className=\"text-sm\">Operatore</Label>\n                <Select\n                  value={formData.id_operatore?.toString() || ''}\n                  onValueChange={(value) => handleInputChange('id_operatore', parseInt(value))}\n                >\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Seleziona operatore...\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {responsabili.map((resp) => (\n                      <SelectItem key={resp.id_responsabile} value={resp.id_responsabile.toString()}>\n                        {resp.nome_responsabile}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n\n              <div className=\"space-y-1\">\n                <Label htmlFor=\"id_strumento\" className=\"text-sm\">Strumento</Label>\n                <Select\n                  value={formData.id_strumento?.toString() || ''}\n                  onValueChange={(value) => {\n                    const strumento = strumenti.find(s => s.id_strumento === parseInt(value))\n                    handleInputChange('id_strumento', parseInt(value))\n                    if (strumento) {\n                      handleInputChange('strumento_utilizzato', `${strumento.marca} ${strumento.modello}`)\n                    }\n                  }}\n                >\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Seleziona strumento...\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {strumenti.map((strumento) => (\n                      <SelectItem key={strumento.id_strumento} value={strumento.id_strumento.toString()}>\n                        {strumento.nome} - {strumento.marca} {strumento.modello}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n\n              <div className=\"space-y-1\">\n                <Label htmlFor=\"tipo_certificato\" className=\"text-sm\">Tipo Certificato</Label>\n                <Select\n                  value={formData.tipo_certificato || 'SINGOLO'}\n                  onValueChange={(value) => handleInputChange('tipo_certificato', value)}\n                >\n                  <SelectTrigger>\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"SINGOLO\">Singolo</SelectItem>\n                    <SelectItem value=\"GRUPPO\">Gruppo</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n              {/* Condizioni Ambientali Automatiche */}\n              {weatherData && (\n                <Card>\n                  <CardHeader className=\"pb-1 px-3 pt-2\">\n                    <CardTitle className=\"flex items-center gap-2 text-sm font-semibold\">\n                      <Cloud className=\"h-3 w-3\" />\n                      Condizioni Ambientali\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent className=\"pt-0 px-3 pb-2\">\n              <div className={`p-3 rounded-lg border ${weatherData.isDemo ? 'bg-yellow-50 border-yellow-200' : 'bg-green-50 border-green-200'}`}>\n                <div className=\"flex items-center gap-3\">\n                  {isLoadingWeather ? (\n                    <Loader2 className=\"h-4 w-4 animate-spin\" />\n                  ) : (\n                    <span className=\"text-lg\">🌤️</span>\n                  )}\n                  <div className=\"flex-1\">\n                    {!isWeatherOverride ? (\n                      <>\n                        <div className=\"flex items-center gap-4 text-sm\">\n                          <div>\n                            <span className=\"text-xs text-slate-600\">Temperatura:</span>\n                            <span className=\"ml-1 font-semibold text-sm\">{weatherData.temperature}°C</span>\n                          </div>\n                          <div>\n                            <span className=\"text-xs text-slate-600\">Umidità:</span>\n                            <span className=\"ml-1 font-semibold text-sm\">{weatherData.humidity}%</span>\n                          </div>\n                          {weatherData.city && (\n                            <div>\n                              <span className=\"text-xs text-slate-600\">Località:</span>\n                              <span className=\"ml-1 font-semibold text-sm\">{weatherData.city}</span>\n                            </div>\n                          )}\n                        </div>\n                        <div className=\"text-xs text-slate-500 mt-1\">\n                          {weatherData.isDemo ? 'Dati demo' : 'Dati live'} -\n                          {weatherData.source === 'cantiere_database' ? ' Database cantiere' : ' API meteo'}\n                        </div>\n                      </>\n                    ) : (\n                      <div className=\"grid grid-cols-2 gap-3\">\n                        <div className=\"space-y-1\">\n                          <Label htmlFor=\"temperatura_override\" className=\"text-xs\">Temperatura (°C)</Label>\n                          <Input\n                            id=\"temperatura_override\"\n                            type=\"number\"\n                            value={formData.temperatura_prova}\n                            onChange={(e) => handleInputChange('temperatura_prova', parseInt(e.target.value))}\n                            className=\"h-8 text-sm\"\n                          />\n                        </div>\n                        <div className=\"space-y-1\">\n                          <Label htmlFor=\"umidita_override\" className=\"text-xs\">Umidità (%)</Label>\n                          <Input\n                            id=\"umidita_override\"\n                            type=\"number\"\n                            min=\"0\"\n                            max=\"100\"\n                            value={formData.umidita_prova}\n                            onChange={(e) => handleInputChange('umidita_prova', parseInt(e.target.value))}\n                            className=\"h-8 text-sm\"\n                          />\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                  <Button\n                    type=\"button\"\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={() => setIsWeatherOverride(!isWeatherOverride)}\n                    className=\"ml-2\"\n                  >\n                    {isWeatherOverride ? (\n                      <>\n                        <X className=\"h-3 w-3 mr-1\" />\n                        Auto\n                      </>\n                    ) : (\n                      <>\n                        <Settings className=\"h-3 w-3 mr-1\" />\n                        Modifica\n                      </>\n                    )}\n                  </Button>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        )}\n            </div>\n\n            {/* COLONNA DESTRA */}\n            <div className=\"space-y-2\">\n              {/* Misurazioni */}\n              <Card>\n                <CardHeader className=\"pb-1 px-3 pt-2\">\n                  <CardTitle className=\"flex items-center gap-2 text-sm font-semibold\">\n                    <Settings className=\"h-3 w-3\" />\n                    Misurazioni e Test\n                  </CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-2 pt-0 px-3 pb-2\">\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-3\">\n              {/* Prima riga: Misurazioni principali */}\n              <div className=\"space-y-1\">\n                <Label htmlFor=\"valore_isolamento\">Isolamento (MΩ) *</Label>\n                <Input\n                  id=\"valore_isolamento\"\n                  type=\"number\"\n                  step=\"0.1\"\n                  value={formData.valore_isolamento}\n                  onChange={(e) => handleInputChange('valore_isolamento', e.target.value)}\n                  className={validationErrors.valore_isolamento ? 'border-red-500' : ''}\n                  placeholder=\"500\"\n                />\n                {validationErrors.valore_isolamento && (\n                  <p className=\"text-sm text-red-600\">{validationErrors.valore_isolamento}</p>\n                )}\n              </div>\n\n              <div className=\"space-y-1\">\n                <Label htmlFor=\"valore_continuita\">Continuità</Label>\n                <Select\n                  value={formData.valore_continuita}\n                  onValueChange={(value) => handleInputChange('valore_continuita', value)}\n                >\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Seleziona esito...\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"CONFORME\">✅ CONFORME</SelectItem>\n                    <SelectItem value=\"NON_CONFORME\">❌ NON CONFORME</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n\n              <div className=\"space-y-1\">\n                <Label htmlFor=\"valore_resistenza\">Resistenza (Ω)</Label>\n                <Input\n                  id=\"valore_resistenza\"\n                  type=\"number\"\n                  step=\"0.01\"\n                  value={formData.valore_resistenza}\n                  onChange={(e) => handleInputChange('valore_resistenza', e.target.value)}\n                  placeholder=\"0.5\"\n                />\n              </div>\n            </div>\n\n            {/* Seconda riga: Parametri di prova */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n              <div className=\"space-y-1\">\n                <Label htmlFor=\"tensione_prova_isolamento\">Tensione Prova (V)</Label>\n                <Input\n                  id=\"tensione_prova_isolamento\"\n                  type=\"number\"\n                  value={formData.tensione_prova_isolamento}\n                  onChange={(e) => handleInputChange('tensione_prova_isolamento', parseInt(e.target.value))}\n                  placeholder=\"500\"\n                />\n              </div>\n\n              {/* Temperatura e Umidità utilizzano automaticamente i dati dalle Condizioni Ambientali */}\n              <div className=\"flex items-center text-sm text-slate-600 bg-slate-50 p-3 rounded-lg\">\n                <Info className=\"h-4 w-4 mr-2\" />\n                Temperatura e umidità utilizzano i dati rilevati automaticamente\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n              {/* Note e Stato */}\n              <Card>\n                <CardHeader className=\"pb-1 px-3 pt-2\">\n                  <CardTitle className=\"text-sm font-semibold\">Note e Stato</CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-2 px-3 pb-2\">\n            <div className=\"space-y-1\">\n              <Label htmlFor=\"note\" className=\"text-sm\">Note</Label>\n              <Textarea\n                id=\"note\"\n                value={formData.note}\n                onChange={(e) => handleInputChange('note', e.target.value)}\n                placeholder=\"Note aggiuntive...\"\n                rows={2}\n                className=\"text-sm\"\n              />\n            </div>\n\n            <div className=\"space-y-1\">\n              <Label htmlFor=\"stato_certificato\" className=\"text-sm\">Stato Certificato</Label>\n              <Select\n                value={formData.stato_certificato || 'BOZZA'}\n                onValueChange={(value) => handleInputChange('stato_certificato', value)}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"BOZZA\">Bozza</SelectItem>\n                  <SelectItem value=\"CONFORME\">Conforme</SelectItem>\n                  <SelectItem value=\"NON_CONFORME\">Non Conforme</SelectItem>\n                  <SelectItem value=\"CONFORME_CON_RISERVA\">Conforme con Riserva</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </CardContent>\n        </Card>\n            </div>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAlBA;;;;;;;;;;;;AAsDe,SAAS,mBAAmB,EACzC,UAAU,EACV,cAAc,EACd,SAAS,EACT,iBAAiB,EACjB,SAAS,EACT,QAAQ,EACgB;IACxB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;QACjE,SAAS,qBAAqB;QAC9B,cAAc;QACd,sBAAsB;QACtB,cAAc;QACd,oBAAoB;QACpB,mBAAmB;QACnB,mBAAmB;QACnB,mBAAmB;QACnB,MAAM;QACN,kBAAkB;QAClB,mBAAmB;QACnB,yBAAyB;QACzB,mBAAmB;QACnB,2BAA2B;QAC3B,yBAAyB;QACzB,0BAA0B;QAC1B,mBAAmB;QACnB,eAAe;QACf,mBAAmB;IACrB;IAEA,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAClF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,SAAS,CAAC,CAAC;IACjB,MAAM,eAAe,UAAU,CAAC,CAAC;IAEjC,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;IACF,GAAG;QAAC;KAAW,EAAE,iCAAiC;;IAElD,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB;YAClB,YAAY;gBACV,SAAS,eAAe,OAAO;gBAC/B,cAAc,eAAe,YAAY;gBACzC,sBAAsB,eAAe,oBAAoB,IAAI;gBAC7D,cAAc,eAAe,YAAY;gBACzC,oBAAoB,eAAe,kBAAkB;gBACrD,mBAAmB,eAAe,iBAAiB,IAAI;gBACvD,mBAAmB,eAAe,iBAAiB,IAAI;gBACvD,mBAAmB,eAAe,iBAAiB,IAAI;gBACvD,MAAM,eAAe,IAAI,IAAI;gBAC7B,kBAAkB,eAAe,gBAAgB,IAAI;gBACrD,mBAAmB,eAAe,iBAAiB,IAAI;gBACvD,yBAAyB,eAAe,uBAAuB,IAAI;gBACnE,mBAAmB,eAAe,iBAAiB,IAAI;gBACvD,2BAA2B,eAAe,yBAAyB,IAAI;gBACvE,yBAAyB,eAAe,uBAAuB,IAAI;gBACnE,0BAA0B,eAAe,wBAAwB,IAAI;gBACrE,mBAAmB,eAAe,iBAAiB,IAAI,aAAa,eAAe;gBACnF,eAAe,eAAe,aAAa,IAAI,aAAa,YAAY;gBACxE,mBAAmB,eAAe,iBAAiB,IAAI;YACzD;QACF;IACF,GAAG;QAAC;KAAe;IAEnB,8DAA8D;IAC9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe,CAAC,gBAAgB;YAClC,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,mBAAmB,YAAY,WAAW,IAAI;oBAC9C,eAAe,YAAY,QAAQ,IAAI;gBACzC,CAAC;QACH;IACF,GAAG;QAAC;QAAa;KAAe;IAEhC,MAAM,kBAAkB;QACtB,IAAI;YACF,aAAa;YACb,MAAM,CAAC,UAAU,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACrD,iHAAA,CAAA,UAAO,CAAC,OAAO,CAAC;gBAChB,iHAAA,CAAA,kBAAe,CAAC,eAAe,CAAC;aACjC;YACD,QAAQ;YACR,gBAAgB;QAClB,EAAE,OAAO,OAAY;YACnB,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI;YACF,oBAAoB;YACpB,MAAM,WAAW,MAAM,iHAAA,CAAA,cAAW,CAAC,cAAc,CAAC;YAClD,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,eAAe,SAAS,IAAI;YAC5B,kEAAkE;YACpE;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,IAAI,CAAC,4CAA4C;QACzD,yDAAyD;QAC3D,SAAU;YACR,oBAAoB;QACtB;IACF;IAEA,uEAAuE;IACvE,MAAM,wBAAwB,OAAO,QAAgB,eAAuB,UAAU;QACpF,IAAI;YACF,IAAI,oBAAoB;YACxB,IAAI,kBAAkB;YAEtB,qCAAqC;YACrC,IAAI;gBACF,MAAM,iHAAA,CAAA,UAAO,CAAC,WAAW,CAAC,YAAY,QAAQ,YAAY;gBAC1D,oBAAoB;YACtB,EAAE,OAAO,OAAY;gBACnB,IAAI,MAAM,QAAQ,EAAE,MAAM,QAAQ,SAAS,kBAAkB;oBAC3D,oBAAoB;gBACtB,OAAO;oBACL,MAAM;gBACR;YACF;YAEA,mCAAmC;YACnC,IAAI;gBACF,MAAM,iHAAA,CAAA,UAAO,CAAC,WAAW,CAAC,YAAY,QAAQ,UAAU;gBACxD,kBAAkB;YACpB,EAAE,OAAO,OAAY;gBACnB,IAAI,MAAM,QAAQ,EAAE,MAAM,QAAQ,SAAS,kBAAkB;oBAC3D,kBAAkB;gBACpB,OAAO;oBACL,MAAM;gBACR;YACF;YAEA,OAAO,qBAAqB;QAC9B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,MAAM;QACR;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,SAAiC,CAAC;QAExC,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,OAAO,OAAO,GAAG;QACnB;QAEA,IAAI,CAAC,SAAS,iBAAiB,EAAE;YAC/B,OAAO,iBAAiB,GAAG;QAC7B,OAAO,IAAI,WAAW,SAAS,iBAAiB,IAAI,GAAG;YACrD,OAAO,iBAAiB,GAAG;QAC7B;QAEA,IAAI,SAAS,yBAAyB,IAAI,SAAS,yBAAyB,GAAG,GAAG;YAChF,OAAO,yBAAyB,GAAG;QACrC;QAEA,IAAI,SAAS,iBAAiB,IAAI,CAAC,SAAS,iBAAiB,GAAG,CAAC,MAAM,SAAS,iBAAiB,GAAG,EAAE,GAAG;YACvG,OAAO,iBAAiB,GAAG;QAC7B;QAEA,IAAI,SAAS,aAAa,IAAI,CAAC,SAAS,aAAa,GAAG,KAAK,SAAS,aAAa,GAAG,GAAG,GAAG;YAC1F,OAAO,aAAa,GAAG;QACzB;QAEA,oBAAoB;QACpB,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;IACxC;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,IAAI;YACF,YAAY;YACZ,SAAS;YAET,sEAAsE;YACtE,IAAI,CAAC,UAAU,SAAS,OAAO,EAAE;gBAC/B,MAAM,OAAO,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,SAAS,OAAO;gBAC1D,MAAM,wBAAwB,QAAQ,CAAC,KAAK,YAAY,IAAI,CAAC,MAAM;gBAEnE,IAAI,CAAC,uBAAuB;oBAC1B,MAAM,oBAAoB,CAAC,MAAM,gBAAgB,CAAC,MAAM,IAAI,kBACnC,CAAC,MAAM,gBAAgB,CAAC,MAAM,IAAI,4BAClC,CAAC,MAAM,gBAAgB,CAAC,MAAM,IAAI,0BAClC;oBAEzB,MAAM,WAAW,OAAO,OAAO,CAC7B,CAAC,oBAAoB,EAAE,SAAS,OAAO,CAAC,yCAAyC,CAAC,GAClF,CAAC,oBAAoB,EAAE,kBAAkB,IAAI,CAAC,GAC9C,CAAC,uGAAuG,CAAC,GACzG,CAAC,+EAA+E,CAAC;oBAGnF,IAAI,CAAC,UAAU;wBACb,YAAY;wBACZ;oBACF;oBAEA,kCAAkC;oBAClC,IAAI;wBACF,MAAM,sBAAsB,SAAS,OAAO,EAAE;wBAC9C,0CAA0C;wBAC1C,MAAM,cAAc,MAAM,iHAAA,CAAA,UAAO,CAAC,OAAO,CAAC;wBAC1C,QAAQ;oBACV,EAAE,OAAO,OAAY;wBACnB,SAAS,CAAC,oCAAoC,EAAE,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,EAAE;wBAC/F,YAAY;wBACZ;oBACF;gBACF;YACF;YAEA,uDAAuD;YACvD,MAAM,aAAa,WAAW,SAAS,iBAAiB,IAAI;YAC5D,MAAM,kBAAkB,cAAc,CAAC,SAAS,wBAAwB,IAAI,GAAG,IAAI,aAAa;YAEhG,MAAM,eAAe;gBACnB,GAAG,QAAQ;gBACX,mBAAmB,SAAS,iBAAiB,KAAK,UAAU,kBAAkB,SAAS,iBAAiB;gBACxG,mBAAmB,cAAc,CAAC,SAAS,wBAAwB,IAAI,GAAG,IAAI,aAAa;gBAC3F,0CAA0C;gBAC1C,mBAAmB,SAAS,iBAAiB,IAAI,aAAa,eAAe;gBAC7E,eAAe,SAAS,aAAa,IAAI,aAAa,YAAY;YACpE;YAEA,IAAI,UAAU,gBAAgB;gBAC5B,MAAM,iHAAA,CAAA,oBAAiB,CAAC,oBAAoB,CAAC,YAAY,eAAe,iBAAiB,EAAE;YAC7F,OAAO;gBACL,MAAM,iHAAA,CAAA,oBAAiB,CAAC,oBAAoB,CAAC,YAAY;YAC3D;YAEA,UAAU;QACZ,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,QAAQ,EAAE,MAAM,UAAU;QAC3C,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,oBAAoB,CAAC,OAAuC;QAChE,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAEhD,4CAA4C;QAC5C,IAAI,gBAAgB,CAAC,MAAM,EAAE;YAC3B,oBAAoB,CAAA;gBAClB,MAAM,YAAY;oBAAE,GAAG,IAAI;gBAAC;gBAC5B,OAAO,SAAS,CAAC,MAAM;gBACvB,OAAO;YACT;QACF;IACF;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;oBAAyB;;;;;;;;;;;;IAKpD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;gCAAG,WAAU;0CACX,SAAS,4BAA4B;;;;;;;;;;;;kCAI1C,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAc,UAAU;wBAAU,MAAK;;4BACrD,yBAAW,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;qDAAiC,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAC/E,SAAS,aAAa;;;;;;;;;;;;;0BAK3B,8OAAC;gBAAI,WAAU;;oBACZ,uBACC,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAc,WAAU;;0CACrC,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC,iIAAA,CAAA,mBAAgB;0CAAE;;;;;;;;;;;;kCAIvB,8OAAC;wBAAK,UAAU;kCAEd,cAAA,8OAAC;4BAAI,WAAU;;8CAGb,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC,gIAAA,CAAA,OAAI;;8DACH,8OAAC,gIAAA,CAAA,aAAU;oDAAC,WAAU;8DACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAwB;;;;;;;;;;;8DAE/C,8OAAC,gIAAA,CAAA,cAAW;oDAAC,WAAU;8DAC3B,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAU,WAAU;kFAAU;;;;;;kFAC7C,8OAAC,kIAAA,CAAA,SAAM;wEACL,OAAO,SAAS,OAAO;wEACvB,eAAe,CAAC,QAAU,kBAAkB,WAAW;wEACvD,UAAU;;0FAEV,8OAAC,kIAAA,CAAA,gBAAa;gFAAC,WAAW,iBAAiB,OAAO,GAAG,mBAAmB;0FACtE,cAAA,8OAAC,kIAAA,CAAA,cAAW;oFAAC,aAAY;;;;;;;;;;;0FAE3B,8OAAC,kIAAA,CAAA,gBAAa;0FACX,KAAK,GAAG,CAAC,CAAC,qBACT,8OAAC,kIAAA,CAAA,aAAU;wFAAoB,OAAO,KAAK,OAAO;;4FAC/C,KAAK,OAAO;4FAAC;4FAAI,KAAK,SAAS;4FAAC;4FAAE,KAAK,OAAO;;uFADhC,KAAK,OAAO;;;;;;;;;;;;;;;;oEAMlC,iBAAiB,OAAO,kBACvB,8OAAC;wEAAE,WAAU;kFAAwB,iBAAiB,OAAO;;;;;;;;;;;;0EAIjE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAe,WAAU;kFAAU;;;;;;kFAClD,8OAAC,kIAAA,CAAA,SAAM;wEACL,OAAO,SAAS,YAAY,EAAE,cAAc;wEAC5C,eAAe,CAAC,QAAU,kBAAkB,gBAAgB,SAAS;;0FAErE,8OAAC,kIAAA,CAAA,gBAAa;0FACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;oFAAC,aAAY;;;;;;;;;;;0FAE3B,8OAAC,kIAAA,CAAA,gBAAa;0FACX,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,kIAAA,CAAA,aAAU;wFAA4B,OAAO,KAAK,eAAe,CAAC,QAAQ;kGACxE,KAAK,iBAAiB;uFADR,KAAK,eAAe;;;;;;;;;;;;;;;;;;;;;;0EAQ7C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAe,WAAU;kFAAU;;;;;;kFAClD,8OAAC,kIAAA,CAAA,SAAM;wEACL,OAAO,SAAS,YAAY,EAAE,cAAc;wEAC5C,eAAe,CAAC;4EACd,MAAM,YAAY,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK,SAAS;4EAClE,kBAAkB,gBAAgB,SAAS;4EAC3C,IAAI,WAAW;gFACb,kBAAkB,wBAAwB,GAAG,UAAU,KAAK,CAAC,CAAC,EAAE,UAAU,OAAO,EAAE;4EACrF;wEACF;;0FAEA,8OAAC,kIAAA,CAAA,gBAAa;0FACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;oFAAC,aAAY;;;;;;;;;;;0FAE3B,8OAAC,kIAAA,CAAA,gBAAa;0FACX,UAAU,GAAG,CAAC,CAAC,0BACd,8OAAC,kIAAA,CAAA,aAAU;wFAA8B,OAAO,UAAU,YAAY,CAAC,QAAQ;;4FAC5E,UAAU,IAAI;4FAAC;4FAAI,UAAU,KAAK;4FAAC;4FAAE,UAAU,OAAO;;uFADxC,UAAU,YAAY;;;;;;;;;;;;;;;;;;;;;;0EAQ/C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAmB,WAAU;kFAAU;;;;;;kFACtD,8OAAC,kIAAA,CAAA,SAAM;wEACL,OAAO,SAAS,gBAAgB,IAAI;wEACpC,eAAe,CAAC,QAAU,kBAAkB,oBAAoB;;0FAEhE,8OAAC,kIAAA,CAAA,gBAAa;0FACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0FAEd,8OAAC,kIAAA,CAAA,gBAAa;;kGACZ,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAU;;;;;;kGAC5B,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCAShC,6BACC,8OAAC,gIAAA,CAAA,OAAI;;8DACH,8OAAC,gIAAA,CAAA,aAAU;oDAAC,WAAU;8DACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;;0EACnB,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAY;;;;;;;;;;;;8DAIjC,8OAAC,gIAAA,CAAA,cAAW;oDAAC,WAAU;8DAC3B,cAAA,8OAAC;wDAAI,WAAW,CAAC,sBAAsB,EAAE,YAAY,MAAM,GAAG,mCAAmC,gCAAgC;kEAC/H,cAAA,8OAAC;4DAAI,WAAU;;gEACZ,iCACC,8OAAC,iNAAA,CAAA,UAAO;oEAAC,WAAU;;;;;yFAEnB,8OAAC;oEAAK,WAAU;8EAAU;;;;;;8EAE5B,8OAAC;oEAAI,WAAU;8EACZ,CAAC,kCACA;;0FACE,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;;0GACC,8OAAC;gGAAK,WAAU;0GAAyB;;;;;;0GACzC,8OAAC;gGAAK,WAAU;;oGAA8B,YAAY,WAAW;oGAAC;;;;;;;;;;;;;kGAExE,8OAAC;;0GACC,8OAAC;gGAAK,WAAU;0GAAyB;;;;;;0GACzC,8OAAC;gGAAK,WAAU;;oGAA8B,YAAY,QAAQ;oGAAC;;;;;;;;;;;;;oFAEpE,YAAY,IAAI,kBACf,8OAAC;;0GACC,8OAAC;gGAAK,WAAU;0GAAyB;;;;;;0GACzC,8OAAC;gGAAK,WAAU;0GAA8B,YAAY,IAAI;;;;;;;;;;;;;;;;;;0FAIpE,8OAAC;gFAAI,WAAU;;oFACZ,YAAY,MAAM,GAAG,cAAc;oFAAY;oFAC/C,YAAY,MAAM,KAAK,sBAAsB,uBAAuB;;;;;;;;qGAIzE,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,iIAAA,CAAA,QAAK;wFAAC,SAAQ;wFAAuB,WAAU;kGAAU;;;;;;kGAC1D,8OAAC,iIAAA,CAAA,QAAK;wFACJ,IAAG;wFACH,MAAK;wFACL,OAAO,SAAS,iBAAiB;wFACjC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,SAAS,EAAE,MAAM,CAAC,KAAK;wFAC/E,WAAU;;;;;;;;;;;;0FAGd,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,iIAAA,CAAA,QAAK;wFAAC,SAAQ;wFAAmB,WAAU;kGAAU;;;;;;kGACtD,8OAAC,iIAAA,CAAA,QAAK;wFACJ,IAAG;wFACH,MAAK;wFACL,KAAI;wFACJ,KAAI;wFACJ,OAAO,SAAS,aAAa;wFAC7B,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,SAAS,EAAE,MAAM,CAAC,KAAK;wFAC3E,WAAU;;;;;;;;;;;;;;;;;;;;;;;8EAMpB,8OAAC,kIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,qBAAqB,CAAC;oEACrC,WAAU;8EAET,kCACC;;0FACE,8OAAC,4LAAA,CAAA,IAAC;gFAAC,WAAU;;;;;;4EAAiB;;qGAIhC;;0FACE,8OAAC,0MAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAajD,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC,gIAAA,CAAA,OAAI;;8DACH,8OAAC,gIAAA,CAAA,aAAU;oDAAC,WAAU;8DACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;;0EACnB,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAY;;;;;;;;;;;;8DAIpC,8OAAC,gIAAA,CAAA,cAAW;oDAAC,WAAU;;sEAC3B,8OAAC;4DAAI,WAAU;;8EAEb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,iIAAA,CAAA,QAAK;4EAAC,SAAQ;sFAAoB;;;;;;sFACnC,8OAAC,iIAAA,CAAA,QAAK;4EACJ,IAAG;4EACH,MAAK;4EACL,MAAK;4EACL,OAAO,SAAS,iBAAiB;4EACjC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;4EACtE,WAAW,iBAAiB,iBAAiB,GAAG,mBAAmB;4EACnE,aAAY;;;;;;wEAEb,iBAAiB,iBAAiB,kBACjC,8OAAC;4EAAE,WAAU;sFAAwB,iBAAiB,iBAAiB;;;;;;;;;;;;8EAI3E,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,iIAAA,CAAA,QAAK;4EAAC,SAAQ;sFAAoB;;;;;;sFACnC,8OAAC,kIAAA,CAAA,SAAM;4EACL,OAAO,SAAS,iBAAiB;4EACjC,eAAe,CAAC,QAAU,kBAAkB,qBAAqB;;8FAEjE,8OAAC,kIAAA,CAAA,gBAAa;8FACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;wFAAC,aAAY;;;;;;;;;;;8FAE3B,8OAAC,kIAAA,CAAA,gBAAa;;sGACZ,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAW;;;;;;sGAC7B,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAe;;;;;;;;;;;;;;;;;;;;;;;;8EAKvC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,iIAAA,CAAA,QAAK;4EAAC,SAAQ;sFAAoB;;;;;;sFACnC,8OAAC,iIAAA,CAAA,QAAK;4EACJ,IAAG;4EACH,MAAK;4EACL,MAAK;4EACL,OAAO,SAAS,iBAAiB;4EACjC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;4EACtE,aAAY;;;;;;;;;;;;;;;;;;sEAMlB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,iIAAA,CAAA,QAAK;4EAAC,SAAQ;sFAA4B;;;;;;sFAC3C,8OAAC,iIAAA,CAAA,QAAK;4EACJ,IAAG;4EACH,MAAK;4EACL,OAAO,SAAS,yBAAyB;4EACzC,UAAU,CAAC,IAAM,kBAAkB,6BAA6B,SAAS,EAAE,MAAM,CAAC,KAAK;4EACvF,aAAY;;;;;;;;;;;;8EAKhB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;sDAQnC,8OAAC,gIAAA,CAAA,OAAI;;8DACH,8OAAC,gIAAA,CAAA,aAAU;oDAAC,WAAU;8DACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAwB;;;;;;;;;;;8DAE/C,8OAAC,gIAAA,CAAA,cAAW;oDAAC,WAAU;;sEAC3B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAO,WAAU;8EAAU;;;;;;8EAC1C,8OAAC,oIAAA,CAAA,WAAQ;oEACP,IAAG;oEACH,OAAO,SAAS,IAAI;oEACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;oEACzD,aAAY;oEACZ,MAAM;oEACN,WAAU;;;;;;;;;;;;sEAId,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAoB,WAAU;8EAAU;;;;;;8EACvD,8OAAC,kIAAA,CAAA,SAAM;oEACL,OAAO,SAAS,iBAAiB,IAAI;oEACrC,eAAe,CAAC,QAAU,kBAAkB,qBAAqB;;sFAEjE,8OAAC,kIAAA,CAAA,gBAAa;sFACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;sFAEd,8OAAC,kIAAA,CAAA,gBAAa;;8FACZ,8OAAC,kIAAA,CAAA,aAAU;oFAAC,OAAM;8FAAQ;;;;;;8FAC1B,8OAAC,kIAAA,CAAA,aAAU;oFAAC,OAAM;8FAAW;;;;;;8FAC7B,8OAAC,kIAAA,CAAA,aAAU;oFAAC,OAAM;8FAAe;;;;;;8FACjC,8OAAC,kIAAA,CAAA,aAAU;oFAAC,OAAM;8FAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY3D", "debugId": null}}]}