{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/certificazioni/CertificazioneForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport { Input } from '@/components/ui/input'\nimport { Textarea } from '@/components/ui/textarea'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, Save, X, FileText, Settings, Cloud, Info } from 'lucide-react'\nimport { CertificazioneCavo, CertificazioneCavoCreate, StrumentoCertificato } from '@/types/certificazioni'\nimport { certificazioniApi, caviApi, responsabiliApi, cantieriApi } from '@/lib/api'\n\ninterface CertificazioneFormProps {\n  cantiereId: number\n  certificazione?: CertificazioneCavo | null\n  strumenti: StrumentoCertificato[]\n  preselectedCavoId?: string // Cavo preselezionato per certificazione singola\n  onSuccess: (certificazioneData?: any) => void\n  onCancel: () => void\n}\n\ninterface Cavo {\n  id_cavo: string\n  tipologia?: string\n  sezione?: string\n  lunghezza?: number\n  stato_installazione?: string\n}\n\ninterface Responsabile {\n  id_responsabile: number\n  nome_responsabile: string\n}\n\ninterface WeatherData {\n  temperature: number\n  humidity: number\n  pressure?: number\n  description?: string\n  city?: string\n  country?: string\n  timestamp?: string\n  isDemo?: boolean\n  source?: string\n}\n\nexport default function CertificazioneForm({\n  cantiereId,\n  certificazione,\n  strumenti,\n  preselectedCavoId,\n  onSuccess,\n  onCancel\n}: CertificazioneFormProps) {\n  const [formData, setFormData] = useState<CertificazioneCavoCreate>({\n    id_cavo: preselectedCavoId || '',\n    id_operatore: undefined,\n    strumento_utilizzato: '',\n    id_strumento: undefined,\n    lunghezza_misurata: undefined,\n    valore_continuita: '',\n    valore_isolamento: '',\n    valore_resistenza: '',\n    note: '',\n    tipo_certificato: 'SINGOLO',\n    stato_certificato: 'BOZZA',\n    designazione_funzionale: '',\n    tensione_nominale: '230V',\n    tensione_prova_isolamento: 500,\n    durata_prova_isolamento: 60,\n    valore_minimo_isolamento: 500,\n    temperatura_prova: 20,\n    umidita_prova: 50,\n    esito_complessivo: ''\n  })\n\n  const [cavi, setCavi] = useState<Cavo[]>([])\n  const [responsabili, setResponsabili] = useState<Responsabile[]>([])\n  const [weatherData, setWeatherData] = useState<WeatherData | null>(null)\n  const [isLoading, setIsLoading] = useState(false)\n  const [isSaving, setIsSaving] = useState(false)\n  const [isLoadingWeather, setIsLoadingWeather] = useState(false)\n  const [error, setError] = useState('')\n  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})\n  const [isWeatherOverride, setIsWeatherOverride] = useState(false)\n\n  const isEdit = !!certificazione\n  const isCavoLocked = isEdit || !!preselectedCavoId\n\n  // Carica dati iniziali solo una volta\n  useEffect(() => {\n    loadInitialData()\n    loadWeatherData()\n  }, [cantiereId]) // Solo quando cambia il cantiere\n\n  // Aggiorna form quando cambia la certificazione\n  useEffect(() => {\n    if (certificazione) {\n      setFormData({\n        id_cavo: certificazione.id_cavo,\n        id_operatore: certificazione.id_operatore,\n        strumento_utilizzato: certificazione.strumento_utilizzato || '',\n        id_strumento: certificazione.id_strumento,\n        lunghezza_misurata: certificazione.lunghezza_misurata,\n        valore_continuita: certificazione.valore_continuita || '',\n        valore_isolamento: certificazione.valore_isolamento || '',\n        valore_resistenza: certificazione.valore_resistenza || '',\n        note: certificazione.note || '',\n        tipo_certificato: certificazione.tipo_certificato || 'SINGOLO',\n        stato_certificato: certificazione.stato_certificato || 'BOZZA',\n        designazione_funzionale: certificazione.designazione_funzionale || '',\n        tensione_nominale: certificazione.tensione_nominale || '230V',\n        tensione_prova_isolamento: certificazione.tensione_prova_isolamento || 500,\n        durata_prova_isolamento: certificazione.durata_prova_isolamento || 60,\n        valore_minimo_isolamento: certificazione.valore_minimo_isolamento || 500,\n        temperatura_prova: certificazione.temperatura_prova || weatherData?.temperature || 20,\n        umidita_prova: certificazione.umidita_prova || weatherData?.humidity || 50,\n        esito_complessivo: certificazione.esito_complessivo || ''\n      })\n    }\n  }, [certificazione])\n\n  // Aggiorna temperatura e umidità quando arrivano i dati meteo\n  useEffect(() => {\n    if (weatherData && !certificazione) {\n      setFormData(prev => ({\n        ...prev,\n        temperatura_prova: weatherData.temperature || 20,\n        umidita_prova: weatherData.humidity || 50\n      }))\n    }\n  }, [weatherData, certificazione])\n\n  const loadInitialData = async () => {\n    try {\n      setIsLoading(true)\n      const [caviData, responsabiliData] = await Promise.all([\n        caviApi.getCavi(cantiereId),\n        responsabiliApi.getResponsabili(cantiereId)\n      ])\n      setCavi(caviData)\n      setResponsabili(responsabiliData)\n    } catch (error: any) {\n      setError('Errore durante il caricamento dei dati')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const loadWeatherData = async () => {\n    try {\n      setIsLoadingWeather(true)\n      const response = await cantieriApi.getWeatherData(cantiereId)\n      if (response.success && response.data) {\n        setWeatherData(response.data)\n        // I valori del form vengono aggiornati tramite useEffect separato\n      }\n    } catch (error: any) {\n      console.warn('Impossibile caricare dati meteorologici:', error)\n      // Non mostrare errore all'utente, usa valori predefiniti\n    } finally {\n      setIsLoadingWeather(false)\n    }\n  }\n\n  // Funzione per collegare automaticamente un cavo (CEI 64-8 compliance)\n  const collegaCavoAutomatico = async (cavoId: string, responsabile: string = 'cantiere') => {\n    try {\n      let partenzaCollegata = false\n      let arrivoCollegato = false\n\n      // Prova a collegare il lato partenza\n      try {\n        await caviApi.collegaCavo(cantiereId, cavoId, 'partenza', responsabile)\n        partenzaCollegata = true\n      } catch (error: any) {\n        if (error.response?.data?.detail?.includes('già collegato')) {\n          partenzaCollegata = true\n        } else {\n          throw error\n        }\n      }\n\n      // Prova a collegare il lato arrivo\n      try {\n        await caviApi.collegaCavo(cantiereId, cavoId, 'arrivo', responsabile)\n        arrivoCollegato = true\n      } catch (error: any) {\n        if (error.response?.data?.detail?.includes('già collegato')) {\n          arrivoCollegato = true\n        } else {\n          throw error\n        }\n      }\n\n      return partenzaCollegata && arrivoCollegato\n    } catch (error) {\n      console.error('Errore nel collegamento automatico:', error)\n      throw error\n    }\n  }\n\n  const validateForm = (): boolean => {\n    const errors: Record<string, string> = {}\n\n    if (!formData.id_cavo) {\n      errors.id_cavo = 'Seleziona un cavo'\n    }\n\n    if (!formData.valore_isolamento) {\n      errors.valore_isolamento = 'Inserisci il valore di isolamento'\n    } else if (parseFloat(formData.valore_isolamento) < 0) {\n      errors.valore_isolamento = 'Il valore deve essere positivo'\n    }\n\n    if (formData.tensione_prova_isolamento && formData.tensione_prova_isolamento < 0) {\n      errors.tensione_prova_isolamento = 'La tensione deve essere positiva'\n    }\n\n    if (formData.temperatura_prova && (formData.temperatura_prova < -40 || formData.temperatura_prova > 80)) {\n      errors.temperatura_prova = 'Temperatura deve essere tra -40°C e 80°C'\n    }\n\n    if (formData.umidita_prova && (formData.umidita_prova < 0 || formData.umidita_prova > 100)) {\n      errors.umidita_prova = 'Umidità deve essere tra 0% e 100%'\n    }\n\n    setValidationErrors(errors)\n    return Object.keys(errors).length === 0\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!validateForm()) {\n      return\n    }\n\n    try {\n      setIsSaving(true)\n      setError('')\n\n      // Verifica se il cavo è completamente collegato (CEI 64-8 compliance)\n      if (!isEdit && formData.id_cavo) {\n        const cavo = cavi.find(c => c.id_cavo === formData.id_cavo)\n        const isCompletelyConnected = cavo && (cavo.collegamenti || 0) === 3\n\n        if (!isCompletelyConnected) {\n          const statoCollegamenti = (cavo?.collegamenti || 0) === 0 ? 'Non collegato' :\n                                   (cavo?.collegamenti || 0) === 1 ? 'Solo partenza collegata' :\n                                   (cavo?.collegamenti || 0) === 2 ? 'Solo arrivo collegato' :\n                                   'Stato sconosciuto'\n\n          const conferma = window.confirm(\n            `ATTENZIONE: Il cavo ${formData.id_cavo} non risulta completamente collegato.\\n\\n` +\n            `Stato collegamenti: ${statoCollegamenti}\\n\\n` +\n            `Vuoi collegare automaticamente entrambi i lati del cavo e procedere con la certificazione CEI 64-8?\\n\\n` +\n            `(Il sistema collegherà automaticamente il cavo a \"cantiere\" su entrambi i lati)`\n          )\n\n          if (!conferma) {\n            setIsSaving(false)\n            return\n          }\n\n          // Collega automaticamente il cavo\n          try {\n            await collegaCavoAutomatico(formData.id_cavo, 'cantiere')\n            // Ricarica i cavi per aggiornare lo stato\n            const updatedCavi = await caviApi.getCavi(cantiereId)\n            setCavi(updatedCavi)\n          } catch (error: any) {\n            setError(`Errore nel collegamento automatico: ${error.response?.data?.detail || error.message}`)\n            setIsSaving(false)\n            return\n          }\n        }\n      }\n\n      // Determina automaticamente lo stato in base ai valori\n      const isolamento = parseFloat(formData.valore_isolamento || '0')\n      const statoAutomatico = isolamento >= (formData.valore_minimo_isolamento || 500) ? 'CONFORME' : 'NON_CONFORME'\n      \n      const dataToSubmit = {\n        ...formData,\n        stato_certificato: formData.stato_certificato === 'BOZZA' ? statoAutomatico : formData.stato_certificato,\n        esito_complessivo: isolamento >= (formData.valore_minimo_isolamento || 500) ? 'CONFORME' : 'NON CONFORME',\n        // Includi i dati meteorologici automatici\n        temperatura_prova: formData.temperatura_prova || weatherData?.temperature || 20,\n        umidita_prova: formData.umidita_prova || weatherData?.humidity || 50\n      }\n\n      if (isEdit && certificazione) {\n        await certificazioniApi.updateCertificazione(cantiereId, certificazione.id_certificazione, dataToSubmit)\n      } else {\n        await certificazioniApi.createCertificazione(cantiereId, dataToSubmit)\n      }\n\n      onSuccess(dataToSubmit)\n    } catch (error: any) {\n      setError(error.response?.data?.detail || 'Errore durante il salvataggio')\n    } finally {\n      setIsSaving(false)\n    }\n  }\n\n  const handleInputChange = (field: keyof CertificazioneCavoCreate, value: any) => {\n    setFormData(prev => ({ ...prev, [field]: value }))\n    \n    // Rimuovi errore di validazione se presente\n    if (validationErrors[field]) {\n      setValidationErrors(prev => {\n        const newErrors = { ...prev }\n        delete newErrors[field]\n        return newErrors\n      })\n    }\n  }\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center p-8\">\n        <div className=\"flex items-center gap-2\">\n          <Loader2 className=\"h-4 w-4 animate-spin\" />\n          Caricamento dati...\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"flex flex-col h-full min-h-0\">\n      {/* Header Sticky */}\n      <div className=\"flex-shrink-0 bg-white z-10 p-4 pb-3 border-b shadow-sm\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-lg font-bold text-slate-900 flex items-center gap-2\">\n              <FileText className=\"h-4 w-4 text-blue-600\" />\n              {isEdit ? 'Modifica Certificazione' : 'Nuova Certificazione'}\n            </h1>\n            <p className=\"text-slate-600 text-xs mt-1\">\n              {isEdit ? 'Modifica i dati della certificazione esistente' : 'Crea una nuova certificazione CEI 64-8'}\n            </p>\n          </div>\n\n          <div className=\"flex gap-2\">\n            <Button onClick={handleSubmit} disabled={isSaving} size=\"sm\">\n              {isSaving ? <Loader2 className=\"h-4 w-4 animate-spin mr-2\" /> : <Save className=\"h-4 w-4 mr-2\" />}\n              {isEdit ? 'Aggiorna' : 'Salva'}\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Contenuto Scrollabile */}\n      <div className=\"flex-1 overflow-y-auto p-4 min-h-0\">\n        {error && (\n          <Alert variant=\"destructive\" className=\"mb-3\">\n            <AlertCircle className=\"h-4 w-4\" />\n            <AlertDescription>{error}</AlertDescription>\n          </Alert>\n        )}\n\n        <form onSubmit={handleSubmit} className=\"space-y-3\">\n        {/* Informazioni Base */}\n        <Card>\n          <CardHeader className=\"pb-2 px-4 pt-3\">\n            <CardTitle className=\"text-base\">Informazioni Base</CardTitle>\n            <CardDescription className=\"text-xs\">Dati principali della certificazione</CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-2 pt-0 px-4 pb-3\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n              <div className=\"space-y-1\">\n                <Label htmlFor=\"id_cavo\" className=\"text-sm\">Cavo *</Label>\n                <Select\n                  value={formData.id_cavo}\n                  onValueChange={(value) => handleInputChange('id_cavo', value)}\n                  disabled={isCavoLocked}\n                >\n                  <SelectTrigger className={validationErrors.id_cavo ? 'border-red-500' : ''}>\n                    <SelectValue placeholder=\"Seleziona cavo...\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {cavi.map((cavo) => (\n                      <SelectItem key={cavo.id_cavo} value={cavo.id_cavo}>\n                        {cavo.id_cavo} - {cavo.tipologia} {cavo.sezione}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n                {validationErrors.id_cavo && (\n                  <p className=\"text-sm text-red-600\">{validationErrors.id_cavo}</p>\n                )}\n              </div>\n\n              <div className=\"space-y-1\">\n                <Label htmlFor=\"id_operatore\" className=\"text-sm\">Operatore</Label>\n                <Select\n                  value={formData.id_operatore?.toString() || ''}\n                  onValueChange={(value) => handleInputChange('id_operatore', parseInt(value))}\n                >\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Seleziona operatore...\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {responsabili.map((resp) => (\n                      <SelectItem key={resp.id_responsabile} value={resp.id_responsabile.toString()}>\n                        {resp.nome_responsabile}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n\n              <div className=\"space-y-1\">\n                <Label htmlFor=\"id_strumento\" className=\"text-sm\">Strumento</Label>\n                <Select\n                  value={formData.id_strumento?.toString() || ''}\n                  onValueChange={(value) => {\n                    const strumento = strumenti.find(s => s.id_strumento === parseInt(value))\n                    handleInputChange('id_strumento', parseInt(value))\n                    if (strumento) {\n                      handleInputChange('strumento_utilizzato', `${strumento.marca} ${strumento.modello}`)\n                    }\n                  }}\n                >\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Seleziona strumento...\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {strumenti.map((strumento) => (\n                      <SelectItem key={strumento.id_strumento} value={strumento.id_strumento.toString()}>\n                        {strumento.nome} - {strumento.marca} {strumento.modello}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n\n              <div className=\"space-y-1\">\n                <Label htmlFor=\"tipo_certificato\" className=\"text-sm\">Tipo Certificato</Label>\n                <Select\n                  value={formData.tipo_certificato || 'SINGOLO'}\n                  onValueChange={(value) => handleInputChange('tipo_certificato', value)}\n                >\n                  <SelectTrigger>\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"SINGOLO\">Singolo</SelectItem>\n                    <SelectItem value=\"GRUPPO\">Gruppo</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Condizioni Ambientali Automatiche */}\n        {weatherData && (\n          <Card>\n            <CardHeader className=\"pb-2 px-4 pt-3\">\n              <CardTitle className=\"flex items-center gap-2 text-base\">\n                <Cloud className=\"h-4 w-4\" />\n                Condizioni Ambientali (Rilevate Automaticamente)\n              </CardTitle>\n              <CardDescription className=\"text-xs\">Dati meteorologici per il cantiere</CardDescription>\n            </CardHeader>\n            <CardContent className=\"pt-0 px-4 pb-3\">\n              <div className={`p-3 rounded-lg border ${weatherData.isDemo ? 'bg-yellow-50 border-yellow-200' : 'bg-green-50 border-green-200'}`}>\n                <div className=\"flex items-center gap-3\">\n                  {isLoadingWeather ? (\n                    <Loader2 className=\"h-4 w-4 animate-spin\" />\n                  ) : (\n                    <span className=\"text-lg\">🌤️</span>\n                  )}\n                  <div className=\"flex-1\">\n                    {!isWeatherOverride ? (\n                      <>\n                        <div className=\"flex items-center gap-4 text-sm\">\n                          <div>\n                            <span className=\"text-xs text-slate-600\">Temperatura:</span>\n                            <span className=\"ml-1 font-semibold text-sm\">{weatherData.temperature}°C</span>\n                          </div>\n                          <div>\n                            <span className=\"text-xs text-slate-600\">Umidità:</span>\n                            <span className=\"ml-1 font-semibold text-sm\">{weatherData.humidity}%</span>\n                          </div>\n                          {weatherData.city && (\n                            <div>\n                              <span className=\"text-xs text-slate-600\">Località:</span>\n                              <span className=\"ml-1 font-semibold text-sm\">{weatherData.city}</span>\n                            </div>\n                          )}\n                        </div>\n                        <div className=\"text-xs text-slate-500 mt-1\">\n                          {weatherData.isDemo ? 'Dati demo' : 'Dati live'} -\n                          {weatherData.source === 'cantiere_database' ? ' Database cantiere' : ' API meteo'}\n                        </div>\n                      </>\n                    ) : (\n                      <div className=\"grid grid-cols-2 gap-3\">\n                        <div className=\"space-y-1\">\n                          <Label htmlFor=\"temperatura_override\" className=\"text-xs\">Temperatura (°C)</Label>\n                          <Input\n                            id=\"temperatura_override\"\n                            type=\"number\"\n                            value={formData.temperatura_prova}\n                            onChange={(e) => handleInputChange('temperatura_prova', parseInt(e.target.value))}\n                            className=\"h-8 text-sm\"\n                          />\n                        </div>\n                        <div className=\"space-y-1\">\n                          <Label htmlFor=\"umidita_override\" className=\"text-xs\">Umidità (%)</Label>\n                          <Input\n                            id=\"umidita_override\"\n                            type=\"number\"\n                            min=\"0\"\n                            max=\"100\"\n                            value={formData.umidita_prova}\n                            onChange={(e) => handleInputChange('umidita_prova', parseInt(e.target.value))}\n                            className=\"h-8 text-sm\"\n                          />\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                  <Button\n                    type=\"button\"\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={() => setIsWeatherOverride(!isWeatherOverride)}\n                    className=\"ml-2\"\n                  >\n                    {isWeatherOverride ? (\n                      <>\n                        <X className=\"h-3 w-3 mr-1\" />\n                        Auto\n                      </>\n                    ) : (\n                      <>\n                        <Settings className=\"h-3 w-3 mr-1\" />\n                        Modifica\n                      </>\n                    )}\n                  </Button>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        )}\n\n        {/* Misurazioni */}\n        <Card>\n          <CardHeader className=\"pb-2 px-4 pt-3\">\n            <CardTitle className=\"flex items-center gap-2 text-base\">\n              <Settings className=\"h-4 w-4\" />\n              Misurazioni e Test\n            </CardTitle>\n            <CardDescription className=\"text-xs\">Risultati delle prove elettriche</CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-2 pt-0 px-4 pb-3\">\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-3\">\n              {/* Prima riga: Misurazioni principali */}\n              <div className=\"space-y-1\">\n                <Label htmlFor=\"valore_isolamento\">Isolamento (MΩ) *</Label>\n                <Input\n                  id=\"valore_isolamento\"\n                  type=\"number\"\n                  step=\"0.1\"\n                  value={formData.valore_isolamento}\n                  onChange={(e) => handleInputChange('valore_isolamento', e.target.value)}\n                  className={validationErrors.valore_isolamento ? 'border-red-500' : ''}\n                  placeholder=\"500\"\n                />\n                {validationErrors.valore_isolamento && (\n                  <p className=\"text-sm text-red-600\">{validationErrors.valore_isolamento}</p>\n                )}\n              </div>\n\n              <div className=\"space-y-1\">\n                <Label htmlFor=\"valore_continuita\">Continuità</Label>\n                <Select\n                  value={formData.valore_continuita}\n                  onValueChange={(value) => handleInputChange('valore_continuita', value)}\n                >\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Seleziona esito...\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"CONFORME\">✅ CONFORME</SelectItem>\n                    <SelectItem value=\"NON_CONFORME\">❌ NON CONFORME</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n\n              <div className=\"space-y-1\">\n                <Label htmlFor=\"valore_resistenza\">Resistenza (Ω)</Label>\n                <Input\n                  id=\"valore_resistenza\"\n                  type=\"number\"\n                  step=\"0.01\"\n                  value={formData.valore_resistenza}\n                  onChange={(e) => handleInputChange('valore_resistenza', e.target.value)}\n                  placeholder=\"0.5\"\n                />\n              </div>\n            </div>\n\n            {/* Seconda riga: Parametri di prova */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n              <div className=\"space-y-1\">\n                <Label htmlFor=\"tensione_prova_isolamento\">Tensione Prova (V)</Label>\n                <Input\n                  id=\"tensione_prova_isolamento\"\n                  type=\"number\"\n                  value={formData.tensione_prova_isolamento}\n                  onChange={(e) => handleInputChange('tensione_prova_isolamento', parseInt(e.target.value))}\n                  placeholder=\"500\"\n                />\n              </div>\n\n              {/* Temperatura e Umidità utilizzano automaticamente i dati dalle Condizioni Ambientali */}\n              <div className=\"flex items-center text-sm text-slate-600 bg-slate-50 p-3 rounded-lg\">\n                <Info className=\"h-4 w-4 mr-2\" />\n                Temperatura e umidità utilizzano i dati rilevati automaticamente\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Note e Stato */}\n        <Card>\n          <CardHeader className=\"pb-2 px-4 pt-3\">\n            <CardTitle className=\"text-base\">Note e Stato</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-2 px-4 pb-3\">\n            <div className=\"space-y-1\">\n              <Label htmlFor=\"note\" className=\"text-sm\">Note</Label>\n              <Textarea\n                id=\"note\"\n                value={formData.note}\n                onChange={(e) => handleInputChange('note', e.target.value)}\n                placeholder=\"Note aggiuntive sulla certificazione...\"\n                rows={3}\n              />\n            </div>\n\n            <div className=\"space-y-1\">\n              <Label htmlFor=\"stato_certificato\" className=\"text-sm\">Stato Certificato</Label>\n              <Select\n                value={formData.stato_certificato || 'BOZZA'}\n                onValueChange={(value) => handleInputChange('stato_certificato', value)}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"BOZZA\">Bozza</SelectItem>\n                  <SelectItem value=\"CONFORME\">Conforme</SelectItem>\n                  <SelectItem value=\"NON_CONFORME\">Non Conforme</SelectItem>\n                  <SelectItem value=\"CONFORME_CON_RISERVA\">Conforme con Riserva</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </CardContent>\n        </Card>\n\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAlBA;;;;;;;;;;;;AAsDe,SAAS,mBAAmB,EACzC,UAAU,EACV,cAAc,EACd,SAAS,EACT,iBAAiB,EACjB,SAAS,EACT,QAAQ,EACgB;IACxB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;QACjE,SAAS,qBAAqB;QAC9B,cAAc;QACd,sBAAsB;QACtB,cAAc;QACd,oBAAoB;QACpB,mBAAmB;QACnB,mBAAmB;QACnB,mBAAmB;QACnB,MAAM;QACN,kBAAkB;QAClB,mBAAmB;QACnB,yBAAyB;QACzB,mBAAmB;QACnB,2BAA2B;QAC3B,yBAAyB;QACzB,0BAA0B;QAC1B,mBAAmB;QACnB,eAAe;QACf,mBAAmB;IACrB;IAEA,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAClF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,SAAS,CAAC,CAAC;IACjB,MAAM,eAAe,UAAU,CAAC,CAAC;IAEjC,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;IACF,GAAG;QAAC;KAAW,EAAE,iCAAiC;;IAElD,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB;YAClB,YAAY;gBACV,SAAS,eAAe,OAAO;gBAC/B,cAAc,eAAe,YAAY;gBACzC,sBAAsB,eAAe,oBAAoB,IAAI;gBAC7D,cAAc,eAAe,YAAY;gBACzC,oBAAoB,eAAe,kBAAkB;gBACrD,mBAAmB,eAAe,iBAAiB,IAAI;gBACvD,mBAAmB,eAAe,iBAAiB,IAAI;gBACvD,mBAAmB,eAAe,iBAAiB,IAAI;gBACvD,MAAM,eAAe,IAAI,IAAI;gBAC7B,kBAAkB,eAAe,gBAAgB,IAAI;gBACrD,mBAAmB,eAAe,iBAAiB,IAAI;gBACvD,yBAAyB,eAAe,uBAAuB,IAAI;gBACnE,mBAAmB,eAAe,iBAAiB,IAAI;gBACvD,2BAA2B,eAAe,yBAAyB,IAAI;gBACvE,yBAAyB,eAAe,uBAAuB,IAAI;gBACnE,0BAA0B,eAAe,wBAAwB,IAAI;gBACrE,mBAAmB,eAAe,iBAAiB,IAAI,aAAa,eAAe;gBACnF,eAAe,eAAe,aAAa,IAAI,aAAa,YAAY;gBACxE,mBAAmB,eAAe,iBAAiB,IAAI;YACzD;QACF;IACF,GAAG;QAAC;KAAe;IAEnB,8DAA8D;IAC9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe,CAAC,gBAAgB;YAClC,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,mBAAmB,YAAY,WAAW,IAAI;oBAC9C,eAAe,YAAY,QAAQ,IAAI;gBACzC,CAAC;QACH;IACF,GAAG;QAAC;QAAa;KAAe;IAEhC,MAAM,kBAAkB;QACtB,IAAI;YACF,aAAa;YACb,MAAM,CAAC,UAAU,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACrD,iHAAA,CAAA,UAAO,CAAC,OAAO,CAAC;gBAChB,iHAAA,CAAA,kBAAe,CAAC,eAAe,CAAC;aACjC;YACD,QAAQ;YACR,gBAAgB;QAClB,EAAE,OAAO,OAAY;YACnB,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI;YACF,oBAAoB;YACpB,MAAM,WAAW,MAAM,iHAAA,CAAA,cAAW,CAAC,cAAc,CAAC;YAClD,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,eAAe,SAAS,IAAI;YAC5B,kEAAkE;YACpE;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,IAAI,CAAC,4CAA4C;QACzD,yDAAyD;QAC3D,SAAU;YACR,oBAAoB;QACtB;IACF;IAEA,uEAAuE;IACvE,MAAM,wBAAwB,OAAO,QAAgB,eAAuB,UAAU;QACpF,IAAI;YACF,IAAI,oBAAoB;YACxB,IAAI,kBAAkB;YAEtB,qCAAqC;YACrC,IAAI;gBACF,MAAM,iHAAA,CAAA,UAAO,CAAC,WAAW,CAAC,YAAY,QAAQ,YAAY;gBAC1D,oBAAoB;YACtB,EAAE,OAAO,OAAY;gBACnB,IAAI,MAAM,QAAQ,EAAE,MAAM,QAAQ,SAAS,kBAAkB;oBAC3D,oBAAoB;gBACtB,OAAO;oBACL,MAAM;gBACR;YACF;YAEA,mCAAmC;YACnC,IAAI;gBACF,MAAM,iHAAA,CAAA,UAAO,CAAC,WAAW,CAAC,YAAY,QAAQ,UAAU;gBACxD,kBAAkB;YACpB,EAAE,OAAO,OAAY;gBACnB,IAAI,MAAM,QAAQ,EAAE,MAAM,QAAQ,SAAS,kBAAkB;oBAC3D,kBAAkB;gBACpB,OAAO;oBACL,MAAM;gBACR;YACF;YAEA,OAAO,qBAAqB;QAC9B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,MAAM;QACR;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,SAAiC,CAAC;QAExC,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,OAAO,OAAO,GAAG;QACnB;QAEA,IAAI,CAAC,SAAS,iBAAiB,EAAE;YAC/B,OAAO,iBAAiB,GAAG;QAC7B,OAAO,IAAI,WAAW,SAAS,iBAAiB,IAAI,GAAG;YACrD,OAAO,iBAAiB,GAAG;QAC7B;QAEA,IAAI,SAAS,yBAAyB,IAAI,SAAS,yBAAyB,GAAG,GAAG;YAChF,OAAO,yBAAyB,GAAG;QACrC;QAEA,IAAI,SAAS,iBAAiB,IAAI,CAAC,SAAS,iBAAiB,GAAG,CAAC,MAAM,SAAS,iBAAiB,GAAG,EAAE,GAAG;YACvG,OAAO,iBAAiB,GAAG;QAC7B;QAEA,IAAI,SAAS,aAAa,IAAI,CAAC,SAAS,aAAa,GAAG,KAAK,SAAS,aAAa,GAAG,GAAG,GAAG;YAC1F,OAAO,aAAa,GAAG;QACzB;QAEA,oBAAoB;QACpB,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;IACxC;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,IAAI;YACF,YAAY;YACZ,SAAS;YAET,sEAAsE;YACtE,IAAI,CAAC,UAAU,SAAS,OAAO,EAAE;gBAC/B,MAAM,OAAO,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,SAAS,OAAO;gBAC1D,MAAM,wBAAwB,QAAQ,CAAC,KAAK,YAAY,IAAI,CAAC,MAAM;gBAEnE,IAAI,CAAC,uBAAuB;oBAC1B,MAAM,oBAAoB,CAAC,MAAM,gBAAgB,CAAC,MAAM,IAAI,kBACnC,CAAC,MAAM,gBAAgB,CAAC,MAAM,IAAI,4BAClC,CAAC,MAAM,gBAAgB,CAAC,MAAM,IAAI,0BAClC;oBAEzB,MAAM,WAAW,OAAO,OAAO,CAC7B,CAAC,oBAAoB,EAAE,SAAS,OAAO,CAAC,yCAAyC,CAAC,GAClF,CAAC,oBAAoB,EAAE,kBAAkB,IAAI,CAAC,GAC9C,CAAC,uGAAuG,CAAC,GACzG,CAAC,+EAA+E,CAAC;oBAGnF,IAAI,CAAC,UAAU;wBACb,YAAY;wBACZ;oBACF;oBAEA,kCAAkC;oBAClC,IAAI;wBACF,MAAM,sBAAsB,SAAS,OAAO,EAAE;wBAC9C,0CAA0C;wBAC1C,MAAM,cAAc,MAAM,iHAAA,CAAA,UAAO,CAAC,OAAO,CAAC;wBAC1C,QAAQ;oBACV,EAAE,OAAO,OAAY;wBACnB,SAAS,CAAC,oCAAoC,EAAE,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,EAAE;wBAC/F,YAAY;wBACZ;oBACF;gBACF;YACF;YAEA,uDAAuD;YACvD,MAAM,aAAa,WAAW,SAAS,iBAAiB,IAAI;YAC5D,MAAM,kBAAkB,cAAc,CAAC,SAAS,wBAAwB,IAAI,GAAG,IAAI,aAAa;YAEhG,MAAM,eAAe;gBACnB,GAAG,QAAQ;gBACX,mBAAmB,SAAS,iBAAiB,KAAK,UAAU,kBAAkB,SAAS,iBAAiB;gBACxG,mBAAmB,cAAc,CAAC,SAAS,wBAAwB,IAAI,GAAG,IAAI,aAAa;gBAC3F,0CAA0C;gBAC1C,mBAAmB,SAAS,iBAAiB,IAAI,aAAa,eAAe;gBAC7E,eAAe,SAAS,aAAa,IAAI,aAAa,YAAY;YACpE;YAEA,IAAI,UAAU,gBAAgB;gBAC5B,MAAM,iHAAA,CAAA,oBAAiB,CAAC,oBAAoB,CAAC,YAAY,eAAe,iBAAiB,EAAE;YAC7F,OAAO;gBACL,MAAM,iHAAA,CAAA,oBAAiB,CAAC,oBAAoB,CAAC,YAAY;YAC3D;YAEA,UAAU;QACZ,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,QAAQ,EAAE,MAAM,UAAU;QAC3C,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,oBAAoB,CAAC,OAAuC;QAChE,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAEhD,4CAA4C;QAC5C,IAAI,gBAAgB,CAAC,MAAM,EAAE;YAC3B,oBAAoB,CAAA;gBAClB,MAAM,YAAY;oBAAE,GAAG,IAAI;gBAAC;gBAC5B,OAAO,SAAS,CAAC,MAAM;gBACvB,OAAO;YACT;QACF;IACF;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;oBAAyB;;;;;;;;;;;;IAKpD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCACnB,SAAS,4BAA4B;;;;;;;8CAExC,8OAAC;oCAAE,WAAU;8CACV,SAAS,mDAAmD;;;;;;;;;;;;sCAIjE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS;gCAAc,UAAU;gCAAU,MAAK;;oCACrD,yBAAW,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAAiC,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAC/E,SAAS,aAAa;;;;;;;;;;;;;;;;;;;;;;;0BAO/B,8OAAC;gBAAI,WAAU;;oBACZ,uBACC,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAc,WAAU;;0CACrC,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC,iIAAA,CAAA,mBAAgB;0CAAE;;;;;;;;;;;;kCAIvB,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CAExC,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAY;;;;;;0DACjC,8OAAC,gIAAA,CAAA,kBAAe;gDAAC,WAAU;0DAAU;;;;;;;;;;;;kDAEvC,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAAU;;;;;;sEAC7C,8OAAC,kIAAA,CAAA,SAAM;4DACL,OAAO,SAAS,OAAO;4DACvB,eAAe,CAAC,QAAU,kBAAkB,WAAW;4DACvD,UAAU;;8EAEV,8OAAC,kIAAA,CAAA,gBAAa;oEAAC,WAAW,iBAAiB,OAAO,GAAG,mBAAmB;8EACtE,cAAA,8OAAC,kIAAA,CAAA,cAAW;wEAAC,aAAY;;;;;;;;;;;8EAE3B,8OAAC,kIAAA,CAAA,gBAAa;8EACX,KAAK,GAAG,CAAC,CAAC,qBACT,8OAAC,kIAAA,CAAA,aAAU;4EAAoB,OAAO,KAAK,OAAO;;gFAC/C,KAAK,OAAO;gFAAC;gFAAI,KAAK,SAAS;gFAAC;gFAAE,KAAK,OAAO;;2EADhC,KAAK,OAAO;;;;;;;;;;;;;;;;wDAMlC,iBAAiB,OAAO,kBACvB,8OAAC;4DAAE,WAAU;sEAAwB,iBAAiB,OAAO;;;;;;;;;;;;8DAIjE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAe,WAAU;sEAAU;;;;;;sEAClD,8OAAC,kIAAA,CAAA,SAAM;4DACL,OAAO,SAAS,YAAY,EAAE,cAAc;4DAC5C,eAAe,CAAC,QAAU,kBAAkB,gBAAgB,SAAS;;8EAErE,8OAAC,kIAAA,CAAA,gBAAa;8EACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;wEAAC,aAAY;;;;;;;;;;;8EAE3B,8OAAC,kIAAA,CAAA,gBAAa;8EACX,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,kIAAA,CAAA,aAAU;4EAA4B,OAAO,KAAK,eAAe,CAAC,QAAQ;sFACxE,KAAK,iBAAiB;2EADR,KAAK,eAAe;;;;;;;;;;;;;;;;;;;;;;8DAQ7C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAe,WAAU;sEAAU;;;;;;sEAClD,8OAAC,kIAAA,CAAA,SAAM;4DACL,OAAO,SAAS,YAAY,EAAE,cAAc;4DAC5C,eAAe,CAAC;gEACd,MAAM,YAAY,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK,SAAS;gEAClE,kBAAkB,gBAAgB,SAAS;gEAC3C,IAAI,WAAW;oEACb,kBAAkB,wBAAwB,GAAG,UAAU,KAAK,CAAC,CAAC,EAAE,UAAU,OAAO,EAAE;gEACrF;4DACF;;8EAEA,8OAAC,kIAAA,CAAA,gBAAa;8EACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;wEAAC,aAAY;;;;;;;;;;;8EAE3B,8OAAC,kIAAA,CAAA,gBAAa;8EACX,UAAU,GAAG,CAAC,CAAC,0BACd,8OAAC,kIAAA,CAAA,aAAU;4EAA8B,OAAO,UAAU,YAAY,CAAC,QAAQ;;gFAC5E,UAAU,IAAI;gFAAC;gFAAI,UAAU,KAAK;gFAAC;gFAAE,UAAU,OAAO;;2EADxC,UAAU,YAAY;;;;;;;;;;;;;;;;;;;;;;8DAQ/C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAmB,WAAU;sEAAU;;;;;;sEACtD,8OAAC,kIAAA,CAAA,SAAM;4DACL,OAAO,SAAS,gBAAgB,IAAI;4DACpC,eAAe,CAAC,QAAU,kBAAkB,oBAAoB;;8EAEhE,8OAAC,kIAAA,CAAA,gBAAa;8EACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;8EAEd,8OAAC,kIAAA,CAAA,gBAAa;;sFACZ,8OAAC,kIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAU;;;;;;sFAC5B,8OAAC,kIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAStC,6BACC,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAG/B,8OAAC,gIAAA,CAAA,kBAAe;gDAAC,WAAU;0DAAU;;;;;;;;;;;;kDAEvC,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAW,CAAC,sBAAsB,EAAE,YAAY,MAAM,GAAG,mCAAmC,gCAAgC;sDAC/H,cAAA,8OAAC;gDAAI,WAAU;;oDACZ,iCACC,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;6EAEnB,8OAAC;wDAAK,WAAU;kEAAU;;;;;;kEAE5B,8OAAC;wDAAI,WAAU;kEACZ,CAAC,kCACA;;8EACE,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;;8FACC,8OAAC;oFAAK,WAAU;8FAAyB;;;;;;8FACzC,8OAAC;oFAAK,WAAU;;wFAA8B,YAAY,WAAW;wFAAC;;;;;;;;;;;;;sFAExE,8OAAC;;8FACC,8OAAC;oFAAK,WAAU;8FAAyB;;;;;;8FACzC,8OAAC;oFAAK,WAAU;;wFAA8B,YAAY,QAAQ;wFAAC;;;;;;;;;;;;;wEAEpE,YAAY,IAAI,kBACf,8OAAC;;8FACC,8OAAC;oFAAK,WAAU;8FAAyB;;;;;;8FACzC,8OAAC;oFAAK,WAAU;8FAA8B,YAAY,IAAI;;;;;;;;;;;;;;;;;;8EAIpE,8OAAC;oEAAI,WAAU;;wEACZ,YAAY,MAAM,GAAG,cAAc;wEAAY;wEAC/C,YAAY,MAAM,KAAK,sBAAsB,uBAAuB;;;;;;;;yFAIzE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,iIAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAuB,WAAU;sFAAU;;;;;;sFAC1D,8OAAC,iIAAA,CAAA,QAAK;4EACJ,IAAG;4EACH,MAAK;4EACL,OAAO,SAAS,iBAAiB;4EACjC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,SAAS,EAAE,MAAM,CAAC,KAAK;4EAC/E,WAAU;;;;;;;;;;;;8EAGd,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,iIAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAmB,WAAU;sFAAU;;;;;;sFACtD,8OAAC,iIAAA,CAAA,QAAK;4EACJ,IAAG;4EACH,MAAK;4EACL,KAAI;4EACJ,KAAI;4EACJ,OAAO,SAAS,aAAa;4EAC7B,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,SAAS,EAAE,MAAM,CAAC,KAAK;4EAC3E,WAAU;;;;;;;;;;;;;;;;;;;;;;;kEAMpB,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,qBAAqB,CAAC;wDACrC,WAAU;kEAET,kCACC;;8EACE,8OAAC,4LAAA,CAAA,IAAC;oEAAC,WAAU;;;;;;gEAAiB;;yFAIhC;;8EACE,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAYrD,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGlC,8OAAC,gIAAA,CAAA,kBAAe;gDAAC,WAAU;0DAAU;;;;;;;;;;;;kDAEvC,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAoB;;;;;;0EACnC,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,iBAAiB;gEACjC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;gEACtE,WAAW,iBAAiB,iBAAiB,GAAG,mBAAmB;gEACnE,aAAY;;;;;;4DAEb,iBAAiB,iBAAiB,kBACjC,8OAAC;gEAAE,WAAU;0EAAwB,iBAAiB,iBAAiB;;;;;;;;;;;;kEAI3E,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAoB;;;;;;0EACnC,8OAAC,kIAAA,CAAA,SAAM;gEACL,OAAO,SAAS,iBAAiB;gEACjC,eAAe,CAAC,QAAU,kBAAkB,qBAAqB;;kFAEjE,8OAAC,kIAAA,CAAA,gBAAa;kFACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;4EAAC,aAAY;;;;;;;;;;;kFAE3B,8OAAC,kIAAA,CAAA,gBAAa;;0FACZ,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAW;;;;;;0FAC7B,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAe;;;;;;;;;;;;;;;;;;;;;;;;kEAKvC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAoB;;;;;;0EACnC,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,iBAAiB;gEACjC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;gEACtE,aAAY;;;;;;;;;;;;;;;;;;0DAMlB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAA4B;;;;;;0EAC3C,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,yBAAyB;gEACzC,UAAU,CAAC,IAAM,kBAAkB,6BAA6B,SAAS,EAAE,MAAM,CAAC,KAAK;gEACvF,aAAY;;;;;;;;;;;;kEAKhB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;0CAQzC,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAY;;;;;;;;;;;kDAEnC,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAO,WAAU;kEAAU;;;;;;kEAC1C,8OAAC,oIAAA,CAAA,WAAQ;wDACP,IAAG;wDACH,OAAO,SAAS,IAAI;wDACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;wDACzD,aAAY;wDACZ,MAAM;;;;;;;;;;;;0DAIV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAoB,WAAU;kEAAU;;;;;;kEACvD,8OAAC,kIAAA,CAAA,SAAM;wDACL,OAAO,SAAS,iBAAiB,IAAI;wDACrC,eAAe,CAAC,QAAU,kBAAkB,qBAAqB;;0EAEjE,8OAAC,kIAAA,CAAA,gBAAa;0EACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0EAEd,8OAAC,kIAAA,CAAA,gBAAa;;kFACZ,8OAAC,kIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAQ;;;;;;kFAC1B,8OAAC,kIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAW;;;;;;kFAC7B,8OAAC,kIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAe;;;;;;kFACjC,8OAAC,kIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW3D", "debugId": null}}]}