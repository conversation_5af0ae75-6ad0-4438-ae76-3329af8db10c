(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1669],{26126:(e,t,a)=>{"use strict";a.d(t,{E:()=>l});var s=a(95155);a(12115);var i=a(99708),r=a(74466),n=a(59434);let c=(0,r.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:a,asChild:r=!1,...l}=e,o=r?i.DX:"span";return(0,s.jsx)(o,{"data-slot":"badge",className:(0,n.cn)(c({variant:a}),t),...l})}},30285:(e,t,a)=>{"use strict";a.d(t,{$:()=>l});var s=a(95155);a(12115);var i=a(99708),r=a(74466),n=a(59434);let c=(0,r.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:a,size:r,asChild:l=!1,...o}=e,d=l?i.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,n.cn)(c({variant:a,size:r,className:t})),...o})}},38962:(e,t,a)=>{Promise.resolve().then(a.bind(a,97811))},40646:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},51154:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},59434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>r});var s=a(52596),i=a(39688);function r(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,i.QP)((0,s.$)(t))}},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>l,Wu:()=>o,ZB:()=>c,Zp:()=>r,aR:()=>n});var s=a(95155);a(12115);var i=a(59434);function r(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,i.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,i.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,i.cn)("leading-none font-semibold",t),...a})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,i.cn)("text-muted-foreground text-sm",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,i.cn)("px-6",t),...a})}},85339:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},97811:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p});var s=a(95155),i=a(12115),r=a(66695),n=a(30285),c=a(26126),l=a(40646),o=a(85339),d=a(75021),u=a(51154),m=a(57434),x=a(50589),h=a(38164),g=a(381),v=a(25731);function p(){let[e,t]=(0,i.useState)(!1),[a,p]=(0,i.useState)([]),[f]=(0,i.useState)(1),b=async()=>{var e,a,s,i,r,n,c,l,o,d;t(!0),p([]);let u=[];try{let e=await v._I.getWeatherData(f);u.push({test:"Dati Meteorologici",status:"success",message:"Temperatura: ".concat(e.data.temperature,"\xb0C, Umidit\xe0: ").concat(e.data.humidity,"%"),data:e.data})}catch(t){u.push({test:"Dati Meteorologici",status:"error",message:(null==(a=t.response)||null==(e=a.data)?void 0:e.detail)||t.message})}try{let e=await v.At.getCavi(f),t=e.filter(e=>3>(e.collegamenti||0));u.push({test:"Caricamento Cavi",status:"success",message:"".concat(e.length," cavi totali, ").concat(t.length," non completamente collegati"),data:{totali:e.length,nonCollegati:t.length}})}catch(e){u.push({test:"Caricamento Cavi",status:"error",message:(null==(i=e.response)||null==(s=i.data)?void 0:s.detail)||e.message})}try{let e=await v.kw.getStrumenti(f),t=e.filter(e=>"ATTIVO"===e.stato);u.push({test:"Caricamento Strumenti",status:"success",message:"".concat(e.length," strumenti totali, ").concat(t.length," attivi"),data:{totali:e.length,attivi:t.length}})}catch(e){u.push({test:"Caricamento Strumenti",status:"error",message:(null==(n=e.response)||null==(r=n.data)?void 0:r.detail)||e.message})}try{let e=await v.km.getCertificazioni(f),t=e.filter(e=>"CONFORME"===e.stato_certificato);u.push({test:"Caricamento Certificazioni",status:"success",message:"".concat(e.length," certificazioni totali, ").concat(t.length," conformi"),data:{totali:e.length,conformi:t.length}})}catch(e){u.push({test:"Caricamento Certificazioni",status:"error",message:(null==(l=e.response)||null==(c=l.data)?void 0:c.detail)||e.message})}try{let e=(await v.At.getCavi(f)).find(e=>3>(e.collegamenti||0));e?u.push({test:"Collegamento Automatico",status:"info",message:"Cavo ".concat(e.id_cavo," disponibile per test collegamento (collegamenti: ").concat(e.collegamenti||0,"/3)"),data:{cavoId:e.id_cavo,collegamenti:e.collegamenti||0}}):u.push({test:"Collegamento Automatico",status:"warning",message:"Nessun cavo disponibile per test collegamento (tutti gi\xe0 collegati)"})}catch(e){u.push({test:"Collegamento Automatico",status:"error",message:(null==(d=e.response)||null==(o=d.data)?void 0:o.detail)||e.message})}p(u),t(!1)},j=e=>{switch(e){case"success":return(0,s.jsx)(l.A,{className:"h-5 w-5 text-green-600"});case"error":return(0,s.jsx)(o.A,{className:"h-5 w-5 text-red-600"});case"warning":return(0,s.jsx)(o.A,{className:"h-5 w-5 text-yellow-600"});case"info":return(0,s.jsx)(d.A,{className:"h-5 w-5 text-blue-600"});default:return(0,s.jsx)(u.A,{className:"h-5 w-5 animate-spin"})}},N=e=>{switch(e){case"success":return(0,s.jsx)(c.E,{className:"bg-green-100 text-green-800 border-green-200",children:"Successo"});case"error":return(0,s.jsx)(c.E,{className:"bg-red-100 text-red-800 border-red-200",children:"Errore"});case"warning":return(0,s.jsx)(c.E,{className:"bg-yellow-100 text-yellow-800 border-yellow-200",children:"Attenzione"});case"info":return(0,s.jsx)(c.E,{className:"bg-blue-100 text-blue-800 border-blue-200",children:"Info"});default:return(0,s.jsx)(c.E,{variant:"outline",children:"In corso"})}};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h1",{className:"text-3xl font-bold text-slate-900 flex items-center gap-3",children:[(0,s.jsx)(d.A,{className:"h-8 w-8 text-blue-600"}),"Test Sistema Certificazioni"]}),(0,s.jsx)("p",{className:"text-slate-600 mt-1",children:"Verifica funzionalit\xe0 e automazioni CEI 64-8"})]}),(0,s.jsx)(n.$,{onClick:b,disabled:e,children:e?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(u.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Test in corso..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Avvia Test"]})})]}),(0,s.jsxs)(r.Zp,{children:[(0,s.jsxs)(r.aR,{children:[(0,s.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(m.A,{className:"h-5 w-5"}),"Funzionalit\xe0 Implementate"]}),(0,s.jsx)(r.BT,{children:"Sistema certificazioni CEI 64-8 con automazioni"})]}),(0,s.jsx)(r.Wu,{children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(x.A,{className:"h-4 w-4 text-blue-500"}),(0,s.jsx)("span",{className:"font-medium",children:"Dati Meteorologici Automatici"})]}),(0,s.jsx)("p",{className:"text-sm text-slate-600 ml-6",children:"Temperatura e umidit\xe0 rilevate automaticamente per il cantiere"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(h.A,{className:"h-4 w-4 text-green-500"}),(0,s.jsx)("span",{className:"font-medium",children:"Collegamento Automatico Cavi"})]}),(0,s.jsx)("p",{className:"text-sm text-slate-600 ml-6",children:"Collegamento automatico durante certificazione CEI 64-8"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(l.A,{className:"h-4 w-4 text-purple-500"}),(0,s.jsx)("span",{className:"font-medium",children:"Validazione Automatica"})]}),(0,s.jsx)("p",{className:"text-sm text-slate-600 ml-6",children:"Determinazione automatica conformit\xe0 in base ai valori"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(g.A,{className:"h-4 w-4 text-orange-500"}),(0,s.jsx)("span",{className:"font-medium",children:"Gestione Strumenti"})]}),(0,s.jsx)("p",{className:"text-sm text-slate-600 ml-6",children:"Tracking calibrazione e notifiche scadenze"})]})]})})]}),a.length>0&&(0,s.jsxs)(r.Zp,{children:[(0,s.jsxs)(r.aR,{children:[(0,s.jsx)(r.ZB,{children:"Risultati Test"}),(0,s.jsx)(r.BT,{children:"Verifica delle funzionalit\xe0 del sistema"})]}),(0,s.jsx)(r.Wu,{children:(0,s.jsx)("div",{className:"space-y-4",children:a.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-start gap-3 p-3 rounded-lg border",children:[j(e.status),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h4",{className:"font-medium",children:e.test}),N(e.status)]}),(0,s.jsx)("p",{className:"text-sm text-slate-600 mt-1",children:e.message}),e.data&&(0,s.jsx)("pre",{className:"text-xs bg-slate-100 p-2 rounded mt-2 overflow-auto",children:JSON.stringify(e.data,null,2)})]})]},t))})})]}),(0,s.jsxs)(r.Zp,{children:[(0,s.jsxs)(r.aR,{children:[(0,s.jsx)(r.ZB,{children:"Accesso al Sistema"}),(0,s.jsx)(r.BT,{children:"Link per testare l'interfaccia completa"})]}),(0,s.jsx)(r.Wu,{children:(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)(n.$,{asChild:!0,children:(0,s.jsxs)("a",{href:"/certificazioni",target:"_blank",children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Sistema Certificazioni"]})}),(0,s.jsx)(n.$,{variant:"outline",asChild:!0,children:(0,s.jsxs)("a",{href:"/cavi",target:"_blank",children:[(0,s.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Gestione Cavi"]})})]})})]})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[3455,3464,7697,8441,1684,7358],()=>t(38962)),_N_E=e.O()}]);