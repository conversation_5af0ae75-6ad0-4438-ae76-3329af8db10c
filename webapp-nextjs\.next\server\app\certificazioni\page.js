(()=>{var e={};e.id=992,e.ids=[992],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,s,a)=>{"use strict";a.d(s,{A0:()=>l,BF:()=>n,Hj:()=>c,XI:()=>r,nA:()=>d,nd:()=>o});var t=a(60687);a(43210);var i=a(4780);function r({className:e,...s}){return(0,t.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,t.jsx)("table",{"data-slot":"table",className:(0,i.cn)("w-full caption-bottom text-sm border-collapse",e),...s})})}function l({className:e,...s}){return(0,t.jsx)("thead",{"data-slot":"table-header",className:(0,i.cn)("[&_tr]:border-b",e),...s})}function n({className:e,...s}){return(0,t.jsx)("tbody",{"data-slot":"table-body",className:(0,i.cn)("[&_tr:last-child]:border-0",e),...s})}function c({className:e,...s}){return(0,t.jsx)("tr",{"data-slot":"table-row",className:(0,i.cn)("data-[state=selected]:bg-muted border-b",e),...s})}function o({className:e,...s}){return(0,t.jsx)("th",{"data-slot":"table-head",className:(0,i.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...s})}function d({className:e,...s}){return(0,t.jsx)("td",{"data-slot":"table-cell",className:(0,i.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...s})}},8819:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10002:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>o});var t=a(65239),i=a(48088),r=a(88170),l=a.n(r),n=a(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);a.d(s,c);let o={children:["",{children:["certificazioni",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,71792)),"C:\\CMS\\webapp-nextjs\\src\\app\\certificazioni\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\CMS\\webapp-nextjs\\src\\app\\certificazioni\\page.tsx"],x={require:a,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/certificazioni/page",pathname:"/certificazioni",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13861:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},22029:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>$});var t=a(60687),i=a(63213),r=a(43210),l=a(44493),n=a(29523),c=a(96834),o=a(89667),d=a(29844),x=a(6211),m=a(62185),h=a(41862),u=a(86561),j=a(10022),p=a(84027),v=a(53411),f=a(43649),N=a(99270),g=a(96474),b=a(5336),w=a(93613),y=a(48730),_=a(63143),z=a(88233),A=a(91136),C=a(91821),S=a(80013),I=a(34729),R=a(15079),T=a(11860),E=a(8819);function M({cantiereId:e,strumento:s,onSuccess:a,onCancel:i}){let[c,d]=(0,r.useState)({nome:"",marca:"",modello:"",numero_serie:"",data_calibrazione:"",data_scadenza_calibrazione:"",note:"",tipo_strumento:"MEGGER",ente_certificatore:"",numero_certificato_calibrazione:"",range_misura:"",precisione:"",stato_strumento:"ATTIVO"}),[x,u]=(0,r.useState)(!1),[j,v]=(0,r.useState)(""),[f,N]=(0,r.useState)({}),g=!!s,b=()=>{let e={};if(c.nome.trim()||(e.nome="Il nome \xe8 obbligatorio"),c.marca.trim()||(e.marca="La marca \xe8 obbligatoria"),c.modello.trim()||(e.modello="Il modello \xe8 obbligatorio"),c.numero_serie.trim()||(e.numero_serie="Il numero di serie \xe8 obbligatorio"),c.data_calibrazione||(e.data_calibrazione="La data di calibrazione \xe8 obbligatoria"),c.data_scadenza_calibrazione||(e.data_scadenza_calibrazione="La data di scadenza \xe8 obbligatoria"),c.data_calibrazione&&c.data_scadenza_calibrazione){let s=new Date(c.data_calibrazione);new Date(c.data_scadenza_calibrazione)<=s&&(e.data_scadenza_calibrazione="La data di scadenza deve essere successiva alla calibrazione")}return N(e),0===Object.keys(e).length},y=async t=>{if(t.preventDefault(),b())try{u(!0),v(""),g&&s?await m.kw.updateStrumento(e,s.id_strumento,c):await m.kw.createStrumento(e,c),a()}catch(e){v(e.response?.data?.detail||"Errore durante il salvataggio")}finally{u(!1)}},_=(e,s)=>{d(a=>({...a,[e]:s})),f[e]&&N(s=>{let a={...s};return delete a[e],a})},z=e=>{if(_("data_calibrazione",e),e&&!c.data_scadenza_calibrazione){let s=new Date(new Date(e));s.setFullYear(s.getFullYear()+1),_("data_scadenza_calibrazione",s.toISOString().split("T")[0])}};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold text-slate-900 flex items-center gap-3",children:[(0,t.jsx)(p.A,{className:"h-6 w-6 text-blue-600"}),g?"Modifica Strumento":"Nuovo Strumento"]}),(0,t.jsx)("p",{className:"text-slate-600 mt-1",children:g?"Modifica i dati dello strumento esistente":"Aggiungi un nuovo strumento di misura"})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(n.$,{variant:"outline",onClick:i,children:[(0,t.jsx)(T.A,{className:"h-4 w-4 mr-2"}),"Annulla"]}),(0,t.jsxs)(n.$,{onClick:y,disabled:x,children:[x?(0,t.jsx)(h.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(E.A,{className:"h-4 w-4 mr-2"}),g?"Aggiorna":"Salva"]})]})]}),(0,t.jsxs)("form",{onSubmit:y,className:"space-y-6",children:[(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsx)(l.ZB,{children:"Informazioni Base"}),(0,t.jsx)(l.BT,{children:"Dati identificativi dello strumento"})]}),(0,t.jsx)(l.Wu,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(S.J,{htmlFor:"nome",children:"Nome Strumento *"}),(0,t.jsx)(o.p,{id:"nome",value:c.nome,onChange:e=>_("nome",e.target.value),className:f.nome?"border-red-500":"",placeholder:"es. Megger MFT1741"}),f.nome&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:f.nome})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(S.J,{htmlFor:"tipo_strumento",children:"Tipo Strumento"}),(0,t.jsxs)(R.l6,{value:c.tipo_strumento,onValueChange:e=>_("tipo_strumento",e),children:[(0,t.jsx)(R.bq,{children:(0,t.jsx)(R.yv,{})}),(0,t.jsxs)(R.gC,{children:[(0,t.jsx)(R.eb,{value:"MEGGER",children:"Megger"}),(0,t.jsx)(R.eb,{value:"MULTIMETRO",children:"Multimetro"}),(0,t.jsx)(R.eb,{value:"OSCILLOSCOPIO",children:"Oscilloscopio"}),(0,t.jsx)(R.eb,{value:"ALTRO",children:"Altro"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(S.J,{htmlFor:"marca",children:"Marca *"}),(0,t.jsx)(o.p,{id:"marca",value:c.marca,onChange:e=>_("marca",e.target.value),className:f.marca?"border-red-500":"",placeholder:"es. Fluke, Megger, Keysight"}),f.marca&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:f.marca})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(S.J,{htmlFor:"modello",children:"Modello *"}),(0,t.jsx)(o.p,{id:"modello",value:c.modello,onChange:e=>_("modello",e.target.value),className:f.modello?"border-red-500":"",placeholder:"es. MFT1741, 87V"}),f.modello&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:f.modello})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(S.J,{htmlFor:"numero_serie",children:"Numero Serie *"}),(0,t.jsx)(o.p,{id:"numero_serie",value:c.numero_serie,onChange:e=>_("numero_serie",e.target.value),className:f.numero_serie?"border-red-500":"",placeholder:"Numero di serie univoco"}),f.numero_serie&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:f.numero_serie})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(S.J,{htmlFor:"stato_strumento",children:"Stato"}),(0,t.jsxs)(R.l6,{value:c.stato_strumento,onValueChange:e=>_("stato_strumento",e),children:[(0,t.jsx)(R.bq,{children:(0,t.jsx)(R.yv,{})}),(0,t.jsxs)(R.gC,{children:[(0,t.jsx)(R.eb,{value:"ATTIVO",children:"Attivo"}),(0,t.jsx)(R.eb,{value:"SCADUTO",children:"Scaduto"}),(0,t.jsx)(R.eb,{value:"FUORI_SERVIZIO",children:"Fuori Servizio"})]})]})]})]})})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsx)(l.ZB,{children:"Calibrazione"}),(0,t.jsx)(l.BT,{children:"Informazioni sulla calibrazione dello strumento"})]}),(0,t.jsx)(l.Wu,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(S.J,{htmlFor:"data_calibrazione",children:"Data Calibrazione *"}),(0,t.jsx)(o.p,{id:"data_calibrazione",type:"date",value:c.data_calibrazione,onChange:e=>z(e.target.value),className:f.data_calibrazione?"border-red-500":""}),f.data_calibrazione&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:f.data_calibrazione})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(S.J,{htmlFor:"data_scadenza_calibrazione",children:"Data Scadenza *"}),(0,t.jsx)(o.p,{id:"data_scadenza_calibrazione",type:"date",value:c.data_scadenza_calibrazione,onChange:e=>_("data_scadenza_calibrazione",e.target.value),className:f.data_scadenza_calibrazione?"border-red-500":""}),f.data_scadenza_calibrazione&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:f.data_scadenza_calibrazione})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(S.J,{htmlFor:"ente_certificatore",children:"Ente Certificatore"}),(0,t.jsx)(o.p,{id:"ente_certificatore",value:c.ente_certificatore,onChange:e=>_("ente_certificatore",e.target.value),placeholder:"es. LAT 123, ACCREDIA"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(S.J,{htmlFor:"numero_certificato_calibrazione",children:"Numero Certificato"}),(0,t.jsx)(o.p,{id:"numero_certificato_calibrazione",value:c.numero_certificato_calibrazione,onChange:e=>_("numero_certificato_calibrazione",e.target.value),placeholder:"Numero del certificato di calibrazione"})]})]})})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsx)(l.ZB,{children:"Specifiche Tecniche"}),(0,t.jsx)(l.BT,{children:"Caratteristiche tecniche dello strumento"})]}),(0,t.jsxs)(l.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(S.J,{htmlFor:"range_misura",children:"Range di Misura"}),(0,t.jsx)(o.p,{id:"range_misura",value:c.range_misura,onChange:e=>_("range_misura",e.target.value),placeholder:"es. 0-1000V, 0-20A"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(S.J,{htmlFor:"precisione",children:"Precisione"}),(0,t.jsx)(o.p,{id:"precisione",value:c.precisione,onChange:e=>_("precisione",e.target.value),placeholder:"es. \xb10.1%, \xb12 digit"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(S.J,{htmlFor:"note",children:"Note"}),(0,t.jsx)(I.T,{id:"note",value:c.note,onChange:e=>_("note",e.target.value),placeholder:"Note aggiuntive sullo strumento...",rows:3})]})]})]}),j&&(0,t.jsxs)(C.Fc,{className:"border-red-200 bg-red-50",children:[(0,t.jsx)(w.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)(C.TN,{className:"text-red-800",children:j})]})]})]})}function F({cantiereId:e,strumenti:s,onUpdate:a}){let[i,d]=(0,r.useState)(""),[u,j]=(0,r.useState)(!1),[v,f]=(0,r.useState)(null),[A,S]=(0,r.useState)(!1),[I,R]=(0,r.useState)(""),T=e=>{f(e),j(!0)},E=async s=>{if(confirm("Sei sicuro di voler eliminare questo strumento?"))try{S(!0),await m.kw.deleteStrumento(e,s),a()}catch(e){R(e.response?.data?.detail||"Errore durante l'eliminazione")}finally{S(!1)}},F=e=>{let s=new Date,a=Math.ceil((new Date(e.data_scadenza_calibrazione).getTime()-s.getTime())/864e5);return"FUORI_SERVIZIO"===e.stato_strumento?(0,t.jsx)(c.E,{className:"bg-gray-100 text-gray-800 border-gray-200",children:"Fuori Servizio"}):a<0?(0,t.jsx)(c.E,{className:"bg-red-100 text-red-800 border-red-200",children:"Scaduto"}):a<=30?(0,t.jsx)(c.E,{className:"bg-yellow-100 text-yellow-800 border-yellow-200",children:"In Scadenza"}):(0,t.jsx)(c.E,{className:"bg-green-100 text-green-800 border-green-200",children:"Attivo"})},k=e=>{let s=new Date,a=Math.ceil((new Date(e.data_scadenza_calibrazione).getTime()-s.getTime())/864e5);return"FUORI_SERVIZIO"===e.stato_strumento||a<0?(0,t.jsx)(w.A,{className:"h-4 w-4 text-red-500"}):a<=30?(0,t.jsx)(y.A,{className:"h-4 w-4 text-yellow-500"}):(0,t.jsx)(b.A,{className:"h-4 w-4 text-green-500"})},D=s.filter(e=>{let s=i.toLowerCase();return e.nome?.toLowerCase().includes(s)||e.marca?.toLowerCase().includes(s)||e.modello?.toLowerCase().includes(s)||e.numero_serie?.toLowerCase().includes(s)}),O={totali:s.length,attivi:s.filter(e=>{let s=new Date,a=new Date(e.data_scadenza_calibrazione);return"ATTIVO"===e.stato_strumento&&a>s}).length,scaduti:s.filter(e=>{let s=new Date;return new Date(e.data_scadenza_calibrazione)<=s}).length,in_scadenza:s.filter(e=>{let s=new Date,a=Math.ceil((new Date(e.data_scadenza_calibrazione).getTime()-s.getTime())/864e5);return a>0&&a<=30}).length};return u?(0,t.jsx)(M,{cantiereId:e,strumento:v,onSuccess:()=>{j(!1),a()},onCancel:()=>j(!1)}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold text-slate-900 flex items-center gap-3",children:[(0,t.jsx)(p.A,{className:"h-6 w-6 text-blue-600"}),"Gestione Strumenti"]}),(0,t.jsx)("p",{className:"text-slate-600 mt-1",children:"Strumenti di misura e calibrazione"})]}),(0,t.jsxs)(n.$,{onClick:()=>{f(null),j(!0)},children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Nuovo Strumento"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Totali"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:O.totali})]}),(0,t.jsx)(p.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Attivi"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-green-600",children:O.attivi})]}),(0,t.jsx)(b.A,{className:"h-8 w-8 text-green-500"})]})})}),(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"In Scadenza"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:O.in_scadenza})]}),(0,t.jsx)(y.A,{className:"h-8 w-8 text-yellow-500"})]})})}),(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Scaduti"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-red-600",children:O.scaduti})]}),(0,t.jsx)(w.A,{className:"h-8 w-8 text-red-500"})]})})})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{children:(0,t.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(N.A,{className:"h-5 w-5"}),"Ricerca Strumenti"]})}),(0,t.jsx)(l.Wu,{children:(0,t.jsx)(o.p,{placeholder:"Cerca per nome, marca, modello o numero serie...",value:i,onChange:e=>d(e.target.value)})})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsxs)(l.ZB,{children:["Elenco Strumenti (",D.length,")"]}),(0,t.jsx)(l.BT,{children:"Gestione strumenti di misura e certificazione"})]}),(0,t.jsx)(l.Wu,{children:(0,t.jsx)("div",{className:"rounded-md border",children:(0,t.jsxs)(x.XI,{children:[(0,t.jsx)(x.A0,{children:(0,t.jsxs)(x.Hj,{children:[(0,t.jsx)(x.nd,{children:"Nome"}),(0,t.jsx)(x.nd,{children:"Marca/Modello"}),(0,t.jsx)(x.nd,{children:"Numero Serie"}),(0,t.jsx)(x.nd,{children:"Tipo"}),(0,t.jsx)(x.nd,{children:"Calibrazione"}),(0,t.jsx)(x.nd,{children:"Scadenza"}),(0,t.jsx)(x.nd,{children:"Stato"}),(0,t.jsx)(x.nd,{children:"Azioni"})]})}),(0,t.jsx)(x.BF,{children:A?(0,t.jsx)(x.Hj,{children:(0,t.jsx)(x.nA,{colSpan:8,className:"text-center py-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 animate-spin"}),"Caricamento..."]})})}):0===D.length?(0,t.jsx)(x.Hj,{children:(0,t.jsx)(x.nA,{colSpan:8,className:"text-center py-8 text-slate-500",children:"Nessuno strumento trovato"})}):D.map(e=>{let s=new Date,a=Math.ceil((new Date(e.data_scadenza_calibrazione).getTime()-s.getTime())/864e5);return(0,t.jsxs)(x.Hj,{children:[(0,t.jsx)(x.nA,{className:"font-medium",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[k(e),e.nome]})}),(0,t.jsx)(x.nA,{children:(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:e.marca}),(0,t.jsx)("div",{className:"text-sm text-slate-500",children:e.modello})]})}),(0,t.jsx)(x.nA,{className:"font-mono text-sm",children:e.numero_serie}),(0,t.jsx)(x.nA,{children:(0,t.jsx)(c.E,{variant:"outline",children:e.tipo_strumento||"N/A"})}),(0,t.jsx)(x.nA,{children:new Date(e.data_calibrazione).toLocaleDateString("it-IT")}),(0,t.jsx)(x.nA,{children:(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{children:new Date(e.data_scadenza_calibrazione).toLocaleDateString("it-IT")}),a>0&&a<=30&&(0,t.jsxs)("div",{className:"text-xs text-yellow-600",children:[a," giorni rimanenti"]}),a<0&&(0,t.jsxs)("div",{className:"text-xs text-red-600",children:["Scaduto da ",Math.abs(a)," giorni"]})]})}),(0,t.jsx)(x.nA,{children:F(e)}),(0,t.jsx)(x.nA,{children:(0,t.jsxs)("div",{className:"flex gap-1",children:[(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>T(e),children:(0,t.jsx)(_.A,{className:"h-4 w-4"})}),(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>E(e.id_strumento),children:(0,t.jsx)(z.A,{className:"h-4 w-4"})})]})})]},e.id_strumento)})})]})})})]}),I&&(0,t.jsxs)(C.Fc,{className:"border-red-200 bg-red-50",children:[(0,t.jsx)(w.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)(C.TN,{className:"text-red-800",children:I})]})]})}var k=a(78122),D=a(31158);function O({cantiereId:e,rapporti:s,onUpdate:a}){let[i,d]=(0,r.useState)(""),[u,p]=(0,r.useState)(!1),[f,y]=(0,r.useState)(""),A=e=>{console.log("Modifica rapporto",e)},S=async s=>{if(confirm("Sei sicuro di voler eliminare questo rapporto?"))try{p(!0),await m.l9.deleteRapporto(e,s),a()}catch(e){y(e.response?.data?.detail||"Errore durante l'eliminazione")}finally{p(!1)}},I=async s=>{try{p(!0),await m.l9.aggiornaStatistiche(e,s),a()}catch(e){y(e.response?.data?.detail||"Errore durante l'aggiornamento")}finally{p(!1)}},R=e=>{switch(e?.toLowerCase()){case"completato":return(0,t.jsx)(c.E,{className:"bg-green-100 text-green-800 border-green-200",children:"Completato"});case"approvato":return(0,t.jsx)(c.E,{className:"bg-blue-100 text-blue-800 border-blue-200",children:"Approvato"});case"bozza":return(0,t.jsx)(c.E,{className:"bg-gray-100 text-gray-800 border-gray-200",children:"Bozza"});default:return(0,t.jsx)(c.E,{className:"bg-gray-100 text-gray-800 border-gray-200",children:"Da Verificare"})}},T=e=>{if(0===e.numero_cavi_totali)return(0,t.jsx)(c.E,{variant:"outline",children:"Nessun Cavo"});let s=e.numero_cavi_conformi/e.numero_cavi_totali*100;return 100===s?(0,t.jsx)(c.E,{className:"bg-green-100 text-green-800 border-green-200",children:"100% Conforme"}):s>=90?(0,t.jsxs)(c.E,{className:"bg-yellow-100 text-yellow-800 border-yellow-200",children:[s.toFixed(1),"% Conforme"]}):(0,t.jsxs)(c.E,{className:"bg-red-100 text-red-800 border-red-200",children:[s.toFixed(1),"% Conforme"]})},E=s.filter(e=>{let s=i.toLowerCase();return e.numero_rapporto?.toLowerCase().includes(s)||e.nome_progetto?.toLowerCase().includes(s)||e.cliente_finale?.toLowerCase().includes(s)}),M={totali:s.length,completati:s.filter(e=>"COMPLETATO"===e.stato_rapporto).length,approvati:s.filter(e=>"APPROVATO"===e.stato_rapporto).length,bozze:s.filter(e=>"BOZZA"===e.stato_rapporto).length,cavi_totali:s.reduce((e,s)=>e+s.numero_cavi_totali,0),cavi_conformi:s.reduce((e,s)=>e+s.numero_cavi_conformi,0)};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold text-slate-900 flex items-center gap-3",children:[(0,t.jsx)(v.A,{className:"h-6 w-6 text-blue-600"}),"Rapporti Generali di Collaudo"]}),(0,t.jsx)("p",{className:"text-slate-600 mt-1",children:"Gestione rapporti generali CEI 64-8"})]}),(0,t.jsxs)(n.$,{onClick:()=>{console.log("Crea nuovo rapporto")},children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Nuovo Rapporto"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Totali"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:M.totali})]}),(0,t.jsx)(j.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Completati"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-green-600",children:M.completati})]}),(0,t.jsx)(b.A,{className:"h-8 w-8 text-green-500"})]})})}),(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Approvati"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:M.approvati})]}),(0,t.jsx)(v.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Cavi Totali"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:M.cavi_totali})]}),(0,t.jsx)(j.A,{className:"h-8 w-8 text-slate-500"})]})})}),(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Conformit\xe0"}),(0,t.jsxs)("p",{className:"text-2xl font-bold text-green-600",children:[M.cavi_totali>0?(M.cavi_conformi/M.cavi_totali*100).toFixed(1):0,"%"]})]}),(0,t.jsx)(b.A,{className:"h-8 w-8 text-green-500"})]})})})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{children:(0,t.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(N.A,{className:"h-5 w-5"}),"Ricerca Rapporti"]})}),(0,t.jsx)(l.Wu,{children:(0,t.jsx)(o.p,{placeholder:"Cerca per numero rapporto, progetto o cliente...",value:i,onChange:e=>d(e.target.value)})})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsxs)(l.ZB,{children:["Elenco Rapporti (",E.length,")"]}),(0,t.jsx)(l.BT,{children:"Gestione rapporti generali di collaudo"})]}),(0,t.jsx)(l.Wu,{children:(0,t.jsx)("div",{className:"rounded-md border",children:(0,t.jsxs)(x.XI,{children:[(0,t.jsx)(x.A0,{children:(0,t.jsxs)(x.Hj,{children:[(0,t.jsx)(x.nd,{children:"Numero Rapporto"}),(0,t.jsx)(x.nd,{children:"Progetto"}),(0,t.jsx)(x.nd,{children:"Cliente"}),(0,t.jsx)(x.nd,{children:"Data"}),(0,t.jsx)(x.nd,{children:"Cavi"}),(0,t.jsx)(x.nd,{children:"Conformit\xe0"}),(0,t.jsx)(x.nd,{children:"Stato"}),(0,t.jsx)(x.nd,{children:"Azioni"})]})}),(0,t.jsx)(x.BF,{children:u?(0,t.jsx)(x.Hj,{children:(0,t.jsx)(x.nA,{colSpan:8,className:"text-center py-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 animate-spin"}),"Caricamento..."]})})}):0===E.length?(0,t.jsx)(x.Hj,{children:(0,t.jsx)(x.nA,{colSpan:8,className:"text-center py-8 text-slate-500",children:"Nessun rapporto trovato"})}):E.map(e=>(0,t.jsxs)(x.Hj,{children:[(0,t.jsx)(x.nA,{className:"font-medium",children:e.numero_rapporto}),(0,t.jsx)(x.nA,{children:(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:e.nome_progetto||"-"}),(0,t.jsx)("div",{className:"text-sm text-slate-500",children:e.societa_installatrice||"-"})]})}),(0,t.jsx)(x.nA,{children:e.cliente_finale||"-"}),(0,t.jsx)(x.nA,{children:new Date(e.data_rapporto).toLocaleDateString("it-IT")}),(0,t.jsx)(x.nA,{children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"font-medium",children:e.numero_cavi_totali}),(0,t.jsxs)("div",{className:"text-xs text-slate-500",children:[e.numero_cavi_conformi,"C / ",e.numero_cavi_non_conformi,"NC"]})]})}),(0,t.jsx)(x.nA,{children:T(e)}),(0,t.jsx)(x.nA,{children:R(e.stato_rapporto)}),(0,t.jsx)(x.nA,{children:(0,t.jsxs)("div",{className:"flex gap-1",children:[(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>A(e),title:"Visualizza/Modifica",children:(0,t.jsx)(_.A,{className:"h-4 w-4"})}),(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>I(e.id_rapporto),title:"Aggiorna Statistiche",children:(0,t.jsx)(k.A,{className:"h-4 w-4"})}),(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>{},title:"Genera PDF",children:(0,t.jsx)(D.A,{className:"h-4 w-4"})}),(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>S(e.id_rapporto),title:"Elimina",children:(0,t.jsx)(z.A,{className:"h-4 w-4"})})]})})]},e.id_rapporto))})]})})})]}),f&&(0,t.jsxs)(C.Fc,{className:"border-red-200 bg-red-50",children:[(0,t.jsx)(w.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)(C.TN,{className:"text-red-800",children:f})]})]})}var Z=a(46657),L=a(28947),B=a(25541);function P({stats:e,detailed:s=!1}){let a=e.totali>0?e.conformi/e.totali*100:0,i=e.totali>0?(e.conformi+e.non_conformi+e.con_riserva)/e.totali*100:0;return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Totali"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:e.totali})]}),(0,t.jsx)(j.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Conformi"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-green-600",children:e.conformi}),(0,t.jsx)("p",{className:"text-xs text-slate-500",children:e.totali>0?`${(e.conformi/e.totali*100).toFixed(1)}%`:"0%"})]}),(0,t.jsx)(b.A,{className:"h-8 w-8 text-green-500"})]})})}),(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Non Conformi"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-red-600",children:e.non_conformi}),(0,t.jsx)("p",{className:"text-xs text-slate-500",children:e.totali>0?`${(e.non_conformi/e.totali*100).toFixed(1)}%`:"0%"})]}),(0,t.jsx)(w.A,{className:"h-8 w-8 text-red-500"})]})})}),(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Bozze"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:e.bozze}),(0,t.jsx)("p",{className:"text-xs text-slate-500",children:e.totali>0?`${(e.bozze/e.totali*100).toFixed(1)}%`:"0%"})]}),(0,t.jsx)(y.A,{className:"h-8 w-8 text-yellow-500"})]})})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(L.A,{className:"h-5 w-5 text-blue-600"}),"Tasso di Conformit\xe0"]}),(0,t.jsx)(l.BT,{children:"Percentuale di certificazioni conformi sul totale"})]}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"Conformit\xe0"}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[a>=95?(0,t.jsx)(b.A,{className:"h-5 w-5 text-green-600"}):a>=85?(0,t.jsx)(f.A,{className:"h-5 w-5 text-yellow-600"}):(0,t.jsx)(w.A,{className:"h-5 w-5 text-red-600"}),(0,t.jsxs)("span",{className:`text-lg font-bold ${a>=95?"text-green-600":a>=85?"text-yellow-600":"text-red-600"}`,children:[a.toFixed(1),"%"]})]})]}),(0,t.jsx)(Z.k,{value:a,className:"h-2"}),(0,t.jsxs)("div",{className:"flex justify-between text-xs text-slate-500",children:[(0,t.jsx)("span",{children:"Target: 95%"}),(0,t.jsxs)("span",{children:[e.conformi," / ",e.totali]})]})]})})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(B.A,{className:"h-5 w-5 text-green-600"}),"Completamento Certificazioni"]}),(0,t.jsx)(l.BT,{children:"Percentuale di certificazioni completate (non bozze)"})]}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"Completamento"}),(0,t.jsxs)("span",{className:"text-lg font-bold text-blue-600",children:[i.toFixed(1),"%"]})]}),(0,t.jsx)(Z.k,{value:i,className:"h-2"}),(0,t.jsxs)("div",{className:"flex justify-between text-xs text-slate-500",children:[(0,t.jsxs)("span",{children:["Completate: ",e.conformi+e.non_conformi+e.con_riserva]}),(0,t.jsxs)("span",{children:["Bozze: ",e.bozze]})]})]})})]})]}),s&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{children:(0,t.jsx)(l.ZB,{className:"text-lg",children:"Distribuzione Stati"})}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),(0,t.jsx)("span",{className:"text-sm",children:"Conformi"})]}),(0,t.jsx)(c.E,{variant:"outline",className:"text-green-600 border-green-200",children:e.conformi})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-red-500 rounded-full"}),(0,t.jsx)("span",{className:"text-sm",children:"Non Conformi"})]}),(0,t.jsx)(c.E,{variant:"outline",className:"text-red-600 border-red-200",children:e.non_conformi})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-yellow-500 rounded-full"}),(0,t.jsx)("span",{className:"text-sm",children:"Con Riserva"})]}),(0,t.jsx)(c.E,{variant:"outline",className:"text-yellow-600 border-yellow-200",children:e.con_riserva})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-gray-500 rounded-full"}),(0,t.jsx)("span",{className:"text-sm",children:"Bozze"})]}),(0,t.jsx)(c.E,{variant:"outline",className:"text-gray-600 border-gray-200",children:e.bozze})]})]})})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{children:(0,t.jsx)(l.ZB,{className:"text-lg",children:"Indicatori Qualit\xe0"})}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,t.jsx)("span",{children:"Tasso Successo"}),(0,t.jsxs)("span",{className:"font-medium",children:[e.totali>0?((e.conformi+e.con_riserva)/e.totali*100).toFixed(1):0,"%"]})]}),(0,t.jsx)(Z.k,{value:e.totali>0?(e.conformi+e.con_riserva)/e.totali*100:0,className:"h-2"})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,t.jsx)("span",{children:"Tasso Fallimento"}),(0,t.jsxs)("span",{className:"font-medium text-red-600",children:[e.totali>0?(e.non_conformi/e.totali*100).toFixed(1):0,"%"]})]}),(0,t.jsx)(Z.k,{value:e.totali>0?e.non_conformi/e.totali*100:0,className:"h-2"})]})]})})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{children:(0,t.jsxs)(l.ZB,{className:"text-lg flex items-center gap-2",children:[(0,t.jsx)(u.A,{className:"h-5 w-5 text-yellow-500"}),"Valutazione Complessiva"]})}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("div",{className:"text-center space-y-2",children:[(0,t.jsx)("div",{className:"text-3xl font-bold",children:a>=95?"\uD83C\uDFC6":a>=85?"⭐":a>=70?"\uD83D\uDC4D":"⚠️"}),(0,t.jsx)("div",{className:"text-lg font-semibold",children:a>=95?"Eccellente":a>=85?"Buono":a>=70?"Sufficiente":"Da Migliorare"}),(0,t.jsx)("div",{className:"text-sm text-slate-600",children:a>=95?"Qualit\xe0 certificazioni ottimale":a>=85?"Qualit\xe0 certificazioni buona":a>=70?"Qualit\xe0 certificazioni accettabile":"Necessario miglioramento qualit\xe0"})]})})]})]}),(a<95||e.bozze>0)&&(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{children:(0,t.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(f.A,{className:"h-5 w-5 text-yellow-600"}),"Raccomandazioni"]})}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("div",{className:"space-y-2",children:[a<95&&(0,t.jsxs)("div",{className:"flex items-start gap-2 text-sm",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-yellow-500 rounded-full mt-2"}),(0,t.jsxs)("span",{children:["Il tasso di conformit\xe0 \xe8 del ",a.toFixed(1),"%. Obiettivo raccomandato: 95%. Verificare procedure di test e qualit\xe0 installazioni."]})]}),e.bozze>0&&(0,t.jsxs)("div",{className:"flex items-start gap-2 text-sm",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full mt-2"}),(0,t.jsxs)("span",{children:["Ci sono ",e.bozze," certificazioni in bozza. Completare le certificazioni per avere dati accurati."]})]}),e.non_conformi>e.conformi&&(0,t.jsxs)("div",{className:"flex items-start gap-2 text-sm",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full mt-2"}),(0,t.jsx)("span",{children:"Il numero di certificazioni non conformi supera quelle conformi. Rivedere urgentemente le procedure di installazione e test."})]})]})})]})]})]})}var W=a(13861);function q({cantiereId:e,nonConformita:s,onSuccess:a,onCancel:i}){let[c,d]=(0,r.useState)({id_cavo:"",tipo_non_conformita:"",descrizione:"",severita:"MEDIA",stato:"APERTA",responsabile_rilevazione:"",data_rilevazione:new Date().toISOString().split("T")[0],azione_correttiva:"",responsabile_risoluzione:"",data_risoluzione:null,note:""}),[x,u]=(0,r.useState)([]),[j,p]=(0,r.useState)([]),[v,N]=(0,r.useState)(!1),[g,b]=(0,r.useState)(!1),[y,_]=(0,r.useState)(""),[z,A]=(0,r.useState)({}),M=!!s,F=()=>{let e={};return c.id_cavo||(e.id_cavo="ID Cavo \xe8 obbligatorio"),c.tipo_non_conformita||(e.tipo_non_conformita="Tipo non conformit\xe0 \xe8 obbligatorio"),c.descrizione||(e.descrizione="Descrizione \xe8 obbligatoria"),c.responsabile_rilevazione||(e.responsabile_rilevazione="Responsabile rilevazione \xe8 obbligatorio"),A(e),0===Object.keys(e).length},k=async t=>{if(t.preventDefault(),F())try{b(!0),_("");let t={...c,data_risoluzione:c.data_risoluzione||null};M&&s?await m.om.updateNonConformita(e,s.id_non_conformita,t):await m.om.createNonConformita(e,t),a()}catch(e){_(e.response?.data?.detail||"Errore durante il salvataggio")}finally{b(!1)}},D=(e,s)=>{d(a=>({...a,[e]:s})),z[e]&&A(s=>{let a={...s};return delete a[e],a})};return v?(0,t.jsx)("div",{className:"flex items-center justify-center p-8",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 animate-spin"}),"Caricamento dati..."]})}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsxs)("div",{children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold text-slate-900 flex items-center gap-3",children:[(0,t.jsx)(f.A,{className:"h-6 w-6 text-red-600"}),M?"Modifica Non Conformit\xe0":"Nuova Non Conformit\xe0"]}),(0,t.jsx)("p",{className:"text-slate-600 mt-1",children:M?"Aggiorna i dettagli della non conformit\xe0":"Registra una nuova non conformit\xe0"})]})}),(0,t.jsxs)("form",{onSubmit:k,className:"space-y-6",children:[(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsx)(l.ZB,{children:"Informazioni Base"}),(0,t.jsx)(l.BT,{children:"Dettagli principali della non conformit\xe0"})]}),(0,t.jsxs)(l.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(S.J,{htmlFor:"id_cavo",children:"ID Cavo *"}),(0,t.jsxs)(R.l6,{value:c.id_cavo,onValueChange:e=>D("id_cavo",e),children:[(0,t.jsx)(R.bq,{className:z.id_cavo?"border-red-500":"",children:(0,t.jsx)(R.yv,{placeholder:"Seleziona cavo"})}),(0,t.jsx)(R.gC,{children:x.map(e=>(0,t.jsxs)(R.eb,{value:e.id_cavo,children:[e.id_cavo," ",e.tipologia&&`- ${e.tipologia}`]},e.id_cavo))})]}),z.id_cavo&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:z.id_cavo})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(S.J,{htmlFor:"tipo_non_conformita",children:"Tipo Non Conformit\xe0 *"}),(0,t.jsxs)(R.l6,{value:c.tipo_non_conformita,onValueChange:e=>D("tipo_non_conformita",e),children:[(0,t.jsx)(R.bq,{className:z.tipo_non_conformita?"border-red-500":"",children:(0,t.jsx)(R.yv,{placeholder:"Seleziona tipo"})}),(0,t.jsxs)(R.gC,{children:[(0,t.jsx)(R.eb,{value:"ISOLAMENTO",children:"Isolamento"}),(0,t.jsx)(R.eb,{value:"CONTINUITA",children:"Continuit\xe0"}),(0,t.jsx)(R.eb,{value:"RESISTENZA",children:"Resistenza"}),(0,t.jsx)(R.eb,{value:"INSTALLAZIONE",children:"Installazione"}),(0,t.jsx)(R.eb,{value:"COLLEGAMENTO",children:"Collegamento"}),(0,t.jsx)(R.eb,{value:"DOCUMENTAZIONE",children:"Documentazione"}),(0,t.jsx)(R.eb,{value:"ALTRO",children:"Altro"})]})]}),z.tipo_non_conformita&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:z.tipo_non_conformita})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(S.J,{htmlFor:"severita",children:"Severit\xe0"}),(0,t.jsxs)(R.l6,{value:c.severita,onValueChange:e=>D("severita",e),children:[(0,t.jsx)(R.bq,{children:(0,t.jsx)(R.yv,{})}),(0,t.jsxs)(R.gC,{children:[(0,t.jsx)(R.eb,{value:"BASSA",children:"Bassa"}),(0,t.jsx)(R.eb,{value:"MEDIA",children:"Media"}),(0,t.jsx)(R.eb,{value:"ALTA",children:"Alta"}),(0,t.jsx)(R.eb,{value:"CRITICA",children:"Critica"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(S.J,{htmlFor:"stato",children:"Stato"}),(0,t.jsxs)(R.l6,{value:c.stato,onValueChange:e=>D("stato",e),children:[(0,t.jsx)(R.bq,{children:(0,t.jsx)(R.yv,{})}),(0,t.jsxs)(R.gC,{children:[(0,t.jsx)(R.eb,{value:"APERTA",children:"Aperta"}),(0,t.jsx)(R.eb,{value:"IN_RISOLUZIONE",children:"In Risoluzione"}),(0,t.jsx)(R.eb,{value:"RISOLTA",children:"Risolta"}),(0,t.jsx)(R.eb,{value:"CHIUSA",children:"Chiusa"})]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(S.J,{htmlFor:"descrizione",children:"Descrizione *"}),(0,t.jsx)(I.T,{id:"descrizione",value:c.descrizione,onChange:e=>D("descrizione",e.target.value),className:z.descrizione?"border-red-500":"",placeholder:"Descrivi dettagliatamente la non conformit\xe0 rilevata...",rows:3}),z.descrizione&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:z.descrizione})]})]})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsx)(l.ZB,{children:"Responsabili e Date"}),(0,t.jsx)(l.BT,{children:"Informazioni su rilevazione e risoluzione"})]}),(0,t.jsxs)(l.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(S.J,{htmlFor:"responsabile_rilevazione",children:"Responsabile Rilevazione *"}),(0,t.jsx)(o.p,{id:"responsabile_rilevazione",value:c.responsabile_rilevazione,onChange:e=>D("responsabile_rilevazione",e.target.value),className:z.responsabile_rilevazione?"border-red-500":"",placeholder:"Nome responsabile"}),z.responsabile_rilevazione&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:z.responsabile_rilevazione})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(S.J,{htmlFor:"data_rilevazione",children:"Data Rilevazione"}),(0,t.jsx)(o.p,{id:"data_rilevazione",type:"date",value:c.data_rilevazione,onChange:e=>D("data_rilevazione",e.target.value)})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(S.J,{htmlFor:"responsabile_risoluzione",children:"Responsabile Risoluzione"}),(0,t.jsx)(o.p,{id:"responsabile_risoluzione",value:c.responsabile_risoluzione,onChange:e=>D("responsabile_risoluzione",e.target.value),placeholder:"Nome responsabile risoluzione"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(S.J,{htmlFor:"data_risoluzione",children:"Data Risoluzione"}),(0,t.jsx)(o.p,{id:"data_risoluzione",type:"date",value:c.data_risoluzione||"",onChange:e=>D("data_risoluzione",e.target.value||null)})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(S.J,{htmlFor:"azione_correttiva",children:"Azione Correttiva"}),(0,t.jsx)(I.T,{id:"azione_correttiva",value:c.azione_correttiva,onChange:e=>D("azione_correttiva",e.target.value),placeholder:"Descrivi l'azione correttiva intrapresa...",rows:2})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(S.J,{htmlFor:"note",children:"Note"}),(0,t.jsx)(I.T,{id:"note",value:c.note,onChange:e=>D("note",e.target.value),placeholder:"Note aggiuntive...",rows:2})]})]})]}),(0,t.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,t.jsxs)(n.$,{type:"button",variant:"outline",onClick:i,children:[(0,t.jsx)(T.A,{className:"h-4 w-4 mr-2"}),"Annulla"]}),(0,t.jsx)(n.$,{type:"submit",disabled:g,children:g?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Salvataggio..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(E.A,{className:"h-4 w-4 mr-2"}),M?"Aggiorna":"Crea"," Non Conformit\xe0"]})})]}),y&&(0,t.jsxs)(C.Fc,{className:"border-red-200 bg-red-50",children:[(0,t.jsx)(w.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)(C.TN,{className:"text-red-800",children:y})]})]})]})}function G({cantiereId:e,nonConformita:s,onUpdate:a}){let[i,d]=(0,r.useState)(""),[u,p]=(0,r.useState)("all"),[v,A]=(0,r.useState)(!1),[S,I]=(0,r.useState)(""),[R,T]=(0,r.useState)(!1),[E,M]=(0,r.useState)(null),F=e=>{M(e),T(!0)},k=async s=>{if(confirm("Sei sicuro di voler eliminare questa non conformit\xe0?"))try{A(!0),await m.om.deleteNonConformita(e,s),a()}catch(e){I(e.response?.data?.detail||"Errore durante l'eliminazione")}finally{A(!1)}},D=e=>{switch(e?.toLowerCase()){case"aperta":return(0,t.jsx)(c.E,{className:"bg-red-100 text-red-800 border-red-200",children:"Aperta"});case"in_risoluzione":return(0,t.jsx)(c.E,{className:"bg-yellow-100 text-yellow-800 border-yellow-200",children:"In Risoluzione"});case"risolta":return(0,t.jsx)(c.E,{className:"bg-green-100 text-green-800 border-green-200",children:"Risolta"});case"chiusa":return(0,t.jsx)(c.E,{className:"bg-gray-100 text-gray-800 border-gray-200",children:"Chiusa"});default:return(0,t.jsx)(c.E,{className:"bg-gray-100 text-gray-800 border-gray-200",children:"Da Verificare"})}},O=e=>{switch(e?.toLowerCase()){case"critica":return(0,t.jsx)(c.E,{className:"bg-red-100 text-red-800 border-red-200",children:"Critica"});case"alta":return(0,t.jsx)(c.E,{className:"bg-orange-100 text-orange-800 border-orange-200",children:"Alta"});case"media":return(0,t.jsx)(c.E,{className:"bg-yellow-100 text-yellow-800 border-yellow-200",children:"Media"});case"bassa":return(0,t.jsx)(c.E,{className:"bg-blue-100 text-blue-800 border-blue-200",children:"Bassa"});default:return(0,t.jsx)(c.E,{variant:"outline",children:"Non Specificata"})}},Z=e=>{switch(e?.toLowerCase()){case"aperta":return(0,t.jsx)(w.A,{className:"h-4 w-4 text-red-500"});case"in_risoluzione":return(0,t.jsx)(y.A,{className:"h-4 w-4 text-yellow-500"});case"risolta":case"chiusa":return(0,t.jsx)(b.A,{className:"h-4 w-4 text-green-500"});default:return(0,t.jsx)(f.A,{className:"h-4 w-4 text-gray-500"})}},L=s.filter(e=>{let s=i.toLowerCase(),a=e.id_cavo?.toLowerCase().includes(s)||e.descrizione?.toLowerCase().includes(s)||e.tipo_non_conformita?.toLowerCase().includes(s)||e.responsabile_rilevazione?.toLowerCase().includes(s),t=!0;return"all"!==u&&(t=e.stato?.toLowerCase()===u),a&&t}),B={totali:s.length,aperte:s.filter(e=>"APERTA"===e.stato).length,in_risoluzione:s.filter(e=>"IN_RISOLUZIONE"===e.stato).length,risolte:s.filter(e=>"RISOLTA"===e.stato||"CHIUSA"===e.stato).length,critiche:s.filter(e=>"CRITICA"===e.severita).length};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold text-slate-900 flex items-center gap-3",children:[(0,t.jsx)(f.A,{className:"h-6 w-6 text-red-600"}),"Gestione Non Conformit\xe0"]}),(0,t.jsx)("p",{className:"text-slate-600 mt-1",children:"Tracciamento e risoluzione delle non conformit\xe0"})]}),(0,t.jsxs)(n.$,{onClick:()=>{M(null),T(!0)},children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Nuova Non Conformit\xe0"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Totali"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:B.totali})]}),(0,t.jsx)(j.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Aperte"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-red-600",children:B.aperte})]}),(0,t.jsx)(w.A,{className:"h-8 w-8 text-red-500"})]})})}),(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"In Risoluzione"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:B.in_risoluzione})]}),(0,t.jsx)(y.A,{className:"h-8 w-8 text-yellow-500"})]})})}),(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Risolte"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-green-600",children:B.risolte})]}),(0,t.jsx)(b.A,{className:"h-8 w-8 text-green-500"})]})})}),(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Critiche"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-red-600",children:B.critiche})]}),(0,t.jsx)(f.A,{className:"h-8 w-8 text-red-500"})]})})})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{children:(0,t.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(N.A,{className:"h-5 w-5"}),"Ricerca e Filtri"]})}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)(o.p,{placeholder:"Cerca per ID cavo, descrizione, tipo o responsabile...",value:i,onChange:e=>d(e.target.value)})}),(0,t.jsx)("div",{className:"flex gap-2",children:[{value:"all",label:"Tutte"},{value:"aperta",label:"Aperte"},{value:"in_risoluzione",label:"In Risoluzione"},{value:"risolta",label:"Risolte"},{value:"chiusa",label:"Chiuse"}].map(e=>(0,t.jsx)(n.$,{variant:u===e.value?"default":"outline",size:"sm",onClick:()=>p(e.value),children:e.label},e.value))})]})})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsxs)(l.ZB,{children:["Elenco Non Conformit\xe0 (",L.length,")"]}),(0,t.jsx)(l.BT,{children:"Gestione delle non conformit\xe0 rilevate durante le certificazioni"})]}),(0,t.jsx)(l.Wu,{children:(0,t.jsx)("div",{className:"rounded-md border",children:(0,t.jsxs)(x.XI,{children:[(0,t.jsx)(x.A0,{children:(0,t.jsxs)(x.Hj,{children:[(0,t.jsx)(x.nd,{children:"ID Cavo"}),(0,t.jsx)(x.nd,{children:"Tipo"}),(0,t.jsx)(x.nd,{children:"Descrizione"}),(0,t.jsx)(x.nd,{children:"Severit\xe0"}),(0,t.jsx)(x.nd,{children:"Data Rilevazione"}),(0,t.jsx)(x.nd,{children:"Responsabile"}),(0,t.jsx)(x.nd,{children:"Stato"}),(0,t.jsx)(x.nd,{children:"Azioni"})]})}),(0,t.jsx)(x.BF,{children:v?(0,t.jsx)(x.Hj,{children:(0,t.jsx)(x.nA,{colSpan:8,className:"text-center py-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 animate-spin"}),"Caricamento..."]})})}):0===L.length?(0,t.jsx)(x.Hj,{children:(0,t.jsx)(x.nA,{colSpan:8,className:"text-center py-8 text-slate-500",children:"Nessuna non conformit\xe0 trovata"})}):L.map(e=>(0,t.jsxs)(x.Hj,{children:[(0,t.jsx)(x.nA,{className:"font-medium",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[Z(e.stato),e.id_cavo]})}),(0,t.jsx)(x.nA,{children:(0,t.jsx)(c.E,{variant:"outline",children:e.tipo_non_conformita||"Non Specificato"})}),(0,t.jsx)(x.nA,{children:(0,t.jsxs)("div",{className:"max-w-xs",children:[(0,t.jsx)("div",{className:"font-medium truncate",title:e.descrizione,children:e.descrizione}),e.azione_correttiva&&(0,t.jsxs)("div",{className:"text-xs text-slate-500 truncate",title:e.azione_correttiva,children:["Azione: ",e.azione_correttiva]})]})}),(0,t.jsx)(x.nA,{children:O(e.severita)}),(0,t.jsx)(x.nA,{children:new Date(e.data_rilevazione).toLocaleDateString("it-IT")}),(0,t.jsx)(x.nA,{children:e.responsabile_rilevazione||"-"}),(0,t.jsx)(x.nA,{children:D(e.stato)}),(0,t.jsx)(x.nA,{children:(0,t.jsxs)("div",{className:"flex gap-1",children:[(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>{},title:"Visualizza Dettagli",children:(0,t.jsx)(W.A,{className:"h-4 w-4"})}),(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>F(e),title:"Modifica",children:(0,t.jsx)(_.A,{className:"h-4 w-4"})}),(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>k(e.id_non_conformita),title:"Elimina",children:(0,t.jsx)(z.A,{className:"h-4 w-4"})})]})})]},e.id_non_conformita))})]})})})]}),S&&(0,t.jsxs)(C.Fc,{className:"border-red-200 bg-red-50",children:[(0,t.jsx)(w.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)(C.TN,{className:"text-red-800",children:S})]}),R&&(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,t.jsx)("div",{className:"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:(0,t.jsx)("div",{className:"p-6",children:(0,t.jsx)(q,{cantiereId:e,nonConformita:E,onSuccess:()=>{T(!1),M(null),a()},onCancel:()=>{T(!1),M(null)}})})})})]})}function V({cantiereId:e}){let[s,a]=(0,r.useState)("certificazioni"),[C,S]=(0,r.useState)(""),[I,R]=(0,r.useState)("all"),[T,E]=(0,r.useState)([]),[M,k]=(0,r.useState)([]),[D,Z]=(0,r.useState)([]),[L,B]=(0,r.useState)([]),[W,q]=(0,r.useState)(!0),[V,$]=(0,r.useState)(""),[U,H]=(0,r.useState)(!1),[J,X]=(0,r.useState)(null),{user:K,cantiere:Q}=(0,i.A)(),Y=async()=>{try{q(!0),$("");let[s,a,t,i]=await Promise.all([m.km.getCertificazioni(e),m.kw.getStrumenti(e),m.l9.getRapporti(e),m.om.getNonConformita(e)]);E(s),k(a),Z(t),B(i)}catch(e){$(e.response?.data?.detail||"Errore durante il caricamento dei dati")}finally{q(!1)}},ee=e=>{X(e),H(!0)},es=async s=>{if(confirm("Sei sicuro di voler eliminare questa certificazione?"))try{await m.km.deleteCertificazione(e,s),Y()}catch(e){$(e.response?.data?.detail||"Errore durante l'eliminazione")}},ea=e=>{switch(e){case"CONFORME":return(0,t.jsx)(c.E,{className:"bg-green-100 text-green-800 border-green-200",children:"Conforme"});case"NON_CONFORME":return(0,t.jsx)(c.E,{className:"bg-red-100 text-red-800 border-red-200",children:"Non Conforme"});case"BOZZA":return(0,t.jsx)(c.E,{className:"bg-gray-100 text-gray-800 border-gray-200",children:"Bozza"});case"IN_REVISIONE":return(0,t.jsx)(c.E,{className:"bg-yellow-100 text-yellow-800 border-yellow-200",children:"In Revisione"});default:return(0,t.jsx)(c.E,{variant:"outline",children:e})}},et=T.filter(e=>{let s=!C||e.id_cavo?.toLowerCase().includes(C.toLowerCase())||e.responsabile_certificazione?.toLowerCase().includes(C.toLowerCase()),a="all"===I||e.stato_certificato===I;return s&&a});return W?(0,t.jsx)("div",{className:"flex items-center justify-center p-8",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 animate-spin"}),"Caricamento certificazioni..."]})}):U?(0,t.jsx)(A.A,{cantiereId:e,certificazione:J,strumenti:M,onSuccess:()=>{H(!1),Y()},onCancel:()=>H(!1)}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"text-3xl font-bold text-slate-900 flex items-center gap-3",children:[(0,t.jsx)(u.A,{className:"h-8 w-8 text-blue-600"}),"Sistema Certificazioni CEI 64-8"]}),(0,t.jsx)("p",{className:"text-slate-600 mt-1",children:"Gestione completa certificazioni, strumenti e rapporti di collaudo"})]})}),(0,t.jsxs)(d.tU,{value:s,onValueChange:a,className:"space-y-6",children:[(0,t.jsxs)(d.j7,{className:"grid w-full grid-cols-5",children:[(0,t.jsxs)(d.Xi,{value:"certificazioni",className:"flex items-center gap-2",children:[(0,t.jsx)(j.A,{className:"h-4 w-4"}),"Certificazioni"]}),(0,t.jsxs)(d.Xi,{value:"strumenti",className:"flex items-center gap-2",children:[(0,t.jsx)(p.A,{className:"h-4 w-4"}),"Strumenti"]}),(0,t.jsxs)(d.Xi,{value:"rapporti",className:"flex items-center gap-2",children:[(0,t.jsx)(v.A,{className:"h-4 w-4"}),"Rapporti Generali"]}),(0,t.jsxs)(d.Xi,{value:"non-conformita",className:"flex items-center gap-2",children:[(0,t.jsx)(f.A,{className:"h-4 w-4"}),"Non Conformit\xe0"]}),(0,t.jsxs)(d.Xi,{value:"statistiche",className:"flex items-center gap-2",children:[(0,t.jsx)(u.A,{className:"h-4 w-4"}),"Statistiche"]})]}),(0,t.jsxs)(d.av,{value:"certificazioni",className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 flex-1",children:[(0,t.jsxs)("div",{className:"relative flex-1 max-w-md",children:[(0,t.jsx)(N.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,t.jsx)(o.p,{placeholder:"Cerca per ID cavo o responsabile...",value:C,onChange:e=>S(e.target.value),className:"pl-10"})]}),(0,t.jsxs)("select",{value:I,onChange:e=>R(e.target.value),className:"px-3 py-2 border border-slate-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"all",children:"Tutti gli stati"}),(0,t.jsx)("option",{value:"CONFORME",children:"Conforme"}),(0,t.jsx)("option",{value:"NON_CONFORME",children:"Non Conforme"}),(0,t.jsx)("option",{value:"BOZZA",children:"Bozza"}),(0,t.jsx)("option",{value:"IN_REVISIONE",children:"In Revisione"})]})]}),(0,t.jsxs)(n.$,{onClick:()=>{X(null),H(!0)},children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Nuova Certificazione"]})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsx)(l.ZB,{children:"Certificazioni Cavi"}),(0,t.jsxs)(l.BT,{children:[et.length," certificazioni trovate"]})]}),(0,t.jsx)(l.Wu,{children:(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)(x.XI,{children:[(0,t.jsx)(x.A0,{children:(0,t.jsxs)(x.Hj,{children:[(0,t.jsx)(x.nd,{children:"ID Cavo"}),(0,t.jsx)(x.nd,{children:"Data Certificazione"}),(0,t.jsx)(x.nd,{children:"Responsabile"}),(0,t.jsx)(x.nd,{children:"Stato"}),(0,t.jsx)(x.nd,{children:"Isolamento (MΩ)"}),(0,t.jsx)(x.nd,{children:"Esito"}),(0,t.jsx)(x.nd,{children:"Azioni"})]})}),(0,t.jsx)(x.BF,{children:0===et.length?(0,t.jsx)(x.Hj,{children:(0,t.jsx)(x.nA,{colSpan:7,className:"text-center py-8 text-slate-500",children:"Nessuna certificazione trovata"})}):et.map(e=>(0,t.jsxs)(x.Hj,{children:[(0,t.jsx)(x.nA,{className:"font-medium",children:e.id_cavo}),(0,t.jsx)(x.nA,{children:e.data_certificazione?new Date(e.data_certificazione).toLocaleDateString("it-IT"):"-"}),(0,t.jsx)(x.nA,{children:e.responsabile_certificazione||"-"}),(0,t.jsx)(x.nA,{children:ea(e.stato_certificato||"BOZZA")}),(0,t.jsx)(x.nA,{children:e.valore_isolamento||"-"}),(0,t.jsx)(x.nA,{children:"CONFORME"===e.esito_complessivo?(0,t.jsx)(b.A,{className:"h-4 w-4 text-green-600"}):"NON_CONFORME"===e.esito_complessivo?(0,t.jsx)(w.A,{className:"h-4 w-4 text-red-600"}):(0,t.jsx)(y.A,{className:"h-4 w-4 text-yellow-600"})}),(0,t.jsx)(x.nA,{children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>ee(e),children:(0,t.jsx)(_.A,{className:"h-4 w-4"})}),(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>es(e.id_certificazione),children:(0,t.jsx)(z.A,{className:"h-4 w-4"})})]})})]},e.id_certificazione))})]})})})]})]}),(0,t.jsx)(d.av,{value:"strumenti",children:(0,t.jsx)(F,{cantiereId:e,strumenti:M,onUpdate:Y})}),(0,t.jsx)(d.av,{value:"rapporti",children:(0,t.jsx)(O,{cantiereId:e,rapporti:D,onUpdate:Y})}),(0,t.jsx)(d.av,{value:"non-conformita",children:(0,t.jsx)(G,{cantiereId:e,nonConformita:L,onUpdate:Y})}),(0,t.jsx)(d.av,{value:"statistiche",children:(0,t.jsx)(P,{cantiereId:e,certificazioni:T,strumenti:M,nonConformita:L})})]}),V&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(w.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)("span",{className:"text-red-800",children:V})]})})]})}function $(){let{user:e,cantiere:s}=(0,i.A)(),a=s?.id_cantiere||e?.id_utente;return a?(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,t.jsx)("div",{className:"max-w-[95%] mx-auto",children:(0,t.jsx)(V,{cantiereId:a})})}):(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,t.jsx)("div",{className:"max-w-[90%] mx-auto",children:(0,t.jsxs)(C.Fc,{className:"border-red-200 bg-red-50",children:[(0,t.jsx)(w.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)(C.TN,{className:"text-red-800",children:"Cantiere non selezionato. Seleziona un cantiere per accedere alle certificazioni."})]})})})}},25541:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28947:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29844:(e,s,a)=>{"use strict";a.d(s,{tU:()=>I,av:()=>E,j7:()=>R,Xi:()=>T});var t=a(60687),i=a(43210),r=a(70569),l=a(11273),n=a(72942),c=a(46059),o=a(14163),d=a(43),x=a(65551),m=a(96963),h="Tabs",[u,j]=(0,l.A)(h,[n.RG]),p=(0,n.RG)(),[v,f]=u(h),N=i.forwardRef((e,s)=>{let{__scopeTabs:a,value:i,onValueChange:r,defaultValue:l,orientation:n="horizontal",dir:c,activationMode:u="automatic",...j}=e,p=(0,d.jH)(c),[f,N]=(0,x.i)({prop:i,onChange:r,defaultProp:l??"",caller:h});return(0,t.jsx)(v,{scope:a,baseId:(0,m.B)(),value:f,onValueChange:N,orientation:n,dir:p,activationMode:u,children:(0,t.jsx)(o.sG.div,{dir:p,"data-orientation":n,...j,ref:s})})});N.displayName=h;var g="TabsList",b=i.forwardRef((e,s)=>{let{__scopeTabs:a,loop:i=!0,...r}=e,l=f(g,a),c=p(a);return(0,t.jsx)(n.bL,{asChild:!0,...c,orientation:l.orientation,dir:l.dir,loop:i,children:(0,t.jsx)(o.sG.div,{role:"tablist","aria-orientation":l.orientation,...r,ref:s})})});b.displayName=g;var w="TabsTrigger",y=i.forwardRef((e,s)=>{let{__scopeTabs:a,value:i,disabled:l=!1,...c}=e,d=f(w,a),x=p(a),m=A(d.baseId,i),h=C(d.baseId,i),u=i===d.value;return(0,t.jsx)(n.q7,{asChild:!0,...x,focusable:!l,active:u,children:(0,t.jsx)(o.sG.button,{type:"button",role:"tab","aria-selected":u,"aria-controls":h,"data-state":u?"active":"inactive","data-disabled":l?"":void 0,disabled:l,id:m,...c,ref:s,onMouseDown:(0,r.m)(e.onMouseDown,e=>{l||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(i)}),onKeyDown:(0,r.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(i)}),onFocus:(0,r.m)(e.onFocus,()=>{let e="manual"!==d.activationMode;u||l||!e||d.onValueChange(i)})})})});y.displayName=w;var _="TabsContent",z=i.forwardRef((e,s)=>{let{__scopeTabs:a,value:r,forceMount:l,children:n,...d}=e,x=f(_,a),m=A(x.baseId,r),h=C(x.baseId,r),u=r===x.value,j=i.useRef(u);return i.useEffect(()=>{let e=requestAnimationFrame(()=>j.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,t.jsx)(c.C,{present:l||u,children:({present:a})=>(0,t.jsx)(o.sG.div,{"data-state":u?"active":"inactive","data-orientation":x.orientation,role:"tabpanel","aria-labelledby":m,hidden:!a,id:h,tabIndex:0,...d,ref:s,style:{...e.style,animationDuration:j.current?"0s":void 0},children:a&&n})})});function A(e,s){return`${e}-trigger-${s}`}function C(e,s){return`${e}-content-${s}`}z.displayName=_;var S=a(4780);let I=N,R=i.forwardRef(({className:e,...s},a)=>(0,t.jsx)(b,{ref:a,className:(0,S.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));R.displayName=b.displayName;let T=i.forwardRef(({className:e,...s},a)=>(0,t.jsx)(y,{ref:a,className:(0,S.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));T.displayName=y.displayName;let E=i.forwardRef(({className:e,...s},a)=>(0,t.jsx)(z,{ref:a,className:(0,S.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));E.displayName=z.displayName},31158:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33873:e=>{"use strict";e.exports=require("path")},44765:(e,s,a)=>{Promise.resolve().then(a.bind(a,71792))},46059:(e,s,a)=>{"use strict";a.d(s,{C:()=>l});var t=a(43210),i=a(98599),r=a(66156),l=e=>{let{present:s,children:a}=e,l=function(e){var s,a;let[i,l]=t.useState(),c=t.useRef(null),o=t.useRef(e),d=t.useRef("none"),[x,m]=(s=e?"mounted":"unmounted",a={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},t.useReducer((e,s)=>a[e][s]??e,s));return t.useEffect(()=>{let e=n(c.current);d.current="mounted"===x?e:"none"},[x]),(0,r.N)(()=>{let s=c.current,a=o.current;if(a!==e){let t=d.current,i=n(s);e?m("MOUNT"):"none"===i||s?.display==="none"?m("UNMOUNT"):a&&t!==i?m("ANIMATION_OUT"):m("UNMOUNT"),o.current=e}},[e,m]),(0,r.N)(()=>{if(i){let e,s=i.ownerDocument.defaultView??window,a=a=>{let t=n(c.current).includes(a.animationName);if(a.target===i&&t&&(m("ANIMATION_END"),!o.current)){let a=i.style.animationFillMode;i.style.animationFillMode="forwards",e=s.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=a)})}},t=e=>{e.target===i&&(d.current=n(c.current))};return i.addEventListener("animationstart",t),i.addEventListener("animationcancel",a),i.addEventListener("animationend",a),()=>{s.clearTimeout(e),i.removeEventListener("animationstart",t),i.removeEventListener("animationcancel",a),i.removeEventListener("animationend",a)}}m("ANIMATION_END")},[i,m]),{isPresent:["mounted","unmountSuspended"].includes(x),ref:t.useCallback(e=>{c.current=e?getComputedStyle(e):null,l(e)},[])}}(s),c="function"==typeof a?a({present:l.isPresent}):t.Children.only(a),o=(0,i.s)(l.ref,function(e){let s=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,a=s&&"isReactWarning"in s&&s.isReactWarning;return a?e.ref:(a=(s=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in s&&s.isReactWarning)?e.props.ref:e.props.ref||e.ref}(c));return"function"==typeof a||l.isPresent?t.cloneElement(c,{ref:o}):null};function n(e){return e?.animationName||"none"}l.displayName="Presence"},46657:(e,s,a)=>{"use strict";a.d(s,{k:()=>b});var t=a(60687),i=a(43210),r=a(11273),l=a(14163),n="Progress",[c,o]=(0,r.A)(n),[d,x]=c(n),m=i.forwardRef((e,s)=>{var a,i;let{__scopeProgress:r,value:n=null,max:c,getValueLabel:o=j,...x}=e;(c||0===c)&&!f(c)&&console.error((a=`${c}`,`Invalid prop \`max\` of value \`${a}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let m=f(c)?c:100;null===n||N(n,m)||console.error((i=`${n}`,`Invalid prop \`value\` of value \`${i}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let h=N(n,m)?n:null,u=v(h)?o(h,m):void 0;return(0,t.jsx)(d,{scope:r,value:h,max:m,children:(0,t.jsx)(l.sG.div,{"aria-valuemax":m,"aria-valuemin":0,"aria-valuenow":v(h)?h:void 0,"aria-valuetext":u,role:"progressbar","data-state":p(h,m),"data-value":h??void 0,"data-max":m,...x,ref:s})})});m.displayName=n;var h="ProgressIndicator",u=i.forwardRef((e,s)=>{let{__scopeProgress:a,...i}=e,r=x(h,a);return(0,t.jsx)(l.sG.div,{"data-state":p(r.value,r.max),"data-value":r.value??void 0,"data-max":r.max,...i,ref:s})});function j(e,s){return`${Math.round(e/s*100)}%`}function p(e,s){return null==e?"indeterminate":e===s?"complete":"loading"}function v(e){return"number"==typeof e}function f(e){return v(e)&&!isNaN(e)&&e>0}function N(e,s){return v(e)&&!isNaN(e)&&e<=s&&e>=0}u.displayName=h;var g=a(4780);function b({className:e,value:s,...a}){return(0,t.jsx)(m,{"data-slot":"progress",className:(0,g.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",e),...a,children:(0,t.jsx)(u,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(s||0)}%)`}})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56381:(e,s,a)=>{Promise.resolve().then(a.bind(a,22029))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},71792:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\certificazioni\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\certificazioni\\page.tsx","default")},72942:(e,s,a)=>{"use strict";a.d(s,{RG:()=>b,bL:()=>R,q7:()=>T});var t=a(43210),i=a(70569),r=a(9510),l=a(98599),n=a(11273),c=a(96963),o=a(14163),d=a(13495),x=a(65551),m=a(43),h=a(60687),u="rovingFocusGroup.onEntryFocus",j={bubbles:!1,cancelable:!0},p="RovingFocusGroup",[v,f,N]=(0,r.N)(p),[g,b]=(0,n.A)(p,[N]),[w,y]=g(p),_=t.forwardRef((e,s)=>(0,h.jsx)(v.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,h.jsx)(v.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,h.jsx)(z,{...e,ref:s})})}));_.displayName=p;var z=t.forwardRef((e,s)=>{let{__scopeRovingFocusGroup:a,orientation:r,loop:n=!1,dir:c,currentTabStopId:v,defaultCurrentTabStopId:N,onCurrentTabStopIdChange:g,onEntryFocus:b,preventScrollOnEntryFocus:y=!1,..._}=e,z=t.useRef(null),A=(0,l.s)(s,z),C=(0,m.jH)(c),[S,R]=(0,x.i)({prop:v,defaultProp:N??null,onChange:g,caller:p}),[T,E]=t.useState(!1),M=(0,d.c)(b),F=f(a),k=t.useRef(!1),[D,O]=t.useState(0);return t.useEffect(()=>{let e=z.current;if(e)return e.addEventListener(u,M),()=>e.removeEventListener(u,M)},[M]),(0,h.jsx)(w,{scope:a,orientation:r,dir:C,loop:n,currentTabStopId:S,onItemFocus:t.useCallback(e=>R(e),[R]),onItemShiftTab:t.useCallback(()=>E(!0),[]),onFocusableItemAdd:t.useCallback(()=>O(e=>e+1),[]),onFocusableItemRemove:t.useCallback(()=>O(e=>e-1),[]),children:(0,h.jsx)(o.sG.div,{tabIndex:T||0===D?-1:0,"data-orientation":r,..._,ref:A,style:{outline:"none",...e.style},onMouseDown:(0,i.m)(e.onMouseDown,()=>{k.current=!0}),onFocus:(0,i.m)(e.onFocus,e=>{let s=!k.current;if(e.target===e.currentTarget&&s&&!T){let s=new CustomEvent(u,j);if(e.currentTarget.dispatchEvent(s),!s.defaultPrevented){let e=F().filter(e=>e.focusable);I([e.find(e=>e.active),e.find(e=>e.id===S),...e].filter(Boolean).map(e=>e.ref.current),y)}}k.current=!1}),onBlur:(0,i.m)(e.onBlur,()=>E(!1))})})}),A="RovingFocusGroupItem",C=t.forwardRef((e,s)=>{let{__scopeRovingFocusGroup:a,focusable:r=!0,active:l=!1,tabStopId:n,children:d,...x}=e,m=(0,c.B)(),u=n||m,j=y(A,a),p=j.currentTabStopId===u,N=f(a),{onFocusableItemAdd:g,onFocusableItemRemove:b,currentTabStopId:w}=j;return t.useEffect(()=>{if(r)return g(),()=>b()},[r,g,b]),(0,h.jsx)(v.ItemSlot,{scope:a,id:u,focusable:r,active:l,children:(0,h.jsx)(o.sG.span,{tabIndex:p?0:-1,"data-orientation":j.orientation,...x,ref:s,onMouseDown:(0,i.m)(e.onMouseDown,e=>{r?j.onItemFocus(u):e.preventDefault()}),onFocus:(0,i.m)(e.onFocus,()=>j.onItemFocus(u)),onKeyDown:(0,i.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void j.onItemShiftTab();if(e.target!==e.currentTarget)return;let s=function(e,s,a){var t;let i=(t=e.key,"rtl"!==a?t:"ArrowLeft"===t?"ArrowRight":"ArrowRight"===t?"ArrowLeft":t);if(!("vertical"===s&&["ArrowLeft","ArrowRight"].includes(i))&&!("horizontal"===s&&["ArrowUp","ArrowDown"].includes(i)))return S[i]}(e,j.orientation,j.dir);if(void 0!==s){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let a=N().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===s)a.reverse();else if("prev"===s||"next"===s){"prev"===s&&a.reverse();let t=a.indexOf(e.currentTarget);a=j.loop?function(e,s){return e.map((a,t)=>e[(s+t)%e.length])}(a,t+1):a.slice(t+1)}setTimeout(()=>I(a))}}),children:"function"==typeof d?d({isCurrentTabStop:p,hasTabStop:null!=w}):d})})});C.displayName=A;var S={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function I(e,s=!1){let a=document.activeElement;for(let t of e)if(t===a||(t.focus({preventScroll:s}),document.activeElement!==a))return}var R=_,T=C},74075:e=>{"use strict";e.exports=require("zlib")},78122:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84027:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},86561:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},88233:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},94735:e=>{"use strict";e.exports=require("events")},96474:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},99270:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[447,991,658,947,400,818,639,16],()=>a(10002));module.exports=t})();