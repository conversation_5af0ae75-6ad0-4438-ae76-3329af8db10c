module.exports = {

"[project]/src/hooks/useCantiere.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useCantiere": (()=>useCantiere),
    "useCantiereId": (()=>useCantiereId),
    "useRequiredCantiere": (()=>useRequiredCantiere)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-ssr] (ecmascript)");
'use client';
;
;
function useCantiere() {
    const { cantiere, isLoading: authLoading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuth"])();
    const [cantiereId, setCantiereId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    // Funzione per validare un ID cantiere
    const validateCantiere = (id)=>{
        if (id === null || id === undefined) return false;
        const numId = typeof id === 'string' ? parseInt(id, 10) : id;
        if (isNaN(numId) || numId <= 0) {
            console.warn('🏗️ useCantiere: ID cantiere non valido:', id);
            return false;
        }
        return true;
    };
    // Effetto per sincronizzare con AuthContext e localStorage
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (authLoading) {
            console.log('🏗️ useCantiere: Autenticazione in corso...');
            return;
        }
        setIsLoading(true);
        setError(null);
        try {
            let selectedId = null;
            // Priorità 1: Cantiere dal context di autenticazione (login cantiere diretto)
            if (cantiere?.id_cantiere && validateCantiere(cantiere.id_cantiere)) {
                selectedId = cantiere.id_cantiere;
                console.log('🏗️ useCantiere: Usando cantiere dal context (login cantiere):', selectedId);
            } else {
                // Priorità 2: Cantiere dal localStorage (cantiere_data per login cantiere)
                const cantiereData = localStorage.getItem('cantiere_data');
                if (cantiereData) {
                    try {
                        const parsedData = JSON.parse(cantiereData);
                        if (parsedData.id_cantiere && validateCantiere(parsedData.id_cantiere)) {
                            selectedId = parsedData.id_cantiere;
                            console.log('🏗️ useCantiere: Usando cantiere da cantiere_data:', selectedId);
                        }
                    } catch (parseError) {
                        console.warn('🏗️ useCantiere: Errore parsing cantiere_data:', parseError);
                    }
                }
                // Priorità 3: Cantiere dal localStorage (selectedCantiereId per selezione manuale)
                if (!selectedId) {
                    const storedId = localStorage.getItem('selectedCantiereId');
                    if (storedId && validateCantiere(storedId)) {
                        selectedId = parseInt(storedId, 10);
                        console.log('🏗️ useCantiere: Usando cantiere da selectedCantiereId:', selectedId);
                    }
                }
            }
            if (selectedId) {
                setCantiereId(selectedId);
                console.log('🏗️ useCantiere: Cantiere valido impostato:', selectedId);
            } else {
                console.warn('🏗️ useCantiere: Nessun cantiere valido trovato');
                setCantiereId(null);
                setError('Nessun cantiere selezionato. Seleziona un cantiere per continuare.');
            }
        } catch (err) {
            console.error('🏗️ useCantiere: Errore nella gestione cantiere:', err);
            setError('Errore nella gestione del cantiere selezionato.');
            setCantiereId(null);
        } finally{
            setIsLoading(false);
        }
    }, [
        cantiere,
        authLoading
    ]);
    const clearError = ()=>setError(null);
    return {
        cantiereId,
        cantiere,
        isValidCantiere: cantiereId !== null && cantiereId > 0,
        isLoading,
        error,
        validateCantiere,
        clearError
    };
}
function useCantiereId() {
    const { cantiereId } = useCantiere();
    return cantiereId;
}
function useRequiredCantiere() {
    const { cantiereId, cantiere, isLoading, error } = useCantiere();
    if (isLoading) {
        throw new Error('Caricamento cantiere in corso...');
    }
    if (error) {
        throw new Error(error);
    }
    if (!cantiereId || cantiereId <= 0) {
        throw new Error('Nessun cantiere selezionato. Seleziona un cantiere per continuare.');
    }
    return {
        cantiereId,
        cantiere
    };
}
}}),
"[project]/src/utils/softColors.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * PALETTE COLORI CENTRALIZZATA v3.0 - CABLYS
 * Versione ristretta e professionale per eliminare l'effetto "Arlecchino"
 * Data: 30 Giugno 2025
 */ // COLORI PRIMARI E NEUTRI (Uso rigoroso e limitato)
__turbopack_context__.s({
    "BOBINA_COLORS": (()=>BOBINA_COLORS),
    "CABLYS_COLORS": (()=>CABLYS_COLORS),
    "CAVO_COLORS": (()=>CAVO_COLORS),
    "COMANDA_COLORS": (()=>COMANDA_COLORS),
    "PRIORITY_COLORS": (()=>PRIORITY_COLORS),
    "getBobinaColorClasses": (()=>getBobinaColorClasses),
    "getCavoColorClasses": (()=>getCavoColorClasses),
    "getClickableBadgeClasses": (()=>getClickableBadgeClasses),
    "getComandaColorClasses": (()=>getComandaColorClasses),
    "getPrimaryActionClasses": (()=>getPrimaryActionClasses),
    "getPriorityColorClasses": (()=>getPriorityColorClasses),
    "getProgressColor": (()=>getProgressColor),
    "getSecondaryActionClasses": (()=>getSecondaryActionClasses),
    "getUnavailableClasses": (()=>getUnavailableClasses),
    "getUniformButtonClasses": (()=>getUniformButtonClasses)
});
const CABLYS_COLORS = {
    // COLORE PRIMARIO - Blu CABLYS (Unico colore per azioni e elementi cliccabili)
    PRIMARY: {
        bg: 'bg-blue-50',
        text: 'text-blue-600',
        border: 'border-blue-300',
        hover: 'hover:bg-blue-50',
        active: 'hover:border-blue-400',
        hex: '#007bff' // Blu primario CABLYS
    },
    // COLORI NEUTRI - Grigio (Per testo, disabilitato, non disponibile)
    NEUTRAL: {
        text_dark: 'text-gray-800',
        text_medium: 'text-gray-600',
        text_light: 'text-gray-500',
        bg_white: 'bg-white',
        bg_light: 'bg-gray-50',
        border: 'border-gray-300',
        hex_dark: '#343A40',
        hex_medium: '#6c757d',
        hex_light: '#DEE2E6' // Grigio chiaro per bordi
    },
    // COLORI DI STATO (Desaturati e pastello per badge informativi)
    STATUS: {
        // Successo/Positivo - Verde desaturato
        SUCCESS: {
            bg: 'bg-green-50',
            text: 'text-green-700',
            border: 'border-green-200',
            hex: '#28A745' // Verde pulito
        },
        // Avviso/Attenzione - Arancione desaturato
        WARNING: {
            bg: 'bg-orange-50',
            text: 'text-orange-700',
            border: 'border-orange-200',
            hex: '#FD7E14' // Arancione energico
        },
        // Errore/Pericolo - Rosso desaturato
        ERROR: {
            bg: 'bg-red-50',
            text: 'text-red-700',
            border: 'border-red-200',
            hex: '#DC3545' // Rosso chiaro e diretto
        }
    }
};
const BOBINA_COLORS = {
    DISPONIBILE: CABLYS_COLORS.STATUS.SUCCESS,
    IN_USO: CABLYS_COLORS.STATUS.WARNING,
    TERMINATA: CABLYS_COLORS.NEUTRAL,
    OVER: CABLYS_COLORS.STATUS.ERROR,
    VUOTA: CABLYS_COLORS.NEUTRAL,
    ERRORE: CABLYS_COLORS.STATUS.ERROR
};
const CAVO_COLORS = {
    DA_INSTALLARE: CABLYS_COLORS.NEUTRAL,
    INSTALLATO: CABLYS_COLORS.STATUS.SUCCESS,
    COLLEGATO_PARTENZA: CABLYS_COLORS.STATUS.WARNING,
    COLLEGATO_ARRIVO: CABLYS_COLORS.STATUS.WARNING,
    COLLEGATO: CABLYS_COLORS.STATUS.SUCCESS,
    CERTIFICATO: CABLYS_COLORS.STATUS.SUCCESS,
    SPARE: CABLYS_COLORS.STATUS.WARNING,
    ERRORE: CABLYS_COLORS.STATUS.ERROR
};
const COMANDA_COLORS = {
    ATTIVA: CABLYS_COLORS.STATUS.SUCCESS,
    COMPLETATA: CABLYS_COLORS.STATUS.SUCCESS,
    ANNULLATA: CABLYS_COLORS.NEUTRAL,
    IN_CORSO: CABLYS_COLORS.STATUS.WARNING,
    ERRORE: CABLYS_COLORS.STATUS.ERROR
};
const getBobinaColorClasses = (stato)=>{
    const normalizedStato = stato?.toUpperCase();
    const color = BOBINA_COLORS[normalizedStato] || BOBINA_COLORS.ERRORE;
    return {
        // Badge informativo a pillola (senza hover - NON cliccabile)
        badge: `${color.bg} ${color.text} rounded-full px-3 py-1 text-xs font-medium`,
        text: color.text,
        bg: color.bg,
        border: color.border,
        hex: color.hex
    };
};
const getCavoColorClasses = (stato)=>{
    const normalizedStato = stato?.toUpperCase().replace(/\s+/g, '_');
    const color = CAVO_COLORS[normalizedStato] || CAVO_COLORS.ERRORE;
    return {
        // Badge informativo a pillola (senza hover - NON cliccabile)
        badge: `${color.bg} ${color.text} rounded-full px-3 py-1 text-xs font-medium`,
        text: color.text,
        bg: color.bg,
        border: color.border,
        hex: color.hex
    };
};
const getUniformButtonClasses = ()=>{
    return {
        // Pulsante uniforme moderno - solo bordo inferiore prima dell'hover (senza effetti sovrapposti)
        button: `inline-flex items-center justify-center gap-1 px-3 py-2 text-xs font-medium bg-white text-gray-800 border-b-2 border-[#315cfd] rounded-full cursor-pointer min-w-[4rem] h-8 transition-colors duration-300 hover:border-2 hover:bg-[#315cfd] hover:text-white`,
        text: 'text-gray-800',
        border: 'border-b-[#315cfd]',
        hover: 'hover:bg-[#315cfd] hover:text-white hover:border-2'
    };
};
const getPrimaryActionClasses = ()=>{
    return getUniformButtonClasses();
};
const getSecondaryActionClasses = ()=>{
    return getUniformButtonClasses();
};
const getClickableBadgeClasses = ()=>{
    return getUniformButtonClasses();
};
const getUnavailableClasses = ()=>{
    return {
        // Testo grigio statico (NON cliccabile)
        text: `inline-flex items-center gap-1 px-2 py-1 text-xs font-medium ${CABLYS_COLORS.NEUTRAL.text_light}`,
        color: CABLYS_COLORS.NEUTRAL.text_light
    };
};
const getComandaColorClasses = (stato)=>{
    const normalizedStato = stato?.toUpperCase().replace(/\s+/g, '_');
    const color = COMANDA_COLORS[normalizedStato] || COMANDA_COLORS.ERRORE;
    return {
        badge: `${color.bg} ${color.text} ${color.border}`,
        button: `${color.bg} ${color.text} ${color.border} ${color.hover}`,
        alert: `${color.bg} ${color.text} ${color.border}`,
        text: color.text,
        bg: color.bg,
        border: color.border,
        hover: color.hover,
        hex: color.hex
    };
};
const getProgressColor = (percentage)=>{
    if (percentage >= 90) return CABLYS_COLORS.STATUS.SUCCESS;
    if (percentage >= 70) return CABLYS_COLORS.STATUS.SUCCESS;
    if (percentage >= 50) return CABLYS_COLORS.STATUS.WARNING;
    if (percentage >= 30) return CABLYS_COLORS.STATUS.WARNING;
    return CABLYS_COLORS.STATUS.ERROR;
};
const PRIORITY_COLORS = {
    ALTA: CABLYS_COLORS.STATUS.ERROR,
    MEDIA: CABLYS_COLORS.STATUS.WARNING,
    BASSA: CABLYS_COLORS.NEUTRAL,
    NORMALE: CABLYS_COLORS.NEUTRAL
};
const getPriorityColorClasses = (priority)=>{
    const normalizedPriority = priority?.toUpperCase();
    const color = PRIORITY_COLORS[normalizedPriority] || PRIORITY_COLORS.NORMALE;
    return {
        // Badge informativo a pillola (senza hover - NON cliccabile)
        badge: `${color.bg} ${color.text} rounded-full px-3 py-1 text-xs font-medium`,
        text: color.text,
        bg: color.bg,
        border: color.border,
        hex: color.hex
    };
};
}}),
"[project]/src/app/cavi/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>CaviPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/alert.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useCantiere$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useCantiere.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cantiere$2f$CantiereErrorBoundary$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/cantiere/CantiereErrorBoundary.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cavi$2f$CaviTable$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/cavi/CaviTable.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cavi$2f$CaviStatistics$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/cavi/CaviStatistics.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cavi$2f$CollegamentiDialogSimple$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/cavi/CollegamentiDialogSimple.tsx [app-ssr] (ecmascript)");
// Import dei nuovi componenti modali migliorati
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cavi$2f$modals$2f$CableActionModals$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/cavi/modals/CableActionModals.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cavi$2f$modals$2f$BobinaManagementModals$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/cavi/modals/BobinaManagementModals.tsx [app-ssr] (ecmascript)");
// Import del vecchio dialog per InserisciMetri (temporaneo)
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cavi$2f$InserisciMetriDialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/cavi/InserisciMetriDialog.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cavi$2f$CreaComandaDialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/cavi/CreaComandaDialog.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cavi$2f$ImportExcelDialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/cavi/ImportExcelDialog.tsx [app-ssr] (ecmascript)");
// Import dei dialog per gestione cavi
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cavi$2f$AggiungiCavoDialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/cavi/AggiungiCavoDialog.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cavi$2f$ModificaCavoDialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/cavi/ModificaCavoDialog.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cavi$2f$EliminaCavoDialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/cavi/EliminaCavoDialog.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cavi$2f$ExportDataDialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/cavi/ExportDataDialog.tsx [app-ssr] (ecmascript)");
// import { useToast } from '@/hooks/use-toast'
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$package$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Package$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/package.js [app-ssr] (ecmascript) <export default as Package>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-alert.js [app-ssr] (ecmascript) <export default as AlertCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-ssr] (ecmascript) <export default as Loader2>");
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
function CaviPage() {
    const { user, isAuthenticated, isLoading: authLoading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuth"])();
    const { cantiereId, cantiere, isValidCantiere, isLoading: cantiereLoading, error: cantiereError } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useCantiere$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCantiere"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    // Sistema toast semplice
    const toast = ({ title, description, variant })=>{
    // TODO: Implementare sistema toast visuale
    };
    const [cavi, setCavi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [caviSpare, setCaviSpare] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const [selectedCavi, setSelectedCavi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [selectionEnabled, setSelectionEnabled] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [filteredCavi, setFilteredCavi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [revisioneCorrente, setRevisioneCorrente] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    // Update filtered cavi when main cavi change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        setFilteredCavi(cavi);
    }, [
        cavi
    ]);
    // Stati per i dialoghi
    const [inserisciMetriDialog, setInserisciMetriDialog] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        open: false,
        cavo: null
    });
    const [dettagliCavoDialog, setDettagliCavoDialog] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        open: false,
        cavo: null
    });
    // Stati per i dialog di gestione cavi
    const [aggiungiCavoDialog, setAggiungiCavoDialog] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [modificaCavoDialog, setModificaCavoDialog] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        open: false,
        cavo: null
    });
    const [eliminaCavoDialog, setEliminaCavoDialog] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        open: false,
        cavo: null
    });
    const [modificaBobinaDialog, setModificaBobinaDialog] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        open: false,
        cavo: null
    });
    // Stato per la modale unificata
    const [unifiedModal, setUnifiedModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        open: false,
        mode: null,
        cavo: null
    });
    const [collegamentiDialog, setCollegamentiDialog] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        open: false,
        cavo: null
    });
    // Stati per i nuovi modali migliorati
    const [disconnectModal, setDisconnectModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        open: false,
        cavo: null
    });
    const [generatePdfModal, setGeneratePdfModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        open: false,
        cavo: null
    });
    const [certificationModal, setCertificationModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        open: false,
        cavo: null
    });
    const [certificationErrorModal, setCertificationErrorModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        open: false,
        cavo: null,
        error: ''
    });
    const [successToast, setSuccessToast] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        visible: false,
        message: ''
    });
    const [creaComandaDialog, setCreaComandaDialog] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        open: false
    });
    const [importExcelDialog, setImportExcelDialog] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        open: false
    });
    const [exportDataDialog, setExportDataDialog] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [stats, setStats] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        totali: 0,
        installati: 0,
        collegati: 0,
        certificati: 0,
        percentualeInstallazione: 0,
        percentualeCollegamento: 0,
        percentualeCertificazione: 0,
        metriTotali: 0,
        metriInstallati: 0,
        metriCollegati: 0,
        metriCertificati: 0
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!authLoading && !isAuthenticated) {
            router.push('/login');
        }
    }, [
        isAuthenticated,
        authLoading,
        router
    ]);
    // Crea oggetto cantiere per il dialog
    const cantiereForDialog = cantiere || (cantiereId && cantiereId > 0 ? {
        id_cantiere: cantiereId,
        commessa: `Cantiere ${cantiereId}`
    } : null);
    // Carica i cavi dal backend - MIGLIORATO con nuovo hook
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (isValidCantiere && cantiereId && cantiereId > 0 && !cantiereLoading) {
            loadCavi();
            loadRevisioneCorrente();
        } else if (!cantiereLoading && !isValidCantiere) {
            setCavi([]);
            setCaviSpare([]);
            setError(cantiereError || 'Nessun cantiere selezionato');
        }
    }, [
        cantiereId,
        isValidCantiere,
        cantiereLoading,
        cantiereError
    ]);
    const loadRevisioneCorrente = async ()=>{
        try {
            const response = await fetch(`http://localhost:8001/api/cavi/${cantiereId}/revisione-corrente`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json'
                }
            });
            if (response.ok) {
                const data = await response.json();
                setRevisioneCorrente(data.revisione_corrente || '00');
            } else {
                setRevisioneCorrente('00');
            }
        } catch (error) {
            setRevisioneCorrente('00');
        }
    };
    const loadCavi = async ()=>{
        try {
            setLoading(true);
            setError('');
            // Prima prova con l'API normale
            try {
                const data = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["caviApi"].getCavi(cantiereId);
                // Separa cavi attivi e spare usando la logica corretta
                // SPARE: modificato_manualmente = 3
                // ATTIVI: modificato_manualmente ≠ 3
                const caviAttivi = data.filter((cavo)=>cavo.modificato_manualmente !== 3);
                const caviSpareFiltered = data.filter((cavo)=>cavo.modificato_manualmente === 3);
                setCavi(caviAttivi);
                setCaviSpare(caviSpareFiltered);
                // Calcola statistiche
                calculateStats(caviAttivi);
            } catch (apiError) {
                throw apiError;
            }
        } catch (error) {
            setError(`Errore nel caricamento dei cavi: ${error.response?.data?.detail || error.message}`);
        } finally{
            setLoading(false);
        }
    };
    const calculateStats = (caviData)=>{
        const totali = caviData.length;
        const installati = caviData.filter((c)=>(c.metri_posati || c.metratura_reale || 0) > 0).length;
        const collegati = caviData.filter((c)=>(c.collegamento || c.collegamenti) === 3).length // 3 = collegato
        ;
        const certificati = caviData.filter((c)=>c.certificato).length;
        const metriTotali = caviData.reduce((sum, c)=>sum + (c.metri_teorici || 0), 0);
        const metriInstallati = caviData.reduce((sum, c)=>sum + (c.metri_posati || 0), 0);
        const metriCollegati = caviData.filter((c)=>c.collegamento === 3).reduce((sum, c)=>sum + (c.metri_posati || 0), 0);
        const metriCertificati = caviData.filter((c)=>c.certificato).reduce((sum, c)=>sum + (c.metri_posati || 0), 0);
        setStats({
            totali,
            installati,
            collegati,
            certificati,
            percentualeInstallazione: totali > 0 ? Math.round(installati / totali * 100) : 0,
            percentualeCollegamento: totali > 0 ? Math.round(collegati / totali * 100) : 0,
            percentualeCertificazione: totali > 0 ? Math.round(certificati / totali * 100) : 0,
            metriTotali,
            metriInstallati,
            metriCollegati,
            metriCertificati
        });
    };
    // Gestione azioni sui cavi con i nuovi modali migliorati
    const handleStatusAction = (cavo, action, label)=>{
        switch(action){
            case 'insert_meters':
                // Usa la modale unificata in modalità aggiungi_metri
                setUnifiedModal({
                    open: true,
                    mode: 'aggiungi_metri',
                    cavo
                });
                break;
            case 'modify_reel':
                // Usa la modale unificata in modalità modifica_bobina
                setUnifiedModal({
                    open: true,
                    mode: 'modifica_bobina',
                    cavo
                });
                break;
            case 'view_command':
                toast({
                    title: "Visualizza Comanda",
                    description: `Apertura comanda ${label} per cavo ${cavo.id_cavo}`
                });
                break;
            case 'connect_cable':
            case 'connect_arrival':
            case 'connect_departure':
            case 'manage_connections':
                // Usa il modal specifico per gestione collegamenti
                setCollegamentiDialog({
                    open: true,
                    cavo
                });
                break;
            case 'disconnect_cable':
                // Usa il modal specifico per gestione collegamenti (scollegamento)
                setCollegamentiDialog({
                    open: true,
                    cavo
                });
                break;
            case 'create_certificate':
                // Usa il nuovo modal di certificazione
                setCertificationModal({
                    open: true,
                    cavo
                });
                break;
            case 'generate_pdf':
                // Usa il nuovo modal di generazione PDF
                setGeneratePdfModal({
                    open: true,
                    cavo
                });
                break;
        }
    };
    const handleContextMenuAction = (cavo, action)=>{
        switch(action){
            case 'view_details':
                setDettagliCavoDialog({
                    open: true,
                    cavo
                });
                break;
            case 'edit':
                setModificaCavoDialog({
                    open: true,
                    cavo
                });
                break;
            case 'delete':
                setEliminaCavoDialog({
                    open: true,
                    cavo
                });
                break;
            case 'add_new':
                setAggiungiCavoDialog(true);
                break;
            case 'select':
                const isSelected = selectedCavi.includes(cavo.id_cavo);
                if (isSelected) {
                    setSelectedCavi(selectedCavi.filter((id)=>id !== cavo.id_cavo));
                    toast({
                        title: "Cavo Deselezionato",
                        description: `Cavo ${cavo.id_cavo} deselezionato`
                    });
                } else {
                    setSelectedCavi([
                        ...selectedCavi,
                        cavo.id_cavo
                    ]);
                    toast({
                        title: "Cavo Selezionato",
                        description: `Cavo ${cavo.id_cavo} selezionato`
                    });
                }
                break;
            case 'copy_id':
                navigator.clipboard.writeText(cavo.id_cavo);
                toast({
                    title: "ID Copiato",
                    description: `ID cavo ${cavo.id_cavo} copiato negli appunti`
                });
                break;
            case 'copy_details':
                const details = `ID: ${cavo.id_cavo}, Tipologia: ${cavo.tipologia}, Formazione: ${cavo.formazione || cavo.sezione}, Metri: ${cavo.metri_teorici}`;
                navigator.clipboard.writeText(details);
                toast({
                    title: "Dettagli Copiati",
                    description: "Dettagli cavo copiati negli appunti"
                });
                break;
            case 'add_to_command':
                toast({
                    title: "Aggiungi a Comanda",
                    description: "Funzione aggiunta a comanda in sviluppo"
                });
                break;
            case 'remove_from_command':
                toast({
                    title: "Rimuovi da Comanda",
                    description: "Funzione rimozione da comanda in sviluppo"
                });
                break;
            case 'create_command_posa':
                setCreaComandaDialog({
                    open: true,
                    tipoComanda: 'POSA'
                });
                break;
            case 'create_command_collegamento_partenza':
                setCreaComandaDialog({
                    open: true,
                    tipoComanda: 'COLLEGAMENTO_PARTENZA'
                });
                break;
            case 'create_command_collegamento_arrivo':
                setCreaComandaDialog({
                    open: true,
                    tipoComanda: 'COLLEGAMENTO_ARRIVO'
                });
                break;
            case 'create_command_certificazione':
                setCreaComandaDialog({
                    open: true,
                    tipoComanda: 'CERTIFICAZIONE'
                });
                break;
            case 'add_multiple_to_command':
                toast({
                    title: "Aggiungi Tutti a Comanda",
                    description: "Funzione aggiunta multipla a comanda in sviluppo"
                });
                break;
            case 'remove_multiple_from_commands':
                toast({
                    title: "Rimuovi Tutti dalle Comande",
                    description: "Funzione rimozione multipla dalle comande in sviluppo"
                });
                break;
            default:
                toast({
                    title: "Azione non implementata",
                    description: `Azione ${action} non ancora implementata`
                });
                break;
        }
    };
    // Gestione successo/errore dialoghi
    const handleDialogSuccess = (message)=>{
        toast({
            title: "Operazione completata",
            description: message
        });
        // Ricarica i dati
        loadCavi();
    };
    const handleDialogError = (message)=>{
        toast({
            title: "Errore",
            description: message,
            variant: "destructive"
        });
    };
    // Handler unificato per la nuova modale
    const handleUnifiedModalSave = async (data)=>{
        try {
            console.log('🔍 DEBUG handleUnifiedModalSave:', {
                cantiere,
                cantiereId,
                cantiereForDialog,
                isValidCantiere,
                data
            });
            if (!cantiereId || !isValidCantiere) {
                throw new Error('Cantiere non selezionato o non valido');
            }
            let message = '';
            if (data.mode === 'aggiungi_metri') {
                // Gestione inserimento metri
                console.log('🚀 UnifiedModal: Inserimento metri:', data);
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["caviApi"].updateMetriPosati(cantiereId, data.cableId, data.metersToInstall, data.bobbinId === 'BOBINA_VUOTA' ? 'BOBINA_VUOTA' : data.bobbinId, true);
                message = `Metri posati aggiornati con successo per il cavo ${data.cableId}`;
            } else if (data.mode === 'modifica_bobina') {
                // Gestione modifica bobina
                console.log('🚀 UnifiedModal: Modifica bobina:', data);
                if (data.editOption === 'cambia_bobina') {
                    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["caviApi"].updateMetriPosati(cantiereId, data.cableId, data.newLaidMeters, data.newBobbinId, true);
                    message = `Bobina aggiornata con successo per il cavo ${data.cableId}`;
                } else if (data.editOption === 'bobina_vuota') {
                    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["caviApi"].updateMetriPosati(cantiereId, data.cableId, data.newLaidMeters, 'BOBINA_VUOTA', false);
                    message = `Bobina rimossa con successo per il cavo ${data.cableId}`;
                } else if (data.editOption === 'annulla_posa') {
                    // Implementa logica per annullare la posa
                    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["caviApi"].updateMetriPosati(cantiereId, data.cableId, 0, 'BOBINA_VUOTA', false);
                    message = `Posa annullata con successo per il cavo ${data.cableId}`;
                }
            }
            // Ricarica i dati
            await loadCavi();
            // Mostra messaggio di successo
            toast({
                title: "Operazione completata",
                description: message,
                variant: "default"
            });
        } catch (error) {
            console.error('Errore unified modal save:', error);
            toast({
                title: "Errore",
                description: error.message || 'Errore durante l\'operazione',
                variant: "destructive"
            });
            throw error // Re-throw per permettere alla modale di gestire l'errore
            ;
        }
    };
    // Gestione dei nuovi modali migliorati
    const handleDisconnectSuccess = ()=>{
        setSuccessToast({
            visible: true,
            message: 'Cavo scollegato con successo'
        });
        setDisconnectModal({
            open: false,
            cavo: null
        });
        loadCavi() // Ricarica i dati
        ;
    };
    const handleDisconnectError = (error)=>{
        setCertificationErrorModal({
            open: true,
            cavo: disconnectModal.cavo,
            error
        });
        setDisconnectModal({
            open: false,
            cavo: null
        });
    };
    const handlePdfSuccess = ()=>{
        setSuccessToast({
            visible: true,
            message: 'PDF generato con successo'
        });
        setGeneratePdfModal({
            open: false,
            cavo: null
        });
    };
    const handlePdfError = (error)=>{
        setCertificationErrorModal({
            open: true,
            cavo: generatePdfModal.cavo,
            error
        });
        setGeneratePdfModal({
            open: false,
            cavo: null
        });
    };
    const handleCertificationSuccess = ()=>{
        setSuccessToast({
            visible: true,
            message: 'Certificazione completata con successo'
        });
        setCertificationModal({
            open: false,
            cavo: null
        });
        loadCavi() // Ricarica i dati
        ;
    };
    const handleCertificationError = (error)=>{
        setCertificationErrorModal({
            open: true,
            cavo: certificationModal.cavo,
            error
        });
        setCertificationModal({
            open: false,
            cavo: null
        });
    };
    // Mostra loader se stiamo caricando i dati dei cavi
    if (loading && isValidCantiere) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-center min-h-screen",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                    className: "h-8 w-8 animate-spin"
                }, void 0, false, {
                    fileName: "[project]/src/app/cavi/page.tsx",
                    lineNumber: 572,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    className: "ml-2",
                    children: "Caricamento cavi..."
                }, void 0, false, {
                    fileName: "[project]/src/app/cavi/page.tsx",
                    lineNumber: 573,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/cavi/page.tsx",
            lineNumber: 571,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cantiere$2f$CantiereErrorBoundary$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CantiereErrorBoundary"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "max-w-[90%] mx-auto p-6",
            children: [
                error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Alert"], {
                    variant: "destructive",
                    className: "mb-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__["AlertCircle"], {
                            className: "h-4 w-4"
                        }, void 0, false, {
                            fileName: "[project]/src/app/cavi/page.tsx",
                            lineNumber: 585,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AlertDescription"], {
                            children: error
                        }, void 0, false, {
                            fileName: "[project]/src/app/cavi/page.tsx",
                            lineNumber: 586,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/cavi/page.tsx",
                    lineNumber: 584,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cavi$2f$CaviStatistics$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                    cavi: cavi,
                    filteredCavi: filteredCavi,
                    revisioneCorrente: revisioneCorrente,
                    className: "mb-2"
                }, void 0, false, {
                    fileName: "[project]/src/app/cavi/page.tsx",
                    lineNumber: 591,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mb-8",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cavi$2f$CaviTable$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        cavi: cavi,
                        loading: loading,
                        selectionEnabled: selectionEnabled,
                        selectedCavi: selectedCavi,
                        onSelectionChange: setSelectedCavi,
                        onStatusAction: handleStatusAction,
                        onContextMenuAction: handleContextMenuAction
                    }, void 0, false, {
                        fileName: "[project]/src/app/cavi/page.tsx",
                        lineNumber: 600,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/cavi/page.tsx",
                    lineNumber: 599,
                    columnNumber: 7
                }, this),
                caviSpare.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mb-8",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Card"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardHeader"], {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardTitle"], {
                                    className: "flex items-center space-x-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$package$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Package$3e$__["Package"], {
                                            className: "h-5 w-5"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/cavi/page.tsx",
                                            lineNumber: 617,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            children: [
                                                "Cavi Spare (",
                                                caviSpare.length,
                                                ")"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/cavi/page.tsx",
                                            lineNumber: 618,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/cavi/page.tsx",
                                    lineNumber: 616,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/cavi/page.tsx",
                                lineNumber: 615,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardContent"], {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cavi$2f$CaviTable$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    cavi: caviSpare,
                                    loading: loading,
                                    selectionEnabled: false,
                                    onStatusAction: handleStatusAction,
                                    onContextMenuAction: handleContextMenuAction
                                }, void 0, false, {
                                    fileName: "[project]/src/app/cavi/page.tsx",
                                    lineNumber: 622,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/cavi/page.tsx",
                                lineNumber: 621,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/cavi/page.tsx",
                        lineNumber: 614,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/cavi/page.tsx",
                    lineNumber: 613,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cavi$2f$modals$2f$BobinaManagementModals$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UnifiedCableBobbinModal"], {
                    mode: unifiedModal.mode || 'aggiungi_metri',
                    open: unifiedModal.open,
                    onClose: ()=>setUnifiedModal({
                            open: false,
                            mode: null,
                            cavo: null
                        }),
                    cavo: unifiedModal.cavo,
                    cantiere: cantiereForDialog,
                    onSave: handleUnifiedModalSave
                }, void 0, false, {
                    fileName: "[project]/src/app/cavi/page.tsx",
                    lineNumber: 639,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cavi$2f$InserisciMetriDialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                    open: inserisciMetriDialog.open,
                    onClose: ()=>setInserisciMetriDialog({
                            open: false,
                            cavo: null
                        }),
                    cavo: inserisciMetriDialog.cavo,
                    cantiere: cantiereForDialog,
                    onSuccess: handleDialogSuccess,
                    onError: handleDialogError
                }, void 0, false, {
                    fileName: "[project]/src/app/cavi/page.tsx",
                    lineNumber: 649,
                    columnNumber: 7
                }, this),
                collegamentiDialog.cavo && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cavi$2f$CollegamentiDialogSimple$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                    open: collegamentiDialog.open,
                    onClose: ()=>setCollegamentiDialog({
                            open: false,
                            cavo: null
                        }),
                    cavo: collegamentiDialog.cavo,
                    cantiere: cantiereForDialog,
                    onSuccess: ()=>{
                        toast({
                            title: "Successo",
                            description: "Operazione completata con successo"
                        });
                        loadCavi() // Ricarica i dati
                        ;
                    }
                }, void 0, false, {
                    fileName: "[project]/src/app/cavi/page.tsx",
                    lineNumber: 661,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cavi$2f$modals$2f$CableActionModals$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DisconnectCableModal"], {
                    open: disconnectModal.open,
                    onClose: ()=>setDisconnectModal({
                            open: false,
                            cavo: null
                        }),
                    cavo: disconnectModal.cavo,
                    onConfirm: handleDisconnectSuccess,
                    onError: handleDisconnectError
                }, void 0, false, {
                    fileName: "[project]/src/app/cavi/page.tsx",
                    lineNumber: 676,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cavi$2f$modals$2f$CableActionModals$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["GeneratePdfModal"], {
                    open: generatePdfModal.open,
                    onClose: ()=>setGeneratePdfModal({
                            open: false,
                            cavo: null
                        }),
                    cavo: generatePdfModal.cavo,
                    onSuccess: handlePdfSuccess,
                    onError: handlePdfError
                }, void 0, false, {
                    fileName: "[project]/src/app/cavi/page.tsx",
                    lineNumber: 684,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cavi$2f$modals$2f$CableActionModals$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CertificationModal"], {
                    open: certificationModal.open,
                    onClose: ()=>setCertificationModal({
                            open: false,
                            cavo: null
                        }),
                    cavo: certificationModal.cavo,
                    onSuccess: handleCertificationSuccess,
                    onError: handleCertificationError
                }, void 0, false, {
                    fileName: "[project]/src/app/cavi/page.tsx",
                    lineNumber: 692,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cavi$2f$modals$2f$CableActionModals$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CertificationErrorModal"], {
                    open: certificationErrorModal.open,
                    onClose: ()=>setCertificationErrorModal({
                            open: false,
                            cavo: null,
                            error: ''
                        }),
                    cavo: certificationErrorModal.cavo,
                    error: certificationErrorModal.error,
                    onRetry: ()=>{
                        setCertificationErrorModal({
                            open: false,
                            cavo: null,
                            error: ''
                        });
                        // Riapri il modal appropriato basato sul contesto
                        if (certificationErrorModal.cavo) {
                            setCertificationModal({
                                open: true,
                                cavo: certificationErrorModal.cavo
                            });
                        }
                    }
                }, void 0, false, {
                    fileName: "[project]/src/app/cavi/page.tsx",
                    lineNumber: 700,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cavi$2f$modals$2f$CableActionModals$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SuccessToast"], {
                    visible: successToast.visible,
                    message: successToast.message,
                    onClose: ()=>setSuccessToast({
                            visible: false,
                            message: ''
                        })
                }, void 0, false, {
                    fileName: "[project]/src/app/cavi/page.tsx",
                    lineNumber: 714,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cavi$2f$CreaComandaDialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                    open: creaComandaDialog.open,
                    onClose: ()=>setCreaComandaDialog({
                            open: false
                        }),
                    caviSelezionati: selectedCavi,
                    tipoComanda: creaComandaDialog.tipoComanda,
                    onSuccess: handleDialogSuccess,
                    onError: handleDialogError
                }, void 0, false, {
                    fileName: "[project]/src/app/cavi/page.tsx",
                    lineNumber: 720,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cavi$2f$ImportExcelDialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                    open: importExcelDialog.open,
                    onClose: ()=>setImportExcelDialog({
                            open: false
                        }),
                    tipo: importExcelDialog.tipo || 'cavi',
                    onSuccess: handleDialogSuccess,
                    onError: handleDialogError
                }, void 0, false, {
                    fileName: "[project]/src/app/cavi/page.tsx",
                    lineNumber: 729,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cavi$2f$ExportDataDialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                    open: exportDataDialog,
                    onClose: ()=>setExportDataDialog(false),
                    onSuccess: handleDialogSuccess,
                    onError: handleDialogError
                }, void 0, false, {
                    fileName: "[project]/src/app/cavi/page.tsx",
                    lineNumber: 737,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cavi$2f$AggiungiCavoDialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                    open: aggiungiCavoDialog,
                    onClose: ()=>setAggiungiCavoDialog(false),
                    cantiere: cantiere,
                    onSuccess: (message)=>{
                        handleDialogSuccess(message);
                        loadCavi() // Ricarica la lista cavi
                        ;
                    },
                    onError: handleDialogError
                }, void 0, false, {
                    fileName: "[project]/src/app/cavi/page.tsx",
                    lineNumber: 745,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cavi$2f$ModificaCavoDialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                    open: modificaCavoDialog.open,
                    onClose: ()=>setModificaCavoDialog({
                            open: false,
                            cavo: null
                        }),
                    cavo: modificaCavoDialog.cavo,
                    cantiere: cantiere,
                    onSuccess: (message)=>{
                        handleDialogSuccess(message);
                        loadCavi() // Ricarica la lista cavi
                        ;
                    },
                    onError: handleDialogError
                }, void 0, false, {
                    fileName: "[project]/src/app/cavi/page.tsx",
                    lineNumber: 756,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cavi$2f$EliminaCavoDialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                    open: eliminaCavoDialog.open,
                    onClose: ()=>setEliminaCavoDialog({
                            open: false,
                            cavo: null
                        }),
                    cavo: eliminaCavoDialog.cavo,
                    cantiere: cantiere,
                    onSuccess: (message)=>{
                        handleDialogSuccess(message);
                        loadCavi() // Ricarica la lista cavi
                        ;
                    },
                    onError: handleDialogError
                }, void 0, false, {
                    fileName: "[project]/src/app/cavi/page.tsx",
                    lineNumber: 768,
                    columnNumber: 7
                }, this),
                dettagliCavoDialog.open && dettagliCavoDialog.cavo && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex justify-between items-center mb-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                        className: "text-xl font-semibold",
                                        children: [
                                            "Dettagli Cavo ",
                                            dettagliCavoDialog.cavo.id_cavo
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/cavi/page.tsx",
                                        lineNumber: 785,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: ()=>setDettagliCavoDialog({
                                                open: false,
                                                cavo: null
                                            }),
                                        className: "text-gray-500 hover:text-gray-700",
                                        children: "✕"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/cavi/page.tsx",
                                        lineNumber: 786,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/cavi/page.tsx",
                                lineNumber: 784,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "grid grid-cols-2 gap-4 text-sm",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-medium text-gray-600",
                                                children: "ID Cavo:"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/cavi/page.tsx",
                                                lineNumber: 795,
                                                columnNumber: 20
                                            }, this),
                                            " ",
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-bold",
                                                children: dettagliCavoDialog.cavo.id_cavo
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/cavi/page.tsx",
                                                lineNumber: 795,
                                                columnNumber: 80
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/cavi/page.tsx",
                                        lineNumber: 795,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-medium text-gray-600",
                                                children: "Tipologia:"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/cavi/page.tsx",
                                                lineNumber: 796,
                                                columnNumber: 20
                                            }, this),
                                            " ",
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-bold",
                                                children: dettagliCavoDialog.cavo.tipologia || 'N/A'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/cavi/page.tsx",
                                                lineNumber: 796,
                                                columnNumber: 82
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/cavi/page.tsx",
                                        lineNumber: 796,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-medium text-gray-600",
                                                children: "Sezione:"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/cavi/page.tsx",
                                                lineNumber: 797,
                                                columnNumber: 20
                                            }, this),
                                            " ",
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-bold",
                                                children: dettagliCavoDialog.cavo.sezione || dettagliCavoDialog.cavo.formazione || 'N/A'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/cavi/page.tsx",
                                                lineNumber: 797,
                                                columnNumber: 80
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/cavi/page.tsx",
                                        lineNumber: 797,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-medium text-gray-600",
                                                children: "Sistema:"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/cavi/page.tsx",
                                                lineNumber: 798,
                                                columnNumber: 20
                                            }, this),
                                            " ",
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-bold",
                                                children: dettagliCavoDialog.cavo.sistema || 'N/A'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/cavi/page.tsx",
                                                lineNumber: 798,
                                                columnNumber: 80
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/cavi/page.tsx",
                                        lineNumber: 798,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-medium text-gray-600",
                                                children: "Utility:"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/cavi/page.tsx",
                                                lineNumber: 799,
                                                columnNumber: 20
                                            }, this),
                                            " ",
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-bold",
                                                children: dettagliCavoDialog.cavo.utility || 'N/A'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/cavi/page.tsx",
                                                lineNumber: 799,
                                                columnNumber: 80
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/cavi/page.tsx",
                                        lineNumber: 799,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-medium text-gray-600",
                                                children: "Colore:"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/cavi/page.tsx",
                                                lineNumber: 800,
                                                columnNumber: 20
                                            }, this),
                                            " ",
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-bold",
                                                children: dettagliCavoDialog.cavo.colore_cavo || 'N/A'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/cavi/page.tsx",
                                                lineNumber: 800,
                                                columnNumber: 79
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/cavi/page.tsx",
                                        lineNumber: 800,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-medium text-gray-600",
                                                children: "Da:"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/cavi/page.tsx",
                                                lineNumber: 801,
                                                columnNumber: 20
                                            }, this),
                                            " ",
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-bold",
                                                children: dettagliCavoDialog.cavo.ubicazione_partenza || dettagliCavoDialog.cavo.da || 'N/A'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/cavi/page.tsx",
                                                lineNumber: 801,
                                                columnNumber: 75
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/cavi/page.tsx",
                                        lineNumber: 801,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-medium text-gray-600",
                                                children: "A:"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/cavi/page.tsx",
                                                lineNumber: 802,
                                                columnNumber: 20
                                            }, this),
                                            " ",
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-bold",
                                                children: dettagliCavoDialog.cavo.ubicazione_arrivo || dettagliCavoDialog.cavo.a || 'N/A'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/cavi/page.tsx",
                                                lineNumber: 802,
                                                columnNumber: 74
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/cavi/page.tsx",
                                        lineNumber: 802,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-medium text-gray-600",
                                                children: "Metri Teorici:"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/cavi/page.tsx",
                                                lineNumber: 803,
                                                columnNumber: 20
                                            }, this),
                                            " ",
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-bold",
                                                children: dettagliCavoDialog.cavo.metri_teorici || 0
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/cavi/page.tsx",
                                                lineNumber: 803,
                                                columnNumber: 86
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/cavi/page.tsx",
                                        lineNumber: 803,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-medium text-gray-600",
                                                children: "Metri Posati:"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/cavi/page.tsx",
                                                lineNumber: 804,
                                                columnNumber: 20
                                            }, this),
                                            " ",
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-bold",
                                                children: dettagliCavoDialog.cavo.metri_posati || dettagliCavoDialog.cavo.metratura_reale || 0
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/cavi/page.tsx",
                                                lineNumber: 804,
                                                columnNumber: 85
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/cavi/page.tsx",
                                        lineNumber: 804,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-medium text-gray-600",
                                                children: "Stato:"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/cavi/page.tsx",
                                                lineNumber: 805,
                                                columnNumber: 20
                                            }, this),
                                            " ",
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-bold",
                                                children: dettagliCavoDialog.cavo.stato_installazione || 'N/A'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/cavi/page.tsx",
                                                lineNumber: 805,
                                                columnNumber: 78
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/cavi/page.tsx",
                                        lineNumber: 805,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-medium text-gray-600",
                                                children: "Bobina:"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/cavi/page.tsx",
                                                lineNumber: 806,
                                                columnNumber: 20
                                            }, this),
                                            " ",
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-bold",
                                                children: dettagliCavoDialog.cavo.id_bobina || 'N/A'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/cavi/page.tsx",
                                                lineNumber: 806,
                                                columnNumber: 79
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/cavi/page.tsx",
                                        lineNumber: 806,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-medium text-gray-600",
                                                children: "Collegamenti:"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/cavi/page.tsx",
                                                lineNumber: 807,
                                                columnNumber: 20
                                            }, this),
                                            " ",
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-bold",
                                                children: dettagliCavoDialog.cavo.collegamento || dettagliCavoDialog.cavo.collegamenti || 0
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/cavi/page.tsx",
                                                lineNumber: 807,
                                                columnNumber: 85
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/cavi/page.tsx",
                                        lineNumber: 807,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-medium text-gray-600",
                                                children: "Modificato Manualmente:"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/cavi/page.tsx",
                                                lineNumber: 808,
                                                columnNumber: 20
                                            }, this),
                                            " ",
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-bold",
                                                children: dettagliCavoDialog.cavo.modificato_manualmente || 0
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/cavi/page.tsx",
                                                lineNumber: 808,
                                                columnNumber: 95
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/cavi/page.tsx",
                                        lineNumber: 808,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-medium text-gray-600",
                                                children: "Responsabile Posa:"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/cavi/page.tsx",
                                                lineNumber: 809,
                                                columnNumber: 20
                                            }, this),
                                            " ",
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-bold",
                                                children: dettagliCavoDialog.cavo.responsabile_posa || 'N/A'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/cavi/page.tsx",
                                                lineNumber: 809,
                                                columnNumber: 90
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/cavi/page.tsx",
                                        lineNumber: 809,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-medium text-gray-600",
                                                children: "Timestamp:"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/cavi/page.tsx",
                                                lineNumber: 810,
                                                columnNumber: 20
                                            }, this),
                                            " ",
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-bold",
                                                children: dettagliCavoDialog.cavo.timestamp || 'N/A'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/cavi/page.tsx",
                                                lineNumber: 810,
                                                columnNumber: 82
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/cavi/page.tsx",
                                        lineNumber: 810,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/cavi/page.tsx",
                                lineNumber: 794,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mt-6 flex justify-end",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>setDettagliCavoDialog({
                                            open: false,
                                            cavo: null
                                        }),
                                    className: "px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",
                                    children: "Chiudi"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/cavi/page.tsx",
                                    lineNumber: 814,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/cavi/page.tsx",
                                lineNumber: 813,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/cavi/page.tsx",
                        lineNumber: 783,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/cavi/page.tsx",
                    lineNumber: 782,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/cavi/page.tsx",
            lineNumber: 580,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/cavi/page.tsx",
        lineNumber: 579,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=src_54f69010._.js.map