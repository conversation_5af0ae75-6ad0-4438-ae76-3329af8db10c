(()=>{var e={};e.id=562,e.ids=[562],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,t,a)=>{"use strict";a.d(t,{A0:()=>n,BF:()=>o,Hj:()=>l,XI:()=>r,nA:()=>c,nd:()=>d});var i=a(60687);a(43210);var s=a(4780);function r({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,i.jsx)("table",{"data-slot":"table",className:(0,s.cn)("w-full caption-bottom text-sm border-collapse",e),...t})})}function n({className:e,...t}){return(0,i.jsx)("thead",{"data-slot":"table-header",className:(0,s.cn)("[&_tr]:border-b",e),...t})}function o({className:e,...t}){return(0,i.jsx)("tbody",{"data-slot":"table-body",className:(0,s.cn)("[&_tr:last-child]:border-0",e),...t})}function l({className:e,...t}){return(0,i.jsx)("tr",{"data-slot":"table-row",className:(0,s.cn)("data-[state=selected]:bg-muted border-b",e),...t})}function d({className:e,...t}){return(0,i.jsx)("th",{"data-slot":"table-head",className:(0,s.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function c({className:e,...t}){return(0,i.jsx)("td",{"data-slot":"table-cell",className:(0,s.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},15079:(e,t,a)=>{"use strict";a.d(t,{bq:()=>m,eb:()=>x,gC:()=>u,l6:()=>d,yv:()=>c});var i=a(60687);a(43210);var s=a(97822),r=a(78272),n=a(13964),o=a(3589),l=a(4780);function d({...e}){return(0,i.jsx)(s.bL,{"data-slot":"select",...e})}function c({...e}){return(0,i.jsx)(s.WT,{"data-slot":"select-value",...e})}function m({className:e,size:t="default",children:a,...n}){return(0,i.jsxs)(s.l9,{"data-slot":"select-trigger","data-size":t,className:(0,l.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n,children:[a,(0,i.jsx)(s.In,{asChild:!0,children:(0,i.jsx)(r.A,{className:"size-4 opacity-50"})})]})}function u({className:e,children:t,position:a="popper",...r}){return(0,i.jsx)(s.ZL,{children:(0,i.jsxs)(s.UC,{"data-slot":"select-content",className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...r,children:[(0,i.jsx)(p,{}),(0,i.jsx)(s.LM,{className:(0,l.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,i.jsx)(b,{})]})})}function x({className:e,children:t,...a}){return(0,i.jsxs)(s.q7,{"data-slot":"select-item",className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...a,children:[(0,i.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,i.jsx)(s.VF,{children:(0,i.jsx)(n.A,{className:"size-4"})})}),(0,i.jsx)(s.p4,{children:t})]})}function p({className:e,...t}){return(0,i.jsx)(s.PP,{"data-slot":"select-scroll-up-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,i.jsx)(o.A,{className:"size-4"})})}function b({className:e,...t}){return(0,i.jsx)(s.wn,{"data-slot":"select-scroll-down-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,i.jsx)(r.A,{className:"size-4"})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28227:(e,t,a)=>{Promise.resolve().then(a.bind(a,64435))},28354:e=>{"use strict";e.exports=require("util")},28907:(e,t,a)=>{Promise.resolve().then(a.bind(a,71902))},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29844:(e,t,a)=>{"use strict";a.d(t,{tU:()=>S,av:()=>I,j7:()=>E,Xi:()=>T});var i=a(60687),s=a(43210),r=a(70569),n=a(11273),o=a(72942),l=a(46059),d=a(14163),c=a(43),m=a(65551),u=a(96963),x="Tabs",[p,b]=(0,n.A)(x,[o.RG]),h=(0,o.RG)(),[g,v]=p(x),f=s.forwardRef((e,t)=>{let{__scopeTabs:a,value:s,onValueChange:r,defaultValue:n,orientation:o="horizontal",dir:l,activationMode:p="automatic",...b}=e,h=(0,c.jH)(l),[v,f]=(0,m.i)({prop:s,onChange:r,defaultProp:n??"",caller:x});return(0,i.jsx)(g,{scope:a,baseId:(0,u.B)(),value:v,onValueChange:f,orientation:o,dir:h,activationMode:p,children:(0,i.jsx)(d.sG.div,{dir:h,"data-orientation":o,...b,ref:t})})});f.displayName=x;var j="TabsList",N=s.forwardRef((e,t)=>{let{__scopeTabs:a,loop:s=!0,...r}=e,n=v(j,a),l=h(a);return(0,i.jsx)(o.bL,{asChild:!0,...l,orientation:n.orientation,dir:n.dir,loop:s,children:(0,i.jsx)(d.sG.div,{role:"tablist","aria-orientation":n.orientation,...r,ref:t})})});N.displayName=j;var y="TabsTrigger",_=s.forwardRef((e,t)=>{let{__scopeTabs:a,value:s,disabled:n=!1,...l}=e,c=v(y,a),m=h(a),u=C(c.baseId,s),x=D(c.baseId,s),p=s===c.value;return(0,i.jsx)(o.q7,{asChild:!0,...m,focusable:!n,active:p,children:(0,i.jsx)(d.sG.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":x,"data-state":p?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:u,...l,ref:t,onMouseDown:(0,r.m)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(s)}),onKeyDown:(0,r.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(s)}),onFocus:(0,r.m)(e.onFocus,()=>{let e="manual"!==c.activationMode;p||n||!e||c.onValueChange(s)})})})});_.displayName=y;var w="TabsContent",z=s.forwardRef((e,t)=>{let{__scopeTabs:a,value:r,forceMount:n,children:o,...c}=e,m=v(w,a),u=C(m.baseId,r),x=D(m.baseId,r),p=r===m.value,b=s.useRef(p);return s.useEffect(()=>{let e=requestAnimationFrame(()=>b.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,i.jsx)(l.C,{present:n||p,children:({present:a})=>(0,i.jsx)(d.sG.div,{"data-state":p?"active":"inactive","data-orientation":m.orientation,role:"tabpanel","aria-labelledby":u,hidden:!a,id:x,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:b.current?"0s":void 0},children:a&&o})})});function C(e,t){return`${e}-trigger-${t}`}function D(e,t){return`${e}-content-${t}`}z.displayName=w;var A=a(4780);let S=f,E=s.forwardRef(({className:e,...t},a)=>(0,i.jsx)(N,{ref:a,className:(0,A.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));E.displayName=N.displayName;let T=s.forwardRef(({className:e,...t},a)=>(0,i.jsx)(_,{ref:a,className:(0,A.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));T.displayName=_.displayName;let I=s.forwardRef(({className:e,...t},a)=>(0,i.jsx)(z,{ref:a,className:(0,A.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));I.displayName=z.displayName},33873:e=>{"use strict";e.exports=require("path")},44493:(e,t,a)=>{"use strict";a.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>o,Zp:()=>r,aR:()=>n});var i=a(60687);a(43210);var s=a(4780);function r({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...t})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},63503:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>m,Es:()=>x,L3:()=>p,c7:()=>u,lG:()=>o,rr:()=>b,zM:()=>l});var i=a(60687);a(43210);var s=a(26134),r=a(11860),n=a(4780);function o({...e}){return(0,i.jsx)(s.bL,{"data-slot":"dialog",...e})}function l({...e}){return(0,i.jsx)(s.l9,{"data-slot":"dialog-trigger",...e})}function d({...e}){return(0,i.jsx)(s.ZL,{"data-slot":"dialog-portal",...e})}function c({className:e,...t}){return(0,i.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function m({className:e,children:t,showCloseButton:a=!0,...o}){return(0,i.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,i.jsx)(c,{}),(0,i.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...o,children:[t,a&&(0,i.jsxs)(s.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,i.jsx)(r.A,{}),(0,i.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function x({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"dialog-footer",className:(0,n.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function p({className:e,...t}){return(0,i.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,n.cn)("text-lg leading-none font-semibold",e),...t})}function b({className:e,...t}){return(0,i.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t})}},64435:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>Z});var i=a(60687),s=a(43210),r=a(29523),n=a(89667),o=a(6211),l=a(91821),d=a(63213),c=a(76628),m=a(25653),u=a(62185);let x={DISPONIBILE:"Disponibile",IN_USO:"In uso",TERMINATA:"Terminata",OVER:"Over"},p=(e,t)=>e<0?x.OVER:0===e?x.TERMINATA:e<t?x.IN_USO:x.DISPONIBILE,b=e=>e===x.DISPONIBILE||e===x.IN_USO,h=e=>{switch(e){case x.DISPONIBILE:return"hover:bg-green-50";case x.IN_USO:return"hover:bg-yellow-50";case x.TERMINATA:return"hover:bg-red-50";case x.OVER:return"hover:bg-red-100";default:return"hover:bg-gray-50"}},g=e=>e!==x.OVER&&e!==x.TERMINATA,v=(e,t)=>t<=0?0:Math.max(0,Math.min(100,(t-e)/t*100)),f=e=>`${e.toFixed(1)}m`,j=e=>{switch(e){case x.DISPONIBILE:return"Bobina disponibile per nuove installazioni";case x.IN_USO:return"Bobina parzialmente utilizzata";case x.TERMINATA:return"Bobina completamente esaurita";case x.OVER:return"Bobina sovra-utilizzata (metri negativi)";default:return"Stato non definito"}};var N=a(63503),y=a(80013),_=a(96474),w=a(41862),z=a(93613);let C={numero_bobina:"",utility:"",tipologia:"",n_conduttori:"0",sezione:"",metri_totali:"",ubicazione_bobina:"TBD",fornitore:"TBD",n_DDT:"TBD",data_DDT:"",configurazione:"s"};function D({open:e,onClose:t,cantiereId:a,onSuccess:o,onError:d}){let[c,m]=(0,s.useState)(C),[x,p]=(0,s.useState)(!1),[b,h]=(0,s.useState)(""),[g,v]=(0,s.useState)("1"),[f,j]=(0,s.useState)(!1),[D,A]=(0,s.useState)(!0),[S,E]=(0,s.useState)(""),[T,I]=(0,s.useState)(!1),k=async()=>{if(a&&!(a<=0))try{let e=await u.Fw.getBobine(a);if(e&&e.length>0){let t=e.filter(e=>e.numero_bobina&&/^\d+$/.test(e.numero_bobina));if(t.length>0){let e=Math.max(...t.map(e=>parseInt(e.numero_bobina,10))),a=String(e+1);v(a),m(e=>({...e,numero_bobina:a}))}else v("1"),m(e=>({...e,numero_bobina:"1"}))}else v("1"),m(e=>({...e,numero_bobina:"1"}))}catch(e){v("1"),m(e=>({...e,numero_bobina:"1"}))}},F=(e,t)=>{m(a=>({...a,[e]:t})),h("")},M=async e=>{E(e),m(t=>({...t,configurazione:e})),I(!1),"s"===e?await k():m(e=>({...e,numero_bobina:""}))},R=()=>{if(!c.numero_bobina.trim())return"Il numero bobina \xe8 obbligatorio";if("m"===c.configurazione){let e=c.numero_bobina.trim();if(/[\s\\/:*?"<>|]/.test(e))return'Il numero bobina non pu\xf2 contenere spazi o caratteri speciali come \\ / : * ? " < > |'}if(!c.utility.trim())return"La utility \xe8 obbligatoria";if(!c.tipologia.trim())return"La tipologia \xe8 obbligatoria";if(!c.sezione.trim())return"La formazione \xe8 obbligatoria";if(!c.metri_totali.trim())return"I metri totali sono obbligatori";let e=parseFloat(c.metri_totali);return isNaN(e)||e<=0?"I metri totali devono essere un numero positivo":null},O=async()=>{let e=R();if(e)return void h(e);try{if(p(!0),h(""),!a||a<=0)throw Error("Cantiere non selezionato");let e={numero_bobina:c.numero_bobina.trim(),utility:c.utility.trim().toUpperCase(),tipologia:c.tipologia.trim().toUpperCase(),n_conduttori:"0",sezione:c.sezione.trim(),metri_totali:parseFloat(c.metri_totali),ubicazione_bobina:c.ubicazione_bobina.trim()||"TBD",fornitore:c.fornitore.trim()||"TBD",n_DDT:c.n_DDT.trim()||"TBD",data_DDT:c.data_DDT||null,configurazione:c.configurazione};await u.Fw.createBobina(a,e),o(`Bobina ${c.numero_bobina} creata con successo`),t()}catch(t){let e=t.response?.data?.detail||t.message||"Errore durante la creazione della bobina";e.includes("gi\xe0 presente nel cantiere")||e.includes("gi\xe0 esistente")?h(`⚠️ Bobina con numero ${c.numero_bobina} gi\xe0 esistente. Scegli un numero diverso.`):d(e)}finally{p(!1)}},B=()=>{x||(m(C),h(""),t())};return(0,i.jsx)(N.lG,{open:e,onOpenChange:B,children:(0,i.jsxs)(N.Cf,{className:"max-h-[95vh] overflow-y-auto",style:{width:"1000px !important",maxWidth:"95vw !important",minWidth:"1000px"},children:[(0,i.jsxs)(N.c7,{children:[(0,i.jsxs)(N.L3,{className:"flex items-center gap-2",children:[(0,i.jsx)(_.A,{className:"h-5 w-5"}),"Crea Nuova Bobina"]}),(0,i.jsx)(N.rr,{})]}),(0,i.jsxs)("div",{className:"grid gap-4 py-4",children:[f&&(0,i.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,i.jsx)(w.A,{className:"h-6 w-6 animate-spin mr-2"}),(0,i.jsx)("span",{children:"Verifica configurazione..."})]}),T&&!f&&(0,i.jsxs)("div",{className:"border rounded-lg p-4 bg-blue-50",children:[(0,i.jsx)("h4",{className:"font-medium mb-3",children:"Seleziona configurazione per questo cantiere"}),(0,i.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Questa scelta determiner\xe0 come verranno numerati tutti i futuri inserimenti di bobine in questo cantiere."}),(0,i.jsxs)("div",{className:"grid grid-cols-1 gap-3",children:[(0,i.jsx)(r.$,{variant:"outline",className:"justify-start h-auto p-4",onClick:()=>M("s"),children:(0,i.jsxs)("div",{className:"text-left",children:[(0,i.jsx)("div",{className:"font-medium",children:"Standard (s) - Numerazione automatica"}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:"I numeri bobina vengono generati automaticamente: 1, 2, 3..."})]})}),(0,i.jsx)(r.$,{variant:"outline",className:"justify-start h-auto p-4",onClick:()=>M("m"),children:(0,i.jsxs)("div",{className:"text-left",children:[(0,i.jsx)("div",{className:"font-medium",children:"Manuale (m) - Inserimento manuale"}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:"Puoi inserire numeri personalizzati: A123, TEST01, ecc."})]})})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{className:"space-y-4",children:[!T&&!f&&(0,i.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,i.jsx)(y.J,{htmlFor:"numero_bobina",className:"text-right",children:"Bobina *"}),(0,i.jsx)(n.p,{id:"numero_bobina",value:c.numero_bobina,onChange:e=>F("numero_bobina",e.target.value),placeholder:"s"===c.configurazione?"Generato automaticamente":"Es: A123, TEST01",disabled:x||"s"===c.configurazione,className:`col-span-2 ${"s"===c.configurazione?"bg-gray-50":""}`})]}),(0,i.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,i.jsx)(y.J,{htmlFor:"utility",className:"text-right",children:"Utility *"}),(0,i.jsx)(n.p,{id:"utility",value:c.utility,onChange:e=>F("utility",e.target.value),className:"col-span-2",placeholder:"Es: ENEL, TIM, OPEN FIBER",disabled:x})]}),(0,i.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,i.jsx)(y.J,{htmlFor:"tipologia",className:"text-right",children:"Tipologia *"}),(0,i.jsx)(n.p,{id:"tipologia",value:c.tipologia,onChange:e=>F("tipologia",e.target.value),className:"col-span-2",placeholder:"Es: FO, RAME",disabled:x})]}),(0,i.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,i.jsx)(y.J,{htmlFor:"sezione",className:"text-right",children:"Formazione *"}),(0,i.jsx)(n.p,{id:"sezione",value:c.sezione,onChange:e=>F("sezione",e.target.value),className:"col-span-2",placeholder:"Es: 9/125, 50/125, 1.5",disabled:x})]}),(0,i.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,i.jsx)(y.J,{htmlFor:"metri_totali",className:"text-right",children:"Metri Totali *"}),(0,i.jsx)(n.p,{id:"metri_totali",type:"number",step:"0.1",min:"0",value:c.metri_totali,onChange:e=>F("metri_totali",e.target.value),className:"col-span-2",placeholder:"Es: 1000",disabled:x})]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,i.jsx)(y.J,{htmlFor:"ubicazione_bobina",className:"text-right",children:"Ubicazione"}),(0,i.jsx)(n.p,{id:"ubicazione_bobina",value:c.ubicazione_bobina,onChange:e=>F("ubicazione_bobina",e.target.value),className:"col-span-2",placeholder:"Es: Magazzino A, Cantiere",disabled:x})]}),(0,i.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,i.jsx)(y.J,{htmlFor:"fornitore",className:"text-right",children:"Fornitore"}),(0,i.jsx)(n.p,{id:"fornitore",value:c.fornitore,onChange:e=>F("fornitore",e.target.value),className:"col-span-2",placeholder:"Es: Prysmian, Nexans",disabled:x})]}),(0,i.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,i.jsx)(y.J,{htmlFor:"n_DDT",className:"text-right",children:"N\xb0 DDT"}),(0,i.jsx)(n.p,{id:"n_DDT",value:c.n_DDT,onChange:e=>F("n_DDT",e.target.value),className:"col-span-2",placeholder:"Es: DDT001",disabled:x})]}),(0,i.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,i.jsx)(y.J,{htmlFor:"data_DDT",className:"text-right",children:"Data DDT"}),(0,i.jsx)(n.p,{id:"data_DDT",type:"date",value:c.data_DDT,onChange:e=>F("data_DDT",e.target.value),className:"col-span-2",disabled:x})]})]})]}),b&&(0,i.jsxs)(l.Fc,{variant:"destructive",children:[(0,i.jsx)(z.A,{className:"h-4 w-4"}),(0,i.jsx)(l.TN,{children:b})]})]}),(0,i.jsxs)(N.Es,{children:[(0,i.jsx)(r.$,{variant:"outline",onClick:B,disabled:x||T,children:"Annulla"}),(0,i.jsxs)(r.$,{onClick:O,disabled:x||T||f,children:[x&&(0,i.jsx)(w.A,{className:"mr-2 h-4 w-4 animate-spin"}),x?"Creando...":"Crea Bobina"]})]})]})})}var A=a(15079),S=a(19080),E=a(96882);function T({open:e,onClose:t,bobina:a,onSuccess:o,onError:c}){let{cantiere:m}=(0,d.A)(),[x,p]=(0,s.useState)({numero_bobina:"",utility:"",tipologia:"",sezione:"",metri_totali:"",metri_residui:"",stato_bobina:"",ubicazione_bobina:"",fornitore:"",n_DDT:"",data_DDT:"",configurazione:""}),[b,h]=(0,s.useState)({}),[g,v]=(0,s.useState)({}),[f,j]=(0,s.useState)(!1),_=(e,t)=>{p(i=>{let s={...i,[e]:t};return"metri_totali"===e&&a&&(s.metri_residui=Math.max(0,(parseFloat(t)||0)-(a.metri_totali-a.metri_residui)).toString()),s})},C=()=>!!a&&("Over"===a.stato_bobina||"Disponibile"===a.stato_bobina),D=e=>!!a&&(!!["fornitore","ubicazione_bobina","n_DDT","data_DDT"].includes(e)||"Disponibile"===a.stato_bobina),T=async()=>{if(a&&m&&!(Object.keys(b).length>0)){if(!C())return void c("La bobina non pu\xf2 essere modificata nel suo stato attuale");try{j(!0);let e={utility:x.utility,tipologia:x.tipologia,sezione:x.sezione,metri_totali:parseFloat(x.metri_totali),ubicazione_bobina:x.ubicazione_bobina,fornitore:x.fornitore,n_DDT:x.n_DDT,data_DDT:x.data_DDT||null};await u.Fw.updateBobina(m.id_cantiere,a.id_bobina,e),o(`Bobina ${a.numero_bobina} aggiornata con successo`),t()}catch(e){c(e.response?.data?.detail||e.message||"Errore durante la modifica della bobina")}finally{j(!1)}}},I=()=>{f||(p({numero_bobina:"",utility:"",tipologia:"",sezione:"",metri_totali:"",metri_residui:"",stato_bobina:"",ubicazione_bobina:"",fornitore:"",n_DDT:"",data_DDT:"",configurazione:""}),h({}),v({}),t())};return a?(0,i.jsx)(N.lG,{open:e,onOpenChange:I,children:(0,i.jsxs)(N.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,i.jsxs)(N.c7,{children:[(0,i.jsxs)(N.L3,{className:"flex items-center gap-2",children:[(0,i.jsx)(S.A,{className:"h-5 w-5"}),"Modifica Bobina"]}),(0,i.jsxs)(N.rr,{children:["Modifica i dati della bobina ",a.numero_bobina]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)(l.Fc,{className:"border-blue-200 bg-blue-50",children:[(0,i.jsx)(E.A,{className:"h-4 w-4 text-blue-600"}),(0,i.jsxs)(l.TN,{className:"text-blue-800",children:[(0,i.jsx)("div",{className:"font-semibold mb-1",children:"Condizioni per la modifica:"}),(0,i.jsxs)("ul",{className:"list-disc list-inside text-sm space-y-1",children:[(0,i.jsx)("li",{children:'La bobina deve essere nello stato "Disponibile"'}),(0,i.jsx)("li",{children:"La bobina non deve essere associata a nessun cavo"}),(0,i.jsx)("li",{children:"Se modifichi i metri totali, i metri residui verranno aggiornati automaticamente"})]}),(0,i.jsxs)("div",{className:"mt-2 text-sm",children:[(0,i.jsx)("strong",{children:"Stato attuale:"})," ",a.stato_bobina]})]})]}),Object.keys(g).length>0&&(0,i.jsxs)(l.Fc,{className:"border-amber-200 bg-amber-50",children:[(0,i.jsx)(z.A,{className:"h-4 w-4 text-amber-600"}),(0,i.jsxs)(l.TN,{className:"text-amber-800",children:[(0,i.jsx)("div",{className:"font-semibold",children:"Attenzione:"}),(0,i.jsx)("ul",{className:"list-disc list-inside text-sm",children:Object.values(g).map((e,t)=>(0,i.jsx)("li",{children:e},t))})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(y.J,{htmlFor:"numero_bobina",children:"ID Bobina"}),(0,i.jsx)(n.p,{id:"numero_bobina",value:x.numero_bobina,disabled:!0,className:"bg-gray-100 font-semibold"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(y.J,{htmlFor:"utility",children:"Utility *"}),(0,i.jsx)(n.p,{id:"utility",value:x.utility,onChange:e=>_("utility",e.target.value),disabled:f||!D("utility"),className:b.utility?"border-red-500":""}),b.utility&&(0,i.jsx)("p",{className:"text-sm text-red-600",children:b.utility})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(y.J,{htmlFor:"tipologia",children:"Tipologia *"}),(0,i.jsx)(n.p,{id:"tipologia",value:x.tipologia,onChange:e=>_("tipologia",e.target.value),disabled:f||!D("tipologia"),className:b.tipologia?"border-red-500":""}),b.tipologia&&(0,i.jsx)("p",{className:"text-sm text-red-600",children:b.tipologia})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(y.J,{htmlFor:"sezione",children:"Formazione *"}),(0,i.jsx)(n.p,{id:"sezione",value:x.sezione,onChange:e=>_("sezione",e.target.value),disabled:f||!D("sezione"),className:b.sezione?"border-red-500":""}),b.sezione&&(0,i.jsx)("p",{className:"text-sm text-red-600",children:b.sezione})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(y.J,{htmlFor:"metri_totali",children:"Metri Totali *"}),(0,i.jsx)(n.p,{id:"metri_totali",type:"number",value:x.metri_totali,onChange:e=>_("metri_totali",e.target.value),disabled:f||!D("metri_totali"),className:b.metri_totali?"border-red-500":"",step:"0.1",min:"0"}),b.metri_totali&&(0,i.jsx)("p",{className:"text-sm text-red-600",children:b.metri_totali})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(y.J,{htmlFor:"metri_residui",children:"Metri Residui"}),(0,i.jsx)(n.p,{id:"metri_residui",type:"number",value:x.metri_residui,disabled:!0,className:"bg-gray-100"}),(0,i.jsx)("p",{className:"text-sm text-gray-500",children:"I metri residui non possono essere modificati direttamente"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(y.J,{htmlFor:"stato_bobina",children:"Stato Bobina"}),(0,i.jsxs)(A.l6,{value:x.stato_bobina,disabled:!0,children:[(0,i.jsx)(A.bq,{className:"bg-gray-100",children:(0,i.jsx)(A.yv,{})}),(0,i.jsxs)(A.gC,{children:[(0,i.jsx)(A.eb,{value:"Disponibile",children:"Disponibile"}),(0,i.jsx)(A.eb,{value:"In uso",children:"In uso"}),(0,i.jsx)(A.eb,{value:"Terminata",children:"Terminata"}),(0,i.jsx)(A.eb,{value:"Danneggiata",children:"Danneggiata"}),(0,i.jsx)(A.eb,{value:"Over",children:"Over"})]})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(y.J,{htmlFor:"ubicazione_bobina",children:"Ubicazione Bobina"}),(0,i.jsx)(n.p,{id:"ubicazione_bobina",value:x.ubicazione_bobina,onChange:e=>_("ubicazione_bobina",e.target.value),disabled:f})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(y.J,{htmlFor:"fornitore",children:"Fornitore"}),(0,i.jsx)(n.p,{id:"fornitore",value:x.fornitore,onChange:e=>_("fornitore",e.target.value),disabled:f})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(y.J,{htmlFor:"n_DDT",children:"Numero DDT"}),(0,i.jsx)(n.p,{id:"n_DDT",value:x.n_DDT,onChange:e=>_("n_DDT",e.target.value),disabled:f})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(y.J,{htmlFor:"data_DDT",children:"Data DDT (YYYY-MM-DD)"}),(0,i.jsx)(n.p,{id:"data_DDT",type:"date",value:x.data_DDT,onChange:e=>_("data_DDT",e.target.value),disabled:f,className:b.data_DDT?"border-red-500":""}),b.data_DDT&&(0,i.jsx)("p",{className:"text-sm text-red-600",children:b.data_DDT})]}),(0,i.jsxs)("div",{className:"space-y-2 md:col-span-2",children:[(0,i.jsx)(y.J,{htmlFor:"configurazione",children:"Modalit\xe0 Numerazione"}),(0,i.jsx)(n.p,{id:"configurazione",value:"s"===x.configurazione?"Automatica":"Manuale",disabled:!0,className:"bg-gray-100"}),(0,i.jsx)("p",{className:"text-sm text-gray-500",children:"s"===x.configurazione?"Numerazione progressiva automatica (1, 2, 3, ...)":"Inserimento manuale dell'ID bobina (es. A123, SPEC01, ...)"})]})]})]}),(0,i.jsxs)(N.Es,{children:[(0,i.jsx)(r.$,{variant:"outline",onClick:I,disabled:f,children:"Annulla"}),(0,i.jsxs)(r.$,{onClick:T,disabled:f||Object.keys(b).length>0||!C(),className:"bg-mariner-600 hover:bg-mariner-700",children:[f&&(0,i.jsx)(w.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Salva"]})]})]})}):null}var I=a(88233),k=a(43649);function F({open:e,onClose:t,bobina:a,cantiereId:n,onSuccess:o,onError:d}){let[c,m]=(0,s.useState)(!1),p=async()=>{if(a)try{if(m(!0),!n||n<=0)throw Error("Cantiere non selezionato");let e=a.id_bobina.split("_B")[1],i=await u.Fw.deleteBobina(n,e),s=`Bobina ${a.numero_bobina} eliminata con successo`;i.data?.is_last_bobina&&(s+=". Era l'ultima bobina del cantiere."),o(s),t()}catch(e){d(e.response?.data?.detail||e.message||"Errore durante l'eliminazione della bobina")}finally{m(!1)}},h=()=>{c||t()};if(!a)return null;let g=a.stato_bobina===x.OVER,v=!g&&b(a.stato_bobina)&&a.metri_residui===a.metri_totali,f=g?{type:"error",title:"Bobina OVER - Eliminazione bloccata",message:"Le bobine in stato OVER non possono essere eliminate per preservare la tracciabilit\xe0 del sistema. Contattare l'amministratore per la gestione di bobine OVER."}:b(a.stato_bobina)?a.metri_residui!==a.metri_totali?{type:"error",title:"Bobina in uso",message:`La bobina ha ${a.metri_residui}m residui su ${a.metri_totali}m totali. Solo le bobine completamente disponibili possono essere eliminate.`}:{type:"warning",title:"Conferma eliminazione",message:"Questa operazione \xe8 irreversibile. La bobina verr\xe0 rimossa definitivamente dal parco cavi."}:{type:"error",title:"Eliminazione non consentita",message:`La bobina \xe8 in stato "${a.stato_bobina}" e non pu\xf2 essere eliminata. ${j(a.stato_bobina)}`};return(0,i.jsx)(N.lG,{open:e,onOpenChange:h,children:(0,i.jsxs)(N.Cf,{className:"max-w-md",children:[(0,i.jsxs)(N.c7,{children:[(0,i.jsxs)(N.L3,{className:"flex items-center gap-2",children:[(0,i.jsx)(I.A,{className:"h-5 w-5 text-red-600"}),"Elimina Bobina"]}),(0,i.jsxs)(N.rr,{children:["Stai per eliminare la bobina ",a.numero_bobina]})]}),(0,i.jsxs)("div",{className:"py-4",children:[(0,i.jsxs)("div",{className:"bg-slate-50 p-4 rounded-lg mb-4",children:[(0,i.jsx)("h4",{className:"font-medium mb-2",children:"Dettagli bobina:"}),(0,i.jsxs)("div",{className:"text-sm space-y-1",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Bobina:"})," ",a.numero_bobina]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Utility:"})," ",a.utility]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Tipologia:"})," ",a.tipologia]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Sezione:"})," ",a.sezione]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Stato:"})," ",a.stato_bobina]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Metri:"})," ",a.metri_residui,"m / ",a.metri_totali,"m"]}),a.ubicazione_bobina&&(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Ubicazione:"})," ",a.ubicazione_bobina]})]})]}),(0,i.jsxs)(l.Fc,{variant:"error"===f.type?"destructive":"default",children:[(0,i.jsx)(k.A,{className:"h-4 w-4"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-medium",children:f.title}),(0,i.jsx)(l.TN,{className:"mt-1",children:f.message})]})]})]}),(0,i.jsxs)(N.Es,{children:[(0,i.jsx)(r.$,{variant:"outline",onClick:h,disabled:c,children:"Annulla"}),(0,i.jsxs)(r.$,{variant:"destructive",onClick:p,disabled:c||!v,children:[c&&(0,i.jsx)(w.A,{className:"mr-2 h-4 w-4 animate-spin"}),c?"Eliminando...":"Elimina Bobina"]})]})]})})}var M=a(29844),R=a(23361),O=a(99270);let B=(e,t)=>{let[a,i]=(0,s.useState)(e);return(0,s.useEffect)(()=>{let a=setTimeout(()=>{i(e)},t);return()=>{clearTimeout(a)}},[e,t]),a};function L({open:e,onClose:t,bobina:a,cantiereId:o,onSuccess:l,onError:d}){let[c,m]=(0,s.useState)(!1),[x,p]=(0,s.useState)(!1),[b,h]=(0,s.useState)(!1),[g,v]=(0,s.useState)([]),[f,j]=(0,s.useState)([]),[y,_]=(0,s.useState)([]),[z,C]=(0,s.useState)({}),[D,A]=(0,s.useState)({}),[S,E]=(0,s.useState)(""),T=B(S,300),[I,k]=(0,s.useState)("id_cavo"),[F,L]=(0,s.useState)("asc"),[$,P]=(0,s.useState)("all"),[U,V]=(0,s.useState)("all"),[q,G]=(0,s.useState)(""),[J,H]=(0,s.useState)(""),[K,W]=(0,s.useState)(1),[X,Z]=(0,s.useState)(20),Y=(0,s.useMemo)(()=>{let e=e=>e.filter(e=>{let t=!T||e.id_cavo.toLowerCase().includes(T.toLowerCase())||e.tipologia?.toLowerCase().includes(T.toLowerCase())||e.ubicazione_partenza?.toLowerCase().includes(T.toLowerCase())||e.ubicazione_arrivo?.toLowerCase().includes(T.toLowerCase()),a="all"===$||e.tipologia===$,i="all"===U||e.sezione===U,s=parseFloat(e.metri_teorici?.toString()||"0"),r=!q||s>=parseFloat(q),n=!J||s<=parseFloat(J);return t&&a&&i&&r&&n}),t=e=>[...e].sort((e,t)=>{let a,i;switch(I){case"id_cavo":a=e.id_cavo,i=t.id_cavo;break;case"metri_teorici":a=parseFloat(e.metri_teorici?.toString()||"0"),i=parseFloat(t.metri_teorici?.toString()||"0");break;case"tipologia":a=e.tipologia||"",i=t.tipologia||"";break;case"ubicazione_partenza":a=e.ubicazione_partenza||"",i=t.ubicazione_partenza||"";break;default:return 0}if("string"!=typeof a)return"asc"===F?a-i:i-a;{let e=a.localeCompare(i);return"asc"===F?e:-e}});return{compatibiliFiltrati:t(e(g)),incompatibiliFiltrati:t(e(f))}},[g,f,T,I,F,$,U,q,J]),Q=(0,s.useMemo)(()=>{let{compatibiliFiltrati:e,incompatibiliFiltrati:t}=Y,a=(K-1)*X,i=a+X;return{compatibili:e.slice(a,i),incompatibili:t.slice(a,i),totalCompatibili:e.length,totalIncompatibili:t.length,totalPages:Math.ceil(Math.max(e.length,t.length)/X)}},[Y,K,X]),ee=(0,s.useMemo)(()=>{let e=Object.values(z).reduce((e,t)=>e+parseFloat(t||"0"),0),t=a?.metri_residui||0,i=Object.entries(z).some(([e,a])=>parseFloat(a||"0")>t),s=y.some(e=>e._isIncompatible),r=i||a?.stato_bobina==="OVER",n=Math.max(0,e-t);return{metriTotaliSelezionati:e,metriResiduiBobina:t,isOverState:r,metriEccedenza:n,percentualeUtilizzo:t>0?e/t*100:0,hasSingleCavoOver:i,hasIncompatibleCavi:s}},[z,a,y]);(0,s.useMemo)(()=>Array.from(new Set([...g,...f].map(e=>e.tipologia).filter(Boolean))).sort(),[g,f]),(0,s.useMemo)(()=>Array.from(new Set([...g,...f].map(e=>e.sezione).filter(Boolean))).sort(),[g,f]);let et=(e,t)=>{if(console.log("\uD83C\uDFAF AggiungiCaviDialogSimple: Toggle cavo:",{cavoId:e.id_cavo,isCompatible:t,metriTeorici:e.metri_teorici}),y.some(t=>t.id_cavo===e.id_cavo))_(t=>t.filter(t=>t.id_cavo!==e.id_cavo)),C(t=>{let a={...t};return delete a[e.id_cavo],a});else{let a={...e,_isIncompatible:!t};_(e=>[...e,a]),C(t=>({...t,[e.id_cavo]:"0"}))}},ea=(e,t)=>{a?.metri_residui,parseFloat(t||"0");let i=y.find(t=>t.id_cavo===e);i?._isIncompatible,A(t=>{let a={...t};return delete a[e],a}),C(a=>({...a,[e]:t}))},ei=(0,s.useMemo)(()=>{let e=a?.metri_residui||0,t=!1,i=[],s=[],r=null;for(let a of y){let n=parseFloat(z[a.id_cavo]||"0");n>0?t?s.push(a.id_cavo):(e-n<0?(i.push(a.id_cavo),r=a.id_cavo,t=!0):i.push(a.id_cavo),e-=n):i.push(a.id_cavo)}return{metriResiduiSimulati:e,caviValidi:i,caviBloccati:s,bobinaGiaOver:t,cavoCheCausaOver:r}},[y,z,a?.metri_residui]),es=()=>ei,er=async()=>{if(!o||!a)return;if(0===y.length)return void d("Nessun cavo selezionato");let e=y.filter(e=>{let t=z[e.id_cavo];return!t||""===t.trim()||isNaN(parseFloat(t))||0>parseFloat(t)});if(e.length>0)return void d(`Metri posati mancanti o non validi per: ${e.map(e=>e.id_cavo).join(", ")}`);try{h(!0);let{caviValidi:e,caviBloccati:i}=es(),s=y.filter(e=>{let t=parseFloat(z[e.id_cavo]||"0"),a=i.includes(e.id_cavo);return t>0&&!a});if(0===s.length)return void d("Nessun cavo valido da salvare (tutti bloccati o senza metri)");console.log("\uD83D\uDCBE AggiungiCaviDialogSimple: Preparazione salvataggio:",{caviSelezionati:y.length,caviValidi:e.length,caviBloccati:i.length,caviDaSalvare:s.length});let r=[],n=[],c=a?.metri_residui||0;for(let e of s)try{let t=z[e.id_cavo],i=parseFloat(t),s=c-i<0,n=e._isIncompatible||!1,l=s||n;console.log("⚡ AggiungiCaviDialogSimple: Aggiornamento cavo:",{metriPosati:i,metriResiduiCorrente:c,causaOver:s,isIncompatible:n,needsForceOver:l}),await u.At.updateMetriPosati(o,e.id_cavo,i,a.id_bobina,l),c-=i,r.push({cavo:e.id_cavo,metriPosati:i,success:!0,wasIncompatible:e._isIncompatible,wasForceOver:l})}catch(a){let t="Errore sconosciuto";if(a.response){let e=a.response.status,i=a.response.data;t=400===e?i?.message||i?.error||"Richiesta non valida (400)":404===e?"Cavo o bobina non trovati (404)":409===e?"Conflitto: cavo gi\xe0 assegnato o bobina non disponibile (409)":500===e?"Errore interno del server (500)":`Errore HTTP ${e}: ${i?.message||i?.error||"Errore del server"}`}else t=a.request?"Errore di connessione al server":a.message||"Errore di validazione";n.push({cavo:e.id_cavo,error:t})}if(0===n.length){let e=r.filter(e=>e.wasIncompatible).length,a=r.filter(e=>e.wasForceOver).length,i=`${r.length} cavi aggiornati con successo`;e>0&&(i+=` (${e} incompatibili)`),a>0&&(i+=` (${a} con force_over)`),l(i),t()}else d(`Errori: ${n.map(e=>`${e.cavo}: ${e.error}`).join(", ")}`)}catch(t){let e="Errore durante il salvataggio dei cavi";if(t.response){let a=t.response.status,i=t.response.data;e=400===a?`Errore di validazione: ${i?.message||i?.error||"Dati non validi"}`:401===a?"Sessione scaduta. Effettua nuovamente il login.":403===a?"Non hai i permessi per questa operazione":500===a?"Errore interno del server. Riprova pi\xf9 tardi.":`Errore del server (${a}): ${i?.message||i?.error||"Errore sconosciuto"}`}else e=t.request?"Impossibile contattare il server. Verifica la connessione.":t.message||"Errore imprevisto durante il salvataggio";d(e)}finally{h(!1)}},en=()=>{b||(_([]),C({}),A({}),t())};if(!a)return null;let eo=e=>{let t=e.match(/C\d+_B(\d+)/);return t?t[1]:e},el=(e,t)=>(console.log("\uD83D\uDCCB AggiungiCaviDialogSimple: Rendering lista cavi:",{isCompatible:t,caviLength:e.length,primi3Cavi:e.slice(0,3).map(e=>({id:e.id_cavo,tipologia:e.tipologia,sezione:e.sezione}))}),0===e.length)?(0,i.jsx)("div",{className:"h-[300px] flex items-center justify-center text-gray-500 border rounded",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)(R.A,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,i.jsxs)("div",{children:["Nessun cavo ",t?"compatibile":"incompatibile"," disponibile"]}),(0,i.jsx)("div",{className:"text-xs mt-2 text-gray-400",children:t?`Cerca cavi con tipologia "${a?.tipologia}" e formazione "${a?.sezione}"`:"I cavi incompatibili hanno tipologia o formazione diverse"})]})}):(0,i.jsx)("div",{className:"space-y-1 h-[300px] overflow-y-auto border rounded p-2 w-full",children:e.map((e,a)=>{let s=y.some(t=>t.id_cavo===e.id_cavo),r=z[e.id_cavo]||"",{caviBloccati:n,cavoCheCausaOver:o}=ei,l=s&&n.includes(e.id_cavo),d=s&&e.id_cavo===o;return(0,i.jsxs)("div",{className:`border rounded px-3 py-2 transition-colors ${l?"border-red-300 bg-red-50":d?"border-orange-300 bg-orange-50":s?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300 hover:bg-gray-50"}`,children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 w-full overflow-hidden",children:[(0,i.jsx)("input",{type:"checkbox",checked:s,onChange:a=>{et(e,t)},className:"h-4 w-4 text-blue-600 border-gray-300 rounded flex-shrink-0"}),(0,i.jsxs)("div",{className:"flex items-center gap-2 flex-1 min-w-0 overflow-hidden",children:[(0,i.jsx)("span",{className:"font-medium text-gray-900 flex-shrink-0",children:e.id_cavo}),(0,i.jsx)("span",{className:"text-xs bg-gray-100 px-1.5 py-0.5 rounded flex-shrink-0",children:e.tipologia}),(0,i.jsx)("span",{className:"text-xs bg-gray-100 px-1.5 py-0.5 rounded flex-shrink-0",children:e.sezione}),(0,i.jsxs)("span",{className:"text-xs text-gray-600 flex-shrink-0",children:[e.metri_teorici,"m"]}),l&&(0,i.jsx)("span",{className:"text-xs bg-red-100 text-red-700 px-1.5 py-0.5 rounded flex-shrink-0 font-medium",children:"BLOCCATO"}),d&&(0,i.jsx)("span",{className:"text-xs bg-orange-100 text-orange-700 px-1.5 py-0.5 rounded flex-shrink-0 font-medium",children:"CAUSA OVER"}),(0,i.jsxs)("span",{className:"text-xs text-gray-500 truncate min-w-0",children:[e.ubicazione_partenza||"N/A"," → ",e.ubicazione_arrivo||"N/A"]})]}),s&&(0,i.jsxs)("div",{className:"flex items-center gap-1 flex-shrink-0",children:[(0,i.jsx)("label",{className:"text-xs text-gray-600",children:"m:"}),(0,i.jsx)("input",{type:"number",step:"0.1",min:"0",value:r,onChange:t=>{ea(e.id_cavo,t.target.value)},className:"w-16 px-1 py-1 border rounded text-xs",placeholder:"0"})]})]}),D[e.id_cavo]&&(0,i.jsx)("div",{className:"text-red-600 text-xs mt-1 ml-7",children:D[e.id_cavo]})]},e.id_cavo)})});return(0,i.jsx)(i.Fragment,{children:(0,i.jsx)(N.lG,{open:e,onOpenChange:en,children:(0,i.jsxs)(N.Cf,{className:"h-[85vh] w-full max-w-5xl overflow-hidden",style:{width:"950px !important",maxWidth:"95vw !important",minWidth:"850px"},children:[(0,i.jsxs)(N.c7,{className:"pb-0",children:[(0,i.jsxs)(N.L3,{className:"flex items-center gap-2 mb-0 text-lg",children:[(0,i.jsx)(R.A,{className:"h-5 w-5"}),"\uD83D\uDD25 NUOVO SISTEMA OVER - Aggiungi cavi alla bobina ",eo(a.id_bobina)]}),(0,i.jsx)(N.rr,{className:"mb-0 text-xs text-gray-600 mt-0",children:"Seleziona cavi e inserisci metri posati (SISTEMA AGGIORNATO)"})]}),(0,i.jsxs)("div",{className:"space-y-1 mt-2",children:[(0,i.jsx)("div",{className:"bg-gray-50 px-3 py-1 rounded text-sm",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[(0,i.jsxs)("span",{children:[(0,i.jsxs)("strong",{children:["Bobina ",eo(a.id_bobina)]})," • ",a.tipologia," • ",a.sezione]}),(0,i.jsxs)("span",{children:["Residui: ",(0,i.jsxs)("strong",{children:[a.metri_residui,"m"]})]})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsxs)("span",{children:["Selezionati: ",(0,i.jsx)("strong",{children:y.length})," cavi • ",(0,i.jsxs)("strong",{children:[ee.metriTotaliSelezionati.toFixed(1),"m"]})]}),ee.isOverState&&(0,i.jsx)("span",{className:"text-red-600 font-medium",children:"OVER!"})]})]})}),(0,i.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,i.jsxs)("div",{className:"flex-1 relative",children:[(0,i.jsx)(O.A,{className:"absolute left-2 top-2 h-4 w-4 text-gray-400"}),(0,i.jsx)(n.p,{placeholder:"Cerca cavi...",value:S,onChange:e=>E(e.target.value),className:"pl-8 h-8 text-sm"})]}),(0,i.jsx)(r.$,{variant:"outline",size:"sm",onClick:()=>{_([]),C({}),A({})},disabled:0===y.length,className:"h-8 px-3 text-sm",children:"Reset"})]}),x&&(0,i.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,i.jsx)(w.A,{className:"h-6 w-6 animate-spin mr-2"}),(0,i.jsx)("span",{children:"Caricamento cavi..."})]}),!x&&(0,i.jsxs)(M.tU,{defaultValue:"compatibili",className:"w-full",children:[(0,i.jsxs)(M.j7,{className:"flex justify-center gap-6 bg-transparent border-0 h-auto p-0",children:[(0,i.jsxs)(M.Xi,{value:"compatibili",className:"tab-trigger flex items-center gap-2",children:[(0,i.jsx)("span",{className:"w-2 h-2 rounded-full bg-green-500"}),"Cavi Compatibili (",Q.totalCompatibili,")"]}),(0,i.jsxs)(M.Xi,{value:"incompatibili",className:"tab-trigger flex items-center gap-2",children:[(0,i.jsx)("span",{className:"w-2 h-2 rounded-full bg-orange-500"}),"Cavi Incompatibili (",Q.totalIncompatibili,")"]})]}),(0,i.jsx)(M.av,{value:"compatibili",className:"mt-4 w-full overflow-hidden",children:(0,i.jsx)("div",{className:"w-full overflow-hidden",children:el(Q.compatibili,!0)})}),(0,i.jsx)(M.av,{value:"incompatibili",className:"mt-4 w-full overflow-hidden",children:(0,i.jsx)("div",{className:"w-full overflow-hidden",children:el(Q.incompatibili,!1)})})]})]}),(0,i.jsxs)(N.Es,{className:"flex justify-between items-center",children:[(0,i.jsx)("div",{className:"text-sm text-gray-600",children:y.length>0?(()=>{let{metriResiduiSimulati:e,caviValidi:t,caviBloccati:s,cavoCheCausaOver:r}=ei,n=y.reduce((e,t)=>e+parseFloat(z[t.id_cavo]||"0"),0),o=(a?.metri_residui||0)-e,l=y.filter(e=>e._isIncompatible).length;return(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsxs)("div",{children:[y.length," cavi selezionati • ",n.toFixed(1),"m totali"]}),(0,i.jsxs)("div",{children:["✅ ",t.length," salvabili • ❌ ",s.length," bloccati"]}),(0,i.jsxs)("div",{children:["Usati: ",o.toFixed(1),"m / ",a?.metri_residui||0,"m",e<0&&r&&(0,i.jsxs)("span",{className:"text-orange-600 font-medium ml-2",children:["(OVER da ",r,": +",Math.abs(e).toFixed(1),"m)"]})]}),l>0&&(0,i.jsxs)("div",{className:"text-orange-600 font-medium",children:["⚠️ ",l," cavi incompatibili"]})]})})():(0,i.jsx)("div",{children:"Nessun cavo selezionato"})}),(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsx)(r.$,{variant:"outline",onClick:en,disabled:b,children:"Annulla"}),(0,i.jsxs)(r.$,{onClick:er,disabled:b||0===y.length,className:ee.isOverState?"bg-orange-600 hover:bg-orange-700":"",children:[b&&(0,i.jsx)(w.A,{className:"mr-2 h-4 w-4 animate-spin"}),b?"Salvataggio...":ee.isOverState?`Salva ${y.length} cavi (OVER)`:`Salva ${y.length} cavi`]})]})]})]})})})}var $=a(44493),P=a(53411),U=a(5336),V=a(48730);function q({bobine:e,filteredBobine:t,className:a}){let r=(0,s.useMemo)(()=>{let a=e.length,i=t.length,s=t.filter(e=>"Disponibile"===e.stato_bobina).length,r=t.filter(e=>"In uso"===e.stato_bobina).length,n=t.filter(e=>"Terminata"===e.stato_bobina).length,o=t.filter(e=>"Over"===e.stato_bobina).length,l=t.reduce((e,t)=>e+(t.metri_totali||0),0),d=t.reduce((e,t)=>e+(t.metri_residui||0),0),c=l-d,m=l>0?Math.round(c/l*100):0;return{totalBobine:a,filteredCount:i,disponibili:s,inUso:r,terminate:n,over:o,metriTotali:l,metriResidui:d,metriUtilizzati:c,percentualeUtilizzo:m}},[e,t]);return(0,i.jsx)($.Zp,{className:a,children:(0,i.jsxs)($.Wu,{className:"p-1.5",children:[(0,i.jsx)("div",{className:"flex items-center justify-between mb-1",children:(0,i.jsxs)("div",{className:"flex items-center space-x-1.5",children:[(0,i.jsx)(P.A,{className:"h-3.5 w-3.5 text-mariner-600"}),(0,i.jsx)("span",{className:"text-xs font-semibold text-mariner-900",children:"Statistiche Bobine"})]})}),(0,i.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-1.5 bg-mariner-50 px-1.5 py-1 rounded-lg",children:[(0,i.jsx)(S.A,{className:"h-3.5 w-3.5 text-mariner-600"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-bold text-mariner-900 text-sm",children:r.filteredCount}),(0,i.jsxs)("div",{className:"text-xs text-mariner-600",children:["di ",r.totalBobine," bobine"]})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-1.5 bg-green-50 px-1.5 py-1 rounded-lg",children:[(0,i.jsx)(U.A,{className:"h-3.5 w-3.5 text-green-600"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-bold text-green-700 text-sm",children:r.disponibili}),(0,i.jsx)("div",{className:"text-xs text-green-600",children:"disponibili"})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-1.5 bg-yellow-50 px-1.5 py-1 rounded-lg",children:[(0,i.jsx)(V.A,{className:"h-3.5 w-3.5 text-yellow-600"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-bold text-yellow-700 text-sm",children:r.inUso}),(0,i.jsx)("div",{className:"text-xs text-yellow-600",children:"in uso"})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-1.5 bg-red-50 px-1.5 py-1 rounded-lg",children:[(0,i.jsx)(k.A,{className:"h-3.5 w-3.5 text-red-600"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-bold text-red-700 text-sm",children:r.terminate}),(0,i.jsx)("div",{className:"text-xs text-red-600",children:"terminate"})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-1.5 bg-red-50 px-1.5 py-1 rounded-lg",children:[(0,i.jsx)(z.A,{className:"h-3.5 w-3.5 text-red-600"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-bold text-red-700 text-sm",children:r.over}),(0,i.jsx)("div",{className:"text-xs text-red-600",children:"over"})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-1.5 bg-indigo-50 px-1.5 py-1 rounded-lg",children:[(0,i.jsx)("div",{className:"h-3.5 w-3.5 flex items-center justify-center",children:(0,i.jsx)("div",{className:"h-2 w-2 bg-indigo-600 rounded-full"})}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("div",{className:"font-bold text-indigo-700 text-sm",children:[r.metriUtilizzati.toLocaleString(),"m"]}),(0,i.jsxs)("div",{className:"text-xs text-indigo-600",children:["di ",r.metriTotali.toLocaleString(),"m"]})]})]})]}),r.filteredCount>0&&(0,i.jsxs)("div",{className:"mt-2 bg-gray-50 p-2 rounded-lg",children:[(0,i.jsxs)("div",{className:"flex justify-between text-xs font-medium text-gray-700 mb-1",children:[(0,i.jsx)("span",{children:"Utilizzo Complessivo Bobine"}),(0,i.jsxs)("span",{className:`font-bold ${r.percentualeUtilizzo>=80?"text-amber-700":r.percentualeUtilizzo>=60?"text-orange-700":r.percentualeUtilizzo>=40?"text-yellow-700":"text-emerald-700"}`,children:[r.percentualeUtilizzo,"%"]})]}),(0,i.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,i.jsx)("div",{className:`h-2 rounded-full transition-all duration-500 ease-in-out ${r.percentualeUtilizzo>=80?"bg-gradient-to-r from-amber-500 to-amber-600":r.percentualeUtilizzo>=60?"bg-gradient-to-r from-orange-500 to-orange-600":r.percentualeUtilizzo>=40?"bg-gradient-to-r from-yellow-500 to-yellow-600":"bg-gradient-to-r from-emerald-500 to-emerald-600"}`,style:{width:`${Math.min(r.percentualeUtilizzo,100)}%`}})}),(0,i.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mt-0.5",children:[(0,i.jsx)("span",{children:"Metri utilizzati vs totali disponibili"}),(0,i.jsxs)("span",{children:[r.metriResidui.toLocaleString(),"m residui"]})]})]})]})})}var G=a(62688);let J=(0,G.A)("file-up",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M12 12v6",key:"3ahymv"}],["path",{d:"m15 15-3-3-3 3",key:"15xj92"}]]),H=(0,G.A)("file-down",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M12 18v-6",key:"17g6i2"}],["path",{d:"m9 15 3 3 3-3",key:"1npd3o"}]]);var K=a(63143),W=a(51215);function X({children:e,items:t,onAction:a,disabled:r=!1}){let[n,o]=(0,s.useState)(!1),[l,d]=(0,s.useState)({x:0,y:0}),[c,m]=(0,s.useState)(null),u=(0,s.useRef)(null),x=(0,s.useRef)(null),p=e=>{e.disabled||(o(!1),a(e.action,c))},b=e=>{switch(e){case"warning":return"text-amber-600 hover:bg-amber-50";case"danger":return"text-red-600 hover:bg-red-50";default:return"text-gray-700 hover:bg-gray-100"}};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{ref:x,onContextMenu:e=>{if(r)return;e.preventDefault(),e.stopPropagation(),x.current?.getBoundingClientRect();let a=e.clientX,i=e.clientY,s=40*t.length,n=window.innerWidth;d({x:a+200>n?a-200:a,y:i+s>window.innerHeight?i-s:i}),m(e.currentTarget.dataset),o(!0)},className:"w-full h-full",children:e}),n?(0,W.createPortal)((0,i.jsx)("div",{ref:u,className:"fixed z-50 min-w-[200px] bg-white border border-gray-200 rounded-md shadow-lg py-1",style:{left:l.x,top:l.y},children:t.map((e,t)=>e.separator?(0,i.jsx)("div",{className:"border-t border-gray-200 my-1"},`separator-${t}`):(0,i.jsxs)("button",{className:`w-full px-3 py-2 text-left text-sm flex items-center gap-2 transition-colors ${e.disabled?"text-gray-400 cursor-not-allowed":b(e.color)}`,onClick:()=>p(e),disabled:e.disabled,children:[e.icon&&(0,i.jsx)("span",{className:"w-4 h-4",children:e.icon}),(0,i.jsx)("span",{children:e.label})]},e.id))}),document.body):null]})}function Z(){let[e,t]=(0,s.useState)(""),[a,b]=(0,s.useState)("all"),[N,y]=(0,s.useState)([]),[C,A]=(0,s.useState)(!0),[S,E]=(0,s.useState)(""),{user:k,isLoading:M}=(0,d.A)(),{cantiereId:B,cantiere:$,isValidCantiere:P,isLoading:V,error:G}=(0,c.jV)(),[W,Z]=(0,s.useState)(!1),[Y,Q]=(0,s.useState)(!1),[ee,et]=(0,s.useState)(!1),[ea,ei]=(0,s.useState)(!1),[es,er]=(0,s.useState)(null),[en,eo]=(0,s.useState)(""),[el,ed]=(0,s.useState)(""),ec=async()=>{try{if(A(!0),E(""),!B||B<=0){E("Cantiere non selezionato. Seleziona un cantiere per visualizzare le bobine."),y([]);return}let e=await u.Fw.getBobine(B);y(e||[])}catch(e){E(e.response?.data?.detail||"Errore durante il caricamento delle bobine"),y([])}finally{A(!1)}},em=e=>{er(e),ei(!0)},eu=e=>{er(e),Q(!0)},ex=e=>{er(e),et(!0)},ep=e=>{eo(e),ec()},eb=e=>{ed(e)},eh=()=>{eo("Funzione import in sviluppo")},eg=()=>{eo("Funzione export in sviluppo")},ev=(e,t,a)=>{let s=e||p(t,a),r={disponibile:{dotColor:"bg-green-500",textColor:"text-green-700",label:"DISPONIBILE"},"in uso":{dotColor:"bg-blue-500",textColor:"text-blue-700",label:"IN USO"},terminata:{dotColor:"bg-gray-500",textColor:"text-gray-700",label:"TERMINATA"},over:{dotColor:"bg-red-500",textColor:"text-red-700",label:"OVER"}},n=r[s?.toLowerCase()]||r.terminata;return(0,i.jsxs)("div",{className:"flex items-center gap-2",title:j(s),children:[(0,i.jsx)("div",{className:`w-2 h-2 rounded-full ${n.dotColor}`}),(0,i.jsx)("span",{className:`text-sm font-semibold ${n.textColor}`,children:n.label})]})},ef=N.filter(t=>{let i=t.numero_bobina?.toLowerCase().includes(e.toLowerCase())||t.tipologia?.toLowerCase().includes(e.toLowerCase())||t.utility?.toLowerCase().includes(e.toLowerCase()),s=!0;if("all"!==a){let e=t.stato_bobina||p(t.metri_residui,t.metri_totali);switch(a){case"disponibile":s=e===x.DISPONIBILE;break;case"in_uso":s=e===x.IN_USO;break;case"esaurita":s=e===x.TERMINATA;break;case"over":s=e===x.OVER}}return i&&s});return(0,i.jsx)(m.u,{children:(0,i.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100",children:(0,i.jsxs)("div",{className:"max-w-[90%] mx-auto py-6 space-y-6",children:[S&&(0,i.jsxs)(l.Fc,{variant:"destructive",className:"mb-6",children:[(0,i.jsx)(z.A,{className:"h-4 w-4"}),(0,i.jsx)(l.TN,{children:S})]}),(0,i.jsx)(q,{bobine:N,filteredBobine:ef,className:"mb-6"}),(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,i.jsx)("div",{className:"mb-4",children:(0,i.jsx)("h2",{className:"text-lg font-semibold text-gray-800 mb-1",children:"Ricerca e Filtri Bobine"})}),(0,i.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4 items-start lg:items-center",children:[(0,i.jsxs)("div",{className:"flex-1 relative",children:[(0,i.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,i.jsx)(O.A,{className:"h-4 w-4 text-gray-400"})}),(0,i.jsx)(n.p,{placeholder:"Cerca per ID bobina, tipologia, o numero...",value:e,onChange:e=>t(e.target.value),className:"pl-10 border-gray-300 focus:border-blue-500 focus:ring-blue-500"})]}),(0,i.jsx)("div",{className:"flex flex-wrap gap-2",children:[{key:"all",label:"Tutte",count:N.length},{key:"disponibile",label:"Disponibili",count:N.filter(e=>(e.stato_bobina||p(e.metri_residui,e.metri_totali))===x.DISPONIBILE).length},{key:"in_uso",label:"In Uso",count:N.filter(e=>(e.stato_bobina||p(e.metri_residui,e.metri_totali))===x.IN_USO).length},{key:"esaurita",label:"Esaurite",count:N.filter(e=>(e.stato_bobina||p(e.metri_residui,e.metri_totali))===x.TERMINATA).length},{key:"over",label:"Over",count:N.filter(e=>(e.stato_bobina||p(e.metri_residui,e.metri_totali))===x.OVER).length}].map(e=>(0,i.jsxs)("button",{onClick:()=>b(e.key),className:`inline-flex items-center gap-1.5 px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${a===e.key?"bg-blue-600 text-white shadow-sm":"bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-300"}`,children:[e.label,(0,i.jsx)("span",{className:`text-xs px-1.5 py-0.5 rounded-full ${a===e.key?"bg-blue-500 text-white":"bg-gray-200 text-gray-600"}`,children:e.count})]},e.key))})]})]}),(0,i.jsx)(X,{items:[{id:"import",label:"Importa Bobine",icon:(0,i.jsx)(J,{className:"h-4 w-4"}),action:"import",disabled:!B||B<=0},{id:"export",label:"Esporta Bobine",icon:(0,i.jsx)(H,{className:"h-4 w-4"}),action:"export",disabled:!B||B<=0},{id:"separator1",separator:!0},{id:"add_bobina",label:"Aggiungi Bobina",icon:(0,i.jsx)(_.A,{className:"h-4 w-4"}),action:"add_bobina",disabled:!B||B<=0}],onAction:(e,t)=>{switch(e){case"import":eh();break;case"export":eg();break;case"add_bobina":Z(!0)}},children:(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[(0,i.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("h2",{className:"text-lg font-semibold text-gray-800",children:["Elenco Bobine (",ef.length,")"]}),(0,i.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Gestisci le bobine, visualizza lo stato di utilizzo e le metrature. Clicca tasto destro per azioni aggiuntive."})]}),(0,i.jsxs)(r.$,{onClick:()=>Z(!0),disabled:!B||B<=0,title:!B||B<=0?"Seleziona un cantiere per creare una bobina":"Crea nuova bobina",className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2",children:[(0,i.jsx)(_.A,{className:"h-4 w-4"}),"Nuova Bobina"]})]})}),(0,i.jsx)("div",{className:"overflow-hidden",children:(0,i.jsx)("div",{className:"overflow-x-auto",children:(0,i.jsxs)(o.XI,{children:[(0,i.jsx)(o.A0,{children:(0,i.jsxs)(o.Hj,{className:"border-b border-gray-200",children:[(0,i.jsx)(o.nd,{className:"text-left font-semibold text-gray-700 py-3 px-4",children:"Bobina"}),(0,i.jsx)(o.nd,{className:"text-left font-semibold text-gray-700 py-3 px-4",children:"Utility"}),(0,i.jsx)(o.nd,{className:"text-left font-semibold text-gray-700 py-3 px-4",children:"Tipologia"}),(0,i.jsx)(o.nd,{className:"text-left font-semibold text-gray-700 py-3 px-4",children:"Formazione"}),(0,i.jsx)(o.nd,{className:"text-left font-semibold text-gray-700 py-3 px-4",children:"Metrature"}),(0,i.jsx)(o.nd,{className:"text-center font-semibold text-gray-700 py-3 px-4",children:"Utilizzo"}),(0,i.jsx)(o.nd,{className:"text-left font-semibold text-gray-700 py-3 px-4",children:"Stato"}),(0,i.jsx)(o.nd,{className:"text-left font-semibold text-gray-700 py-3 px-4",children:"Ubicazione"}),(0,i.jsx)(o.nd,{className:"text-center font-semibold text-gray-700 py-3 px-4",children:"Azioni"})]})}),(0,i.jsx)(o.BF,{children:C?(0,i.jsx)(o.Hj,{children:(0,i.jsx)(o.nA,{colSpan:9,className:"text-center py-8",children:(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,i.jsx)(w.A,{className:"h-4 w-4 animate-spin"}),"Caricamento bobine..."]})})}):S?(0,i.jsx)(o.Hj,{children:(0,i.jsx)(o.nA,{colSpan:9,className:"text-center py-8",children:(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2 text-red-600",children:[(0,i.jsx)(z.A,{className:"h-4 w-4"}),S]})})}):0===ef.length?(0,i.jsx)(o.Hj,{children:(0,i.jsx)(o.nA,{colSpan:9,className:"text-center py-8 text-slate-500",children:"Nessuna bobina trovata"})}):ef.map(e=>{let t=v(e.metri_residui,e.metri_totali),a=e.stato_bobina||p(e.metri_residui,e.metri_totali);return h(a),(0,i.jsxs)(o.Hj,{className:"border-b border-gray-100 hover:bg-gray-50 transition-colors duration-150",children:[(0,i.jsx)(o.nA,{className:"py-4 px-4",children:(0,i.jsx)("div",{className:"font-semibold text-gray-900",children:e.numero_bobina||"-"})}),(0,i.jsx)(o.nA,{className:"py-4 px-4",children:(0,i.jsx)("div",{className:"text-gray-700",children:e.utility||"-"})}),(0,i.jsx)(o.nA,{className:"py-4 px-4",children:(0,i.jsx)("div",{className:"text-gray-700",children:e.tipologia||"-"})}),(0,i.jsx)(o.nA,{className:"py-4 px-4",children:(0,i.jsx)("div",{className:"font-medium text-gray-900",children:e.sezione||"-"})}),(0,i.jsx)(o.nA,{className:"py-4 px-4",children:(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)("span",{className:"text-sm text-gray-600",children:"Residuo:"}),(0,i.jsx)("span",{className:`font-semibold ${e.metri_residui<0?"text-red-600":"text-gray-900"}`,children:f(e.metri_residui)})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)("span",{className:"text-sm text-gray-600",children:"Totale:"}),(0,i.jsx)("span",{className:"font-medium text-gray-700",children:f(e.metri_totali)})]})]})}),(0,i.jsx)(o.nA,{className:"py-4 px-4 text-center",children:(0,i.jsxs)("div",{className:"font-semibold text-gray-900",children:[Math.round(t),"%"]})}),(0,i.jsx)(o.nA,{className:"py-4 px-4",children:ev(e.stato_bobina,e.metri_residui,e.metri_totali)}),(0,i.jsx)(o.nA,{className:"py-4 px-4",children:(0,i.jsx)("div",{className:"text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded-md",children:e.ubicazione_bobina||"Non specificata"})}),(0,i.jsx)(o.nA,{className:"py-4 px-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,i.jsx)("button",{onClick:()=>em(e),disabled:!g(a),title:a===x.OVER?"Bobina OVER - Non pu\xf2 accettare nuovi cavi":a===x.TERMINATA?"Bobina terminata - Non pu\xf2 accettare nuovi cavi":"Aggiungi cavo a bobina",className:`p-2 rounded-lg transition-colors duration-200 ${!g(a)?"opacity-50 cursor-not-allowed text-gray-400":"text-gray-600 hover:text-blue-600 hover:bg-blue-50"}`,children:(0,i.jsx)(R.A,{className:"h-4 w-4"})}),(0,i.jsx)("button",{onClick:()=>eu(e),title:a===x.OVER?"Modifica bobina (limitata per bobine OVER)":"Modifica bobina",className:"p-2 rounded-lg text-gray-600 hover:text-blue-600 hover:bg-blue-50 transition-colors duration-200",children:(0,i.jsx)(K.A,{className:"h-4 w-4"})}),(0,i.jsx)("button",{onClick:()=>ex(e),disabled:a===x.OVER||a!==x.DISPONIBILE,title:a===x.OVER?"Bobina OVER - Non pu\xf2 essere eliminata":a!==x.DISPONIBILE?"Solo bobine disponibili possono essere eliminate":"Elimina bobina",className:`p-2 rounded-lg transition-colors duration-200 ${a===x.OVER||a!==x.DISPONIBILE?"opacity-50 cursor-not-allowed text-gray-400":"text-gray-600 hover:text-red-600 hover:bg-red-50"}`,children:(0,i.jsx)(I.A,{className:"h-4 w-4"})})]})})]},e.id_bobina)})})]})})})]})}),en&&(0,i.jsx)("div",{className:"fixed top-4 right-4 z-50",children:(0,i.jsxs)(l.Fc,{className:"bg-green-50 border-green-200",children:[(0,i.jsx)(U.A,{className:"h-4 w-4 text-green-600"}),(0,i.jsx)(l.TN,{className:"text-green-800",children:en})]})}),el&&(0,i.jsx)("div",{className:"fixed top-4 right-4 z-50",children:(0,i.jsxs)(l.Fc,{variant:"destructive",children:[(0,i.jsx)(z.A,{className:"h-4 w-4"}),(0,i.jsx)(l.TN,{children:el})]})}),(0,i.jsx)(D,{open:W,onClose:()=>Z(!1),cantiereId:B,onSuccess:ep,onError:eb}),(0,i.jsx)(T,{open:Y,onClose:()=>Q(!1),bobina:es,cantiereId:B,onSuccess:e=>{eo(e),ec()},onError:e=>{ed(e)}}),(0,i.jsx)(F,{open:ee,onClose:()=>et(!1),bobina:es,cantiereId:B,onSuccess:e=>{eo(e),ec()},onError:e=>{ed(e)}}),(0,i.jsx)(L,{open:ea,onClose:()=>ei(!1),bobina:es,cantiereId:B,onSuccess:ep,onError:eb})]})})})}},70440:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});var i=a(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,i.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71902:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});let i=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\parco-cavi\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\parco-cavi\\page.tsx","default")},72942:(e,t,a)=>{"use strict";a.d(t,{RG:()=>N,bL:()=>E,q7:()=>T});var i=a(43210),s=a(70569),r=a(9510),n=a(98599),o=a(11273),l=a(96963),d=a(14163),c=a(13495),m=a(65551),u=a(43),x=a(60687),p="rovingFocusGroup.onEntryFocus",b={bubbles:!1,cancelable:!0},h="RovingFocusGroup",[g,v,f]=(0,r.N)(h),[j,N]=(0,o.A)(h,[f]),[y,_]=j(h),w=i.forwardRef((e,t)=>(0,x.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,x.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,x.jsx)(z,{...e,ref:t})})}));w.displayName=h;var z=i.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:a,orientation:r,loop:o=!1,dir:l,currentTabStopId:g,defaultCurrentTabStopId:f,onCurrentTabStopIdChange:j,onEntryFocus:N,preventScrollOnEntryFocus:_=!1,...w}=e,z=i.useRef(null),C=(0,n.s)(t,z),D=(0,u.jH)(l),[A,E]=(0,m.i)({prop:g,defaultProp:f??null,onChange:j,caller:h}),[T,I]=i.useState(!1),k=(0,c.c)(N),F=v(a),M=i.useRef(!1),[R,O]=i.useState(0);return i.useEffect(()=>{let e=z.current;if(e)return e.addEventListener(p,k),()=>e.removeEventListener(p,k)},[k]),(0,x.jsx)(y,{scope:a,orientation:r,dir:D,loop:o,currentTabStopId:A,onItemFocus:i.useCallback(e=>E(e),[E]),onItemShiftTab:i.useCallback(()=>I(!0),[]),onFocusableItemAdd:i.useCallback(()=>O(e=>e+1),[]),onFocusableItemRemove:i.useCallback(()=>O(e=>e-1),[]),children:(0,x.jsx)(d.sG.div,{tabIndex:T||0===R?-1:0,"data-orientation":r,...w,ref:C,style:{outline:"none",...e.style},onMouseDown:(0,s.m)(e.onMouseDown,()=>{M.current=!0}),onFocus:(0,s.m)(e.onFocus,e=>{let t=!M.current;if(e.target===e.currentTarget&&t&&!T){let t=new CustomEvent(p,b);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=F().filter(e=>e.focusable);S([e.find(e=>e.active),e.find(e=>e.id===A),...e].filter(Boolean).map(e=>e.ref.current),_)}}M.current=!1}),onBlur:(0,s.m)(e.onBlur,()=>I(!1))})})}),C="RovingFocusGroupItem",D=i.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:a,focusable:r=!0,active:n=!1,tabStopId:o,children:c,...m}=e,u=(0,l.B)(),p=o||u,b=_(C,a),h=b.currentTabStopId===p,f=v(a),{onFocusableItemAdd:j,onFocusableItemRemove:N,currentTabStopId:y}=b;return i.useEffect(()=>{if(r)return j(),()=>N()},[r,j,N]),(0,x.jsx)(g.ItemSlot,{scope:a,id:p,focusable:r,active:n,children:(0,x.jsx)(d.sG.span,{tabIndex:h?0:-1,"data-orientation":b.orientation,...m,ref:t,onMouseDown:(0,s.m)(e.onMouseDown,e=>{r?b.onItemFocus(p):e.preventDefault()}),onFocus:(0,s.m)(e.onFocus,()=>b.onItemFocus(p)),onKeyDown:(0,s.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void b.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,a){var i;let s=(i=e.key,"rtl"!==a?i:"ArrowLeft"===i?"ArrowRight":"ArrowRight"===i?"ArrowLeft":i);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(s))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(s)))return A[s]}(e,b.orientation,b.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let a=f().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)a.reverse();else if("prev"===t||"next"===t){"prev"===t&&a.reverse();let i=a.indexOf(e.currentTarget);a=b.loop?function(e,t){return e.map((a,i)=>e[(t+i)%e.length])}(a,i+1):a.slice(i+1)}setTimeout(()=>S(a))}}),children:"function"==typeof c?c({isCurrentTabStop:h,hasTabStop:null!=y}):c})})});D.displayName=C;var A={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function S(e,t=!1){let a=document.activeElement;for(let i of e)if(i===a||(i.focus({preventScroll:t}),document.activeElement!==a))return}var E=w,T=D},74075:e=>{"use strict";e.exports=require("zlib")},78122:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,a)=>{"use strict";a.d(t,{J:()=>n});var i=a(60687);a(43210);var s=a(78148),r=a(4780);function n({className:e,...t}){return(0,i.jsx)(s.b,{"data-slot":"label",className:(0,r.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85902:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var i=a(65239),s=a(48088),r=a(88170),n=a.n(r),o=a(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);a.d(t,l);let d={children:["",{children:["parco-cavi",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,71902)),"C:\\CMS\\webapp-nextjs\\src\\app\\parco-cavi\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\CMS\\webapp-nextjs\\src\\app\\parco-cavi\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},u=new i.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/parco-cavi/page",pathname:"/parco-cavi",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},88233:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},89667:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var i=a(60687),s=a(43210),r=a(4780);let n=s.forwardRef(({className:e,type:t,...a},s)=>(0,i.jsx)("input",{type:t,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),ref:s,...a}));n.displayName="Input"},94735:e=>{"use strict";e.exports=require("events")},96474:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96882:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(62688).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),i=t.X(0,[447,991,658,947,400,818,203,639,653],()=>a(85902));module.exports=i})();