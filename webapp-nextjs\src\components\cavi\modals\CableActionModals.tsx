'use client'

import React, { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  AlertTriangle,
  FileText,
  X,
  Download,
  AlertCircle,
  CheckCircle,
  Loader2,
  Zap,
  Lock,
  HelpCircle
} from 'lucide-react'
import { Cavo } from '@/types'
import CertificazioneForm from '@/components/certificazioni/CertificazioneForm'

// Types for modal props
interface BaseModalProps {
  open: boolean
  onClose: () => void
  cavo: Cavo | null
}

interface DisconnectModalProps extends BaseModalProps {
  onConfirm: (cavoId: string) => Promise<void>
}

interface GeneratePdfModalProps extends BaseModalProps {
  onGenerate: (cavoId: string, options: PdfGenerationOptions) => Promise<void>
}

interface CertificationErrorModalProps extends BaseModalProps {
  errorMessage?: string
  missingRequirements?: string[]
}

interface PdfGenerationOptions {
  fileName: string
  includeTestData: boolean
  format: 'standard' | 'detailed'
  emailRecipient?: string
}

// Enhanced Modal Header Component with prominent cable ID
interface EnhancedModalHeaderProps {
  icon: React.ReactNode
  title: string
  cableId: string
  description?: string
}

const EnhancedModalHeader: React.FC<EnhancedModalHeaderProps> = ({
  icon,
  title,
  cableId,
  description
}) => (
  <DialogHeader>
    <DialogTitle className="flex items-center gap-2">
      {icon}
      <span className="flex items-center gap-2">
        {title}
        <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-md text-sm font-mono font-semibold">
          {cableId}
        </span>
      </span>
    </DialogTitle>
    {description && (
      <DialogDescription className="text-sm text-muted-foreground">
        {description}
      </DialogDescription>
    )}
  </DialogHeader>
)

// Enhanced Dialog Content with improved overlay behavior
interface EnhancedDialogContentProps {
  children: React.ReactNode
  className?: string
  onKeyDown?: (e: React.KeyboardEvent) => void
  ariaLabelledBy?: string
  ariaDescribedBy?: string
}

const EnhancedDialogContent: React.FC<EnhancedDialogContentProps> = ({
  children,
  className = "sm:max-w-md",
  onKeyDown,
  ariaLabelledBy,
  ariaDescribedBy
}) => (
  <DialogContent
    className={className}
    onKeyDown={onKeyDown}
    aria-labelledby={ariaLabelledBy}
    aria-describedby={ariaDescribedBy}
    onPointerDownOutside={(e) => e.preventDefault()} // Prevent closing on outside click
    onEscapeKeyDown={(e) => {
      // Allow ESC key to close
      if (onKeyDown) {
        onKeyDown(e as any)
      }
    }}
  >
    {children}
  </DialogContent>
)

// Disconnect Confirmation Modal with enhanced UI
export const DisconnectCableModal: React.FC<DisconnectModalProps> = ({
  open,
  onClose,
  cavo,
  onConfirm
}) => {
  const [isLoading, setIsLoading] = useState(false)
  const [showFinalConfirmation, setShowFinalConfirmation] = useState(false)

  const handleInitialConfirm = () => {
    setShowFinalConfirmation(true)
  }

  const handleFinalConfirm = async () => {
    if (!cavo) return

    setIsLoading(true)
    try {
      await onConfirm(cavo.id_cavo)
      onClose()
      setShowFinalConfirmation(false)
    } catch (error) {
      console.error('Error disconnecting cable:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    setShowFinalConfirmation(false)
    onClose()
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      handleClose()
    }
  }

  if (!cavo) return null

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <EnhancedDialogContent
        className="sm:max-w-md"
        onKeyDown={handleKeyDown}
        ariaLabelledBy="disconnect-modal-title"
        ariaDescribedBy="disconnect-modal-description"
      >
        <EnhancedModalHeader
          icon={<Zap className="h-5 w-5 text-orange-500" />}
          title="Gestione Collegamenti"
          cableId={cavo.id_cavo}
          description="Gestisci le connessioni del cavo selezionato"
        />

        {!showFinalConfirmation ? (
          <>
            <div className="py-4">
              <div className="flex items-center gap-2 mb-4">
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 rounded-full bg-green-500"></div>
                  <div className="w-3 h-3 rounded-full bg-green-500"></div>
                </div>
                <span className="text-sm font-medium text-green-700">
                  Completamente collegato
                  <HelpCircle
                    className="inline h-4 w-4 ml-1 cursor-help"
                    title="Cavo collegato sia all'origine che alla destinazione"
                  />
                </span>
              </div>

              <div className="space-y-3">
                <div>
                  <Label htmlFor="responsabile-collegamento" className="text-sm font-medium">
                    Responsabile Collegamento
                  </Label>
                  <select
                    id="responsabile-collegamento"
                    className="w-full mt-1 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    defaultValue=""
                  >
                    <option value="" disabled>Seleziona responsabile...</option>
                    <option value="cantiere">Cantiere</option>
                    <option value="tecnico1">Tecnico 1</option>
                    <option value="tecnico2">Tecnico 2</option>
                  </select>
                </div>
              </div>
            </div>

            <Alert className="my-4 bg-orange-50 border-orange-200">
              <AlertTriangle className="h-4 w-4 text-orange-600" />
              <AlertDescription className="text-orange-800">
                <strong>Attenzione:</strong> Lo scollegamento rimuoverà tutte le connessioni attive del cavo.
                Questa azione potrebbe influenzare altri componenti collegati.
              </AlertDescription>
            </Alert>

            <DialogFooter className="gap-2">
              <Button
                variant="outline"
                onClick={handleClose}
                disabled={isLoading}
                className="flex-1 hover:bg-gray-50"
              >
                Annulla
              </Button>
              <Button
                variant="destructive"
                onClick={handleInitialConfirm}
                disabled={isLoading}
                className="flex-1 hover:bg-red-600"
              >
                <AlertTriangle className="mr-2 h-4 w-4" />
                Scollega Completamente
              </Button>
            </DialogFooter>
          </>
        ) : (
          <>
            <div className="py-4 text-center">
              <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Conferma Scollegamento
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                Sei veramente sicuro di voler scollegare completamente il cavo <strong>{cavo.id_cavo}</strong>?
              </p>
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <p className="text-sm text-red-800 font-medium">
                  ⚠️ Questa azione è irreversibile
                </p>
              </div>
            </div>

            <DialogFooter className="gap-2">
              <Button
                variant="outline"
                onClick={() => setShowFinalConfirmation(false)}
                disabled={isLoading}
                className="flex-1"
              >
                No, Annulla
              </Button>
              <Button
                variant="destructive"
                onClick={handleFinalConfirm}
                disabled={isLoading}
                className="flex-1"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Scollegando...
                  </>
                ) : (
                  'Sì, Scollega'
                )}
              </Button>
            </DialogFooter>
          </>
        )}
      </EnhancedDialogContent>
    </Dialog>
  )
}

// Enhanced PDF Generation Modal
export const GeneratePdfModal: React.FC<GeneratePdfModalProps> = ({
  open,
  onClose,
  cavo,
  onGenerate
}) => {
  const [isLoading, setIsLoading] = useState(false)
  const [options, setOptions] = useState<PdfGenerationOptions>({
    fileName: '',
    includeTestData: true,
    format: 'standard',
    emailRecipient: ''
  })
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})

  // Set default filename when modal opens
  useEffect(() => {
    if (cavo && open) {
      setOptions(prev => ({
        ...prev,
        fileName: `Certificato_${cavo.id_cavo}_${new Date().toISOString().split('T')[0]}.pdf`
      }))
      setValidationErrors({})
    }
  }, [cavo, open])

  // Validate form fields
  const validateForm = () => {
    const errors: Record<string, string> = {}

    if (!options.fileName.trim()) {
      errors.fileName = 'Il nome del file è obbligatorio'
    } else if (!/^[a-zA-Z0-9_\-\s]+\.pdf$/i.test(options.fileName)) {
      errors.fileName = 'Il nome del file deve terminare con .pdf e contenere solo caratteri validi'
    }

    if (options.emailRecipient && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(options.emailRecipient)) {
      errors.emailRecipient = 'Inserisci un indirizzo email valido'
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleGenerate = async () => {
    if (!cavo || !validateForm()) return

    setIsLoading(true)
    try {
      await onGenerate(cavo.id_cavo, options)
      onClose()
    } catch (error) {
      console.error('Error generating PDF:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose()
    }
  }

  const isFormValid = options.fileName.trim() && Object.keys(validationErrors).length === 0

  if (!cavo) return null

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <EnhancedDialogContent
        className="sm:max-w-lg"
        onKeyDown={handleKeyDown}
        ariaLabelledBy="pdf-modal-title"
      >
        <EnhancedModalHeader
          icon={<FileText className="h-5 w-5 text-blue-500" />}
          title="Genera Certificato"
          cableId={cavo.id_cavo}
          description="Configura le opzioni per la generazione del certificato PDF"
        />

        <div className="space-y-4 py-4">
          {/* File Name */}
          <div className="space-y-2">
            <Label htmlFor="fileName" className="text-sm font-medium">
              Nome File *
            </Label>
            <Input
              id="fileName"
              value={options.fileName}
              onChange={(e) => {
                setOptions(prev => ({ ...prev, fileName: e.target.value }))
                if (validationErrors.fileName) {
                  setValidationErrors(prev => ({ ...prev, fileName: '' }))
                }
              }}
              onBlur={validateForm}
              placeholder="Certificato_C001_2025-06-29.pdf"
              className={validationErrors.fileName ? 'border-red-500 focus:ring-red-500' : ''}
            />
            {validationErrors.fileName && (
              <p className="text-sm text-red-600 flex items-center gap-1">
                <AlertCircle className="h-3 w-3" />
                {validationErrors.fileName}
              </p>
            )}
          </div>

          {/* Format Selection */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Formato Certificato</Label>
            <div className="space-y-2">
              <label className="flex items-center space-x-3 cursor-pointer p-2 border rounded-md hover:bg-gray-50 transition-colors">
                <input
                  type="radio"
                  name="format"
                  value="standard"
                  checked={options.format === 'standard'}
                  onChange={(e) => setOptions(prev => ({ ...prev, format: e.target.value as 'standard' | 'detailed' }))}
                  className="text-blue-600 focus:ring-blue-500"
                />
                <div>
                  <span className="text-sm font-medium">Standard</span>
                  <p className="text-xs text-gray-500">Certificato con informazioni essenziali</p>
                </div>
              </label>
              <label className="flex items-center space-x-3 cursor-pointer p-2 border rounded-md hover:bg-gray-50 transition-colors">
                <input
                  type="radio"
                  name="format"
                  value="detailed"
                  checked={options.format === 'detailed'}
                  onChange={(e) => setOptions(prev => ({ ...prev, format: e.target.value as 'standard' | 'detailed' }))}
                  className="text-blue-600 focus:ring-blue-500"
                />
                <div>
                  <span className="text-sm font-medium">Dettagliato</span>
                  <p className="text-xs text-gray-500">Certificato con tutti i dati tecnici</p>
                </div>
              </label>
            </div>
          </div>

          {/* Include Test Data */}
          <div className="flex items-center space-x-3 p-2 border rounded-md">
            <Checkbox
              id="includeTestData"
              checked={options.includeTestData}
              onCheckedChange={(checked) => setOptions(prev => ({ ...prev, includeTestData: checked as boolean }))}
            />
            <div>
              <Label htmlFor="includeTestData" className="text-sm font-medium cursor-pointer">
                Includi Dati di Collaudo
              </Label>
              <p className="text-xs text-gray-500">Aggiunge i risultati dei test al certificato</p>
            </div>
          </div>

          {/* Email Recipient (Optional) */}
          <div className="space-y-2">
            <Label htmlFor="emailRecipient" className="text-sm font-medium">
              Email Destinatario (Opzionale)
            </Label>
            <Input
              id="emailRecipient"
              type="email"
              value={options.emailRecipient}
              onChange={(e) => {
                setOptions(prev => ({ ...prev, emailRecipient: e.target.value }))
                if (validationErrors.emailRecipient) {
                  setValidationErrors(prev => ({ ...prev, emailRecipient: '' }))
                }
              }}
              onBlur={validateForm}
              placeholder="<EMAIL>"
              className={validationErrors.emailRecipient ? 'border-red-500 focus:ring-red-500' : ''}
            />
            {validationErrors.emailRecipient && (
              <p className="text-sm text-red-600 flex items-center gap-1">
                <AlertCircle className="h-3 w-3" />
                {validationErrors.emailRecipient}
              </p>
            )}
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
            className="flex-1 hover:bg-gray-50"
          >
            Annulla
          </Button>
          <Button
            onClick={handleGenerate}
            disabled={isLoading || !isFormValid}
            className={`flex-1 ${!isFormValid ? 'opacity-50 cursor-not-allowed' : 'hover:bg-blue-600'}`}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generando...
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Genera PDF
              </>
            )}
          </Button>
        </DialogFooter>
      </EnhancedDialogContent>
    </Dialog>
  )
}

// Enhanced Certification Error Modal
export const CertificationErrorModal: React.FC<CertificationErrorModalProps> = ({
  open,
  onClose,
  cavo,
  errorMessage,
  missingRequirements = []
}) => {
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose()
    }
  }

  const defaultRequirements = [
    'Il cavo deve essere nello stato "Installato"',
    'Il cavo deve essere completamente collegato',
    'Tutti i dati di collaudo devono essere presenti'
  ]

  const requirements = missingRequirements.length > 0 ? missingRequirements : defaultRequirements

  if (!cavo) return null

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <EnhancedDialogContent
        className="sm:max-w-md"
        onKeyDown={handleKeyDown}
        ariaLabelledBy="certification-error-title"
      >
        <EnhancedModalHeader
          icon={<AlertCircle className="h-5 w-5 text-red-500" />}
          title="Impossibile Certificare Cavo"
          cableId={cavo.id_cavo}
          description="Il cavo non può essere certificato nel suo stato attuale"
        />

        <div className="py-4">
          {errorMessage && (
            <Alert className="mb-4 bg-red-50 border-red-200">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">{errorMessage}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-semibold text-gray-900 mb-3 flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-amber-500" />
                Requisiti mancanti:
              </h4>
              <ul className="space-y-3">
                {requirements.map((requirement, index) => (
                  <li key={index} className="flex items-start gap-3 p-2 bg-red-50 border border-red-200 rounded-md">
                    <div className="flex-shrink-0 w-5 h-5 bg-red-100 rounded-full flex items-center justify-center mt-0.5">
                      <X className="h-3 w-3 text-red-600" />
                    </div>
                    <span className="text-sm text-red-800">{requirement}</span>
                  </li>
                ))}
              </ul>
            </div>

            <Alert className="bg-blue-50 border-blue-200">
              <CheckCircle className="h-4 w-4 text-blue-600" />
              <AlertDescription className="text-blue-800">
                <strong>Prossimi passi:</strong> Completa tutti i requisiti sopra elencati per abilitare la certificazione del cavo.
              </AlertDescription>
            </Alert>
          </div>
        </div>

        <DialogFooter>
          <Button
            onClick={onClose}
            className="w-full hover:bg-blue-600 focus:ring-2 focus:ring-blue-500"
          >
            <CheckCircle className="mr-2 h-4 w-4" />
            Ho Capito
          </Button>
        </DialogFooter>
      </EnhancedDialogContent>
    </Dialog>
  )
}

// Cable Certification Modal
interface CertificationModalProps extends BaseModalProps {
  onCertify: (cavoId: string, certificationData: CertificationData) => Promise<void>
}

interface CertificationData {
  responsabile: string
  dataCertificazione: string
  esitoCertificazione: 'CONFORME' | 'NON_CONFORME' | 'PARZIALMENTE_CONFORME'
  note?: string
}

export const CertificationModal: React.FC<CertificationModalProps> = ({
  open,
  onClose,
  cavo,
  onCertify
}) => {
  const [showFullForm, setShowFullForm] = useState(false)

  if (!cavo) return null

  const handleFormSuccess = () => {
    setShowFullForm(false)
    onClose()
  }

  const handleFormCancel = () => {
    setShowFullForm(false)
    onClose()
  }

  // Se showFullForm è true, mostra il form completo
  if (showFullForm) {
    return (
      <Dialog open={open} onOpenChange={onClose}>
        <DialogContent className="max-w-6xl max-h-[95vh] overflow-y-auto p-0">
          <div className="p-6">
            <CertificazioneForm
              cantiereId={1} // TODO: Get from context
              certificazione={null}
              strumenti={[]}
              onSuccess={handleFormSuccess}
              onCancel={handleFormCancel}
            />
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <EnhancedDialogContent
        className="sm:max-w-lg"
        onKeyDown={handleKeyDown}
        ariaLabelledBy="certification-modal-title"
      >
        <EnhancedModalHeader
          icon={<Lock className="h-5 w-5 text-blue-500" />}
          title="Gestione Certificazione"
          cableId={cavo.id_cavo}
          description="Certifica il cavo dopo aver completato tutti i controlli"
        />

        <div className="space-y-4 py-4">
          {/* Cable Status Indicators */}
          <div className="space-y-3">
            <h4 className="text-sm font-semibold text-gray-900">Stato Cavo:</h4>
            <div className="grid grid-cols-3 gap-3">
              <div className="flex items-center gap-2 p-2 bg-gray-50 rounded-md">
                <div className={`w-3 h-3 rounded-full ${cableStatus.installato ? 'bg-green-500' : 'bg-red-500'}`}></div>
                <span className="text-sm font-medium">
                  {cableStatus.installato ? '✓' : '✗'} Installato
                </span>
              </div>
              <div className="flex items-center gap-2 p-2 bg-gray-50 rounded-md">
                <div className={`w-3 h-3 rounded-full ${cableStatus.collegato ? 'bg-green-500' : 'bg-red-500'}`}></div>
                <span className="text-sm font-medium">
                  {cableStatus.collegato ? '✓' : '✗'} Collegato
                </span>
              </div>
              <div className="flex items-center gap-2 p-2 bg-gray-50 rounded-md">
                <div className={`w-3 h-3 rounded-full ${cableStatus.certificato ? 'bg-green-500' : 'bg-orange-500'}`}></div>
                <span className="text-sm font-medium">
                  {cableStatus.certificato ? '✓' : '⚠'} Non certificato
                </span>
              </div>
            </div>
          </div>

          {/* Responsabile Certificazione */}
          <div className="space-y-2">
            <Label htmlFor="responsabile" className="text-sm font-medium">
              Responsabile Certificazione *
            </Label>
            <select
              id="responsabile"
              value={formData.responsabile}
              onChange={(e) => {
                setFormData(prev => ({ ...prev, responsabile: e.target.value }))
                if (validationErrors.responsabile) {
                  setValidationErrors(prev => ({ ...prev, responsabile: '' }))
                }
              }}
              onBlur={validateForm}
              className={`w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                validationErrors.responsabile ? 'border-red-500' : 'border-gray-300'
              }`}
            >
              <option value="" disabled>Seleziona responsabile...</option>
              <option value="cantiere">Cantiere</option>
              <option value="tecnico_1">Tecnico 1</option>
              <option value="tecnico_2">Tecnico 2</option>
              <option value="supervisore">Supervisore</option>
            </select>
            {validationErrors.responsabile && (
              <p className="text-sm text-red-600 flex items-center gap-1">
                <AlertCircle className="h-3 w-3" />
                {validationErrors.responsabile}
              </p>
            )}
          </div>

          {/* Data Certificazione */}
          <div className="space-y-2">
            <Label htmlFor="dataCertificazione" className="text-sm font-medium">
              Data Certificazione *
            </Label>
            <Input
              id="dataCertificazione"
              type="date"
              value={formData.dataCertificazione}
              onChange={(e) => {
                setFormData(prev => ({ ...prev, dataCertificazione: e.target.value }))
                if (validationErrors.dataCertificazione) {
                  setValidationErrors(prev => ({ ...prev, dataCertificazione: '' }))
                }
              }}
              onBlur={validateForm}
              max={new Date().toISOString().split('T')[0]}
              className={validationErrors.dataCertificazione ? 'border-red-500 focus:ring-red-500' : ''}
            />
            {validationErrors.dataCertificazione && (
              <p className="text-sm text-red-600 flex items-center gap-1">
                <AlertCircle className="h-3 w-3" />
                {validationErrors.dataCertificazione}
              </p>
            )}
          </div>

          {/* Esito Certificazione */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Esito Certificazione</Label>
            <select
              value={formData.esitoCertificazione}
              onChange={(e) => setFormData(prev => ({ ...prev, esitoCertificazione: e.target.value as any }))}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="CONFORME">CONFORME</option>
              <option value="NON_CONFORME">NON CONFORME</option>
              <option value="PARZIALMENTE_CONFORME">PARZIALMENTE CONFORME</option>
            </select>
          </div>

          {/* Note (opzionale) */}
          <div className="space-y-2">
            <Label htmlFor="note" className="text-sm font-medium">
              Note (opzionale)
            </Label>
            <textarea
              id="note"
              value={formData.note}
              onChange={(e) => setFormData(prev => ({ ...prev, note: e.target.value }))}
              placeholder="Inserisci eventuali note sulla certificazione..."
              rows={3}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
            />
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
            className="flex-1 hover:bg-gray-50"
          >
            Chiudi
          </Button>
          <Button
            onClick={handleCertify}
            disabled={isLoading || !isFormValid}
            className={`flex-1 ${!isFormValid ? 'opacity-50 cursor-not-allowed bg-gray-400' : 'bg-blue-600 hover:bg-blue-700'}`}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Certificando...
              </>
            ) : (
              <>
                <Lock className="mr-2 h-4 w-4" />
                Certifica Cavo
              </>
            )}
          </Button>
        </DialogFooter>
      </EnhancedDialogContent>
    </Dialog>
  )
}

// Success Toast Component (for feedback after actions)
interface SuccessToastProps {
  message: string
  visible: boolean
  onClose: () => void
}

export const SuccessToast: React.FC<SuccessToastProps> = ({
  message,
  visible,
  onClose
}) => {
  useEffect(() => {
    if (visible) {
      const timer = setTimeout(() => {
        onClose()
      }, 3000)
      return () => clearTimeout(timer)
    }
  }, [visible, onClose])

  if (!visible) return null

  return (
    <div className="fixed top-4 right-4 z-50 animate-in slide-in-from-top-2">
      <Alert className="bg-green-50 border-green-200 text-green-800 shadow-lg">
        <CheckCircle className="h-4 w-4" />
        <AlertDescription className="font-medium">
          {message}
        </AlertDescription>
        <Button
          variant="ghost"
          size="sm"
          className="absolute top-2 right-2 h-6 w-6 p-0 hover:bg-green-100"
          onClick={onClose}
        >
          <X className="h-3 w-3" />
        </Button>
      </Alert>
    </div>
  )
}

// Export all modal types for easy importing
export type {
  DisconnectModalProps,
  GeneratePdfModalProps,
  CertificationErrorModalProps,
  CertificationModalProps,
  PdfGenerationOptions,
  CertificationData
}
