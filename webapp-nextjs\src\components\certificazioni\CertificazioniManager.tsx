'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { useAuth } from '@/contexts/AuthContext'
import { certificazioniApi, strumentiApi, rapportiGeneraliApi, nonConformitaApi } from '@/lib/api'
import { CertificazioneCavo, StrumentoCertificato, RapportoGeneraleCollaudo, NonConformita } from '@/types/certificazioni'
import { 
  FileText, 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  Eye,
  Download,
  Upload,
  Loader2,
  <PERSON>,
  Settings,
  BarChart3,
  Alert<PERSON>riangle
} from 'lucide-react'
import CertificazioneForm from './CertificazioneForm'
import StrumentiManager from './StrumentiManager'
import RapportiGeneraliManager from './RapportiGeneraliManager'
import CertificazioniStatistics from './CertificazioniStatistics'
import CertificazioneForm from './CertificazioneForm'
import StrumentiManager from './StrumentiManager'
import RapportiGeneraliManager from './RapportiGeneraliManager'
import NonConformitaManager from './NonConformitaManager'

interface CertificazioniManagerProps {
  cantiereId: number
}

export default function CertificazioniManager({ cantiereId }: CertificazioniManagerProps) {
  const [activeTab, setActiveTab] = useState('certificazioni')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [certificazioni, setCertificazioni] = useState<CertificazioneCavo[]>([])
  const [strumenti, setStrumenti] = useState<StrumentoCertificato[]>([])
  const [rapporti, setRapporti] = useState<RapportoGeneraleCollaudo[]>([])
  const [nonConformita, setNonConformita] = useState<NonConformita[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [showForm, setShowForm] = useState(false)
  const [selectedCertificazione, setSelectedCertificazione] = useState<CertificazioneCavo | null>(null)
  const [selectedCertificazione, setSelectedCertificazione] = useState<CertificazioneCavo | null>(null)

  const { user, cantiere } = useAuth()

  useEffect(() => {
    if (cantiereId) {
      loadData()
    }
  }, [cantiereId])

  const loadData = async () => {
    try {
      setIsLoading(true)
      setError('')

      const [certData, strumData, rappData, ncData] = await Promise.all([
        certificazioniApi.getCertificazioni(cantiereId),
        strumentiApi.getStrumenti(cantiereId),
        rapportiGeneraliApi.getRapporti(cantiereId),
        nonConformitaApi.getNonConformita(cantiereId)
      ])

      setCertificazioni(certData)
      setStrumenti(strumData)
      setRapporti(rappData)
      setNonConformita(ncData)
    } catch (error: any) {
      setError(error.response?.data?.detail || 'Errore durante il caricamento dei dati')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreateCertificazione = () => {
    setSelectedCertificazione(null)
    setShowForm(true)
  }

  const handleEditCertificazione = (cert: CertificazioneCavo) => {
    setSelectedCertificazione(cert)
    setShowForm(true)
  }

  const handleDeleteCertificazione = async (id: number) => {
    if (!confirm('Sei sicuro di voler eliminare questa certificazione?')) return
    
    try {
      await certificazioniApi.deleteCertificazione(cantiereId, id)
      await loadData()
    } catch (error: any) {
      setError(error.response?.data?.detail || 'Errore durante l\'eliminazione')
    }
  }

  const handleGeneratePDF = async (id: number) => {
    try {
      const blob = await certificazioniApi.generatePDF(cantiereId, id)
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `certificato_${id}.pdf`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    } catch (error: any) {
      setError(error.response?.data?.detail || 'Errore durante la generazione del PDF')
    }
  }

  const getStatusBadge = (stato: string) => {
    switch (stato?.toLowerCase()) {
      case 'conforme':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Conforme</Badge>
      case 'non_conforme':
        return <Badge className="bg-red-100 text-red-800 border-red-200">Non Conforme</Badge>
      case 'conforme_con_riserva':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Con Riserva</Badge>
      case 'bozza':
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Bozza</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Da Verificare</Badge>
    }
  }

  const filteredCertificazioni = certificazioni.filter(cert => {
    const matchesSearch = cert.id_cavo?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         cert.numero_certificato?.toLowerCase().includes(searchTerm.toLowerCase())
    
    let matchesStatus = true
    if (selectedStatus !== 'all') {
      matchesStatus = cert.stato_certificato?.toLowerCase() === selectedStatus
    }
    
    return matchesSearch && matchesStatus
  })

  const stats = {
    totali: certificazioni.length,
    conformi: certificazioni.filter(c => c.stato_certificato === 'CONFORME').length,
    non_conformi: certificazioni.filter(c => c.stato_certificato === 'NON_CONFORME').length,
    bozze: certificazioni.filter(c => c.stato_certificato === 'BOZZA').length,
    con_riserva: certificazioni.filter(c => c.stato_certificato === 'CONFORME_CON_RISERVA').length
  }

  if (showForm) {
    return (
      <CertificazioneForm
        cantiereId={cantiereId}
        certificazione={selectedCertificazione}
        strumenti={strumenti}
        onSuccess={() => {
          setShowForm(false)
          loadData()
        }}
        onCancel={() => setShowForm(false)}
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-slate-900 flex items-center gap-3">
            <FileText className="h-8 w-8 text-blue-600" />
            Sistema Certificazioni
          </h1>
          <p className="text-slate-600 mt-1">Gestione completa certificazioni CEI 64-8</p>
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={() => {}}>
            <Download className="h-4 w-4 mr-2" />
            Esporta
          </Button>
          <Button size="sm" onClick={handleCreateCertificazione}>
            <Plus className="h-4 w-4 mr-2" />
            Nuova Certificazione
          </Button>
        </div>
      </div>

      {/* Statistics */}
      <CertificazioniStatistics stats={stats} />

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="certificazioni">
            <FileText className="h-4 w-4 mr-2" />
            Certificazioni
          </TabsTrigger>
          <TabsTrigger value="strumenti">
            <Settings className="h-4 w-4 mr-2" />
            Strumenti
          </TabsTrigger>
          <TabsTrigger value="rapporti">
            <BarChart3 className="h-4 w-4 mr-2" />
            Rapporti Generali
          </TabsTrigger>
          <TabsTrigger value="non_conformita">
            <AlertTriangle className="h-4 w-4 mr-2" />
            Non Conformità
          </TabsTrigger>
          <TabsTrigger value="statistiche">
            <Award className="h-4 w-4 mr-2" />
            Statistiche
          </TabsTrigger>
        </TabsList>

        <TabsContent value="certificazioni" className="space-y-4">
          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Search className="h-5 w-5" />
                Ricerca e Filtri
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4">
                <div className="flex-1">
                  <Input
                    placeholder="Cerca per ID cavo o numero certificato..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <div className="flex gap-2">
                  {['all', 'conforme', 'non_conforme', 'bozza', 'conforme_con_riserva'].map((status) => (
                    <Button
                      key={status}
                      variant={selectedStatus === status ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedStatus(status)}
                    >
                      {status === 'all' ? 'Tutte' : 
                       status === 'conforme' ? 'Conformi' :
                       status === 'non_conforme' ? 'Non Conformi' :
                       status === 'bozza' ? 'Bozze' : 'Con Riserva'}
                    </Button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Certificazioni Table */}
          <Card>
            <CardHeader>
              <CardTitle>Elenco Certificazioni ({filteredCertificazioni.length})</CardTitle>
              <CardDescription>
                Gestione completa delle certificazioni CEI 64-8
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Numero Certificato</TableHead>
                      <TableHead>ID Cavo</TableHead>
                      <TableHead>Data</TableHead>
                      <TableHead>Stato</TableHead>
                      <TableHead>Isolamento (MΩ)</TableHead>
                      <TableHead>Operatore</TableHead>
                      <TableHead>Azioni</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {isLoading ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-8">
                          <div className="flex items-center justify-center gap-2">
                            <Loader2 className="h-4 w-4 animate-spin" />
                            Caricamento...
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : filteredCertificazioni.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-8 text-slate-500">
                          Nessuna certificazione trovata
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredCertificazioni.map((cert) => (
                        <TableRow key={cert.id_certificazione}>
                          <TableCell className="font-medium">{cert.numero_certificato}</TableCell>
                          <TableCell className="font-mono">{cert.id_cavo}</TableCell>
                          <TableCell>
                            {new Date(cert.data_certificazione).toLocaleDateString('it-IT')}
                          </TableCell>
                          <TableCell>{getStatusBadge(cert.stato_certificato || '')}</TableCell>
                          <TableCell>
                            <span className={`font-medium ${
                              parseFloat(cert.valore_isolamento || '0') >= 500 
                                ? 'text-green-600' 
                                : 'text-red-600'
                            }`}>
                              {cert.valore_isolamento || '-'}
                            </span>
                          </TableCell>
                          <TableCell>{cert.id_operatore || '-'}</TableCell>
                          <TableCell>
                            <div className="flex gap-1">
                              <Button 
                                variant="ghost" 
                                size="sm"
                                onClick={() => handleEditCertificazione(cert)}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button 
                                variant="ghost" 
                                size="sm"
                                onClick={() => handleEditCertificazione(cert)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button 
                                variant="ghost" 
                                size="sm"
                                onClick={() => handleGeneratePDF(cert.id_certificazione)}
                              >
                                <Download className="h-4 w-4" />
                              </Button>
                              <Button 
                                variant="ghost" 
                                size="sm"
                                onClick={() => handleDeleteCertificazione(cert.id_certificazione)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="strumenti">
          <StrumentiManager cantiereId={cantiereId} strumenti={strumenti} onUpdate={loadData} />
        </TabsContent>

        <TabsContent value="rapporti">
          <RapportiGeneraliManager cantiereId={cantiereId} rapporti={rapporti} onUpdate={loadData} />
        </TabsContent>

        <TabsContent value="strumenti">
          <StrumentiManager
            cantiereId={cantiereId}
            strumenti={strumenti}
            onUpdate={loadData}
          />
        </TabsContent>

        <TabsContent value="rapporti">
          <RapportiGeneraliManager
            cantiereId={cantiereId}
            rapporti={rapporti}
            onUpdate={loadData}
          />
        </TabsContent>

        <TabsContent value="non_conformita">
          <NonConformitaManager
            cantiereId={cantiereId}
            nonConformita={nonConformita}
            onUpdate={loadData}
          />
        </TabsContent>

        <TabsContent value="statistiche">
          <CertificazioniStatistics stats={stats} detailed />
        </TabsContent>
      </Tabs>

      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">{error}</AlertDescription>
        </Alert>
      )}
    </div>
  )
}
