exports.id=665,exports.ids=[665],exports.modules={15079:(e,a,s)=>{"use strict";s.d(a,{bq:()=>m,eb:()=>u,gC:()=>x,l6:()=>o,yv:()=>c});var t=s(60687);s(43210);var r=s(97822),i=s(78272),n=s(13964),l=s(3589),d=s(4780);function o({...e}){return(0,t.jsx)(r.bL,{"data-slot":"select",...e})}function c({...e}){return(0,t.jsx)(r.WT,{"data-slot":"select-value",...e})}function m({className:e,size:a="default",children:s,...n}){return(0,t.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":a,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n,children:[s,(0,t.jsx)(r.In,{asChild:!0,children:(0,t.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function x({className:e,children:a,position:s="popper",...i}){return(0,t.jsx)(r.ZL,{children:(0,t.jsxs)(r.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...i,children:[(0,t.jsx)(p,{}),(0,t.jsx)(r.LM,{className:(0,d.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,t.jsx)(h,{})]})})}function u({className:e,children:a,...s}){return(0,t.jsxs)(r.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,children:[(0,t.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,t.jsx)(r.VF,{children:(0,t.jsx)(n.A,{className:"size-4"})})}),(0,t.jsx)(r.p4,{children:a})]})}function p({className:e,...a}){return(0,t.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,t.jsx)(l.A,{className:"size-4"})})}function h({className:e,...a}){return(0,t.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,t.jsx)(i.A,{className:"size-4"})})}},25210:(e,a,s)=>{"use strict";s.d(a,{A:()=>y});var t=s(60687),r=s(29523),i=s(91821),n=s(41862),l=s(10022),d=s(11860),o=s(93613),c=s(8819),m=s(43210),x=s(62185),u=s(80013),p=s(15079);function h({formData:e,cavi:a,responsabili:s,strumenti:r,validationErrors:i,isCavoLocked:n,onInputChange:l}){return(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"id_cavo",className:"text-sm font-medium text-gray-700",children:"Cavo *"}),(0,t.jsxs)(p.l6,{value:e.id_cavo,onValueChange:e=>l("id_cavo",e),disabled:n,children:[(0,t.jsx)(p.bq,{className:`h-11 text-sm ${i.id_cavo?"border-red-500":"border-gray-300"}`,children:(0,t.jsx)(p.yv,{placeholder:"Seleziona cavo..."})}),(0,t.jsx)(p.gC,{children:a.map(e=>(0,t.jsx)(p.eb,{value:e.id_cavo,children:(0,t.jsxs)("div",{className:"flex flex-col py-1",children:[(0,t.jsx)("span",{className:"font-medium text-sm",children:e.id_cavo}),(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:[e.tipologia," ",e.sezione]})]})},e.id_cavo))})]}),i.id_cavo&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:i.id_cavo})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"id_operatore",className:"text-sm font-medium text-gray-700",children:"Operatore"}),(0,t.jsxs)(p.l6,{value:e.id_operatore?.toString()||"",onValueChange:e=>l("id_operatore",parseInt(e)),children:[(0,t.jsx)(p.bq,{className:"h-11 text-sm border-gray-300",children:(0,t.jsx)(p.yv,{placeholder:"Seleziona operatore..."})}),(0,t.jsx)(p.gC,{children:s.map(e=>(0,t.jsx)(p.eb,{value:e.id_responsabile.toString(),children:(0,t.jsx)("span",{className:"text-sm",children:e.nome_responsabile})},e.id_responsabile))})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"id_strumento",className:"text-sm font-medium text-gray-700",children:"Strumento di Misura"}),(0,t.jsxs)(p.l6,{value:e.id_strumento?.toString()||"",onValueChange:e=>{let a=r.find(a=>a.id_strumento===parseInt(e));l("id_strumento",parseInt(e)),a&&l("strumento_utilizzato",`${a.marca} ${a.modello}`)},children:[(0,t.jsx)(p.bq,{className:"h-11 text-sm border-gray-300",children:(0,t.jsx)(p.yv,{placeholder:"Seleziona strumento..."})}),(0,t.jsx)(p.gC,{children:r.map(e=>(0,t.jsx)(p.eb,{value:e.id_strumento.toString(),children:(0,t.jsxs)("div",{className:"flex flex-col py-1",children:[(0,t.jsx)("span",{className:"font-medium text-sm",children:e.nome}),(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:[e.marca," ",e.modello]})]})},e.id_strumento))})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"tipo_certificato",className:"text-sm font-medium text-gray-700",children:"Tipo Certificato"}),(0,t.jsxs)(p.l6,{value:e.tipo_certificato||"SINGOLO",onValueChange:e=>l("tipo_certificato",e),children:[(0,t.jsx)(p.bq,{className:"h-11 text-sm border-gray-300",children:(0,t.jsx)(p.yv,{})}),(0,t.jsxs)(p.gC,{children:[(0,t.jsx)(p.eb,{value:"SINGOLO",children:(0,t.jsx)("span",{className:"text-sm",children:"\uD83D\uDD0D Singolo"})}),(0,t.jsx)(p.eb,{value:"GRUPPO",children:(0,t.jsx)("span",{className:"text-sm",children:"\uD83D\uDCCA Gruppo"})})]})]})]})]})}var g=s(89667),v=s(84027);function f({formData:e,weatherData:a,isLoadingWeather:s,isWeatherOverride:i,onInputChange:l,onToggleWeatherOverride:o}){return a?(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsx)("div",{className:`p-4 rounded-lg border-2 col-span-full ${a.isDemo?"bg-amber-50 border-amber-200":"bg-emerald-50 border-emerald-200"}`,children:(0,t.jsxs)("div",{className:"flex items-start gap-4",children:[s?(0,t.jsx)(n.A,{className:"h-5 w-5 animate-spin text-blue-600 mt-1"}):(0,t.jsx)("div",{className:"text-2xl",children:a.isDemo?"\uD83D\uDD27":"\uD83C\uDF24️"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"text-lg font-semibold text-gray-900",children:[a.temperature,"\xb0C • ",a.humidity,"% UR"]}),a.city&&(0,t.jsx)("div",{className:"text-sm text-gray-600",children:a.city})]}),(0,t.jsx)(r.$,{type:"button",variant:i?"default":"outline",size:"sm",onClick:o,className:"h-8",children:i?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.A,{className:"h-3 w-3 mr-1"}),"Automatico"]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(v.A,{className:"h-3 w-3 mr-1"}),"Manuale"]})})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:["\uD83D\uDCE1 ",a.source,a.isDemo&&" • Dati dimostrativi"]})]})]})}),i&&(0,t.jsxs)("div",{className:"col-span-full p-4 bg-blue-50 border-2 border-blue-200 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,t.jsx)(v.A,{className:"h-4 w-4 text-blue-600"}),(0,t.jsx)("span",{className:"text-sm font-medium text-blue-800",children:"Inserimento Manuale Parametri"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"temperatura_prova",className:"text-sm font-medium text-gray-700",children:"Temperatura (\xb0C)"}),(0,t.jsx)(g.p,{id:"temperatura_prova",type:"number",step:"0.1",value:e.temperatura_prova||"",onChange:e=>l("temperatura_prova",parseFloat(e.target.value)),placeholder:"20.0",className:"h-11 text-sm"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Range tipico: 15-30\xb0C"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"umidita_prova",className:"text-sm font-medium text-gray-700",children:"Umidit\xe0 Relativa (%)"}),(0,t.jsx)(g.p,{id:"umidita_prova",type:"number",min:"0",max:"100",value:e.umidita_prova||"",onChange:e=>l("umidita_prova",parseFloat(e.target.value)),placeholder:"50",className:"h-11 text-sm"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Range tipico: 30-70%"})]})]})]})]}):null}var b=s(38033),j=s(34729);function N({formData:e,onInputChange:a}){return(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2 col-span-full",children:[(0,t.jsx)(u.J,{htmlFor:"note",className:"text-sm font-medium text-gray-700",children:"Note Aggiuntive"}),(0,t.jsx)(j.T,{id:"note",value:e.note||"",onChange:e=>a("note",e.target.value),placeholder:"Inserisci eventuali note, osservazioni o anomalie riscontrate durante la certificazione...",rows:4,className:"resize-none text-sm border-gray-300"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Descrivi eventuali condizioni particolari o osservazioni rilevanti"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"stato_certificato",className:"text-sm font-medium text-gray-700",children:"Stato Certificazione"}),(0,t.jsxs)(p.l6,{value:e.stato_certificato||"BOZZA",onValueChange:e=>a("stato_certificato",e),children:[(0,t.jsx)(p.bq,{className:"h-11 text-sm border-gray-300",children:(0,t.jsx)(p.yv,{})}),(0,t.jsxs)(p.gC,{children:[(0,t.jsx)(p.eb,{value:"BOZZA",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full bg-gray-400"}),(0,t.jsx)("span",{className:"text-sm",children:"Bozza"})]})}),(0,t.jsx)(p.eb,{value:"CONFORME",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full bg-green-500"}),(0,t.jsx)("span",{className:"text-sm",children:"✅ Conforme"})]})}),(0,t.jsx)(p.eb,{value:"NON_CONFORME",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full bg-red-500"}),(0,t.jsx)("span",{className:"text-sm",children:"❌ Non Conforme"})]})}),(0,t.jsx)(p.eb,{value:"CONFORME_CON_RISERVA",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full bg-yellow-500"}),(0,t.jsx)("span",{className:"text-sm",children:"⚠️ Conforme con Riserva"})]})})]})]}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Lo stato verr\xe0 determinato automaticamente in base ai valori misurati"})]})]})}function y({cantiereId:e,certificazione:a,strumenti:s,preselectedCavoId:u,onSuccess:p,onCancel:g}){let{formData:v,cavi:j,responsabili:y,weatherData:w,isLoading:_,isSaving:C,isLoadingWeather:z,error:D,validationErrors:S,isWeatherOverride:O,isEdit:A,isCavoLocked:k,handleInputChange:F,handleSubmit:I,setIsWeatherOverride:R,onCancel:E}=function({cantiereId:e,certificazione:a,strumenti:s,preselectedCavoId:t,onSuccess:r,onCancel:i}){let[n,l]=(0,m.useState)({id_cantiere:e,tipo_certificato:"SINGOLO",stato_certificato:"BOZZA",valore_continuita:"CONFORME",tensione_prova_isolamento:500,temperatura_prova:20,umidita_prova:50}),[d,o]=(0,m.useState)(!0),[c,u]=(0,m.useState)(!1),[p,h]=(0,m.useState)(!1),[g,v]=(0,m.useState)(""),[f,b]=(0,m.useState)({}),[j,N]=(0,m.useState)(!1),[y,w]=(0,m.useState)([]),[_,C]=(0,m.useState)([]),[z,D]=(0,m.useState)(null),S=!!a;async function O(){if(!function(){let e={};return n.id_cavo||(e.id_cavo="Seleziona un cavo"),(!n.valore_isolamento||n.valore_isolamento<=0)&&(e.valore_isolamento="Inserisci un valore di isolamento valido"),n.valore_continuita||(e.valore_continuita="Seleziona il risultato della continuit\xe0"),n.valore_resistenza||(e.valore_resistenza="Inserisci un valore di resistenza"),b(e),0===Object.keys(e).length}())return void v("Correggi gli errori nel form prima di continuare");try{let s;u(!0),v("");let t=function(){let a={...n};return z&&!j&&(a.temperatura_prova=z.temperature,a.umidita_prova=z.humidity),a.id_cantiere=e,a.data_certificazione=a.data_certificazione||new Date().toISOString().split("T")[0],a}();(s=S&&a?await x.km.updateCertificazione(a.id_certificazione,t):await x.km.createCertificazione(t)).data&&r(s.data)}catch(e){console.error("Errore salvataggio:",e),v(e.response?.data?.detail||"Errore durante il salvataggio")}finally{u(!1)}}return{formData:n,cavi:y,responsabili:_,strumenti:s,weatherData:z,isLoading:d,isSaving:c,isLoadingWeather:p,error:g,validationErrors:f,isWeatherOverride:j,isEdit:S,isCavoLocked:!!t,handleInputChange:function(e,a){l(s=>({...s,[e]:a})),f[e]&&b(a=>{let s={...a};return delete s[e],s})},handleSubmit:O,setIsWeatherOverride:N,onCancel:i}}({cantiereId:e,certificazione:a,strumenti:s,preselectedCavoId:u,onSuccess:p,onCancel:g});return _?(0,t.jsx)("div",{className:"flex items-center justify-center h-96",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(n.A,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Caricamento dati certificazione..."})]})}):(0,t.jsxs)("div",{className:"h-full w-full bg-gray-50",children:[(0,t.jsx)("div",{className:"bg-white border-b border-gray-200 sticky top-0 z-10",children:(0,t.jsx)("div",{className:"max-w-4xl mx-auto px-6 py-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,t.jsx)(l.A,{className:"h-6 w-6 text-blue-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:A?"Modifica Certificazione":"Nuova Certificazione CEI 64-8"}),u&&(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:["Cavo: ",u]})]})]}),(0,t.jsx)(r.$,{variant:"ghost",size:"sm",onClick:g,className:"text-gray-400 hover:text-gray-600",children:(0,t.jsx)(d.A,{className:"h-5 w-5"})})]})})}),D&&(0,t.jsx)("div",{className:"bg-red-50 border-b border-red-200",children:(0,t.jsx)("div",{className:"max-w-4xl mx-auto px-6 py-4",children:(0,t.jsxs)(i.Fc,{variant:"destructive",className:"border-red-200 bg-red-50",children:[(0,t.jsx)(o.A,{className:"h-4 w-4"}),(0,t.jsx)(i.TN,{children:D})]})})}),(0,t.jsx)("div",{className:"max-w-4xl mx-auto px-6 py-8",children:(0,t.jsxs)("form",{onSubmit:e=>{e.preventDefault(),I()},className:"space-y-8",children:[(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden",children:[(0,t.jsxs)("div",{className:"px-6 py-4 border-b border-gray-100 bg-gray-50",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"\uD83D\uDCCB Informazioni Base"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Dati principali del cavo e operatore"})]}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsx)(h,{formData:v,cavi:j,responsabili:y,strumenti:s,validationErrors:S,isCavoLocked:k,onInputChange:F})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden",children:[(0,t.jsxs)("div",{className:"px-6 py-4 border-b border-gray-100 bg-gray-50",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"\uD83C\uDF24️ Condizioni Ambientali"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Temperatura e umidit\xe0 durante la certificazione"})]}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsx)(f,{formData:v,weatherData:w,isLoadingWeather:z,isWeatherOverride:O,onInputChange:F,onToggleWeatherOverride:()=>R(!O)})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden",children:[(0,t.jsxs)("div",{className:"px-6 py-4 border-b border-gray-100 bg-gray-50",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"⚡ Misurazioni e Test CEI 64-8"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Valori di isolamento, continuit\xe0 e parametri di prova"})]}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsx)(b.MisurazioniTest,{formData:v,validationErrors:S,onInputChange:F})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden",children:[(0,t.jsxs)("div",{className:"px-6 py-4 border-b border-gray-100 bg-gray-50",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"\uD83D\uDCDD Note e Stato Certificazione"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Osservazioni aggiuntive e stato finale"})]}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsx)(N,{formData:v,onInputChange:F})})]}),(0,t.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:(0,t.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,t.jsx)(r.$,{type:"button",variant:"outline",onClick:g,disabled:C,className:"px-8 py-2",children:"Annulla"}),(0,t.jsx)(r.$,{type:"submit",disabled:C,className:"px-8 py-2 bg-blue-600 hover:bg-blue-700",children:C?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(n.A,{className:"h-4 w-4 animate-spin mr-2"}),"Salvando..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(c.A,{className:"h-4 w-4 mr-2"}),A?"Aggiorna Certificazione":"Salva Certificazione"]})})]})})]})})]})}},34729:(e,a,s)=>{"use strict";s.d(a,{T:()=>n});var t=s(60687),r=s(43210),i=s(4780);let n=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...a}));n.displayName="Textarea"},38033:()=>{},44493:(e,a,s)=>{"use strict";s.d(a,{BT:()=>d,Wu:()=>o,ZB:()=>l,Zp:()=>i,aR:()=>n});var t=s(60687);s(43210);var r=s(4780);function i({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...a})}function n({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...a})}function l({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",e),...a})}function d({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...a})}function o({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...a})}},70440:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>r});var t=s(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},80013:(e,a,s)=>{"use strict";s.d(a,{J:()=>n});var t=s(60687);s(43210);var r=s(78148),i=s(4780);function n({className:e,...a}){return(0,t.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...a})}},89667:(e,a,s)=>{"use strict";s.d(a,{p:()=>n});var t=s(60687),r=s(43210),i=s(4780);let n=r.forwardRef(({className:e,type:a,...s},r)=>(0,t.jsx)("input",{type:a,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),ref:r,...s}));n.displayName="Input"},96834:(e,a,s)=>{"use strict";s.d(a,{E:()=>d});var t=s(60687);s(43210);var r=s(8730),i=s(24224),n=s(4780);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:a,asChild:s=!1,...i}){let d=s?r.DX:"span";return(0,t.jsx)(d,{"data-slot":"badge",className:(0,n.cn)(l({variant:a}),e),...i})}}};