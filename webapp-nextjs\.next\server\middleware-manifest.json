{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "103363c19b2bbaec096f1f3c56c9993f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d6d8cb812578824f8fc4a5a279fd399fcdfd7881f4b4e03f86b2f61b90c9c2c7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "7a9f065d0ceb7d46a4c93ffd7c4f9815bbe33147c2f7bc04338fbac7e3485665"}}}, "sortedMiddleware": ["/"], "functions": {}}