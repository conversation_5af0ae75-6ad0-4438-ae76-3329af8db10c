{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "6394b5cfbafb2024661894be01a5331b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "3d3fc6a790b074d9ccad6710ac8c188f816d2c2020401506d9b82ce930b8fbf7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "92c178ee85802b68144f738f48b9cff09c4e0b077d3f76803f810c670e9d42a3"}}}, "sortedMiddleware": ["/"], "functions": {}}