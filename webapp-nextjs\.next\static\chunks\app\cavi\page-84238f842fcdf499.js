(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3986],{3469:(e,a,i)=>{Promise.resolve().then(i.bind(i,46067))},13587:(e,a,i)=>{"use strict";i.d(a,{u:()=>u});var t=i(95155);i(12115);var s=i(55365),l=i(30285),n=i(66695),r=i(53904),o=i(85339),c=i(47957),d=i(35169),m=i(35695),x=i(17522);function u(e){let{children:a,fallback:i,showBackButton:u=!0,backUrl:h="/cantieri"}=e,p=(0,m.useRouter)(),{cantiereId:v,cantiere:g,isValidCantiere:b,isLoading:j,error:f,clearError:N}=(0,x.jV)();if(j)return(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,t.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"flex items-center justify-center p-8",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(r.A,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600"}),(0,t.jsx)("p",{className:"text-lg font-medium text-gray-700",children:"Caricamento cantiere..."}),(0,t.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"Verifica della selezione cantiere in corso"})]})})})})});if(f||!b){let e=f||"Nessun cantiere selezionato";return i?(0,t.jsx)(t.Fragment,{children:i}):(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6",children:[(0,t.jsxs)(n.Zp,{className:"border-red-200 bg-red-50",children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center text-red-800",children:[(0,t.jsx)(o.A,{className:"h-6 w-6 mr-2"}),"Problema con la selezione del cantiere"]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)(s.Fc,{variant:"destructive",children:[(0,t.jsx)(o.A,{className:"h-4 w-4"}),(0,t.jsxs)(s.TN,{children:[(0,t.jsx)("strong",{children:"Errore:"})," ",e]})]})})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center text-gray-700",children:[(0,t.jsx)(c.A,{className:"h-5 w-5 mr-2"}),"Informazioni cantiere"]})}),(0,t.jsx)(n.Wu,{className:"space-y-3",children:(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"ID Cantiere:"}),(0,t.jsx)("span",{className:"ml-2 text-gray-800",children:v||"Non disponibile"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Nome Cantiere:"}),(0,t.jsx)("span",{className:"ml-2 text-gray-800",children:(null==g?void 0:g.commessa)||"Non disponibile"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Cantiere Valido:"}),(0,t.jsx)("span",{className:"ml-2 ".concat(b?"text-green-600":"text-red-600"),children:b?"S\xec":"No"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"localStorage ID:"}),(0,t.jsx)("span",{className:"ml-2 text-gray-800",children:localStorage.getItem("selectedCantiereId")||"Non presente"})]})]})})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)(n.ZB,{className:"text-gray-700",children:"Azioni disponibili"})}),(0,t.jsxs)(n.Wu,{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex flex-wrap gap-3",children:[u&&(0,t.jsxs)(l.$,{onClick:()=>p.push(h),variant:"default",className:"flex items-center",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Seleziona Cantiere"]}),f&&(0,t.jsxs)(l.$,{onClick:N,variant:"outline",className:"flex items-center",children:[(0,t.jsx)(r.A,{className:"h-4 w-4 mr-2"}),"Riprova"]}),(0,t.jsxs)(l.$,{onClick:()=>{localStorage.removeItem("selectedCantiereId"),localStorage.removeItem("selectedCantiereName"),localStorage.removeItem("cantiere_data"),window.location.reload()},variant:"outline",className:"flex items-center text-orange-600 border-orange-300 hover:bg-orange-50",children:[(0,t.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Reset Dati Cantiere"]})]}),(0,t.jsx)("div",{className:"mt-4 p-3 bg-blue-50 rounded-lg",children:(0,t.jsxs)("p",{className:"text-sm text-blue-800",children:[(0,t.jsx)("strong",{children:"Suggerimento:"})," Se il problema persiste, prova a selezionare nuovamente un cantiere dalla pagina principale o contatta l'amministratore del sistema."]})})]})]})]})})}return(0,t.jsx)(t.Fragment,{children:a})}},46067:(e,a,i)=>{"use strict";i.r(a),i.d(a,{default:()=>e0});var t=i(95155),s=i(12115),l=i(35695),n=i(66695),r=i(55365),o=i(40283),c=i(17522),d=i(13587),m=i(25731),x=i(30285),u=i(26126),h=i(47262),p=i(63743),v=i(85127),g=i(62523),b=i(59409),j=i(20547),f=i(59434);let N=j.bL,_=j.l9,y=s.forwardRef((e,a)=>{let{className:i,align:s="center",sideOffset:l=4,...n}=e;return(0,t.jsx)(j.ZL,{children:(0,t.jsx)(j.UC,{ref:a,align:s,sideOffset:l,className:(0,f.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",i),...n})})});y.displayName=j.UC.displayName;var C=i(21492),z=i(39881),w=i(58832),A=i(54416),S=i(66932),E=i(52278),T=i(42355),I=i(13052),k=i(12767);function O(e){let{data:a=[],columns:i=[],loading:l=!1,emptyMessage:r="Nessun dato disponibile",onFilteredDataChange:o,renderRow:c,className:d,pagination:m=!0,defaultRowsPerPage:h=25}=e,[p,g]=(0,s.useState)({key:null,direction:null}),[j,O]=(0,s.useState)({}),[R,L]=(0,s.useState)({}),[D,M]=(0,s.useState)(0),[B,U]=(0,s.useState)(h),P=e=>{let t=i.find(a=>a.field===e);return(null==t?void 0:t.getFilterValue)?[...new Set(a.map(e=>t.getFilterValue(e)).filter(Boolean))].sort():[...new Set(a.map(a=>a[e]).filter(Boolean))].sort()},$=(0,s.useMemo)(()=>{let e=[...a];return Object.entries(j).forEach(a=>{let[t,s]=a;!s.value||Array.isArray(s.value)&&0===s.value.length||"string"==typeof s.value&&""===s.value.trim()||(e=e.filter(e=>{let a=i.find(e=>e.field===t),l=(null==a?void 0:a.getFilterValue)?a.getFilterValue(e):e[t];if("select"===s.type)return(Array.isArray(s.value)?s.value:[s.value]).includes(l);if("text"===s.type){let e=s.value.toLowerCase(),a=String(l||"").toLowerCase();return"equals"===s.operator?a===e:a.includes(e)}if("number"===s.type){let e=parseFloat(l),a=parseFloat(s.value);if(isNaN(e)||isNaN(a))return!1;switch(s.operator){case"equals":default:return e===a;case"gt":return e>a;case"lt":return e<a;case"gte":return e>=a;case"lte":return e<=a}}return!0}))}),p.key&&p.direction&&e.sort((e,a)=>{let i=e[p.key],t=a[p.key];if(null==i&&null==t)return 0;if(null==i)return"asc"===p.direction?-1:1;if(null==t)return"asc"===p.direction?1:-1;let s=parseFloat(i),l=parseFloat(t),n=!isNaN(s)&&!isNaN(l),r=0;return r=n?s-l:String(i).localeCompare(String(t)),"asc"===p.direction?r:-r}),e},[a,j,p]),V=(0,s.useMemo)(()=>{if(!m)return $;let e=D*B,a=e+B;return $.slice(e,a)},[$,D,B,m]);(0,s.useEffect)(()=>{M(0)},[j]);let J=Math.ceil($.length/B),G=D*B+1,W=Math.min((D+1)*B,$.length);(0,s.useEffect)(()=>{o&&o($)},[$]);let Z=e=>{let a=i.find(a=>a.field===e);null!=a&&a.disableSort||g(a=>{if(a.key===e){if("asc"===a.direction)return{key:e,direction:"desc"};if("desc"===a.direction)return{key:null,direction:null}}return{key:e,direction:"asc"}})},q=(e,a)=>{O(i=>({...i,[e]:{...i[e],...a}}))},H=e=>{O(a=>{let i={...a};return delete i[e],i})},X=e=>p.key!==e?(0,t.jsx)(C.A,{className:"h-3 w-3"}):"asc"===p.direction?(0,t.jsx)(z.A,{className:"h-3 w-3"}):"desc"===p.direction?(0,t.jsx)(w.A,{className:"h-3 w-3"}):(0,t.jsx)(C.A,{className:"h-3 w-3"}),K=Object.keys(j).length>0;return l?(0,t.jsx)(n.Zp,{className:d,children:(0,t.jsx)(n.Wu,{className:"p-6",children:(0,t.jsx)("div",{className:"text-center",children:"Caricamento..."})})}):(0,t.jsxs)("div",{className:d,children:[K&&(0,t.jsxs)("div",{className:"mb-4 flex flex-wrap gap-2 items-center",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Filtri attivi:"}),Object.entries(j).map(e=>{let[a,s]=e,l=i.find(e=>e.field===a);if(!l)return null;let n=Array.isArray(s.value)?s.value.join(", "):String(s.value);return(0,t.jsxs)(u.E,{variant:"secondary",className:"gap-1",children:[l.headerName,": ",n,(0,t.jsx)(x.$,{variant:"ghost",size:"sm",className:"h-auto p-0 hover:bg-transparent",onClick:()=>H(a),children:(0,t.jsx)(A.A,{className:"h-3 w-3"})})]},a)}),(0,t.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>{O({})},className:"h-6 px-2 text-xs",children:"Pulisci tutti"})]}),(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-0",children:(0,t.jsxs)(v.XI,{children:[(0,t.jsx)(v.A0,{children:(0,t.jsx)(v.Hj,{className:"bg-mariner-50 hover:bg-mariner-50",children:i.map(e=>(0,t.jsx)(v.nd,{className:(0,f.cn)("font-semibold text-mariner-900 border-b border-mariner-200","center"===e.align&&"text-center","right"===e.align&&"text-right"),style:{width:e.width,...e.headerStyle},children:e.renderHeader?e.renderHeader():(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,t.jsx)("span",{className:"truncate",children:e.headerName}),(0,t.jsxs)("div",{className:"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:[!e.disableSort&&(0,t.jsx)(x.$,{variant:"ghost",size:"sm",className:"h-4 w-4 p-0 hover:bg-mariner-100",onClick:()=>Z(e.field),children:X(e.field)}),!e.disableFilter&&(0,t.jsxs)(N,{open:R[e.field],onOpenChange:a=>L(i=>({...i,[e.field]:a})),children:[(0,t.jsx)(_,{asChild:!0,children:(0,t.jsx)(x.$,{variant:"ghost",size:"sm",className:(0,f.cn)("h-4 w-4 p-0 hover:bg-mariner-100",j[e.field]&&"text-mariner-600 opacity-100"),children:(0,t.jsx)(S.A,{className:"h-2.5 w-2.5"})})}),(0,t.jsx)(y,{className:"w-64",align:"start",children:(0,t.jsx)(F,{column:e,data:a,currentFilter:j[e.field],onFilterChange:a=>q(e.field,a),onClearFilter:()=>H(e.field),getUniqueValues:()=>P(e.field)})})]})]})]}),j[e.field]&&(0,t.jsx)("div",{className:"absolute -top-1 -right-1 h-2 w-2 bg-mariner-600 rounded-full"})]})},e.field))})}),(0,t.jsx)(v.BF,{children:V.length>0?V.map((e,a)=>c?c(e,D*B+a):(0,t.jsx)(v.Hj,{className:"hover:bg-mariner-50 border-b border-mariner-100",children:i.map(a=>(0,t.jsx)(v.nA,{className:(0,f.cn)("py-2 px-4","center"===a.align&&"text-center","right"===a.align&&"text-right"),style:a.cellStyle,children:a.renderCell?a.renderCell(e):e[a.field]},a.field))},a)):(0,t.jsx)(v.Hj,{children:(0,t.jsx)(v.nA,{colSpan:i.length,className:"text-center py-8 text-muted-foreground",children:r})})})]})})}),m&&$.length>0&&(0,t.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Righe per pagina:"}),(0,t.jsxs)(b.l6,{value:B.toString(),onValueChange:e=>{U(Number(e)),M(0)},children:[(0,t.jsx)(b.bq,{className:"w-20",children:(0,t.jsx)(b.yv,{})}),(0,t.jsxs)(b.gC,{children:[(0,t.jsx)(b.eb,{value:"10",children:"10"}),(0,t.jsx)(b.eb,{value:"25",children:"25"}),(0,t.jsx)(b.eb,{value:"50",children:"50"}),(0,t.jsx)(b.eb,{value:"100",children:"100"}),(0,t.jsx)(b.eb,{value:$.length.toString(),children:"Tutto"})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:$.length>0?"".concat(G,"-").concat(W," di ").concat($.length):"0 di 0"}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>M(0),disabled:0===D,className:"h-8 w-8 p-0",children:(0,t.jsx)(E.A,{className:"h-4 w-4"})}),(0,t.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>M(e=>Math.max(0,e-1)),disabled:0===D,className:"h-8 w-8 p-0",children:(0,t.jsx)(T.A,{className:"h-4 w-4"})}),(0,t.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>M(e=>Math.min(J-1,e+1)),disabled:D>=J-1,className:"h-8 w-8 p-0",children:(0,t.jsx)(I.A,{className:"h-4 w-4"})}),(0,t.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>M(J-1),disabled:D>=J-1,className:"h-8 w-8 p-0",children:(0,t.jsx)(k.A,{className:"h-4 w-4"})})]})]})]})]})}function F(e){let{column:a,currentFilter:i,onFilterChange:l,onClearFilter:n,getUniqueValues:r}=e,[o,c]=(0,s.useState)((null==i?void 0:i.value)||""),[d,m]=(0,s.useState)((null==i?void 0:i.operator)||"contains"),u=r(),p="number"!==a.dataType&&u.length<=20,v="number"===a.dataType,j=()=>{p?l({type:"select",value:Array.isArray(o)?o:[o]}):v?l({type:"number",value:o,operator:d}):l({type:"text",value:o,operator:d})};return(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"font-medium text-sm",children:["Filtra ",a.headerName]}),p?(0,t.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:u.map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(h.S,{id:"filter-".concat(e),checked:Array.isArray(o)?o.includes(e):o===e,onCheckedChange:a=>{Array.isArray(o)?c(a?[...o,e]:o.filter(a=>a!==e)):c(a?[e]:[])}}),(0,t.jsx)("label",{htmlFor:"filter-".concat(e),className:"text-sm",children:e})]},e))}):(0,t.jsxs)("div",{className:"space-y-2",children:[v&&(0,t.jsxs)(b.l6,{value:d,onValueChange:m,children:[(0,t.jsx)(b.bq,{children:(0,t.jsx)(b.yv,{})}),(0,t.jsxs)(b.gC,{children:[(0,t.jsx)(b.eb,{value:"equals",children:"Uguale a"}),(0,t.jsx)(b.eb,{value:"gt",children:"Maggiore di"}),(0,t.jsx)(b.eb,{value:"lt",children:"Minore di"}),(0,t.jsx)(b.eb,{value:"gte",children:"Maggiore o uguale"}),(0,t.jsx)(b.eb,{value:"lte",children:"Minore o uguale"})]})]}),!v&&(0,t.jsxs)(b.l6,{value:d,onValueChange:m,children:[(0,t.jsx)(b.bq,{children:(0,t.jsx)(b.yv,{})}),(0,t.jsxs)(b.gC,{children:[(0,t.jsx)(b.eb,{value:"contains",children:"Contiene"}),(0,t.jsx)(b.eb,{value:"equals",children:"Uguale a"})]})]}),(0,t.jsx)(g.p,{placeholder:"Cerca ".concat(a.headerName.toLowerCase(),"..."),value:o,onChange:e=>c(e.target.value),onKeyDown:e=>"Enter"===e.key&&j()})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(x.$,{size:"sm",onClick:j,children:"Applica"}),(0,t.jsx)(x.$,{size:"sm",variant:"outline",onClick:n,children:"Pulisci"})]})]})}var R=i(47924),L=i(28826),D=i(18979),M=i(27882);function B(e){let{cavi:a=[],onFilteredDataChange:i,loading:l=!1,selectionEnabled:r=!1,onSelectionToggle:o,selectedCount:c=0,totalCount:d=0}=e,[m,u]=(0,s.useState)(""),[h,p]=(0,s.useState)("contains"),v=e=>e?e.toString().toLowerCase().trim():"",j=e=>{let a=e.match(/^([A-Z]+)(\d+)([A-Z]*)$/);return a?{prefix:a[1],number:a[2],suffix:a[3]||""}:{prefix:"",number:e,suffix:""}},f=(0,s.useCallback)((e,a,i)=>{let t=v(a);if(!t)return!0;let s=v(e.id_cavo),{prefix:l,number:n,suffix:r}=j(e.id_cavo||""),o=v(e.tipologia),c=v(e.formazione||e.sezione),d=v(e.utility),m=v(e.sistema),x=v(e.da||e.ubicazione_partenza),u=v(e.a||e.ubicazione_arrivo),h=v(e.utenza_partenza),p=v(e.utenza_arrivo),g=[s,l,n,r,o,c,d,m,x,u,h,p,v(e.id_bobina),"BOBINA_VUOTA"===e.id_bobina?"bobina vuota":null===e.id_bobina?"":v(e.id_bobina)],b=[{value:e.metri_teorici,name:"metri_teorici"},{value:e.metratura_reale||e.metri_posati,name:"metratura_reale"},{value:parseFloat(c),name:"formazione"}],f=t.match(/^([><=]+)(\d+(?:\.\d+)?)$/);if(f){let e=f[1],a=parseFloat(f[2]);return b.some(i=>{if(null==i.value||isNaN(i.value))return!1;switch(e){case">":return i.value>a;case">=":return i.value>=a;case"<":return i.value<a;case"<=":return i.value<=a;case"=":return i.value===a;default:return!1}})}let N=parseFloat(t);return!!(!isNaN(N)&&b.some(e=>null!=e.value&&!isNaN(e.value)&&e.value===N))||(i?g.some(e=>e===t):g.some(e=>e.includes(t)))},[]),N=(0,s.useCallback)(()=>{if(!m.trim()){null==i||i(a);return}let e=m.split(",").map(e=>e.trim()).filter(e=>e.length>0),t=[];t="equals"===h?1===e.length?a.filter(a=>f(a,e[0],!0)):a.filter(a=>e.every(e=>f(a,e,!0))):a.filter(a=>e.some(e=>f(a,e,!1))),null==i||i(t)},[m,h,a,f]);(0,s.useEffect)(()=>{N()},[m,h,a,f]);let _=e=>{u(e)},y=()=>{u(""),p("contains")};return(0,t.jsx)(n.Zp,{className:"mb-1",children:(0,t.jsxs)(n.Wu,{className:"p-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsxs)("div",{className:"flex-1 relative",children:[(0,t.jsx)(R.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(g.p,{placeholder:"Cerca per ID, sistema, utility, tipologia, ubicazione...",value:m,onChange:e=>_(e.target.value),disabled:l,className:"pl-10 pr-10 h-8","aria-label":"Campo di ricerca intelligente per cavi"}),m&&(0,t.jsx)(x.$,{variant:"ghost",size:"sm",className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-5 w-5 p-0",onClick:y,children:(0,t.jsx)(A.A,{className:"h-2.5 w-2.5"})})]}),(0,t.jsx)("div",{className:"w-32",children:(0,t.jsxs)(b.l6,{value:h,onValueChange:e=>p(e),children:[(0,t.jsx)(b.bq,{className:"h-8",children:(0,t.jsx)(b.yv,{})}),(0,t.jsxs)(b.gC,{children:[(0,t.jsx)(b.eb,{value:"contains",children:"Contiene"}),(0,t.jsx)(b.eb,{value:"equals",children:"Uguale a"})]})]})}),m&&(0,t.jsxs)(x.$,{variant:"outline",size:"sm",onClick:y,disabled:l,className:"transition-all duration-200 hover:scale-105","aria-label":"Pulisci ricerca",children:[(0,t.jsx)(A.A,{className:"h-4 w-4 mr-1"}),"Pulisci"]}),o&&d>0&&(0,t.jsxs)(x.$,{variant:r?"default":"outline",size:"sm",onClick:o,className:"flex items-center gap-2 transition-all duration-200 hover:scale-105","aria-label":r?"Disabilita modalit\xe0 selezione":"Abilita modalit\xe0 selezione",children:[r?(0,t.jsx)(L.A,{className:"h-4 w-4"}):(0,t.jsx)(D.A,{className:"h-4 w-4"}),r?"Disabilita Selezione":"Abilita Selezione"]}),r&&c>0&&(0,t.jsxs)(x.$,{variant:"outline",size:"sm",onClick:()=>{},className:"flex items-center gap-2 transition-all duration-200 hover:scale-105 text-orange-600 border-orange-300 hover:bg-orange-50","aria-label":"Deseleziona tutti i ".concat(c," cavi selezionati"),children:[(0,t.jsx)(M.A,{className:"h-4 w-4"}),"Deseleziona Tutto (",c,")"]})]}),m&&(0,t.jsx)("div",{className:"mt-0.5 text-xs text-muted-foreground",children:(0,t.jsxs)("div",{className:"flex flex-wrap gap-1",children:[(0,t.jsx)("span",{children:"\uD83D\uDCA1"}),(0,t.jsx)("span",{children:"• Virgole per multipli"}),(0,t.jsx)("span",{children:"• >100, <=50 per numeri"})]})})]})})}function U(e){let{text:a,maxLength:i=20,className:l=""}=e,[n,r]=(0,s.useState)(!1),[o,c]=(0,s.useState)({x:0,y:0});if(!a)return(0,t.jsx)("span",{className:"text-gray-400",children:"-"});let d=a.length>i,m=d?"".concat(a.substring(0,i),"..."):a;return d?(0,t.jsxs)("div",{className:"relative inline-block",children:[(0,t.jsx)("span",{className:"cursor-help ".concat(l),style:{textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden",maxWidth:"100%",display:"inline-block"},onMouseEnter:e=>{c({x:e.clientX,y:e.clientY}),r(!0)},onMouseMove:e=>{c({x:e.clientX,y:e.clientY})},onMouseLeave:()=>r(!1),title:a,children:m}),n&&(0,t.jsxs)("div",{className:"fixed z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-md shadow-lg pointer-events-none",style:{top:o.y-40,left:o.x-150,maxWidth:"300px",wordWrap:"break-word",whiteSpace:"normal"},children:[a,(0,t.jsx)("div",{className:"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0",style:{borderLeft:"5px solid transparent",borderRight:"5px solid transparent",borderTop:"5px solid #1f2937"}})]})]}):(0,t.jsx)("span",{className:l,children:a})}var P=i(47650),$=i(92657),V=i(13717),J=i(62525),G=i(84616),W=i(38164),Z=i(37108),q=i(69037),H=i(85690),X=i(82178),K=i(24357),Y=i(57434),Q=i(91788);function ee(e){let{isOpen:a,position:i,cavo:l,isSelected:n,hasMultipleSelection:r,totalSelectedCount:o,onAction:c,onClose:d}=e,m=(0,s.useRef)(null);if((0,s.useEffect)(()=>{let e=e=>{m.current&&!m.current.contains(e.target)&&d()},i=e=>{"Escape"===e.key&&d()};return a&&(document.addEventListener("mousedown",e),document.addEventListener("keydown",i)),()=>{document.removeEventListener("mousedown",e),document.removeEventListener("keydown",i)}},[a,d]),!a||!l)return null;let u=e=>{c(l,e),d()},h=[{section:"Gestione Cavi",items:[{icon:$.A,label:"Visualizza Dettagli",action:"view_details"},{icon:V.A,label:"Modifica Cavo",action:"edit"},{icon:J.A,label:"Elimina Cavo",action:"delete",destructive:!0},{icon:G.A,label:"Aggiungi Nuovo Cavo",action:"add_new"}]},{section:"Comandi",items:[{icon:W.A,label:"Gestisci Collegamenti",action:"manage_connections"},{icon:Z.A,label:"Gestisci Bobina",action:"manage_reel"},{icon:q.A,label:"Certifica Cavo",action:"create_certificate"},{icon:H.A,label:"Avvia Installazione",action:"start_installation"},{icon:X.A,label:"Sospendi Installazione",action:"pause_installation"}]},{section:"Generale",items:[{icon:K.A,label:"Copia ID",action:"copy_id"},{icon:Y.A,label:"Copia Dettagli",action:"copy_details"},{icon:Q.A,label:"Esporta Dati",action:"export_data"}]}],p={x:Math.min(i.x,window.innerWidth-250),y:Math.min(i.y,window.innerHeight-400)};return(0,P.createPortal)((0,t.jsxs)("div",{ref:m,className:"fixed z-[9999] bg-white border-2 border-gray-300 rounded-lg shadow-2xl py-2 min-w-[220px] max-w-[300px]",style:{left:p.x,top:p.y,boxShadow:"0 10px 25px rgba(0, 0, 0, 0.2)"},children:[(0,t.jsxs)("div",{className:"px-3 py-2 border-b border-gray-100",children:[(0,t.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:["Cavo: ",l.id_cavo]}),n&&(0,t.jsxs)("div",{className:"text-xs text-blue-600",children:["Selezionato ",r?"(".concat(o," totali)"):""]})]}),h.map((e,a)=>(0,t.jsxs)("div",{children:[a>0&&(0,t.jsx)("hr",{className:"my-1 border-gray-200"}),(0,t.jsxs)("div",{className:"px-2 py-1",children:[(0,t.jsx)("div",{className:"text-xs font-medium text-gray-500 px-2 py-1",children:e.section}),e.items.map(e=>(0,t.jsxs)(x.$,{variant:"ghost",size:"sm",className:"w-full justify-start text-left h-8 px-2 ".concat(e.destructive?"text-red-600 hover:text-red-700 hover:bg-red-50":"text-gray-700 hover:text-gray-900 hover:bg-gray-50"),onClick:()=>u(e.action),children:[(0,t.jsx)(e.icon,{className:"w-4 h-4 mr-2"}),e.label]},e.action))]})]},e.section))]}),document.body)}var ea=i(66474),ei=i(40646),et=i(14186),es=i(85339),el=i(81284);function en(e){let{cavi:a=[],loading:i=!1,selectionEnabled:l=!1,selectedCavi:n=[],onSelectionChange:r,onStatusAction:o,onContextMenuAction:c}=e,[d,m]=(0,s.useState)(a),[g,b]=(0,s.useState)(a),[j,f]=(0,s.useState)(l),[N,_]=(0,s.useState)({isOpen:!1,position:{x:0,y:0},cavo:null});(0,s.useEffect)(()=>{m(a),b(a)},[a]);let y=e=>{r&&r(e?g.map(e=>e.id_cavo):[])},C=(e,a)=>{r&&r(a?[...n,e]:n.filter(a=>a!==e))},z=async()=>{try{let e=await fetch("/api/cavi/export",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({selectedIds:n,cantiereId:1})});if(e.ok){let a=await e.blob(),i=window.URL.createObjectURL(a),t=document.createElement("a");t.href=i,t.download="cavi_export_".concat(new Date().toISOString().split("T")[0],".csv"),document.body.appendChild(t),t.click(),window.URL.revokeObjectURL(i),document.body.removeChild(t)}else{let a=await e.json();alert("Errore durante l'esportazione: ".concat(a.error))}}catch(e){alert("Errore durante l'esportazione")}},w=async()=>{let e=prompt("Inserisci il nuovo stato (Da installare, In corso, Installato):");if(e)try{let a=await fetch("/api/cavi/bulk-status",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({selectedIds:n,cantiereId:1,newStatus:e})}),i=await a.json();i.success?alert(i.message):alert("Errore: ".concat(i.error))}catch(e){alert("Errore durante il cambio stato")}},A=()=>{let e=g.filter(e=>{let a=e.metri_posati>0||e.metratura_reale>0,i=n.includes(e.id_cavo),t=!0!==e.certificato&&"SI"!==e.certificato&&"CERTIFICATO"!==e.certificato;return i&&a&&t});if(0===e.length)return void alert("Nessun cavo selezionato pu\xf2 essere certificato. I cavi devono essere installati e non ancora certificati.");if(e.length!==n.length){let a=n.length-e.length;if(!confirm("".concat(a," cavi selezionati non possono essere certificati (non installati o gi\xe0 certificati). Procedere con la certificazione di ").concat(e.length," cavi?")))return}o&&o(e[0],"bulk_certify",e)},S=()=>{alert("Assegnazione comanda per ".concat(n.length," cavi"))},E=async()=>{if(confirm("Sei sicuro di voler eliminare ".concat(n.length," cavi?")))try{let e=await fetch("/api/cavi/bulk-delete",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({selectedIds:n,cantiereId:1})}),a=await e.json();a.success?alert(a.message):alert("Errore: ".concat(a.error))}catch(e){alert("Errore durante l'eliminazione")}},T=(0,s.useMemo)(()=>{let e=[{field:"id_cavo",headerName:"ID",dataType:"text",width:70,align:"left",renderCell:e=>(0,t.jsx)("span",{className:"font-semibold text-mariner-900",children:e.id_cavo})},{field:"sistema",headerName:"Sistema",dataType:"text",width:80,renderCell:e=>(0,t.jsx)(U,{text:e.sistema||"",maxLength:8})},{field:"utility",headerName:"Utility",dataType:"text",width:80,renderCell:e=>(0,t.jsx)(U,{text:e.utility||"",maxLength:8})},{field:"tipologia",headerName:"Tipologia",dataType:"text",width:100,renderCell:e=>(0,t.jsx)(U,{text:e.tipologia||"",maxLength:12})},{field:"formazione",headerName:"Form.",dataType:"text",align:"left",width:60,renderCell:e=>e.formazione||e.sezione},{field:"metri_teorici",headerName:"M.Teor.",dataType:"number",align:"left",width:70,renderCell:e=>e.metri_teorici?e.metri_teorici.toFixed(1):"0"},{field:"metri_posati",headerName:"M.Reali",dataType:"number",align:"left",width:70,renderCell:e=>{let a=e.metri_posati||e.metratura_reale||0;return a?a.toFixed(1):"0"}},{field:"ubicazione_partenza",headerName:"Da",dataType:"text",width:140,renderCell:e=>(0,t.jsx)(U,{text:e.da||e.ubicazione_partenza||"",maxLength:18})},{field:"ubicazione_arrivo",headerName:"A",dataType:"text",width:140,renderCell:e=>(0,t.jsx)(U,{text:e.a||e.ubicazione_arrivo||"",maxLength:18})},{field:"id_bobina",headerName:"Bobina",dataType:"text",width:80,align:"center",renderCell:e=>R(e)},{field:"stato_installazione",headerName:"Stato",dataType:"text",align:"left",width:120,disableSort:!0,getFilterValue:e=>I(e),renderCell:e=>L(e)},{field:"collegamenti",headerName:"Collegamenti",dataType:"text",align:"left",width:180,disableSort:!0,getFilterValue:e=>k(e),renderCell:e=>D(e)},{field:"certificato",headerName:"Certificato",dataType:"text",align:"left",width:130,disableSort:!0,getFilterValue:e=>F(e),renderCell:e=>M(e)}];return j&&e.unshift({field:"selection",headerName:"",disableFilter:!0,disableSort:!0,width:50,align:"left",renderHeader:()=>(0,t.jsx)(h.S,{checked:n.length===g.length&&g.length>0,onCheckedChange:y}),renderCell:e=>(0,t.jsx)(h.S,{checked:n.includes(e.id_cavo),onCheckedChange:a=>C(e.id_cavo,a),onClick:e=>e.stopPropagation()})}),e},[j,n,g,y,C]),I=e=>{let a=e.metri_posati||e.metratura_reale||0,i=e.stato_installazione||"Da installare",t=e.comanda_posa,s=e.comanda_partenza,l=e.comanda_arrivo,n=e.comanda_certificazione,r=t||s||l||n;return r&&"In corso"===i?"In corso (".concat(r,")"):"Installato"===i||a>0?"Installato":i},k=e=>{let a=e.metri_posati>0||e.metratura_reale>0,i=e.collegamento||e.collegamenti||0;if(!a)return"Non disponibile";switch(i){case 0:return"Collega";case 1:case 2:return"Completa collegamento";case 3:return"Scollega";default:return"Gestisci collegamenti"}},F=e=>{let a=e.metri_posati>0||e.metratura_reale>0,i=!0===e.certificato||"SI"===e.certificato||"CERTIFICATO"===e.certificato;return a?i?"Genera PDF":"Certifica":"Non disponibile"},R=e=>{let a=e.id_bobina;if(!a||"N/A"===a)return(0,t.jsx)("span",{className:"inline-flex items-center px-2 py-1 text-xs font-medium ".concat(p.mU.NEUTRAL.text_light),children:"-"});if("BOBINA_VUOTA"===a||"VUOTA"===a)return(0,t.jsxs)("button",{className:"cavi-table-button bobina",onClick:a=>{a.stopPropagation(),null==o||o(e,"modify_reel")},title:"Bobina Vuota - Clicca per modificare",children:[(0,t.jsx)("span",{children:"Vuota"}),(0,t.jsx)(ea.A,{className:"icon w-3 h-3 opacity-70"})]});let i=a,s=a.match(/_B(.+)$/);return s||(s=a.match(/_b(.+)$/))||(s=a.match(/c\d+_[bB](\d+)$/))?i=s[1]:(s=a.match(/(\d+)$/))&&(i=s[1]),(0,t.jsxs)("button",{className:"cavi-table-button bobina",onClick:a=>{a.stopPropagation(),null==o||o(e,"modify_reel")},title:"Bobina ".concat(i," - Clicca per modificare"),children:[(0,t.jsx)("span",{children:i}),(0,t.jsx)(ea.A,{className:"icon w-3 h-3 opacity-70"})]})},L=e=>{let a=(e.metri_posati||e.metratura_reale||0)>0,i=e.stato_installazione||"Da installare",s=e.comanda_posa,l=e.comanda_partenza,n=e.comanda_arrivo,r=e.comanda_certificazione,c=s||l||n||r,d=i,m=(0,p.Tr)(i);c&&"In corso"===i&&(d=c,m=(0,p.Tr)("IN_CORSO")),a&&"Installato"!==i&&(d="Installato",m=(0,p.Tr)("INSTALLATO"));let x=e=>{switch(e.toLowerCase()){case"installato":return(0,t.jsx)(ei.A,{className:"icon w-3 h-3"});case"in corso":return(0,t.jsx)(et.A,{className:"icon w-3 h-3"});case"da installare":return(0,t.jsx)(es.A,{className:"icon w-3 h-3"});default:return c?(0,t.jsx)(H.A,{className:"icon w-3 h-3"}):(0,t.jsx)(es.A,{className:"icon w-3 h-3"})}};return"da installare"!==i.toLowerCase()||a?(0,t.jsxs)("span",{className:"inline-flex items-center gap-1 px-2 py-1 rounded text-xs font-medium ".concat(m.text," ").concat(m.bg," ").concat(m.border),title:c?"Comanda attiva: ".concat(c):"Stato: ".concat(d),children:[x(d),(0,t.jsx)("span",{children:d})]}):(0,t.jsxs)("button",{className:"cavi-table-button stato",onClick:a=>{a.stopPropagation(),null==o||o(e,"insert_meters")},title:"Clicca per inserire metri posati",children:[x(d),(0,t.jsx)("span",{children:d})]})},D=e=>{let a=e.metri_posati>0||e.metratura_reale>0,i=e.collegamento||e.collegamenti||0;(0,p.Nj)();let s=(0,p.NM)();if(!a)return(0,t.jsxs)("span",{className:s.text,title:"Collegamento disponibile solo per cavi installati",children:[(0,t.jsx)(el.A,{className:"w-3 h-3"}),(0,t.jsx)("span",{children:"Non disponibile"})]});let l=(a,i,s)=>(0,t.jsxs)("button",{className:"cavi-table-button collegamenti",onClick:a=>{a.stopPropagation(),null==o||o(e,i)},title:"Clicca per ".concat(a.toLowerCase()),children:[(0,t.jsx)("span",{className:"icon text-sm mr-1",children:s}),(0,t.jsx)("span",{children:a})]});switch(i){case 0:return l("Collega","connect_cable","⚪⚪");case 1:return l("Completa collegamento","connect_arrival","\uD83D\uDFE2⚪");case 2:return l("Completa collegamento","connect_departure","⚪\uD83D\uDFE2");case 3:return l("Scollega","disconnect_cable","\uD83D\uDFE2\uD83D\uDFE2");default:return l("Gestisci collegamenti","manage_connections","⚙️")}},M=e=>{let a=e.metri_posati>0||e.metratura_reale>0,i=!0===e.certificato||"SI"===e.certificato||"CERTIFICATO"===e.certificato,s=(0,p.NM)();return a?i?(0,t.jsxs)("button",{className:"cavi-table-button certificazioni",onClick:a=>{a.stopPropagation(),null==o||o(e,"generate_pdf")},title:"Certificato - Clicca per generare PDF",children:[(0,t.jsx)(ei.A,{className:"icon w-3 h-3"}),(0,t.jsx)("span",{children:"PDF"})]}):(0,t.jsxs)("button",{className:"cavi-table-button certificazioni",onClick:a=>{a.stopPropagation(),null==o||o(e,"create_certificate")},title:"Clicca per certificare il cavo",children:[(0,t.jsx)(q.A,{className:"icon w-3 h-3"}),(0,t.jsx)("span",{children:"Certifica"})]}):(0,t.jsxs)("span",{className:s.text,title:"Certificazione disponibile solo per cavi installati",children:[(0,t.jsx)(el.A,{className:"w-3 h-3"}),(0,t.jsx)("span",{children:"Non disponibile"})]})};return(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(B,{cavi:a,onFilteredDataChange:e=>{m(e)},loading:i,selectionEnabled:j,onSelectionToggle:()=>{f(!j)},selectedCount:n.length,totalCount:d.length}),(0,t.jsx)(O,{data:d,columns:T,loading:i,emptyMessage:"Nessun cavo disponibile",onFilteredDataChange:e=>{b(e)},renderRow:(e,a)=>{let i=n.includes(e.id_cavo);return(0,t.jsx)(v.Hj,{className:"\n          ".concat(i?"bg-blue-50 border-blue-200":"bg-white","\n          hover:bg-blue-50 hover:border-blue-200 hover:shadow-sm\n          cursor-pointer border-b border-gray-200\n          transition-all duration-200 ease-in-out\n          ").concat(i?"ring-1 ring-blue-300":"","\n        "),onClick:()=>j&&C(e.id_cavo,!i),onContextMenu:a=>{a.preventDefault(),_({isOpen:!0,position:{x:a.clientX,y:a.clientY},cavo:e})},children:T.map(a=>(0,t.jsx)(v.nA,{className:"\n              py-2 px-2 text-sm text-left\n              ".concat(i?"text-blue-900":"text-gray-900","\n              transition-colors duration-200\n            "),style:{width:a.width,...a.cellStyle},onClick:e=>{["stato_installazione","collegamenti","certificato"].includes(a.field)&&e.stopPropagation()},children:a.renderCell?a.renderCell(e):e[a.field]||(0,t.jsx)("span",{className:"text-gray-400",children:"-"})},a.field))},e.id_cavo)}}),j&&n.length>0&&(0,t.jsx)("div",{className:"sticky bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-10",children:(0,t.jsxs)("div",{className:"flex items-center justify-between p-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsxs)(u.E,{variant:"secondary",className:"bg-mariner-100 text-mariner-800",children:[n.length," cavi selezionati"]}),(0,t.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>y(!1),className:"text-xs",children:"Deseleziona tutto"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(x.$,{variant:"outline",size:"sm",onClick:()=>z(),className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"\uD83D\uDCCA"}),(0,t.jsx)("span",{children:"Esporta"})]}),(0,t.jsxs)(x.$,{variant:"outline",size:"sm",onClick:()=>A(),className:"flex items-center space-x-1 border-blue-200 text-blue-700 hover:bg-blue-50",children:[(0,t.jsx)(q.A,{className:"w-3 h-3"}),(0,t.jsx)("span",{children:"Certifica"})]}),(0,t.jsxs)(x.$,{variant:"outline",size:"sm",onClick:()=>w(),className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"\uD83D\uDD04"}),(0,t.jsx)("span",{children:"Cambia Stato"})]}),(0,t.jsxs)(x.$,{variant:"outline",size:"sm",onClick:()=>S(),className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"\uD83D\uDCCB"}),(0,t.jsx)("span",{children:"Assegna Comanda"})]}),(0,t.jsxs)(x.$,{variant:"destructive",size:"sm",onClick:()=>E(),className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"\uD83D\uDDD1️"}),(0,t.jsx)("span",{children:"Elimina"})]})]})]})}),(0,t.jsx)(ee,{isOpen:N.isOpen,position:N.position,cavo:N.cavo,isSelected:!!N.cavo&&n.includes(N.cavo.id_cavo),hasMultipleSelection:n.length>1,totalSelectedCount:n.length,onAction:(e,a)=>{null==c||c(e,a)},onClose:()=>_({isOpen:!1,position:{x:0,y:0},cavo:null})})]})}var er=i(72713),eo=i(3493),ec=i(1243),ed=i(71539);let em=e=>{let{content:a,children:i,position:l="auto",delay:n=500,className:r="",disabled:o=!1,maxWidth:c=250}=e,[d,m]=(0,s.useState)(!1),[x,u]=(0,s.useState)(null),h=(0,s.useRef)(null),p=(0,s.useRef)(null),v=(0,s.useRef)(null),g=()=>{if(!h.current)return null;let e=h.current.getBoundingClientRect(),a=window.innerWidth,i=window.innerHeight,t=l,s=0,n=0;if("auto"===l){let s=e.top,l=i-e.bottom;e.left;let n=a-e.right;t=s>40&&s>l?"top":l>40?"bottom":n>c?"right":"left"}switch(t){case"top":s=e.top-40-8,n=e.left+e.width/2-c/2;break;case"bottom":s=e.bottom+8,n=e.left+e.width/2-c/2;break;case"left":s=e.top+e.height/2-20,n=e.left-c-8;break;case"right":s=e.top+e.height/2-20,n=e.right+8}return n=Math.max(8,Math.min(n,a-c-8)),{top:s=Math.max(8,Math.min(s,i-40-8)),left:n,position:t}},b=()=>{o||(v.current&&clearTimeout(v.current),v.current=setTimeout(()=>{let e=g();e&&(u(e),m(!0))},n))},j=()=>{v.current&&(clearTimeout(v.current),v.current=null),m(!1),u(null)};(0,s.useEffect)(()=>()=>{v.current&&clearTimeout(v.current)},[]);let f=d&&x?(0,t.jsxs)("div",{ref:p,className:"fixed z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-md shadow-lg pointer-events-none transition-opacity duration-200 ".concat(r),style:{top:x.top,left:x.left,maxWidth:c,wordWrap:"break-word",whiteSpace:"normal"},role:"tooltip","aria-hidden":!d,children:[a,(0,t.jsx)("div",{className:(e=>{let a="absolute w-0 h-0 border-solid";switch(e){case"top":return"".concat(a," top-full left-1/2 transform -translate-x-1/2 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-gray-900");case"bottom":return"".concat(a," bottom-full left-1/2 transform -translate-x-1/2 border-l-4 border-r-4 border-b-4 border-l-transparent border-r-transparent border-b-gray-900");case"left":return"".concat(a," left-full top-1/2 transform -translate-y-1/2 border-t-4 border-b-4 border-l-4 border-t-transparent border-b-transparent border-l-gray-900");case"right":return"".concat(a," right-full top-1/2 transform -translate-y-1/2 border-t-4 border-b-4 border-r-4 border-t-transparent border-b-transparent border-r-gray-900");default:return a}})(x.position)})]}):null;return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{ref:h,onMouseEnter:b,onMouseLeave:j,onFocus:b,onBlur:j,className:"inline-block","aria-describedby":d?"tooltip":void 0,children:i}),"undefined"!=typeof document&&f&&(0,P.createPortal)(f,document.body)]})},ex=e=>{let{type:a,count:i,percentage:s,children:l}=e;return(0,t.jsx)(em,{content:(()=>{let e="".concat(i," cavi"),t=void 0!==s?" (".concat(s.toFixed(1),"%)"):"";switch(a){case"total":return"Totale cavi nel progetto: ".concat(e);case"installed":return"Cavi fisicamente installati: ".concat(e).concat(t);case"in_progress":return"Cavi in corso di installazione: ".concat(e).concat(t);case"to_install":return"Cavi ancora da installare: ".concat(e).concat(t);case"connected":return"Cavi completamente collegati: ".concat(e).concat(t);case"certified":return"Cavi certificati e collaudati: ".concat(e).concat(t);default:return e}})(),delay:200,position:"bottom",children:l})};function eu(e){let{cavi:a,filteredCavi:i,className:l,revisioneCorrente:r}=e,o=(0,s.useMemo)(()=>{let e=a.length,t=i.length,s=i.filter(e=>"Installato"===e.stato_installazione||e.metri_posati&&e.metri_posati>0||e.metratura_reale&&e.metratura_reale>0).length,l=i.filter(e=>"In corso"===e.stato_installazione).length,n=i.filter(e=>3===(e.collegamento||e.collegamenti||0)).length,r=i.filter(e=>{let a=e.collegamento||e.collegamenti||0;return 1===a||2===a}).length,o=i.filter(e=>0===(e.collegamento||e.collegamenti||0)&&(e.metri_posati>0||e.metratura_reale>0)).length,c=i.filter(e=>!0===e.certificato||"SI"===e.certificato||"CERTIFICATO"===e.certificato).length,d=i.reduce((e,a)=>e+(a.metri_teorici||0),0),m=i.reduce((e,a)=>e+(a.metri_posati||a.metratura_reale||0),0),x=0===t?0:Math.round(100*(((s-n)*2+(n-c)*3.5+4*c)/(4*t)*100))/100;return{totalCavi:e,filteredCount:t,installati:s,inCorso:l,daInstallare:t-s-l,collegati:n,parzialmenteCollegati:r,nonCollegati:o,certificati:c,metriTotali:d,metriInstallati:m,percentualeInstallazione:x}},[a,i]);return(0,t.jsx)(n.Zp,{className:l,children:(0,t.jsxs)(n.Wu,{className:"p-1.5",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-1.5",children:[(0,t.jsx)(er.A,{className:"h-3.5 w-3.5 text-mariner-600"}),(0,t.jsx)("span",{className:"text-xs font-semibold text-mariner-900",children:"Statistiche Cavi"})]}),(0,t.jsx)("div",{className:"flex items-center space-x-1",children:r&&(0,t.jsxs)(u.E,{variant:"outline",className:"text-xs font-medium py-0 px-1.5 h-5",children:["Rev. ",r]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-2",children:[(0,t.jsx)(ex,{type:"total",count:o.totalCavi,children:(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-mariner-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)(eo.A,{className:"h-3.5 w-3.5 text-mariner-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-mariner-900 text-sm",children:o.filteredCount}),(0,t.jsxs)("div",{className:"text-xs text-mariner-600",children:["di ",o.totalCavi," cavi"]})]})]})}),(0,t.jsx)(ex,{type:"installed",count:o.installati,percentage:o.installati/o.filteredCount*100,children:(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-green-50 px-1.5 py-1 rounded-lg","aria-label":"Cavi installati: ".concat(o.installati," cavi"),children:[(0,t.jsx)(ei.A,{className:"h-3.5 w-3.5 text-green-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-green-700 text-sm",children:o.installati}),(0,t.jsx)("div",{className:"text-xs text-green-600",children:"installati"})]})]})}),(0,t.jsx)(ex,{type:"in_progress",count:o.inCorso,percentage:o.inCorso/o.filteredCount*100,children:(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-yellow-50 px-1.5 py-1 rounded-lg","aria-label":"Cavi in corso: ".concat(o.inCorso," cavi"),children:[(0,t.jsx)(et.A,{className:"h-3.5 w-3.5 text-yellow-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-yellow-700 text-sm",children:o.inCorso}),(0,t.jsx)("div",{className:"text-xs text-yellow-600",children:"in corso"})]})]})}),(0,t.jsx)(ex,{type:"to_install",count:o.daInstallare,percentage:o.daInstallare/o.filteredCount*100,children:(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-gray-50 px-1.5 py-1 rounded-lg","aria-label":"Cavi da installare: ".concat(o.daInstallare," cavi"),children:[(0,t.jsx)(ec.A,{className:"h-3.5 w-3.5 text-gray-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-gray-700 text-sm",children:o.daInstallare}),(0,t.jsx)("div",{className:"text-xs text-gray-600",children:"da installare"})]})]})}),(0,t.jsx)(ex,{type:"connected",count:o.collegati,percentage:o.collegati/o.filteredCount*100,children:(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-blue-50 px-1.5 py-1 rounded-lg","aria-label":"Cavi collegati: ".concat(o.collegati," cavi"),children:[(0,t.jsx)(ed.A,{className:"h-3.5 w-3.5 text-blue-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-blue-700 text-sm",children:o.collegati}),(0,t.jsx)("div",{className:"text-xs text-blue-600",children:"collegati"})]})]})}),(0,t.jsx)(ex,{type:"certified",count:o.certificati,percentage:o.certificati/o.filteredCount*100,children:(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-purple-50 px-1.5 py-1 rounded-lg","aria-label":"Cavi certificati: ".concat(o.certificati," cavi"),children:[(0,t.jsx)(Z.A,{className:"h-3.5 w-3.5 text-purple-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-purple-700 text-sm",children:o.certificati}),(0,t.jsx)("div",{className:"text-xs text-purple-600",children:"certificati"})]})]})}),(0,t.jsx)(ex,{type:"total",count:o.metriInstallati,children:(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-indigo-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)("div",{className:"h-3.5 w-3.5 flex items-center justify-center",children:(0,t.jsx)("div",{className:"h-2 w-2 bg-indigo-600 rounded-full"})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"font-bold text-indigo-700 text-sm",children:[o.metriInstallati.toLocaleString(),"m"]}),(0,t.jsxs)("div",{className:"text-xs text-indigo-600",children:["di ",o.metriTotali.toLocaleString(),"m"]})]})]})})]}),o.filteredCount>0&&(0,t.jsxs)("div",{className:"mt-2 bg-gray-50 p-2 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex justify-between text-xs font-medium text-gray-700 mb-1",children:[(0,t.jsx)("span",{children:"IAP - Indice Avanzamento Ponderato"}),(0,t.jsxs)("span",{className:"font-bold ".concat(o.percentualeInstallazione>=80?"text-emerald-700":o.percentualeInstallazione>=50?"text-yellow-700":o.percentualeInstallazione>=25?"text-orange-700":"text-amber-700"),children:[o.percentualeInstallazione.toFixed(1),"%"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"h-2 rounded-full transition-all duration-500 ease-in-out ".concat(o.percentualeInstallazione>=80?"bg-gradient-to-r from-emerald-500 to-emerald-600":o.percentualeInstallazione>=50?"bg-gradient-to-r from-yellow-500 to-yellow-600":o.percentualeInstallazione>=25?"bg-gradient-to-r from-orange-500 to-orange-600":"bg-gradient-to-r from-amber-500 to-amber-600"),style:{width:"".concat(Math.min(o.percentualeInstallazione,100),"%")}})}),(0,t.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mt-0.5",children:[(0,t.jsx)("span",{children:"Pesi: Posa(2.0) + Collegamento(1.5) + Certificazione(0.5)"}),(0,t.jsxs)("span",{children:[o.installati,"I + ",o.collegati,"C + ",o.certificati,"Cert"]})]})]})]})})}var eh=i(54165),ep=i(85057),ev=i(71007),eg=i(51154),eb=i(87481);let ej=e=>0===(e=e||0)?{icon:"⚪",text:"Non collegato",color:"text-gray-600"}:1===e||2===e?{icon:"\uD83D\uDFE1",text:"Parzialmente collegato",color:"text-yellow-600"}:3===e?{icon:"\uD83D\uDFE2",text:"Completamente collegato",color:"text-green-600"}:{icon:"❓",text:"Sconosciuto (".concat(e,")"),color:"text-gray-600"},ef=(e,a)=>(e=e||0,"partenza"===a)?1&e?{icon:"\uD83D\uDFE2",text:"Collegato"}:{icon:"⚪",text:"Non collegato"}:2&e?{icon:"\uD83D\uDFE2",text:"Collegato"}:{icon:"⚪",text:"Non collegato"},eN=[{value:"cantiere",label:"Cantiere"},{value:"elettricista",label:"Elettricista"},{value:"tecnico",label:"Tecnico"},{value:"supervisore",label:"Supervisore"}];function e_(e){let{open:a,onClose:i,cavo:l,cantiere:n,onSuccess:c}=e,[d,u]=(0,s.useState)(!1),[h,p]=(0,s.useState)(""),[v,g]=(0,s.useState)(""),{cantiere:j}=(0,o.A)(),{toast:f}=(0,eb.dj)(),N=n||j;(0,s.useEffect)(()=>{a&&(p(""),g(""))},[a]);let _=(null==l?void 0:l.collegamenti)||0,y=(1&_)==1,C=(2&_)==2,z=(()=>{if(0===_)return{title:"Azioni di Collegamento",type:"collegamento",actions:[{id:"partenza",label:"Collega Partenza",icon:"⚡",variant:"default"},{id:"arrivo",label:"Collega Arrivo",icon:"⚡",variant:"default"},{id:"entrambi",label:"Collega Entrambi i Lati",icon:"⚡",variant:"default"}]};{if(3===_)return{title:"Azioni di Scollegamento",type:"scollegamento",actions:[{id:"scollega-partenza",label:"Scollega Partenza",icon:"⛔",variant:"destructive"},{id:"scollega-arrivo",label:"Scollega Arrivo",icon:"⛔",variant:"destructive"},{id:"scollega-entrambi",label:"Scollega Entrambi i Lati",icon:"⛔",variant:"destructive"}]};let e=[];return y?e.push({id:"scollega-partenza",label:"Scollega Partenza",icon:"⛔",variant:"destructive"}):e.push({id:"partenza",label:"Collega Partenza",icon:"⚡",variant:"default"}),C?e.push({id:"scollega-arrivo",label:"Scollega Arrivo",icon:"⛔",variant:"destructive"}):e.push({id:"arrivo",label:"Collega Arrivo",icon:"⚡",variant:"default"}),{title:"Azioni Disponibili",type:"misto",actions:e}}})(),w=async e=>{if(!v&&!e.startsWith("scollega"))return void p("Seleziona un responsabile per abilitare le azioni di collegamento");if(!l||!N){console.error("\uD83D\uDD0C CollegamentiDialogSimple: Mancano dati necessari",{cavo:l,cantiere:N}),p("Errore: dati del cavo o cantiere mancanti. Ricarica la pagina.");return}if(!localStorage.getItem("token")){p("Utente non autenticato. Effettua il login."),f({title:"Errore autenticazione",description:"Utente non autenticato. Effettua il login.",variant:"destructive"});return}u(!0),p("");try{switch(e){case"partenza":await m.At.collegaCavo(N.id_cantiere,l.id_cavo,"partenza",v),f({title:"Successo",description:"Lato partenza collegato con successo!"});break;case"arrivo":await m.At.collegaCavo(N.id_cantiere,l.id_cavo,"arrivo",v),f({title:"Successo",description:"Lato arrivo collegato con successo!"});break;case"entrambi":let a=[];y||(await m.At.collegaCavo(N.id_cantiere,l.id_cavo,"partenza",v),a.push("Lato partenza")),C||(await m.At.collegaCavo(N.id_cantiere,l.id_cavo,"arrivo",v),a.push("Lato arrivo")),a.length>0&&f({title:"Successo",description:"".concat(a.join(" e ")," collegati con successo!")});break;case"scollega-partenza":await m.At.scollegaCavo(N.id_cantiere,l.id_cavo,"partenza"),f({title:"Successo",description:"Lato partenza scollegato con successo!"});break;case"scollega-arrivo":await m.At.scollegaCavo(N.id_cantiere,l.id_cavo,"arrivo"),f({title:"Successo",description:"Lato arrivo scollegato con successo!"});break;case"scollega-entrambi":await m.At.scollegaCavo(N.id_cantiere,l.id_cavo),f({title:"Successo",description:"Entrambi i lati scollegati con successo!"});break;default:throw Error("Azione non riconosciuta: ".concat(e))}null==c||c(),i()}catch(i){var a,t;console.error("Errore collegamenti:",i);let e=(null==i||null==(t=i.response)||null==(a=t.data)?void 0:a.detail)||(null==i?void 0:i.message)||"Errore durante l'operazione";p(e),f({title:"Errore",description:e,variant:"destructive"})}finally{u(!1)}};return(0,t.jsx)(t.Fragment,{children:(0,t.jsx)(eh.lG,{open:a,onOpenChange:i,children:(0,t.jsxs)(eh.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(eh.c7,{children:[(0,t.jsxs)(eh.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(ed.A,{className:"h-5 w-5 text-blue-600"}),"Gestione Collegamenti - Cavo: ",l.id_cavo]}),(0,t.jsxs)(eh.rr,{children:["Gestisci i collegamenti del cavo ",l.id_cavo]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 font-medium text-sm text-gray-700",children:[(0,t.jsx)(Z.A,{className:"h-4 w-4"}),"Informazioni Cavo"]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Partenza:"}),(0,t.jsx)("span",{className:"font-medium text-gray-900",children:l.ubicazione_partenza||"N/A"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Arrivo:"}),(0,t.jsx)("span",{className:"font-medium text-gray-900",children:l.ubicazione_arrivo||"N/A"})]})]})]}),(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 space-y-3",children:[(0,t.jsx)("div",{className:"font-medium text-sm text-gray-700",children:"Stato Attuale Collegamenti"}),(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,t.jsx)("span",{className:"text-2xl",children:ej(_).icon}),(0,t.jsx)("span",{className:"font-medium ".concat(ej(_).color),children:ej(_).text})]}),(0,t.jsxs)("div",{className:"space-y-2 text-sm border-t pt-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"text-lg",children:ef(_,"partenza").icon}),(0,t.jsx)("span",{className:"text-gray-600",children:"Lato Partenza:"}),(0,t.jsx)("span",{className:"font-medium",children:ef(_,"partenza").text})]}),y&&l.responsabile_partenza&&(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["(Resp: ",l.responsabile_partenza,")"]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"text-lg",children:ef(_,"arrivo").icon}),(0,t.jsx)("span",{className:"text-gray-600",children:"Lato Arrivo:"}),(0,t.jsx)("span",{className:"font-medium",children:ef(_,"arrivo").text})]}),C&&l.responsabile_arrivo&&(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["(Resp: ",l.responsabile_arrivo,")"]})]})]})]}),h&&(0,t.jsxs)(r.Fc,{variant:"destructive",children:[(0,t.jsx)(ec.A,{className:"h-4 w-4"}),(0,t.jsx)(r.TN,{children:h})]}),(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 space-y-4",children:[(0,t.jsx)("div",{className:"font-medium text-sm text-gray-700",children:z.title}),"scollegamento"!==z.type&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(ep.J,{htmlFor:"responsabile",className:"flex items-center gap-2 text-sm",children:[(0,t.jsx)(ev.A,{className:"h-4 w-4"}),"Seleziona responsabile..."]}),(0,t.jsxs)(b.l6,{value:v,onValueChange:g,children:[(0,t.jsx)(b.bq,{className:"w-full",children:(0,t.jsx)(b.yv,{placeholder:"Seleziona responsabile..."})}),(0,t.jsx)(b.gC,{children:eN.map(e=>(0,t.jsx)(b.eb,{value:e.value,children:e.label},e.value))})]}),!v&&"scollegamento"!==z.type&&(0,t.jsxs)("div",{className:"flex items-center gap-2 text-xs text-amber-600",children:[(0,t.jsx)(ec.A,{className:"h-3 w-3"}),"Seleziona un responsabile per abilitare le azioni"]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{className:"text-sm text-gray-600",children:"Azioni Disponibili:"}),(0,t.jsx)("div",{className:"grid grid-cols-1 gap-2",children:z.actions.map(e=>(0,t.jsxs)(x.$,{onClick:()=>w(e.id),disabled:d||!v&&!e.id.startsWith("scollega"),variant:e.variant,className:"w-full justify-start",children:[d?(0,t.jsx)(eg.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,t.jsx)("span",{className:"mr-2",children:e.icon}),e.label]},e.id))})]})]})]}),(0,t.jsxs)(eh.Es,{className:"gap-2",children:[(0,t.jsx)(x.$,{variant:"outline",onClick:i,disabled:d,children:"Annulla"}),(0,t.jsxs)(x.$,{onClick:i,disabled:d,className:"bg-green-600 hover:bg-green-700 text-white",children:[(0,t.jsx)(ei.A,{className:"mr-2 h-4 w-4"}),"Chiudi"]})]})]})})})}var ey=i(94788),eC=i(40693);let ez=e=>{let{icon:a,title:i,cableId:s,description:l}=e;return(0,t.jsxs)(eh.c7,{children:[(0,t.jsxs)(eh.L3,{className:"flex items-center gap-2",children:[a,(0,t.jsxs)("span",{className:"flex items-center gap-2",children:[i,(0,t.jsx)("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 rounded-md text-sm font-mono font-semibold",children:s})]})]}),l&&(0,t.jsx)(eh.rr,{className:"text-sm text-muted-foreground",children:l})]})},ew=e=>{let{children:a,className:i="sm:max-w-md",onKeyDown:s,ariaLabelledBy:l,ariaDescribedBy:n}=e;return(0,t.jsx)(eh.Cf,{className:i,onKeyDown:s,"aria-labelledby":l,"aria-describedby":n,onPointerDownOutside:e=>e.preventDefault(),onEscapeKeyDown:e=>{s&&s(e)},children:a})},eA=e=>{let{open:a,onClose:i,cavo:l,onConfirm:n}=e,[o,c]=(0,s.useState)(!1),[d,m]=(0,s.useState)(!1),u=async()=>{if(l){c(!0);try{await n(l.id_cavo),i(),m(!1)}catch(e){console.error("Error disconnecting cable:",e)}finally{c(!1)}}},h=()=>{m(!1),i()};return l?(0,t.jsx)(eh.lG,{open:a,onOpenChange:h,children:(0,t.jsxs)(ew,{className:"sm:max-w-md",onKeyDown:e=>{"Escape"===e.key&&h()},ariaLabelledBy:"disconnect-modal-title",ariaDescribedBy:"disconnect-modal-description",children:[(0,t.jsx)(ez,{icon:(0,t.jsx)(ed.A,{className:"h-5 w-5 text-orange-500"}),title:"Gestione Collegamenti",cableId:l.id_cavo,description:"Gestisci le connessioni del cavo selezionato"}),d?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"py-4 text-center",children:[(0,t.jsx)("div",{className:"mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4",children:(0,t.jsx)(ec.A,{className:"h-6 w-6 text-red-600"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Conferma Scollegamento"}),(0,t.jsxs)("p",{className:"text-sm text-gray-600 mb-4",children:["Sei veramente sicuro di voler scollegare completamente il cavo ",(0,t.jsx)("strong",{children:l.id_cavo}),"?"]}),(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,t.jsx)("p",{className:"text-sm text-red-800 font-medium",children:"⚠️ Questa azione \xe8 irreversibile"})})]}),(0,t.jsxs)(eh.Es,{className:"gap-2",children:[(0,t.jsx)(x.$,{variant:"outline",onClick:()=>m(!1),disabled:o,className:"flex-1",children:"No, Annulla"}),(0,t.jsx)(x.$,{variant:"destructive",onClick:u,disabled:o,className:"flex-1",children:o?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(eg.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Scollegando..."]}):"S\xec, Scollega"})]})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"py-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full bg-green-500"}),(0,t.jsx)("div",{className:"w-3 h-3 rounded-full bg-green-500"})]}),(0,t.jsxs)("span",{className:"text-sm font-medium text-green-700",children:["Completamente collegato",(0,t.jsx)(ey.A,{className:"inline h-4 w-4 ml-1 cursor-help",title:"Cavo collegato sia all'origine che alla destinazione"})]})]}),(0,t.jsx)("div",{className:"space-y-3",children:(0,t.jsxs)("div",{children:[(0,t.jsx)(ep.J,{htmlFor:"responsabile-collegamento",className:"text-sm font-medium",children:"Responsabile Collegamento"}),(0,t.jsxs)("select",{id:"responsabile-collegamento",className:"w-full mt-1 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500",defaultValue:"",children:[(0,t.jsx)("option",{value:"",disabled:!0,children:"Seleziona responsabile..."}),(0,t.jsx)("option",{value:"cantiere",children:"Cantiere"}),(0,t.jsx)("option",{value:"tecnico1",children:"Tecnico 1"}),(0,t.jsx)("option",{value:"tecnico2",children:"Tecnico 2"})]})]})})]}),(0,t.jsxs)(r.Fc,{className:"my-4 bg-orange-50 border-orange-200",children:[(0,t.jsx)(ec.A,{className:"h-4 w-4 text-orange-600"}),(0,t.jsxs)(r.TN,{className:"text-orange-800",children:[(0,t.jsx)("strong",{children:"Attenzione:"})," Lo scollegamento rimuover\xe0 tutte le connessioni attive del cavo. Questa azione potrebbe influenzare altri componenti collegati."]})]}),(0,t.jsxs)(eh.Es,{className:"gap-2",children:[(0,t.jsx)(x.$,{variant:"outline",onClick:h,disabled:o,className:"flex-1 hover:bg-gray-50",children:"Annulla"}),(0,t.jsxs)(x.$,{variant:"destructive",onClick:()=>{m(!0)},disabled:o,className:"flex-1 hover:bg-red-600",children:[(0,t.jsx)(ec.A,{className:"mr-2 h-4 w-4"}),"Scollega Completamente"]})]})]})]})}):null},eS=e=>{let{open:a,onClose:i,cavo:l,onGenerate:n}=e,[r,o]=(0,s.useState)(!1),[c,d]=(0,s.useState)({fileName:"",includeTestData:!0,format:"standard",emailRecipient:""}),[m,u]=(0,s.useState)({});(0,s.useEffect)(()=>{l&&a&&(d(e=>({...e,fileName:"Certificato_".concat(l.id_cavo,"_").concat(new Date().toISOString().split("T")[0],".pdf")})),u({}))},[l,a]);let p=()=>{let e={};return c.fileName.trim()?/^[a-zA-Z0-9_\-\s]+\.pdf$/i.test(c.fileName)||(e.fileName="Il nome del file deve terminare con .pdf e contenere solo caratteri validi"):e.fileName="Il nome del file \xe8 obbligatorio",c.emailRecipient&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(c.emailRecipient)&&(e.emailRecipient="Inserisci un indirizzo email valido"),u(e),0===Object.keys(e).length},v=async()=>{if(l&&p()){o(!0);try{await n(l.id_cavo,c),i()}catch(e){console.error("Error generating PDF:",e)}finally{o(!1)}}},b=c.fileName.trim()&&0===Object.keys(m).length;return l?(0,t.jsx)(eh.lG,{open:a,onOpenChange:i,children:(0,t.jsxs)(ew,{className:"sm:max-w-lg",onKeyDown:e=>{"Escape"===e.key&&i()},ariaLabelledBy:"pdf-modal-title",children:[(0,t.jsx)(ez,{icon:(0,t.jsx)(Y.A,{className:"h-5 w-5 text-blue-500"}),title:"Genera Certificato",cableId:l.id_cavo,description:"Configura le opzioni per la generazione del certificato PDF"}),(0,t.jsxs)("div",{className:"space-y-4 py-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"fileName",className:"text-sm font-medium",children:"Nome File *"}),(0,t.jsx)(g.p,{id:"fileName",value:c.fileName,onChange:e=>{d(a=>({...a,fileName:e.target.value})),m.fileName&&u(e=>({...e,fileName:""}))},onBlur:p,placeholder:"Certificato_C001_2025-06-29.pdf",className:m.fileName?"border-red-500 focus:ring-red-500":""}),m.fileName&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(es.A,{className:"h-3 w-3"}),m.fileName]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(ep.J,{className:"text-sm font-medium",children:"Formato Certificato"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 border rounded-md hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("input",{type:"radio",name:"format",value:"standard",checked:"standard"===c.format,onChange:e=>d(a=>({...a,format:e.target.value})),className:"text-blue-600 focus:ring-blue-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"Standard"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Certificato con informazioni essenziali"})]})]}),(0,t.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 border rounded-md hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("input",{type:"radio",name:"format",value:"detailed",checked:"detailed"===c.format,onChange:e=>d(a=>({...a,format:e.target.value})),className:"text-blue-600 focus:ring-blue-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"Dettagliato"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Certificato con tutti i dati tecnici"})]})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3 p-2 border rounded-md",children:[(0,t.jsx)(h.S,{id:"includeTestData",checked:c.includeTestData,onCheckedChange:e=>d(a=>({...a,includeTestData:e}))}),(0,t.jsxs)("div",{children:[(0,t.jsx)(ep.J,{htmlFor:"includeTestData",className:"text-sm font-medium cursor-pointer",children:"Includi Dati di Collaudo"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Aggiunge i risultati dei test al certificato"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"emailRecipient",className:"text-sm font-medium",children:"Email Destinatario (Opzionale)"}),(0,t.jsx)(g.p,{id:"emailRecipient",type:"email",value:c.emailRecipient,onChange:e=>{d(a=>({...a,emailRecipient:e.target.value})),m.emailRecipient&&u(e=>({...e,emailRecipient:""}))},onBlur:p,placeholder:"<EMAIL>",className:m.emailRecipient?"border-red-500 focus:ring-red-500":""}),m.emailRecipient&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(es.A,{className:"h-3 w-3"}),m.emailRecipient]})]})]}),(0,t.jsxs)(eh.Es,{className:"gap-2",children:[(0,t.jsx)(x.$,{variant:"outline",onClick:i,disabled:r,className:"flex-1 hover:bg-gray-50",children:"Annulla"}),(0,t.jsx)(x.$,{onClick:v,disabled:r||!b,className:"flex-1 ".concat(b?"hover:bg-blue-600":"opacity-50 cursor-not-allowed"),children:r?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(eg.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Generando..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(Q.A,{className:"mr-2 h-4 w-4"}),"Genera PDF"]})})]})]})}):null},eE=e=>{let{open:a,onClose:i,cavo:s,errorMessage:l,missingRequirements:n=[]}=e,o=n.length>0?n:['Il cavo deve essere nello stato "Installato"',"Il cavo deve essere completamente collegato","Tutti i dati di collaudo devono essere presenti"];return s?(0,t.jsx)(eh.lG,{open:a,onOpenChange:i,children:(0,t.jsxs)(ew,{className:"sm:max-w-md",onKeyDown:e=>{"Escape"===e.key&&i()},ariaLabelledBy:"certification-error-title",children:[(0,t.jsx)(ez,{icon:(0,t.jsx)(es.A,{className:"h-5 w-5 text-red-500"}),title:"Impossibile Certificare Cavo",cableId:s.id_cavo,description:"Il cavo non pu\xf2 essere certificato nel suo stato attuale"}),(0,t.jsxs)("div",{className:"py-4",children:[l&&(0,t.jsxs)(r.Fc,{className:"mb-4 bg-red-50 border-red-200",children:[(0,t.jsx)(es.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)(r.TN,{className:"text-red-800",children:l})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"text-sm font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,t.jsx)(ec.A,{className:"h-4 w-4 text-amber-500"}),"Requisiti mancanti:"]}),(0,t.jsx)("ul",{className:"space-y-3",children:o.map((e,a)=>(0,t.jsxs)("li",{className:"flex items-start gap-3 p-2 bg-red-50 border border-red-200 rounded-md",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-5 h-5 bg-red-100 rounded-full flex items-center justify-center mt-0.5",children:(0,t.jsx)(A.A,{className:"h-3 w-3 text-red-600"})}),(0,t.jsx)("span",{className:"text-sm text-red-800",children:e})]},a))})]}),(0,t.jsxs)(r.Fc,{className:"bg-blue-50 border-blue-200",children:[(0,t.jsx)(ei.A,{className:"h-4 w-4 text-blue-600"}),(0,t.jsxs)(r.TN,{className:"text-blue-800",children:[(0,t.jsx)("strong",{children:"Prossimi passi:"})," Completa tutti i requisiti sopra elencati per abilitare la certificazione del cavo."]})]})]})]}),(0,t.jsx)(eh.Es,{children:(0,t.jsxs)(x.$,{onClick:i,className:"w-full hover:bg-blue-600 focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)(ei.A,{className:"mr-2 h-4 w-4"}),"Ho Capito"]})})]})}):null},eT=e=>{let{open:a,onClose:i,cavo:s,cantiereId:l=1,onCertify:n}=e;if(console.log("\uD83D\uDE80 CertificationModal render:",{open:a,cavo:null==s?void 0:s.id_cavo}),!s)return null;let r=async e=>{if(e&&s)try{await n(s.id_cavo,e)}catch(e){console.error("Errore certificazione:",e);return}i()};return(0,t.jsx)(eh.lG,{open:a,onOpenChange:i,children:(0,t.jsxs)(eh.Cf,{className:"max-w-[98vw] w-[98vw] h-[98vh] min-w-[800px] p-0 overflow-hidden",children:[(0,t.jsxs)(eh.L3,{className:"sr-only",children:["Certificazione Cavo ",s.id_cavo]}),(0,t.jsx)(eC.A,{cantiereId:l,certificazione:null,strumenti:[],preselectedCavoId:s.id_cavo,onSuccess:r,onCancel:()=>{i()}})]})})},eI=e=>{let{open:a,onClose:i,cavi:l,onCertify:n}=e,[r,o]=(0,s.useState)(!1),[c,d]=(0,s.useState)(!1),[m,u]=(0,s.useState)({responsabile:"",dataCertificazione:new Date().toISOString().split("T")[0],esitoCertificazione:"CONFORME",note:""}),[h,p]=(0,s.useState)({});(0,s.useEffect)(()=>{a&&(u({responsabile:"",dataCertificazione:new Date().toISOString().split("T")[0],esitoCertificazione:"CONFORME",note:""}),p({}),o(!1))},[a]);let v=()=>{let e={};if(m.responsabile.trim()||(e.responsabile="Il responsabile \xe8 obbligatorio"),m.dataCertificazione){let a=new Date(m.dataCertificazione),i=new Date;i.setHours(0,0,0,0),a>i&&(e.dataCertificazione="La data non pu\xf2 essere futura")}else e.dataCertificazione="La data di certificazione \xe8 obbligatoria";return p(e),0===Object.keys(e).length},b=async()=>{if(v()){d(!0);try{let e=l.map(e=>e.id_cavo);await n(e,m),i()}catch(e){console.error("Error bulk certifying cables:",e)}finally{d(!1)}}},j=m.responsabile.trim()&&m.dataCertificazione&&0===Object.keys(h).length;return 0===l.length?null:(0,t.jsx)(eh.lG,{open:a,onOpenChange:i,children:(0,t.jsxs)(ew,{className:"sm:max-w-2xl",onKeyDown:e=>{"Escape"===e.key&&i()},ariaLabelledBy:"bulk-certification-modal-title",children:[(0,t.jsx)(ez,{icon:(0,t.jsx)(q.A,{className:"h-5 w-5 text-blue-500"}),title:"Certificazione Multipla",cableId:"".concat(l.length," cavi selezionati"),description:"Certifica tutti i cavi selezionati con gli stessi parametri"}),(0,t.jsxs)("div",{className:"space-y-4 py-4",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h4",{className:"text-sm font-semibold text-gray-900",children:"Cavi da certificare:"}),(0,t.jsx)("div",{className:"max-h-32 overflow-y-auto border border-gray-200 rounded-md p-3 bg-gray-50",children:(0,t.jsx)("div",{className:"grid grid-cols-4 gap-2 text-xs",children:l.map(e=>(0,t.jsxs)("div",{className:"flex items-center gap-1 p-1 bg-white rounded border",children:[(0,t.jsx)(ei.A,{className:"w-3 h-3 text-green-500"}),(0,t.jsx)("span",{className:"font-medium",children:e.id_cavo})]},e.id_cavo))})})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"responsabile",className:"text-sm font-medium",children:"Responsabile Certificazione *"}),(0,t.jsxs)("select",{id:"responsabile",value:m.responsabile,onChange:e=>{u(a=>({...a,responsabile:e.target.value})),h.responsabile&&p(e=>({...e,responsabile:""}))},className:"w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ".concat(h.responsabile?"border-red-500":"border-gray-300"),children:[(0,t.jsx)("option",{value:"",children:"Seleziona responsabile..."}),(0,t.jsx)("option",{value:"Tecnico 1",children:"Tecnico 1"}),(0,t.jsx)("option",{value:"Tecnico 2",children:"Tecnico 2"}),(0,t.jsx)("option",{value:"Supervisore",children:"Supervisore"}),(0,t.jsx)("option",{value:"Capo Cantiere",children:"Capo Cantiere"})]}),h.responsabile&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:h.responsabile})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"dataCertificazione",className:"text-sm font-medium",children:"Data Certificazione *"}),(0,t.jsx)(g.p,{id:"dataCertificazione",type:"date",value:m.dataCertificazione,onChange:e=>{u(a=>({...a,dataCertificazione:e.target.value})),h.dataCertificazione&&p(e=>({...e,dataCertificazione:""}))},className:h.dataCertificazione?"border-red-500":""}),h.dataCertificazione&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:h.dataCertificazione})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{className:"text-sm font-medium",children:"Esito Certificazione"}),(0,t.jsxs)("select",{value:m.esitoCertificazione,onChange:e=>u(a=>({...a,esitoCertificazione:e.target.value})),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,t.jsx)("option",{value:"CONFORME",children:"CONFORME"}),(0,t.jsx)("option",{value:"NON_CONFORME",children:"NON CONFORME"}),(0,t.jsx)("option",{value:"PARZIALMENTE_CONFORME",children:"PARZIALMENTE CONFORME"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"note",className:"text-sm font-medium",children:"Note (opzionale)"}),(0,t.jsx)("textarea",{id:"note",value:m.note,onChange:e=>u(a=>({...a,note:e.target.value})),placeholder:"Inserisci eventuali note sulla certificazione multipla...",rows:3,className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"})]})]}),(0,t.jsxs)(eh.Es,{className:"gap-2",children:[(0,t.jsx)(x.$,{variant:"outline",onClick:i,disabled:c,className:"hover:bg-gray-50",children:"Annulla"}),(0,t.jsx)(x.$,{onClick:b,disabled:c||!j,className:"".concat(j?"bg-blue-600 hover:bg-blue-700":"opacity-50 cursor-not-allowed bg-gray-400"),children:c?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(eg.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Certificando ",l.length," cavi..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(q.A,{className:"mr-2 h-4 w-4"}),"Certifica ",l.length," Cavi"]})})]})]})})},ek=e=>{let{message:a,visible:i,onClose:l}=e;return((0,s.useEffect)(()=>{if(i){let e=setTimeout(()=>{l()},3e3);return()=>clearTimeout(e)}},[i,l]),i)?(0,t.jsx)("div",{className:"fixed top-4 right-4 z-50 animate-in slide-in-from-top-2",children:(0,t.jsxs)(r.Fc,{className:"bg-green-50 border-green-200 text-green-800 shadow-lg",children:[(0,t.jsx)(ei.A,{className:"h-4 w-4"}),(0,t.jsx)(r.TN,{className:"font-medium",children:a}),(0,t.jsx)(x.$,{variant:"ghost",size:"sm",className:"absolute top-2 right-2 h-6 w-6 p-0 hover:bg-green-100",onClick:l,children:(0,t.jsx)(A.A,{className:"h-3 w-3"})})]})}):null};var eO=i(84333);i(93304);var eF=i(6740);function eR(e){let{open:a,onClose:i,cavo:l,cantiere:n,onSuccess:c,onError:d}=e,{cantiere:u}=(0,o.A)(),h=n||u,[p,v]=(0,s.useState)("assegna_nuova"),[b,j]=(0,s.useState)(""),[f,N]=(0,s.useState)([]),[_,y]=(0,s.useState)(!1),[C,z]=(0,s.useState)(!1),[w,A]=(0,s.useState)(""),[S,E]=(0,s.useState)(""),[T,I]=(0,s.useState)("compatibili");(0,s.useEffect)(()=>{a&&(v("assegna_nuova"),j(""),E(""),I("compatibili"),A(""),(null==h?void 0:h.id_cantiere)&&k())},[a,null==h?void 0:h.id_cantiere]);let k=async()=>{if(!(null==h?void 0:h.id_cantiere))return void A("Cantiere non disponibile");try{y(!0),A(""),console.log("\uD83D\uDD04 ModificaBobinaDialog: Caricamento bobine per cantiere:",h.id_cantiere);let e=await m.Fw.getBobine(h.id_cantiere),a=[];if(Array.isArray(e))a=e;else if(e&&Array.isArray(e.data))a=e.data;else if(e&&e.bobine&&Array.isArray(e.bobine))a=e.bobine;else throw Error("Formato risposta API non valido");let i=a.filter(e=>"Terminata"!==e.stato_bobina&&"Over"!==e.stato_bobina&&e.metri_residui>0);N(i),console.log("✅ ModificaBobinaDialog: Bobine caricate:",i.length),console.log("\uD83D\uDCCB ModificaBobinaDialog: Dettaglio bobine:",i.map(e=>({id:e.id_bobina,tipologia:e.tipologia,sezione:e.sezione,metri_residui:e.metri_residui,stato:e.stato_bobina})))}catch(e){console.error("❌ ModificaBobinaDialog: Errore caricamento bobine:",e),A("Errore nel caricamento delle bobine"),N([])}finally{y(!1)}},O=(()=>{if(!l)return[];let e=f.filter(e=>{let a=e.tipologia===l.tipologia&&e.sezione===l.sezione,i=""===S||e.id_bobina.toLowerCase().includes(S.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(S.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(S.toLowerCase());return a&&i&&e.metri_residui>0});return console.log("\uD83D\uDD0D ModificaBobinaDialog: Filtro compatibili:",{cavoTipologia:l.tipologia,cavoSezione:l.sezione,totaleBobine:f.length,bobineCompatibili:e.length,searchText:S}),e})(),F=l?f.filter(e=>{let a=e.tipologia!==l.tipologia||e.sezione!==l.sezione,i=""===S||e.id_bobina.toLowerCase().includes(S.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(S.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(S.toLowerCase());return a&&i&&e.metri_residui>0}):[],L=()=>{v("assegna_nuova"),j(""),E(""),A(""),i()},D=async()=>{if(console.log("\uD83D\uDD04 ModificaBobinaDialog: Salvataggio:",{selectedOption:p,selectedBobina:b,cavoId:null==l?void 0:l.id_cavo,cantiereId:null==h?void 0:h.id_cantiere}),l)try{if(z(!0),A(""),"assegna_nuova"===p){if(!b)return void d("Selezionare una bobina");let e=await m.At.updateMetriPosati({id_cavo:l.id_cavo,metri_posati:l.metratura_reale||0,id_bobina:b,force_over:!0});e.success?(c("Bobina aggiornata con successo per il cavo ".concat(l.id_cavo)),L()):d(e.message||"Errore durante l'aggiornamento della bobina")}else if("rimuovi_bobina"===p){let e=await m.At.updateMetriPosati({id_cavo:l.id_cavo,metri_posati:l.metratura_reale||0,id_bobina:"BOBINA_VUOTA",force_over:!1});e.success?(c("Bobina rimossa dal cavo ".concat(l.id_cavo)),L()):d(e.message||"Errore durante la rimozione della bobina")}else if("annulla_installazione"===p){let e=await m.At.updateMetriPosati({id_cavo:l.id_cavo,metri_posati:0,id_bobina:"BOBINA_VUOTA",force_over:!1});e.success?(c("Installazione annullata per il cavo ".concat(l.id_cavo)),L()):d(e.message||"Errore durante l'annullamento dell'installazione")}}catch(e){console.error("❌ ModificaBobinaDialog: Errore salvataggio:",e),d("Errore durante il salvataggio")}finally{z(!1)}};return l?(0,t.jsx)(t.Fragment,{children:(0,t.jsx)(eh.lG,{open:a,onOpenChange:L,children:(0,t.jsxs)(eh.Cf,{className:"max-w-4xl max-h-[90vh] flex flex-col",children:[(0,t.jsx)(eh.c7,{children:(0,t.jsxs)(eh.L3,{children:["Modifica Bobina Cavo ",l.id_cavo]})}),(0,t.jsxs)("div",{className:"flex-1 overflow-hidden space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(Z.A,{className:"h-5 w-5 text-blue-600"}),(0,t.jsx)("h3",{className:"font-medium text-gray-900",children:"Cavo Selezionato"})]}),(0,t.jsx)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-200",children:(0,t.jsxs)("div",{className:"text-sm font-medium text-blue-800",children:["Informazioni Cavo / Tipologia: ",l.tipologia||"N/A"," / Da: ",l.ubicazione_partenza||"N/A"," / Formazione: ",l.sezione||"N/A"," / A: ",l.ubicazione_arrivo||"N/A"," / Metri Posati: ",l.metratura_reale||0," m"]})})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h3",{className:"font-medium",children:"Opzioni di modifica"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50",children:[(0,t.jsx)("input",{type:"radio",name:"operazione",value:"assegna_nuova",checked:"assegna_nuova"===p,onChange:e=>v(e.target.value),className:"w-4 h-4 text-blue-600"}),(0,t.jsx)("span",{className:"text-sm",children:"Cambia bobina"})]}),(0,t.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50",children:[(0,t.jsx)("input",{type:"radio",name:"operazione",value:"rimuovi_bobina",checked:"rimuovi_bobina"===p,onChange:e=>v(e.target.value),className:"w-4 h-4 text-blue-600"}),(0,t.jsx)("span",{className:"text-sm",children:"Bobina vuota"})]}),(0,t.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50",children:[(0,t.jsx)("input",{type:"radio",name:"operazione",value:"annulla_installazione",checked:"annulla_installazione"===p,onChange:e=>v(e.target.value),className:"w-4 h-4 text-blue-600"}),(0,t.jsx)("span",{className:"text-sm text-red-600",children:"Annulla posa"})]})]})]}),"assegna_nuova"===p&&(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h3",{className:"font-medium",children:"Seleziona bobina"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(R.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,t.jsx)(g.p,{placeholder:"Cerca bobina per ID, tipologia o numero...",value:S,onChange:e=>E(e.target.value),className:"pl-10"})]}),(0,t.jsxs)("div",{className:"flex space-x-1 border-b",children:[(0,t.jsx)("button",{onClick:()=>I("compatibili"),className:"px-4 py-2 text-sm font-medium border-b-2 transition-colors ".concat("compatibili"===T?"border-green-500 text-green-600 bg-green-50":"border-transparent text-gray-500 hover:text-gray-700"),children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(ei.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:["Bobine Compatibili (",O.length,")"]})]})}),(0,t.jsx)("button",{onClick:()=>I("incompatibili"),className:"px-4 py-2 text-sm font-medium border-b-2 transition-colors ".concat("incompatibili"===T?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700"),children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(es.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:["Bobine Incompatibili (",F.length,")"]})]})})]}),(0,t.jsx)("div",{className:"border rounded-lg h-64 overflow-y-auto",children:_?(0,t.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(eg.A,{className:"h-4 w-4 animate-spin"}),(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Caricamento bobine..."})]})}):(0,t.jsx)("div",{className:"p-2",children:"compatibili"===T?0===O.length?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("div",{className:"text-gray-500 text-sm mb-2",children:"Nessuna bobina compatibile trovata"}),(0,t.jsxs)("div",{className:"text-xs text-gray-400",children:["Cercando bobine con tipologia ",(0,t.jsx)("strong",{children:l.tipologia})," e formazione ",(0,t.jsx)("strong",{children:l.sezione})]})]}):(0,t.jsx)("div",{className:"space-y-2",children:O.map(e=>(0,t.jsx)("div",{onClick:()=>j(e.id_bobina),className:"p-3 rounded-lg cursor-pointer transition-all duration-200 ".concat(b===e.id_bobina?"bg-blue-100 border-2 border-blue-300 shadow-md":"hover:bg-gray-50 border border-gray-200 hover:border-gray-300"),children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,t.jsx)("div",{className:"font-medium text-sm text-gray-900",children:e.id_bobina}),e.stato_bobina&&(0,t.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("Disponibile"===e.stato_bobina?"bg-green-100 text-green-800":"In uso"===e.stato_bobina?"bg-blue-100 text-blue-800":"bg-yellow-100 text-yellow-800"),children:e.stato_bobina})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500 mb-1",children:[(0,t.jsx)("span",{className:"font-medium",children:e.tipologia})," • ",(0,t.jsx)("span",{children:e.sezione})]})]}),(0,t.jsxs)("div",{className:"text-right ml-3",children:[(0,t.jsxs)("div",{className:"text-sm font-medium ".concat(e.metri_residui>0?"text-green-600":"text-gray-500"),children:[e.metri_residui,"m"]}),(0,t.jsx)("div",{className:"text-xs text-gray-400",children:e.metri_residui>0?"disponibili":"esaurita"})]})]})},e.id_bobina))}):0===F.length?(0,t.jsx)("div",{className:"text-center py-8 text-gray-500 text-sm",children:"Nessuna bobina incompatibile trovata"}):(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"p-3 bg-orange-50 border border-orange-200 rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,t.jsx)(es.A,{className:"h-4 w-4 text-orange-600 mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{className:"text-sm text-orange-800",children:[(0,t.jsx)("div",{className:"font-medium mb-1",children:"Bobine Incompatibili"}),(0,t.jsx)("div",{className:"text-xs",children:"Selezionando una bobina incompatibile, le caratteristiche del cavo verranno aggiornate."})]})]})}),F.map(e=>(0,t.jsx)("div",{onClick:()=>j(e.id_bobina),className:"p-3 rounded-lg cursor-pointer transition-all duration-200 ".concat(b===e.id_bobina?"bg-orange-100 border-2 border-orange-300 shadow-md":"hover:bg-gray-50 border border-gray-200 hover:border-gray-300"),children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,t.jsx)("div",{className:"font-medium text-sm text-gray-900",children:e.id_bobina}),(0,t.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800",children:"INCOMPATIBILE"})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500 mb-1",children:[(0,t.jsx)("span",{className:"font-medium",children:e.tipologia})," • ",(0,t.jsx)("span",{children:e.sezione})]})]}),(0,t.jsxs)("div",{className:"text-right ml-3",children:[(0,t.jsxs)("div",{className:"text-sm font-medium ".concat(e.metri_residui>0?"text-orange-600":"text-gray-500"),children:[e.metri_residui,"m"]}),(0,t.jsx)("div",{className:"text-xs text-gray-400",children:e.metri_residui>0?"disponibili":"esaurita"})]})]})},e.id_bobina))]})})})]})]}),w&&(0,t.jsxs)(r.Fc,{variant:"destructive",children:[(0,t.jsx)(es.A,{className:"h-4 w-4"}),(0,t.jsx)(r.TN,{children:w})]}),(0,t.jsxs)(eh.Es,{className:"flex justify-end space-x-2",children:[(0,t.jsx)(x.$,{variant:"outline",onClick:L,disabled:C,children:"Annulla"}),(0,t.jsxs)(x.$,{onClick:D,disabled:C||"assegna_nuova"===p&&!b,children:[C&&(0,t.jsx)(eg.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Salva"]})]})]})})}):null}function eL(e){let{open:a,onClose:i,cavo:l,cantiere:n,onSuccess:c,onError:d}=e,{cantiere:h}=(0,o.A)(),p=n||h,[v,b]=(0,s.useState)({metri_posati:"",id_bobina:""});(0,s.useEffect)(()=>{console.log("\uD83D\uDCCA InserisciMetriDialog: FormData aggiornato:",{hasMetri:!!v.metri_posati,hasBobina:!!v.id_bobina,metri_posati:v.metri_posati,id_bobina:v.id_bobina})},[v]);let[j,f]=(0,s.useState)({}),[N,_]=(0,s.useState)({}),[y,C]=(0,s.useState)(!1),[z,w]=(0,s.useState)([]),[S,E]=(0,s.useState)(!1),[T,I]=(0,s.useState)(""),[k,O]=(0,s.useState)(!1);(0,s.useEffect)(()=>{a&&l&&(p&&L(),b({metri_posati:"0",id_bobina:""}),f({}),_({}),I(""))},[a,l,p]),(0,s.useEffect)(()=>{v.metri_posati&&l?F(parseFloat(v.metri_posati)):(f(e=>({...e,metri_posati:void 0})),_(e=>({...e,metri_posati:void 0})))},[v.metri_posati,l]);let F=e=>{if(!l)return;let a={...j},i={...N};delete a.metri_posati,delete i.metri_posati,e>1.1*(l.metri_teorici||0)?i.metri_posati="Attenzione: i metri posati superano del 10% i metri teorici (".concat(l.metri_teorici,"m)"):e>(l.metri_teorici||0)&&(i.metri_posati="Metratura superiore ai metri teorici"),f(a),_(i)},L=async()=>{if(console.log("\uD83C\uDFAF InserisciMetriDialog: Caricamento bobine:",{cavo:!!l,cantiere:!!p,cavoId:null==l?void 0:l.id_cavo,cantiereId:null==p?void 0:p.id_cantiere}),l&&p)try{E(!0);let e=await m.Fw.getBobine(p.id_cantiere),a=[];if(Array.isArray(e))a=e;else if(e&&Array.isArray(e.data))a=e.data;else if(e&&e.bobine&&Array.isArray(e.bobine))a=e.bobine;else throw Error("Formato risposta API non valido");let i=a.filter(e=>"Terminata"!==e.stato_bobina&&"Over"!==e.stato_bobina&&e.metri_residui>0);if(l){console.log("\uD83D\uDD0D InserisciMetriDialog: Filtro per cavo:",{tipologia:l.tipologia,sezione:l.sezione});let e=i.filter(e=>e.tipologia===l.tipologia&&e.sezione===l.sezione),a=i.filter(e=>e.tipologia!==l.tipologia||e.sezione!==l.sezione);e.sort((e,a)=>a.metri_residui-e.metri_residui),a.sort((e,a)=>a.metri_residui-e.metri_residui);let t=[...e,...a];w(t)}else i.sort((e,a)=>a.metri_residui-e.metri_residui),w(i)}catch(t){var e,a,i;console.error("❌ InserisciMetriDialog: Errore caricamento bobine:",{message:t.message,response:t.response,status:null==(e=t.response)?void 0:e.status,data:null==(a=t.response)?void 0:a.data}),(null==(i=t.response)?void 0:i.status)!==404&&d("Errore nel caricamento delle bobine. Puoi comunque usare BOBINA VUOTA."),w([])}finally{E(!1)}},D=e=>{if(!e||"BOBINA_VUOTA"===e)return"VUOTA";if(e&&e.includes("_B"))return e.split("_B")[1];let a=z.find(a=>a.id_bobina===e);return a&&a.numero_bobina||e},M=l?z.filter(e=>{let a=e.tipologia===l.tipologia&&e.sezione===l.sezione,i=""===T||e.id_bobina.toLowerCase().includes(T.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(T.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(T.toLowerCase());return a&&i&&e.metri_residui>0}):[],B=l?z.filter(e=>{let a=e.tipologia!==l.tipologia||e.sezione!==l.sezione,i=""===T||e.id_bobina.toLowerCase().includes(T.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(T.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(T.toLowerCase());return a&&i&&e.metri_residui>0}):[],U=e=>{b(a=>({...a,id_bobina:e.id_bobina})),f(e=>{let a={...e};return delete a.id_bobina,a})},P=async()=>{if(console.log("\uD83D\uDCBE InserisciMetriDialog: Salvataggio metri:",{cavo:null==l?void 0:l.id_cavo,metri_posati:v.metri_posati,id_bobina:v.id_bobina}),!l)return;if(!v.metri_posati||0>parseFloat(v.metri_posati))return void d("Inserire metri posati validi (≥ 0)");if(!v.id_bobina)return void d("Selezionare una bobina o BOBINA VUOTA");let e=parseFloat(v.metri_posati);if("BOBINA_VUOTA"!==v.id_bobina){let e=z.find(e=>e.id_bobina===v.id_bobina);e&&e.metri_residui}try{if(C(!0),!p)throw Error("Cantiere non selezionato");console.log("\uD83D\uDE80 InserisciMetriDialog: Chiamata API updateMetriPosati:",{cantiere:p.id_cantiere,cavo:l.id_cavo,metri:e,bobina:v.id_bobina,isBobinaVuota:"BOBINA_VUOTA"===v.id_bobina}),await m.At.updateMetriPosati(p.id_cantiere,l.id_cavo,e,v.id_bobina,!0),c("Metri posati aggiornati con successo per il cavo ".concat(l.id_cavo,": ").concat(e,"m")),i()}catch(e){var a,t;d((null==(t=e.response)||null==(a=t.data)?void 0:a.detail)||e.message||"Errore durante il salvataggio dei metri posati")}finally{C(!1)}},$=()=>{y||(b({metri_posati:"",id_bobina:""}),f({}),_({}),I(""),i())};return l?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(eh.lG,{open:a,onOpenChange:$,children:(0,t.jsxs)(eh.Cf,{className:"max-w-7xl h-[90vh] flex flex-col",children:[(0,t.jsxs)(eh.c7,{className:"flex-shrink-0",children:[(0,t.jsxs)(eh.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(eF.A,{className:"h-5 w-5"}),"Inserisci Metri Posati - ",l.id_cavo]}),(0,t.jsx)(eh.rr,{children:"Inserisci i metri effettivamente posati per il cavo e seleziona una bobina o usa BOBINA VUOTA"})]}),(0,t.jsxs)("div",{className:"flex-1 overflow-y-auto space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-4",children:[(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-200",children:[(0,t.jsx)("h3",{className:"font-semibold text-blue-800 mb-3",children:"Informazioni Cavo"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Tipologia:"})," ",l.tipologia||"N/A"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Da:"})," ",l.ubicazione_partenza||"N/A"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Formazione:"})," ",l.sezione||"N/A"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"A:"})," ",l.ubicazione_arrivo||"N/A"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Metri teorici:"})," ",l.metri_teorici||"N/A"," m"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Gi\xe0 posati:"})," ",l.metratura_reale||0," m"]})]})]})}),(0,t.jsx)("div",{className:"lg:col-span-1",children:(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-300 h-full",children:[(0,t.jsx)("h3",{className:"font-semibold text-blue-800 mb-3",children:"Metri da Installare"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"metri",className:"text-sm font-medium",children:"Metri Posati"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(g.p,{id:"metri",type:"number",value:v.metri_posati,onChange:e=>b(a=>({...a,metri_posati:e.target.value})),placeholder:"Inserisci metri posati",disabled:y,step:"0.1",min:"0",className:"text-lg font-bold text-center border-2 border-blue-400 focus:border-blue-600",autoFocus:!0}),(0,t.jsx)("span",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-lg font-bold text-blue-600",children:"m"})]}),j.metri_posati&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:j.metri_posati}),N.metri_posati&&(0,t.jsx)("p",{className:"text-sm text-amber-600",children:N.metri_posati})]})]})})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"font-semibold text-blue-800 text-lg",children:"Selezione Bobina"}),(0,t.jsx)("div",{className:"p-4 bg-gray-50 rounded-lg",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-12 gap-3 items-center",children:[(0,t.jsx)("div",{className:"sm:col-span-5",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(R.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(g.p,{placeholder:"ID, tipologia, formazione...",value:T,onChange:e=>I(e.target.value),className:"pl-10",disabled:y}),T&&(0,t.jsx)(x.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0",onClick:()=>I(""),children:(0,t.jsx)(A.A,{className:"h-4 w-4"})})]})}),(0,t.jsx)("div",{className:"sm:col-span-7",children:(0,t.jsxs)(x.$,{type:"button",variant:"BOBINA_VUOTA"===v.id_bobina?"default":"outline",className:"w-full h-10 font-bold flex items-center justify-center gap-2 ".concat("BOBINA_VUOTA"===v.id_bobina?"bg-green-600 hover:bg-green-700 text-white":"border-blue-400 text-blue-700 hover:bg-blue-50"),onClick:()=>{b(e=>({...e,id_bobina:"BOBINA_VUOTA"})),f(e=>{let a={...e};return delete a.id_bobina,a}),console.log("\uD83D\uDD04 InserisciMetriDialog: Usando BOBINA_VUOTA:",{saving:!1,metri_posati:v.metri_posati,id_bobina:"BOBINA_VUOTA",errorsCount:Object.keys(j).length})},disabled:y,children:["BOBINA_VUOTA"===v.id_bobina&&(0,t.jsx)(ei.A,{className:"h-5 w-5"}),"BOBINA VUOTA"]})})]})}),S?(0,t.jsxs)("div",{className:"flex items-center justify-center p-8",children:[(0,t.jsx)(eg.A,{className:"h-6 w-6 animate-spin mr-2"}),(0,t.jsx)("span",{children:"Caricamento bobine..."})]}):(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"font-medium text-green-700 mb-2 flex items-center gap-2",children:[(0,t.jsx)(ei.A,{className:"h-4 w-4"}),"Bobine Compatibili (",M.length,")"]}),(0,t.jsx)("div",{className:"max-h-72 overflow-y-auto border rounded-lg",children:0===M.length?(0,t.jsx)("div",{className:"p-4 text-center text-gray-500",children:"Nessuna bobina compatibile trovata"}):(0,t.jsx)("div",{className:"divide-y",children:M.map(e=>(0,t.jsx)("div",{className:"p-3 cursor-pointer transition-all border-2 rounded-lg mb-2 ".concat(v.id_bobina===e.id_bobina?"bg-green-100 border-green-500 shadow-md":"border-gray-200 hover:bg-green-50 hover:border-green-300"),onClick:()=>U(e),children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[v.id_bobina===e.id_bobina&&(0,t.jsx)(ei.A,{className:"h-5 w-5 text-green-600 flex-shrink-0"}),(0,t.jsx)("div",{className:"font-bold text-base min-w-fit",children:D(e.id_bobina)}),(0,t.jsxs)("div",{className:"text-sm text-gray-600 truncate",children:[e.tipologia," - ",e.sezione]})]}),(0,t.jsxs)(u.E,{variant:"outline",className:"bg-green-100 text-green-800 border-green-300 font-medium min-w-fit",children:[e.metri_residui,"m"]})]})},e.id_bobina))})})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"font-medium text-amber-700 mb-2 flex items-center gap-2",children:[(0,t.jsx)(ec.A,{className:"h-4 w-4"}),"Bobine Incompatibili (",B.length,")"]}),(0,t.jsx)("div",{className:"max-h-72 overflow-y-auto border rounded-lg",children:0===B.length?(0,t.jsx)("div",{className:"p-4 text-center text-gray-500",children:"Nessuna bobina incompatibile trovata"}):(0,t.jsx)("div",{className:"divide-y",children:B.map(e=>(0,t.jsx)("div",{className:"p-3 cursor-pointer transition-all border-2 rounded-lg mb-2 ".concat(v.id_bobina===e.id_bobina?"bg-amber-100 border-amber-500 shadow-md":"border-gray-200 hover:bg-amber-50 hover:border-amber-300"),onClick:()=>U(e),children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[v.id_bobina===e.id_bobina&&(0,t.jsx)(ei.A,{className:"h-5 w-5 text-amber-600 flex-shrink-0"}),(0,t.jsx)("div",{className:"font-bold text-base min-w-fit",children:D(e.id_bobina)}),(0,t.jsxs)("div",{className:"text-sm text-gray-600 truncate",children:[e.tipologia," - ",e.sezione]})]}),(0,t.jsxs)(u.E,{variant:"outline",className:"bg-amber-100 text-amber-800 border-amber-300 font-medium min-w-fit",children:[e.metri_residui,"m"]})]})},e.id_bobina))})})]})]}),0===z.length&&!S&&(0,t.jsxs)(r.Fc,{className:"border-amber-200 bg-amber-50",children:[(0,t.jsx)(ec.A,{className:"h-4 w-4 text-amber-600"}),(0,t.jsx)(r.TN,{className:"text-amber-800",children:"Non ci sono bobine disponibili. Puoi procedere con BOBINA VUOTA o aggiungere prima una nuova bobina."})]}),j.id_bobina&&(0,t.jsxs)(r.Fc,{variant:"destructive",children:[(0,t.jsx)(es.A,{className:"h-4 w-4"}),(0,t.jsx)(r.TN,{children:j.id_bobina})]})]})]}),(0,t.jsxs)(eh.Es,{className:"flex-shrink-0 border-t pt-4 mt-4 flex justify-between items-center",children:[(0,t.jsx)("div",{children:"installato"===l.stato_installazione&&l.id_bobina&&(0,t.jsx)(x.$,{variant:"outline",onClick:()=>{O(!0)},disabled:y,className:"text-blue-600 border-blue-300 hover:bg-blue-50",children:"Modifica Bobina"})}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(x.$,{variant:"outline",onClick:$,disabled:y,children:"Annulla"}),(0,t.jsxs)(x.$,{onClick:P,disabled:y||!v.metri_posati||0>parseFloat(v.metri_posati)||!v.id_bobina,className:"bg-mariner-600 hover:bg-mariner-700 text-white disabled:bg-gray-400 disabled:text-gray-200",children:[y&&(0,t.jsx)(eg.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Salva"]})]})]})]})}),(0,t.jsx)(eR,{open:k,onClose:()=>O(!1),cavo:l,onSuccess:e=>{c(e),O(!1),i()},onError:d})]}):null}var eD=i(88539),eM=i(25273),eB=i(17580);function eU(e){let{open:a,onClose:i,caviSelezionati:l,tipoComanda:n,onSuccess:c,onError:d}=e,{cantiere:u}=(0,o.A)(),[h,p]=(0,s.useState)({tipo_comanda:n||"POSA",responsabile:"",note:""}),[v,g]=(0,s.useState)([]),[j,f]=(0,s.useState)(!1),[N,_]=(0,s.useState)(!1),[y,C]=(0,s.useState)("");(0,s.useEffect)(()=>{a&&(p({tipo_comanda:n||"POSA",responsabile:"",note:""}),C(""),z())},[a,n]);let z=async()=>{if(u)try{_(!0);let e=await m.AR.getResponsabili(u.id_cantiere);g(e.data)}catch(e){g([])}finally{_(!1)}},w=async()=>{if(u){if(!h.responsabile)return void C("Seleziona un responsabile per la comanda");if(0===l.length)return void C("Seleziona almeno un cavo per la comanda");try{f(!0),C("");let e={tipo_comanda:h.tipo_comanda,responsabile:h.responsabile,note:h.note||null},a=await m.CV.createComandaWithCavi(u.id_cantiere,e,l);c("Comanda ".concat(a.data.codice_comanda," creata con successo per ").concat(l.length," cavi")),i()}catch(i){var e,a;d((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante la creazione della comanda")}finally{f(!1)}}};return(0,t.jsx)(eh.lG,{open:a,onOpenChange:i,children:(0,t.jsxs)(eh.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(eh.c7,{children:[(0,t.jsxs)(eh.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(eM.A,{className:"h-5 w-5"}),"Crea Nuova Comanda"]}),(0,t.jsxs)(eh.rr,{children:["Crea una nuova comanda per ",l.length," cavi selezionati"]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,t.jsxs)(ep.J,{className:"text-sm font-medium",children:["Cavi Selezionati (",l.length,")"]}),(0,t.jsx)("div",{className:"mt-2 max-h-32 overflow-y-auto",children:(0,t.jsxs)("div",{className:"flex flex-wrap gap-1",children:[l.slice(0,10).map(e=>(0,t.jsx)("span",{className:"inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded",children:e},e)),l.length>10&&(0,t.jsxs)("span",{className:"inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded",children:["+",l.length-10," altri..."]})]})})]}),y&&(0,t.jsxs)(r.Fc,{variant:"destructive",children:[(0,t.jsx)(es.A,{className:"h-4 w-4"}),(0,t.jsx)(r.TN,{children:y})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"tipo",children:"Tipo Comanda *"}),(0,t.jsxs)(b.l6,{value:h.tipo_comanda,onValueChange:e=>p(a=>({...a,tipo_comanda:e})),children:[(0,t.jsx)(b.bq,{children:(0,t.jsx)(b.yv,{})}),(0,t.jsxs)(b.gC,{children:[(0,t.jsx)(b.eb,{value:"POSA",children:"\uD83D\uDD27 Posa Cavi"}),(0,t.jsx)(b.eb,{value:"COLLEGAMENTO_PARTENZA",children:"\uD83D\uDD0C Collegamento Partenza"}),(0,t.jsx)(b.eb,{value:"COLLEGAMENTO_ARRIVO",children:"⚡ Collegamento Arrivo"}),(0,t.jsx)(b.eb,{value:"CERTIFICAZIONE",children:"\uD83D\uDCCB Certificazione"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"responsabile",children:"Responsabile *"}),(0,t.jsxs)(b.l6,{value:h.responsabile,onValueChange:e=>p(a=>({...a,responsabile:e})),disabled:N,children:[(0,t.jsx)(b.bq,{children:(0,t.jsx)(b.yv,{placeholder:"Seleziona responsabile..."})}),(0,t.jsx)(b.gC,{children:v.map(e=>(0,t.jsx)(b.eb,{value:e.nome_responsabile,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(eB.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:e.nome_responsabile}),e.numero_telefono&&(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["- ",e.numero_telefono]})]})},e.id))})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"note",children:"Note (opzionale)"}),(0,t.jsx)(eD.T,{id:"note",placeholder:"Inserisci eventuali note per la comanda...",value:h.note,onChange:e=>p(a=>({...a,note:e.target.value})),rows:3})]}),(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)(ep.J,{className:"text-sm font-medium",children:"Riepilogo Comanda"}),(0,t.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Tipo:"})," ",(e=>{switch(e){case"POSA":return"Posa Cavi";case"COLLEGAMENTO_PARTENZA":return"Collegamento Partenza";case"COLLEGAMENTO_ARRIVO":return"Collegamento Arrivo";case"CERTIFICAZIONE":return"Certificazione";default:return e}})(h.tipo_comanda)]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Responsabile:"})," ",h.responsabile||"Non selezionato"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Cavi:"})," ",l.length," selezionati"]}),h.note&&(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Note:"})," ",h.note]})]})]})]}),(0,t.jsxs)(eh.Es,{children:[(0,t.jsx)(x.$,{variant:"outline",onClick:i,disabled:j,children:"Annulla"}),(0,t.jsxs)(x.$,{onClick:w,disabled:j||!h.responsabile||0===l.length,children:[j?(0,t.jsx)(eg.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(eM.A,{className:"h-4 w-4 mr-2"}),"Crea Comanda"]})]})]})})}var eP=i(29869),e$=i(64261);function eV(e){let{open:a,onClose:i,tipo:l,onSuccess:n,onError:c}=e,{cantiere:d}=(0,o.A)(),[u,h]=(0,s.useState)(null),[p,v]=(0,s.useState)(""),[b,j]=(0,s.useState)(!1),[f,N]=(0,s.useState)(""),[_,y]=(0,s.useState)(0),C=(0,s.useRef)(null),z=async()=>{if(u&&d){if("cavi"===l&&!p.trim())return void N("Inserisci il codice revisione per l'importazione cavi");try{let e;if(j(!0),N(""),y(0),e="cavi"===l?await m.mg.importCavi(d.id_cantiere,u,p.trim()):await m.mg.importBobine(d.id_cantiere,u),y(100),e.data.success){let a=e.data.details,t=e.data.message;"cavi"===l&&(null==a?void 0:a.cavi_importati)?t+=" (".concat(a.cavi_importati," cavi importati)"):"bobine"===l&&(null==a?void 0:a.bobine_importate)&&(t+=" (".concat(a.bobine_importate," bobine importate)")),n(t),i()}else c(e.data.message||"Errore durante l'importazione")}catch(i){var e,a;c((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante l'importazione del file")}finally{j(!1),y(0)}}},w=()=>{b||(h(null),v(""),N(""),y(0),C.current&&(C.current.value=""),i())},A=()=>"cavi"===l?"Cavi":"Bobine";return(0,t.jsx)(eh.lG,{open:a,onOpenChange:w,children:(0,t.jsxs)(eh.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(eh.c7,{children:[(0,t.jsxs)(eh.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(eP.A,{className:"h-5 w-5"}),"Importa ",A()," da Excel"]}),(0,t.jsxs)(eh.rr,{children:["Carica un file Excel per importare ",A().toLowerCase()," nel cantiere"]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)(ep.J,{className:"text-sm font-medium",children:"Requisiti File"}),(0,t.jsx)("ul",{className:"mt-2 space-y-1 text-sm text-gray-600",children:("cavi"===l?["File Excel (.xlsx o .xls)","Colonne richieste: ID_CAVO, SISTEMA, UTILITY, TIPOLOGIA, ecc.","Prima riga deve contenere le intestazioni","Codice revisione obbligatorio per tracciabilit\xe0"]:["File Excel (.xlsx o .xls)","Colonne richieste: NUMERO_BOBINA, UTILITY, TIPOLOGIA, METRI_TOTALI, ecc.","Prima riga deve contenere le intestazioni","I metri residui saranno impostati uguali ai metri totali"]).map((e,a)=>(0,t.jsxs)("li",{className:"flex items-start gap-2",children:[(0,t.jsx)("span",{className:"text-blue-500 mt-0.5",children:"•"}),(0,t.jsx)("span",{children:e})]},a))})]}),f&&(0,t.jsxs)(r.Fc,{variant:"destructive",children:[(0,t.jsx)(es.A,{className:"h-4 w-4"}),(0,t.jsx)(r.TN,{children:f})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"file",children:"File Excel *"}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(g.p,{ref:C,id:"file",type:"file",accept:".xlsx,.xls",onChange:e=>{var a;let i=null==(a=e.target.files)?void 0:a[0];if(i){if(!["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel"].includes(i.type)&&!i.name.toLowerCase().endsWith(".xlsx")&&!i.name.toLowerCase().endsWith(".xls"))return void N("Seleziona un file Excel valido (.xlsx o .xls)");h(i),N("")}},disabled:b,className:"flex-1"}),u&&(0,t.jsxs)("div",{className:"flex items-center gap-1 text-green-600",children:[(0,t.jsx)(ei.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"text-sm",children:"File selezionato"})]})]}),u&&(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,t.jsx)(e$.A,{className:"h-4 w-4 inline mr-1"}),u.name," (",(u.size/1024/1024).toFixed(2)," MB)"]})]}),"cavi"===l&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"revisione",children:"Codice Revisione *"}),(0,t.jsx)(g.p,{id:"revisione",value:p,onChange:e=>v(e.target.value),placeholder:"es. REV001, V1.0, 2024-01",disabled:b}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Codice identificativo della revisione per tracciabilit\xe0 delle modifiche"})]}),b&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(eg.A,{className:"h-4 w-4 animate-spin"}),(0,t.jsx)("span",{className:"text-sm",children:"Caricamento in corso..."})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(_,"%")}})})]}),u&&(0,t.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,t.jsx)(ep.J,{className:"text-sm font-medium",children:"Riepilogo Importazione"}),(0,t.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Tipo:"})," ",A()]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"File:"})," ",u.name]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Dimensione:"})," ",(u.size/1024/1024).toFixed(2)," MB"]}),"cavi"===l&&p&&(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Revisione:"})," ",p]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Cantiere:"})," ",null==d?void 0:d.nome_cantiere]})]})]})]}),(0,t.jsxs)(eh.Es,{children:[(0,t.jsx)(x.$,{variant:"outline",onClick:w,disabled:b,children:"Annulla"}),(0,t.jsxs)(x.$,{onClick:z,disabled:b||!u||"cavi"===l&&!p.trim(),children:[b?(0,t.jsx)(eg.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(eP.A,{className:"h-4 w-4 mr-2"}),"Importa ",A()]})]})]})})}let eJ={id_cavo:"",utility:"",sistema:"",colore_cavo:"",tipologia:"",sezione:"",ubicazione_partenza:"",utenza_partenza:"",descrizione_utenza_partenza:"",ubicazione_arrivo:"",utenza_arrivo:"",descrizione_utenza_arrivo:"",metri_teorici:""};function eG(e){let{open:a,onClose:i,cantiere:l,onSuccess:n,onError:c}=e,{cantiere:d}=(0,o.A)(),u=l||d,[h,p]=(0,s.useState)(eJ),[v,b]=(0,s.useState)(!1),[j,f]=(0,s.useState)({});(0,s.useEffect)(()=>{a&&(p(eJ),f({}))},[a]);let N=(e,a)=>{p(i=>({...i,[e]:"id_cavo"===e?a.toUpperCase():a})),j[e]&&f(a=>{let i={...a};return delete i[e],i})},_=()=>{let e={};if(h.id_cavo.trim()||(e.id_cavo="ID Cavo \xe8 obbligatorio"),h.utility.trim()||(e.utility="Utility \xe8 obbligatoria"),h.metri_teorici.trim()){let a=parseFloat(h.metri_teorici);(isNaN(a)||a<=0)&&(e.metri_teorici="Metri Teorici deve essere un numero positivo")}else e.metri_teorici="Metri Teorici sono obbligatori";return f(e),0===Object.keys(e).length},y=async()=>{if(_()){if(!(null==u?void 0:u.id_cantiere))return void c("Cantiere non selezionato");try{b(!0);let e={...h,metri_teorici:parseFloat(h.metri_teorici),id_cantiere:u.id_cantiere};await m.At.createCavo(parseInt(u.id_cantiere),e),n("Cavo ".concat(h.id_cavo," aggiunto con successo")),C()}catch(i){var e,a;c((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante l'aggiunta del cavo")}finally{b(!1)}}},C=()=>{v||(p(eJ),f({}),i())};return(0,t.jsx)(eh.lG,{open:a,onOpenChange:C,children:(0,t.jsxs)(eh.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(eh.c7,{children:[(0,t.jsxs)(eh.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(G.A,{className:"h-5 w-5"}),"Aggiungi Nuovo Cavo"]}),(0,t.jsxs)(eh.rr,{children:["Inserisci i dati del nuovo cavo per il cantiere ",null==u?void 0:u.nome_cantiere]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Informazioni Generali"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"id_cavo",children:"ID Cavo *"}),(0,t.jsx)(g.p,{id:"id_cavo",value:h.id_cavo,onChange:e=>N("id_cavo",e.target.value),placeholder:"Es. C001",className:j.id_cavo?"border-red-500":""}),j.id_cavo&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:j.id_cavo})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"utility",children:"Utility *"}),(0,t.jsx)(g.p,{id:"utility",value:h.utility,onChange:e=>N("utility",e.target.value),placeholder:"Es. ENEL",className:j.utility?"border-red-500":""}),j.utility&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:j.utility})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"sistema",children:"Sistema"}),(0,t.jsx)(g.p,{id:"sistema",value:h.sistema,onChange:e=>N("sistema",e.target.value),placeholder:"Es. MT"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Caratteristiche Tecniche"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"colore_cavo",children:"Colore Cavo"}),(0,t.jsx)(g.p,{id:"colore_cavo",value:h.colore_cavo,onChange:e=>N("colore_cavo",e.target.value),placeholder:"Es. Nero"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"tipologia",children:"Tipologia"}),(0,t.jsx)(g.p,{id:"tipologia",value:h.tipologia,onChange:e=>N("tipologia",e.target.value),placeholder:"Es. ARE4H5E"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"sezione",children:"Formazione"}),(0,t.jsx)(g.p,{id:"sezione",value:h.sezione,onChange:e=>N("sezione",e.target.value),placeholder:"Es. 3X240+120"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Partenza"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"ubicazione_partenza",children:"Ubicazione Partenza"}),(0,t.jsx)(g.p,{id:"ubicazione_partenza",value:h.ubicazione_partenza,onChange:e=>N("ubicazione_partenza",e.target.value)})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"utenza_partenza",children:"Utenza Partenza"}),(0,t.jsx)(g.p,{id:"utenza_partenza",value:h.utenza_partenza,onChange:e=>N("utenza_partenza",e.target.value)})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"descrizione_utenza_partenza",children:"Descrizione Utenza Partenza"}),(0,t.jsx)(g.p,{id:"descrizione_utenza_partenza",value:h.descrizione_utenza_partenza,onChange:e=>N("descrizione_utenza_partenza",e.target.value)})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Arrivo"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"ubicazione_arrivo",children:"Ubicazione Arrivo"}),(0,t.jsx)(g.p,{id:"ubicazione_arrivo",value:h.ubicazione_arrivo,onChange:e=>N("ubicazione_arrivo",e.target.value)})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"utenza_arrivo",children:"Utenza Arrivo"}),(0,t.jsx)(g.p,{id:"utenza_arrivo",value:h.utenza_arrivo,onChange:e=>N("utenza_arrivo",e.target.value)})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"descrizione_utenza_arrivo",children:"Descrizione Utenza Arrivo"}),(0,t.jsx)(g.p,{id:"descrizione_utenza_arrivo",value:h.descrizione_utenza_arrivo,onChange:e=>N("descrizione_utenza_arrivo",e.target.value)})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Metratura"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"metri_teorici",children:"Metri Teorici *"}),(0,t.jsx)(g.p,{id:"metri_teorici",type:"number",step:"0.01",min:"0",value:h.metri_teorici,onChange:e=>N("metri_teorici",e.target.value),placeholder:"Es. 100.50",className:j.metri_teorici?"border-red-500":""}),j.metri_teorici&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:j.metri_teorici})]})})]}),Object.keys(j).length>0&&(0,t.jsxs)(r.Fc,{variant:"destructive",children:[(0,t.jsx)(es.A,{className:"h-4 w-4"}),(0,t.jsx)(r.TN,{children:"Correggere i campi evidenziati in rosso prima di salvare."})]})]}),(0,t.jsxs)(eh.Es,{children:[(0,t.jsx)(x.$,{variant:"outline",onClick:C,disabled:v,children:"Annulla"}),(0,t.jsxs)(x.$,{onClick:y,disabled:v,children:[v&&(0,t.jsx)(eg.A,{className:"mr-2 h-4 w-4 animate-spin"}),v?"Aggiungendo...":"Aggiungi Cavo"]})]})]})})}function eW(e){let{open:a,onClose:i,cavo:l,cantiere:n,onSuccess:c,onError:d}=e,{cantiere:u}=(0,o.A)(),h=n||u,[p,v]=(0,s.useState)({id_cavo:"",utility:"",sistema:"",colore_cavo:"",tipologia:"",sezione:"",ubicazione_partenza:"",utenza_partenza:"",descrizione_utenza_partenza:"",ubicazione_arrivo:"",utenza_arrivo:"",descrizione_utenza_arrivo:"",metri_teorici:""}),[b,j]=(0,s.useState)(!1),[f,N]=(0,s.useState)({});(0,s.useEffect)(()=>{if(a&&l){var e;v({id_cavo:l.id_cavo||"",utility:l.utility||"",sistema:l.sistema||"",colore_cavo:l.colore_cavo||"",tipologia:l.tipologia||"",sezione:l.sezione||"",ubicazione_partenza:l.ubicazione_partenza||"",utenza_partenza:l.utenza_partenza||"",descrizione_utenza_partenza:l.descrizione_utenza_partenza||"",ubicazione_arrivo:l.ubicazione_arrivo||"",utenza_arrivo:l.utenza_arrivo||"",descrizione_utenza_arrivo:l.descrizione_utenza_arrivo||"",metri_teorici:(null==(e=l.metri_teorici)?void 0:e.toString())||""}),N({})}},[a,l]);let _=(e,a)=>{v(i=>({...i,[e]:"id_cavo"===e?a.toUpperCase():a})),f[e]&&N(a=>{let i={...a};return delete i[e],i})},y=()=>{let e={};if(p.id_cavo.trim()||(e.id_cavo="ID Cavo \xe8 obbligatorio"),p.utility.trim()||(e.utility="Utility \xe8 obbligatoria"),p.metri_teorici.trim()){let a=parseFloat(p.metri_teorici);(isNaN(a)||a<=0)&&(e.metri_teorici="Metri Teorici deve essere un numero positivo")}else e.metri_teorici="Metri Teorici sono obbligatori";return N(e),0===Object.keys(e).length},C=async()=>{if(y()){if(!(null==l?void 0:l.id_cavo))return void d("Cavo non selezionato");if(!(null==h?void 0:h.id_cantiere))return void d("Cantiere non selezionato");try{j(!0);let e={...p,metri_teorici:parseFloat(p.metri_teorici),id_cantiere:h.id_cantiere};await m.At.updateCavo(parseInt(h.id_cantiere),l.id_cavo,e),c("Cavo ".concat(p.id_cavo," modificato con successo")),z()}catch(i){var e,a;d((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante la modifica del cavo")}finally{j(!1)}}},z=()=>{b||(N({}),i())},w=l&&(l.metratura_reale>0||"da installare"!==l.stato_installazione);return l?(0,t.jsx)(eh.lG,{open:a,onOpenChange:z,children:(0,t.jsxs)(eh.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(eh.c7,{children:[(0,t.jsxs)(eh.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(V.A,{className:"h-5 w-5"}),"Modifica Cavo: ",l.id_cavo]}),(0,t.jsxs)(eh.rr,{children:["Modifica i dati del cavo nel cantiere ",null==h?void 0:h.nome_cantiere]})]}),w&&(0,t.jsxs)(r.Fc,{variant:"destructive",children:[(0,t.jsx)(ec.A,{className:"h-4 w-4"}),(0,t.jsxs)(r.TN,{children:[(0,t.jsx)("strong",{children:"Attenzione:"})," Questo cavo \xe8 gi\xe0 stato installato. La modifica potrebbe influire sui dati di installazione esistenti."]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Informazioni Generali"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"id_cavo",children:"ID Cavo *"}),(0,t.jsx)(g.p,{id:"id_cavo",value:p.id_cavo,onChange:e=>_("id_cavo",e.target.value),placeholder:"Es. C001",className:f.id_cavo?"border-red-500":""}),f.id_cavo&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:f.id_cavo})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"utility",children:"Utility *"}),(0,t.jsx)(g.p,{id:"utility",value:p.utility,onChange:e=>_("utility",e.target.value),placeholder:"Es. ENEL",className:f.utility?"border-red-500":""}),f.utility&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:f.utility})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"sistema",children:"Sistema"}),(0,t.jsx)(g.p,{id:"sistema",value:p.sistema,onChange:e=>_("sistema",e.target.value),placeholder:"Es. MT"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Caratteristiche Tecniche"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"colore_cavo",children:"Colore Cavo"}),(0,t.jsx)(g.p,{id:"colore_cavo",value:p.colore_cavo,onChange:e=>_("colore_cavo",e.target.value),placeholder:"Es. Nero"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"tipologia",children:"Tipologia"}),(0,t.jsx)(g.p,{id:"tipologia",value:p.tipologia,onChange:e=>_("tipologia",e.target.value),placeholder:"Es. ARE4H5E"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"sezione",children:"Formazione"}),(0,t.jsx)(g.p,{id:"sezione",value:p.sezione,onChange:e=>_("sezione",e.target.value),placeholder:"Es. 3X240+120"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Partenza"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"ubicazione_partenza",children:"Ubicazione Partenza"}),(0,t.jsx)(g.p,{id:"ubicazione_partenza",value:p.ubicazione_partenza,onChange:e=>_("ubicazione_partenza",e.target.value)})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"utenza_partenza",children:"Utenza Partenza"}),(0,t.jsx)(g.p,{id:"utenza_partenza",value:p.utenza_partenza,onChange:e=>_("utenza_partenza",e.target.value)})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"descrizione_utenza_partenza",children:"Descrizione Utenza Partenza"}),(0,t.jsx)(g.p,{id:"descrizione_utenza_partenza",value:p.descrizione_utenza_partenza,onChange:e=>_("descrizione_utenza_partenza",e.target.value)})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Arrivo"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"ubicazione_arrivo",children:"Ubicazione Arrivo"}),(0,t.jsx)(g.p,{id:"ubicazione_arrivo",value:p.ubicazione_arrivo,onChange:e=>_("ubicazione_arrivo",e.target.value)})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"utenza_arrivo",children:"Utenza Arrivo"}),(0,t.jsx)(g.p,{id:"utenza_arrivo",value:p.utenza_arrivo,onChange:e=>_("utenza_arrivo",e.target.value)})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"descrizione_utenza_arrivo",children:"Descrizione Utenza Arrivo"}),(0,t.jsx)(g.p,{id:"descrizione_utenza_arrivo",value:p.descrizione_utenza_arrivo,onChange:e=>_("descrizione_utenza_arrivo",e.target.value)})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Metratura"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ep.J,{htmlFor:"metri_teorici",children:"Metri Teorici *"}),(0,t.jsx)(g.p,{id:"metri_teorici",type:"number",step:"0.01",min:"0",value:p.metri_teorici,onChange:e=>_("metri_teorici",e.target.value),placeholder:"Es. 100.50",className:f.metri_teorici?"border-red-500":""}),f.metri_teorici&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:f.metri_teorici})]})})]}),Object.keys(f).length>0&&(0,t.jsxs)(r.Fc,{variant:"destructive",children:[(0,t.jsx)(es.A,{className:"h-4 w-4"}),(0,t.jsx)(r.TN,{children:"Correggere i campi evidenziati in rosso prima di salvare."})]})]}),(0,t.jsxs)(eh.Es,{children:[(0,t.jsx)(x.$,{variant:"outline",onClick:z,disabled:b,children:"Annulla"}),(0,t.jsxs)(x.$,{onClick:C,disabled:b,children:[b&&(0,t.jsx)(eg.A,{className:"mr-2 h-4 w-4 animate-spin"}),b?"Salvando...":"Salva Modifiche"]})]})]})}):null}var eZ=i(54059),eq=i(9428);let eH=s.forwardRef((e,a)=>{let{className:i,...s}=e;return(0,t.jsx)(eZ.bL,{className:(0,f.cn)("grid gap-2",i),...s,ref:a})});eH.displayName=eZ.bL.displayName;let eX=s.forwardRef((e,a)=>{let{className:i,...s}=e;return(0,t.jsx)(eZ.q7,{ref:a,className:(0,f.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",i),...s,children:(0,t.jsx)(eZ.C1,{className:"flex items-center justify-center",children:(0,t.jsx)(eq.A,{className:"h-2.5 w-2.5 fill-current text-current"})})})});function eK(e){let{open:a,onClose:i,cavo:l,cantiere:n,onSuccess:c,onError:d}=e,{cantiere:u}=(0,o.A)(),h=n||u,[p,v]=(0,s.useState)(!1),[g,b]=(0,s.useState)("spare"),j=async()=>{if(!(null==l?void 0:l.id_cavo))return void d("Cavo non selezionato");if(!(null==h?void 0:h.id_cantiere))return void d("Cantiere non selezionato");try{if(v(!0),console.log("Iniziando operazione ".concat(g," per cavo ").concat(l.id_cavo)),"spare"===g){console.log("Chiamando markAsSpare API...");let e=await m.At.markAsSpare(parseInt(h.id_cantiere),l.id_cavo,!0);console.log("Risultato markAsSpare:",e),c("Cavo ".concat(l.id_cavo," marcato come SPARE"))}else{console.log("Chiamando deleteCavo API...");let e=await m.At.deleteCavo(parseInt(h.id_cantiere),l.id_cavo);console.log("Risultato deleteCavo:",e),c("Cavo ".concat(l.id_cavo," eliminato definitivamente"))}f()}catch(t){var e,a;console.error("Errore durante operazione:",t);let i=(null==(a=t.response)||null==(e=a.data)?void 0:e.detail)||t.message||"Errore durante l'eliminazione del cavo";console.error("Messaggio errore:",i),d(i)}finally{v(!1)}},f=()=>{p||(b("spare"),i())};if(!l)return null;let N=l.metratura_reale>0||l.stato_installazione&&"da installare"!==l.stato_installazione.toLowerCase(),_=!N,y={id:l.id_cavo,tipologia:l.tipologia||"N/A",sezione:l.sezione||"N/A",metri_teorici:l.metri_teorici||0,metri_reali:l.metratura_reale||0,stato:l.stato_installazione||"N/A",bobina:l.id_bobina||"N/A"};return(0,t.jsx)(eh.lG,{open:a,onOpenChange:f,children:(0,t.jsxs)(eh.Cf,{className:"max-w-2xl",children:[(0,t.jsxs)(eh.c7,{children:[(0,t.jsxs)(eh.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(J.A,{className:"h-5 w-5"}),"Elimina Cavo: ",l.id_cavo]}),(0,t.jsxs)(eh.rr,{children:["Scegli come gestire l'eliminazione del cavo dal cantiere ",null==h?void 0:h.nome_cantiere]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Informazioni Cavo"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"ID:"}),(0,t.jsx)("span",{className:"ml-2 font-medium",children:y.id})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Tipologia:"}),(0,t.jsx)("span",{className:"ml-2 font-medium",children:y.tipologia})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Formazione:"}),(0,t.jsx)("span",{className:"ml-2 font-medium",children:y.sezione})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Stato:"}),(0,t.jsx)("span",{className:"ml-2 font-medium",children:y.stato})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Metri Teorici:"}),(0,t.jsx)("span",{className:"ml-2 font-medium",children:y.metri_teorici})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Metri Installati:"}),(0,t.jsx)("span",{className:"ml-2 font-medium",children:y.metri_reali})]}),(0,t.jsxs)("div",{className:"col-span-2",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Bobina:"}),(0,t.jsx)("span",{className:"ml-2 font-medium",children:y.bobina})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Modalit\xe0 di Eliminazione"}),(0,t.jsxs)(eH,{value:g,onValueChange:e=>b(e),children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(eX,{value:"spare",id:"spare"}),(0,t.jsxs)(ep.J,{htmlFor:"spare",className:"flex-1",children:[(0,t.jsx)("div",{className:"font-medium",children:"Marca come SPARE"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Il cavo viene marcato come spare/consumato ma rimane nel database per tracciabilit\xe0"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(eX,{value:"permanent",id:"permanent",disabled:!_}),(0,t.jsxs)(ep.J,{htmlFor:"permanent",className:"flex-1 ".concat(_?"":"opacity-50"),children:[(0,t.jsx)("div",{className:"font-medium",children:"Eliminazione Definitiva"}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:["Il cavo viene rimosso completamente dal database",!_&&" (non disponibile per cavi installati)"]})]})]})]})]}),N&&(0,t.jsxs)(r.Fc,{children:[(0,t.jsx)(el.A,{className:"h-4 w-4"}),(0,t.jsxs)(r.TN,{children:[(0,t.jsx)("strong",{children:"Cavo Installato:"})," Questo cavo ha metri installati. Si consiglia di marcarlo come SPARE piuttosto che eliminarlo definitivamente."]})]}),"permanent"===g&&(0,t.jsxs)(r.Fc,{variant:"destructive",children:[(0,t.jsx)(ec.A,{className:"h-4 w-4"}),(0,t.jsxs)(r.TN,{children:[(0,t.jsx)("strong",{children:"Attenzione:"})," L'eliminazione definitiva \xe8 irreversibile. Tutti i dati del cavo verranno persi permanentemente."]})]}),"spare"===g&&(0,t.jsxs)(r.Fc,{children:[(0,t.jsx)(el.A,{className:"h-4 w-4"}),(0,t.jsx)(r.TN,{children:"Il cavo verr\xe0 marcato come SPARE e non apparir\xe0 pi\xf9 nelle liste attive, ma rimarr\xe0 nel database per eventuali controlli futuri."})]})]}),(0,t.jsxs)(eh.Es,{children:[(0,t.jsx)(x.$,{variant:"outline",onClick:f,disabled:p,children:"Annulla"}),(0,t.jsxs)(x.$,{variant:"destructive",onClick:j,disabled:p,children:[p&&(0,t.jsx)(eg.A,{className:"mr-2 h-4 w-4 animate-spin"}),p?"Eliminando...":"spare"===g?"Marca come SPARE":"Elimina Definitivamente"]})]})]})})}eX.displayName=eZ.q7.displayName;var eY=i(54213);function eQ(e){let{open:a,onClose:i,onSuccess:l,onError:n}=e,{cantiere:c}=(0,o.A)(),[d,u]=(0,s.useState)({cavi:!0,bobine:!0,comande:!1,certificazioni:!1,responsabili:!1}),[p,v]=(0,s.useState)(!1),[g,b]=(0,s.useState)(""),j=(e,a)=>{u(i=>({...i,[e]:a}))},f=async()=>{if(c)try{v(!0);let e=await m.mg.exportCavi(c.id_cantiere),a=window.URL.createObjectURL(new Blob([e.data])),i=document.createElement("a");i.href=a,i.setAttribute("download","cavi_".concat(c.nome_cantiere,"_").concat(new Date().toISOString().split("T")[0],".xlsx")),document.body.appendChild(i),i.click(),i.remove(),window.URL.revokeObjectURL(a),l("Export cavi completato con successo")}catch(i){var e,a;n((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante l'export dei cavi")}finally{v(!1)}},N=async()=>{if(c)try{v(!0);let e=await m.mg.exportBobine(c.id_cantiere),a=window.URL.createObjectURL(new Blob([e.data])),i=document.createElement("a");i.href=a,i.setAttribute("download","bobine_".concat(c.nome_cantiere,"_").concat(new Date().toISOString().split("T")[0],".xlsx")),document.body.appendChild(i),i.click(),i.remove(),window.URL.revokeObjectURL(a),l("Export bobine completato con successo")}catch(i){var e,a;n((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante l'export delle bobine")}finally{v(!1)}},_=async()=>{if(c)try{v(!0),b("");let e=[];d.cavi&&e.push(f()),d.bobine&&e.push(N()),d.comande,d.certificazioni,d.responsabili,await Promise.all(e);let a=Object.values(d).filter(Boolean).length;l("Export completato: ".concat(a," file scaricati")),i()}catch(i){var e,a;n((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante l'export dei dati")}finally{v(!1)}},y=[{key:"cavi",label:"Cavi",description:"Esporta tutti i cavi del cantiere con stato, collegamenti e certificazioni",icon:(0,t.jsx)(eY.A,{className:"h-4 w-4"}),available:!0},{key:"bobine",label:"Bobine",description:"Esporta tutte le bobine del parco cavi con metri residui e assegnazioni",icon:(0,t.jsx)(e$.A,{className:"h-4 w-4"}),available:!0},{key:"comande",label:"Comande",description:"Esporta tutte le comande con cavi assegnati e responsabili",icon:(0,t.jsx)(e$.A,{className:"h-4 w-4"}),available:!1},{key:"certificazioni",label:"Certificazioni",description:"Esporta tutte le certificazioni con esiti e responsabili",icon:(0,t.jsx)(e$.A,{className:"h-4 w-4"}),available:!1},{key:"responsabili",label:"Responsabili",description:"Esporta tutti i responsabili con contatti e ruoli",icon:(0,t.jsx)(e$.A,{className:"h-4 w-4"}),available:!1}];return(0,t.jsx)(eh.lG,{open:a,onOpenChange:i,children:(0,t.jsxs)(eh.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(eh.c7,{children:[(0,t.jsxs)(eh.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(Q.A,{className:"h-5 w-5"}),"Esporta Dati Cantiere"]}),(0,t.jsxs)(eh.rr,{children:["Seleziona i dati da esportare dal cantiere ",null==c?void 0:c.nome_cantiere]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[g&&(0,t.jsxs)(r.Fc,{variant:"destructive",children:[(0,t.jsx)(es.A,{className:"h-4 w-4"}),(0,t.jsx)(r.TN,{children:g})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(ep.J,{className:"text-sm font-medium",children:"Seleziona Dati da Esportare"}),y.map(e=>(0,t.jsxs)("div",{className:"flex items-start space-x-3 p-3 rounded-lg border ".concat(e.available?"bg-white":"bg-gray-50"),children:[(0,t.jsx)(h.S,{id:e.key,checked:d[e.key],onCheckedChange:a=>j(e.key,a),disabled:!e.available||p}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon,(0,t.jsxs)(ep.J,{htmlFor:e.key,className:"font-medium ".concat(e.available?"":"text-gray-500"),children:[e.label,!e.available&&(0,t.jsx)("span",{className:"ml-2 text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded",children:"In sviluppo"})]})]}),(0,t.jsx)("p",{className:"text-sm mt-1 ".concat(e.available?"text-gray-600":"text-gray-400"),children:e.description})]})]},e.key))]}),(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)(ep.J,{className:"text-sm font-medium",children:"Informazioni Export"}),(0,t.jsxs)("ul",{className:"mt-2 space-y-1 text-sm text-gray-600",children:[(0,t.jsx)("li",{children:"• I file saranno scaricati in formato Excel (.xlsx)"}),(0,t.jsx)("li",{children:"• I nomi file includeranno data e nome cantiere"}),(0,t.jsx)("li",{children:"• I dati esportati riflettono lo stato attuale del database"}),(0,t.jsx)("li",{children:"• L'export non modifica i dati originali"})]})]}),Object.values(d).filter(Boolean).length>0&&(0,t.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,t.jsx)(ep.J,{className:"text-sm font-medium",children:"Riepilogo Export"}),(0,t.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Cantiere:"})," ",null==c?void 0:c.nome_cantiere]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"File da scaricare:"})," ",Object.values(d).filter(Boolean).length]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Data export:"})," ",new Date().toLocaleDateString("it-IT")]})]})]})]}),(0,t.jsxs)(eh.Es,{children:[(0,t.jsx)(x.$,{variant:"outline",onClick:i,disabled:p,children:"Annulla"}),(0,t.jsxs)(x.$,{onClick:_,disabled:p||0===Object.values(d).filter(Boolean).length,children:[p?(0,t.jsx)(eg.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(Q.A,{className:"h-4 w-4 mr-2"}),"Esporta ",Object.values(d).filter(Boolean).length>0?"(".concat(Object.values(d).filter(Boolean).length,")"):""]})]})]})})}function e0(){let{user:e,isAuthenticated:a,isLoading:i}=(0,o.A)(),{cantiereId:x,cantiere:u,isValidCantiere:h,isLoading:p,error:v}=(0,c.jV)(),g=(0,l.useRouter)(),b=e=>{let{title:a,description:i,variant:t}=e},[j,f]=(0,s.useState)([]),[N,_]=(0,s.useState)([]),[y,C]=(0,s.useState)(!0),[z,w]=(0,s.useState)(""),[A,S]=(0,s.useState)([]),[E,T]=(0,s.useState)(!1),[I,k]=(0,s.useState)([]),[O,F]=(0,s.useState)("");(0,s.useEffect)(()=>{k(j)},[j]);let[R,L]=(0,s.useState)({open:!1,cavo:null}),[D,M]=(0,s.useState)({open:!1,cavo:null}),[B,U]=(0,s.useState)(!1),[P,$]=(0,s.useState)({open:!1,cavo:null}),[V,J]=(0,s.useState)({open:!1,cavo:null}),[G,W]=(0,s.useState)({open:!1,cavo:null}),[q,H]=(0,s.useState)({open:!1,mode:null,cavo:null}),[X,K]=(0,s.useState)({open:!1,cavo:null}),[Y,Q]=(0,s.useState)({open:!1,cavo:null}),[ee,ea]=(0,s.useState)({open:!1,cavo:null}),[ei,et]=(0,s.useState)({open:!1,cavo:null}),[el,er]=(0,s.useState)({open:!1,cavi:[]}),[eo,ec]=(0,s.useState)({open:!1,cavo:null,error:""}),[ed,em]=(0,s.useState)({visible:!1,message:""}),[ex,eh]=(0,s.useState)({open:!1}),[ep,ev]=(0,s.useState)({open:!1}),[eb,ej]=(0,s.useState)(!1),[ef,eN]=(0,s.useState)({totali:0,installati:0,collegati:0,certificati:0,percentualeInstallazione:0,percentualeCollegamento:0,percentualeCertificazione:0,metriTotali:0,metriInstallati:0,metriCollegati:0,metriCertificati:0});(0,s.useEffect)(()=>{i||a||g.push("/login")},[a,i,g]);let ey=u||(x&&x>0?{id_cantiere:x,commessa:"Cantiere ".concat(x)}:null);(0,s.useEffect)(()=>{h&&x&&x>0&&!p?(ez(),eC()):p||h||(f([]),_([]),w(v||"Nessun cantiere selezionato"))},[x,h,p,v]);let eC=async()=>{try{let e=await fetch("http://localhost:8001/api/cavi/".concat(x,"/revisione-corrente"),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}});if(e.ok){let a=await e.json();F(a.revisione_corrente||"00")}else F("00")}catch(e){F("00")}},ez=async()=>{try{C(!0),w("");try{let e=await m.At.getCavi(x),a=e.filter(e=>3!==e.modificato_manualmente),i=e.filter(e=>3===e.modificato_manualmente);f(a),_(i),ew(a)}catch(e){throw e}}catch(i){var e,a;w("Errore nel caricamento dei cavi: ".concat((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message))}finally{C(!1)}},ew=e=>{let a=e.length,i=e.filter(e=>(e.metri_posati||e.metratura_reale||0)>0).length,t=e.filter(e=>3===(e.collegamento||e.collegamenti)).length,s=e.filter(e=>e.certificato).length,l=e.reduce((e,a)=>e+(a.metri_teorici||0),0),n=e.reduce((e,a)=>e+(a.metri_posati||0),0);eN({totali:a,installati:i,collegati:t,certificati:s,percentualeInstallazione:a>0?Math.round(i/a*100):0,percentualeCollegamento:a>0?Math.round(t/a*100):0,percentualeCertificazione:a>0?Math.round(s/a*100):0,metriTotali:l,metriInstallati:n,metriCollegati:e.filter(e=>3===e.collegamento).reduce((e,a)=>e+(a.metri_posati||0),0),metriCertificati:e.filter(e=>e.certificato).reduce((e,a)=>e+(a.metri_posati||0),0)})},eF=(e,a,i)=>{switch(a){case"insert_meters":H({open:!0,mode:"aggiungi_metri",cavo:e});break;case"modify_reel":H({open:!0,mode:"modifica_bobina",cavo:e});break;case"view_command":b({title:"Visualizza Comanda",description:"Apertura comanda ".concat(label," per cavo ").concat(e.id_cavo)});break;case"connect_cable":case"connect_arrival":case"connect_departure":case"manage_connections":case"disconnect_cable":K({open:!0,cavo:e});break;case"create_certificate":console.log("\uD83D\uDE80 Opening certification modal for cavo:",e.id_cavo),et({open:!0,cavo:e});break;case"bulk_certify":i&&i.length>0&&er({open:!0,cavi:i});break;case"generate_pdf":ea({open:!0,cavo:e})}},eR=(e,a)=>{switch(a){case"view_details":M({open:!0,cavo:e});break;case"edit":$({open:!0,cavo:e});break;case"delete":J({open:!0,cavo:e});break;case"add_new":U(!0);break;case"select":A.includes(e.id_cavo)?(S(A.filter(a=>a!==e.id_cavo)),b({title:"Cavo Deselezionato",description:"Cavo ".concat(e.id_cavo," deselezionato")})):(S([...A,e.id_cavo]),b({title:"Cavo Selezionato",description:"Cavo ".concat(e.id_cavo," selezionato")}));break;case"copy_id":navigator.clipboard.writeText(e.id_cavo),b({title:"ID Copiato",description:"ID cavo ".concat(e.id_cavo," copiato negli appunti")});break;case"copy_details":let i="ID: ".concat(e.id_cavo,", Tipologia: ").concat(e.tipologia,", Formazione: ").concat(e.formazione||e.sezione,", Metri: ").concat(e.metri_teorici);navigator.clipboard.writeText(i),b({title:"Dettagli Copiati",description:"Dettagli cavo copiati negli appunti"});break;case"add_to_command":b({title:"Aggiungi a Comanda",description:"Funzione aggiunta a comanda in sviluppo"});break;case"remove_from_command":b({title:"Rimuovi da Comanda",description:"Funzione rimozione da comanda in sviluppo"});break;case"create_command_posa":eh({open:!0,tipoComanda:"POSA"});break;case"create_command_collegamento_partenza":eh({open:!0,tipoComanda:"COLLEGAMENTO_PARTENZA"});break;case"create_command_collegamento_arrivo":eh({open:!0,tipoComanda:"COLLEGAMENTO_ARRIVO"});break;case"create_command_certificazione":eh({open:!0,tipoComanda:"CERTIFICAZIONE"});break;case"add_multiple_to_command":b({title:"Aggiungi Tutti a Comanda",description:"Funzione aggiunta multipla a comanda in sviluppo"});break;case"remove_multiple_from_commands":b({title:"Rimuovi Tutti dalle Comande",description:"Funzione rimozione multipla dalle comande in sviluppo"});break;default:b({title:"Azione non implementata",description:"Azione ".concat(a," non ancora implementata")})}},eD=e=>{b({title:"Operazione completata",description:e}),ez()},eM=e=>{b({title:"Errore",description:e,variant:"destructive"})},eB=async e=>{try{if(console.log("\uD83D\uDD0D DEBUG handleUnifiedModalSave:",{cantiere:u,cantiereId:x,cantiereForDialog:ey,isValidCantiere:h,data:e}),!x||!h)throw Error("Cantiere non selezionato o non valido");let a="";"aggiungi_metri"===e.mode?(console.log("\uD83D\uDE80 UnifiedModal: Inserimento metri:",e),await m.At.updateMetriPosati(x,e.cableId,e.metersToInstall,"BOBINA_VUOTA"===e.bobbinId?"BOBINA_VUOTA":e.bobbinId,!0),a="Metri posati aggiornati con successo per il cavo ".concat(e.cableId)):"modifica_bobina"===e.mode&&(console.log("\uD83D\uDE80 UnifiedModal: Modifica bobina:",e),"cambia_bobina"===e.editOption?(await m.At.updateMetriPosati(x,e.cableId,e.newLaidMeters,e.newBobbinId,!0),a="Bobina aggiornata con successo per il cavo ".concat(e.cableId)):"bobina_vuota"===e.editOption?(await m.At.updateMetriPosati(x,e.cableId,e.newLaidMeters,"BOBINA_VUOTA",!1),a="Bobina rimossa con successo per il cavo ".concat(e.cableId)):"annulla_posa"===e.editOption&&(await m.At.updateMetriPosati(x,e.cableId,0,"BOBINA_VUOTA",!1),a="Posa annullata con successo per il cavo ".concat(e.cableId))),await ez(),b({title:"Operazione completata",description:a,variant:"default"})}catch(e){throw console.error("Errore unified modal save:",e),b({title:"Errore",description:e.message||"Errore durante l'operazione",variant:"destructive"}),e}},eP=()=>{em({visible:!0,message:"Certificazione completata con successo"}),et({open:!1,cavo:null}),ez()},e$=async(e,a)=>{try{if(!(null==u?void 0:u.id_cantiere))throw Error("Cantiere non selezionato");console.log("Certificando cavo:",e,a),await new Promise(e=>setTimeout(e,1e3)),eP()}catch(e){console.error("Errore certificazione:",e),ec({open:!0,cavo:ei.cavo,error:e.message||"Errore durante la certificazione"}),et({open:!1,cavo:null})}},eJ=async(e,a)=>{try{if(!(null==u?void 0:u.id_cantiere))throw Error("Cantiere non selezionato");console.log("Certificando cavi multipli:",e,a),await new Promise(e=>setTimeout(e,2e3)),em({visible:!0,message:"".concat(e.length," cavi certificati con successo")}),er({open:!1,cavi:[]}),ez()}catch(e){console.error("Errore certificazione multipla:",e),ec({open:!0,cavo:null,error:e.message||"Errore durante la certificazione multipla"}),er({open:!1,cavi:[]})}};return y&&h?(0,t.jsxs)("div",{className:"flex items-center justify-center min-h-screen",children:[(0,t.jsx)(eg.A,{className:"h-8 w-8 animate-spin"}),(0,t.jsx)("span",{className:"ml-2",children:"Caricamento cavi..."})]}):(0,t.jsx)(d.u,{children:(0,t.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[z&&(0,t.jsxs)(r.Fc,{variant:"destructive",className:"mb-6",children:[(0,t.jsx)(es.A,{className:"h-4 w-4"}),(0,t.jsx)(r.TN,{children:z})]}),(0,t.jsx)(eu,{cavi:j,filteredCavi:I,revisioneCorrente:O,className:"mb-2"}),(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsx)(en,{cavi:j,loading:y,selectionEnabled:E,selectedCavi:A,onSelectionChange:S,onStatusAction:eF,onContextMenuAction:eR})}),N.length>0&&(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(Z.A,{className:"h-5 w-5"}),(0,t.jsxs)("span",{children:["Cavi Spare (",N.length,")"]})]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)(en,{cavi:N,loading:y,selectionEnabled:!1,onStatusAction:eF,onContextMenuAction:eR})})]})}),(0,t.jsx)(eO.B,{mode:q.mode||"aggiungi_metri",open:q.open,onClose:()=>H({open:!1,mode:null,cavo:null}),cavo:q.cavo,cantiere:ey,onSave:eB}),(0,t.jsx)(eL,{open:R.open,onClose:()=>L({open:!1,cavo:null}),cavo:R.cavo,cantiere:ey,onSuccess:eD,onError:eM}),X.cavo&&(0,t.jsx)(e_,{open:X.open,onClose:()=>K({open:!1,cavo:null}),cavo:X.cavo,cantiere:ey,onSuccess:()=>{b({title:"Successo",description:"Operazione completata con successo"}),ez()}}),(0,t.jsx)(eA,{open:Y.open,onClose:()=>Q({open:!1,cavo:null}),cavo:Y.cavo,onConfirm:()=>{em({visible:!0,message:"Cavo scollegato con successo"}),Q({open:!1,cavo:null}),ez()},onError:e=>{ec({open:!0,cavo:Y.cavo,error:e}),Q({open:!1,cavo:null})}}),(0,t.jsx)(eS,{open:ee.open,onClose:()=>ea({open:!1,cavo:null}),cavo:ee.cavo,onSuccess:()=>{em({visible:!0,message:"PDF generato con successo"}),ea({open:!1,cavo:null})},onError:e=>{ec({open:!0,cavo:ee.cavo,error:e}),ea({open:!1,cavo:null})}}),(0,t.jsx)(eT,{open:ei.open,onClose:()=>et({open:!1,cavo:null}),cavo:ei.cavo,cantiereId:null==u?void 0:u.id_cantiere,onCertify:e$}),(0,t.jsx)(eI,{open:el.open,onClose:()=>er({open:!1,cavi:[]}),cavi:el.cavi,onCertify:eJ}),(0,t.jsx)(eE,{open:eo.open,onClose:()=>ec({open:!1,cavo:null,error:""}),cavo:eo.cavo,error:eo.error,onRetry:()=>{ec({open:!1,cavo:null,error:""}),eo.cavo&&et({open:!0,cavo:eo.cavo})}}),(0,t.jsx)(ek,{visible:ed.visible,message:ed.message,onClose:()=>em({visible:!1,message:""})}),(0,t.jsx)(eU,{open:ex.open,onClose:()=>eh({open:!1}),caviSelezionati:A,tipoComanda:ex.tipoComanda,onSuccess:eD,onError:eM}),(0,t.jsx)(eV,{open:ep.open,onClose:()=>ev({open:!1}),tipo:ep.tipo||"cavi",onSuccess:eD,onError:eM}),(0,t.jsx)(eQ,{open:eb,onClose:()=>ej(!1),onSuccess:eD,onError:eM}),(0,t.jsx)(eG,{open:B,onClose:()=>U(!1),cantiere:u,onSuccess:e=>{eD(e),ez()},onError:eM}),(0,t.jsx)(eW,{open:P.open,onClose:()=>$({open:!1,cavo:null}),cavo:P.cavo,cantiere:u,onSuccess:e=>{eD(e),ez()},onError:eM}),(0,t.jsx)(eK,{open:V.open,onClose:()=>J({open:!1,cavo:null}),cavo:V.cavo,cantiere:u,onSuccess:e=>{eD(e),ez()},onError:eM}),D.open&&D.cavo&&(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsxs)("h2",{className:"text-xl font-semibold",children:["Dettagli Cavo ",D.cavo.id_cavo]}),(0,t.jsx)("button",{onClick:()=>M({open:!1,cavo:null}),className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"ID Cavo:"})," ",(0,t.jsx)("span",{className:"font-bold",children:D.cavo.id_cavo})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Tipologia:"})," ",(0,t.jsx)("span",{className:"font-bold",children:D.cavo.tipologia||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Sezione:"})," ",(0,t.jsx)("span",{className:"font-bold",children:D.cavo.sezione||D.cavo.formazione||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Sistema:"})," ",(0,t.jsx)("span",{className:"font-bold",children:D.cavo.sistema||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Utility:"})," ",(0,t.jsx)("span",{className:"font-bold",children:D.cavo.utility||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Colore:"})," ",(0,t.jsx)("span",{className:"font-bold",children:D.cavo.colore_cavo||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Da:"})," ",(0,t.jsx)("span",{className:"font-bold",children:D.cavo.ubicazione_partenza||D.cavo.da||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"A:"})," ",(0,t.jsx)("span",{className:"font-bold",children:D.cavo.ubicazione_arrivo||D.cavo.a||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Metri Teorici:"})," ",(0,t.jsx)("span",{className:"font-bold",children:D.cavo.metri_teorici||0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Metri Posati:"})," ",(0,t.jsx)("span",{className:"font-bold",children:D.cavo.metri_posati||D.cavo.metratura_reale||0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Stato:"})," ",(0,t.jsx)("span",{className:"font-bold",children:D.cavo.stato_installazione||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Bobina:"})," ",(0,t.jsx)("span",{className:"font-bold",children:D.cavo.id_bobina||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Collegamenti:"})," ",(0,t.jsx)("span",{className:"font-bold",children:D.cavo.collegamento||D.cavo.collegamenti||0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Modificato Manualmente:"})," ",(0,t.jsx)("span",{className:"font-bold",children:D.cavo.modificato_manualmente||0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Responsabile Posa:"})," ",(0,t.jsx)("span",{className:"font-bold",children:D.cavo.responsabile_posa||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Timestamp:"})," ",(0,t.jsx)("span",{className:"font-bold",children:D.cavo.timestamp||"N/A"})]})]}),(0,t.jsx)("div",{className:"mt-6 flex justify-end",children:(0,t.jsx)("button",{onClick:()=>M({open:!1,cavo:null}),className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Chiudi"})})]})})]})})}},47262:(e,a,i)=>{"use strict";i.d(a,{S:()=>r});var t=i(95155);i(12115);var s=i(76981),l=i(5196),n=i(59434);function r(e){let{className:a,...i}=e;return(0,t.jsx)(s.bL,{"data-slot":"checkbox",className:(0,n.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...i,children:(0,t.jsx)(s.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,t.jsx)(l.A,{className:"size-3.5"})})})}},63743:(e,a,i)=>{"use strict";i.d(a,{Fw:()=>d,NM:()=>c,Nj:()=>o,Tr:()=>n,mU:()=>t});let t={PRIMARY:{bg:"bg-blue-50",text:"text-blue-600",border:"border-blue-300",hover:"hover:bg-blue-50",active:"hover:border-blue-400",hex:"#007bff"},NEUTRAL:{text_dark:"text-gray-800",text_medium:"text-gray-600",text_light:"text-gray-500",bg_white:"bg-white",bg_light:"bg-gray-50",border:"border-gray-300",hex_dark:"#343A40",hex_medium:"#6c757d",hex_light:"#DEE2E6"},STATUS:{SUCCESS:{bg:"bg-green-50",text:"text-green-700",border:"border-green-200",hex:"#28A745"},WARNING:{bg:"bg-orange-50",text:"text-orange-700",border:"border-orange-200",hex:"#FD7E14"},ERROR:{bg:"bg-red-50",text:"text-red-700",border:"border-red-200",hex:"#DC3545"}}};t.STATUS.SUCCESS,t.STATUS.WARNING,t.NEUTRAL,t.STATUS.ERROR,t.NEUTRAL,t.STATUS.ERROR;let s={DA_INSTALLARE:t.NEUTRAL,INSTALLATO:t.STATUS.SUCCESS,COLLEGATO_PARTENZA:t.STATUS.WARNING,COLLEGATO_ARRIVO:t.STATUS.WARNING,COLLEGATO:t.STATUS.SUCCESS,CERTIFICATO:t.STATUS.SUCCESS,SPARE:t.STATUS.WARNING,ERRORE:t.STATUS.ERROR},l={ATTIVA:t.STATUS.SUCCESS,COMPLETATA:t.STATUS.SUCCESS,ANNULLATA:t.NEUTRAL,IN_CORSO:t.STATUS.WARNING,ERRORE:t.STATUS.ERROR},n=e=>{let a=s[null==e?void 0:e.toUpperCase().replace(/\s+/g,"_")]||s.ERRORE;return{badge:"".concat(a.bg," ").concat(a.text," rounded-full px-3 py-1 text-xs font-medium"),text:a.text,bg:a.bg,border:a.border,hex:a.hex}},r=()=>({button:"inline-flex items-center justify-center gap-1 px-3 py-2 text-xs font-medium bg-white text-gray-800 border-b-2 border-[#315cfd] rounded-full cursor-pointer min-w-[4rem] h-8 transition-colors duration-300 hover:border-2 hover:bg-[#315cfd] hover:text-white",text:"text-gray-800",border:"border-b-[#315cfd]",hover:"hover:bg-[#315cfd] hover:text-white hover:border-2"}),o=()=>r(),c=()=>({text:"inline-flex items-center gap-1 px-2 py-1 text-xs font-medium ".concat(t.NEUTRAL.text_light),color:t.NEUTRAL.text_light}),d=e=>{let a=l[null==e?void 0:e.toUpperCase().replace(/\s+/g,"_")]||l.ERRORE;return{badge:"".concat(a.bg," ").concat(a.text," ").concat(a.border),button:"".concat(a.bg," ").concat(a.text," ").concat(a.border," ").concat(a.hover),alert:"".concat(a.bg," ").concat(a.text," ").concat(a.border),text:a.text,bg:a.bg,border:a.border,hover:a.hover,hex:a.hex}};t.STATUS.ERROR,t.STATUS.WARNING,t.NEUTRAL,t.NEUTRAL},85127:(e,a,i)=>{"use strict";i.d(a,{A0:()=>n,BF:()=>r,Hj:()=>o,XI:()=>l,nA:()=>d,nd:()=>c});var t=i(95155);i(12115);var s=i(59434);function l(e){let{className:a,...i}=e;return(0,t.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,t.jsx)("table",{"data-slot":"table",className:(0,s.cn)("w-full caption-bottom text-sm border-collapse",a),...i})})}function n(e){let{className:a,...i}=e;return(0,t.jsx)("thead",{"data-slot":"table-header",className:(0,s.cn)("[&_tr]:border-b",a),...i})}function r(e){let{className:a,...i}=e;return(0,t.jsx)("tbody",{"data-slot":"table-body",className:(0,s.cn)("[&_tr:last-child]:border-0",a),...i})}function o(e){let{className:a,...i}=e;return(0,t.jsx)("tr",{"data-slot":"table-row",className:(0,s.cn)("data-[state=selected]:bg-muted border-b",a),...i})}function c(e){let{className:a,...i}=e;return(0,t.jsx)("th",{"data-slot":"table-head",className:(0,s.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...i})}function d(e){let{className:a,...i}=e;return(0,t.jsx)("td",{"data-slot":"table-cell",className:(0,s.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...i})}},87481:(e,a,i)=>{"use strict";i.d(a,{dj:()=>x});var t=i(12115);let s=0,l=new Map,n=e=>{if(l.has(e))return;let a=setTimeout(()=>{l.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);l.set(e,a)},r=(e,a)=>{switch(a.type){case"ADD_TOAST":return{...e,toasts:[a.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===a.toast.id?{...e,...a.toast}:e)};case"DISMISS_TOAST":{let{toastId:i}=a;return i?n(i):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===i||void 0===i?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===a.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==a.toastId)}}},o=[],c={toasts:[]};function d(e){c=r(c,e),o.forEach(e=>{e(c)})}function m(e){let{...a}=e,i=(s=(s+1)%Number.MAX_VALUE).toString(),t=()=>d({type:"DISMISS_TOAST",toastId:i});return d({type:"ADD_TOAST",toast:{...a,id:i,open:!0,onOpenChange:e=>{e||t()}}}),{id:i,dismiss:t,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:i}})}}function x(){let[e,a]=(0,t.useState)(c);return(0,t.useEffect)(()=>(o.push(a),()=>{let e=o.indexOf(a);e>-1&&o.splice(e,1)}),[]),{...e,toast:m,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},93304:()=>{}},e=>{var a=a=>e(e.s=a);e.O(0,[1161,3455,3464,9816,9384,5171,6987,3122,7093,283,5768,4333,8441,1684,7358],()=>a(3469)),_N_E=e.O()}]);