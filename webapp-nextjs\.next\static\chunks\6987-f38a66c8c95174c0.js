"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6987],{15452:(e,n,t)=>{t.d(n,{UC:()=>et,VY:()=>eo,ZL:()=>ee,bL:()=>X,bm:()=>el,hE:()=>er,hJ:()=>en,l9:()=>$});var r=t(12115),o=t(85185),l=t(6101),i=t(46081),a=t(61285),s=t(5845),u=t(19178),d=t(25519),c=t(34378),f=t(28905),p=t(63655),m=t(92293),g=t(93795),v=t(38168),N=t(99708),y=t(95155),D="Dialog",[h,O]=(0,i.A)(D),[R,j]=h(D),w=e=>{let{__scopeDialog:n,children:t,open:o,defaultOpen:l,onOpenChange:i,modal:u=!0}=e,d=r.useRef(null),c=r.useRef(null),[f,p]=(0,s.i)({prop:o,defaultProp:null!=l&&l,onChange:i,caller:D});return(0,y.jsx)(R,{scope:n,triggerRef:d,contentRef:c,contentId:(0,a.B)(),titleId:(0,a.B)(),descriptionId:(0,a.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:u,children:t})};w.displayName=D;var C="DialogTrigger",I=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,i=j(C,t),a=(0,l.s)(n,i.triggerRef);return(0,y.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":H(i.open),...r,ref:a,onClick:(0,o.m)(e.onClick,i.onOpenToggle)})});I.displayName=C;var b="DialogPortal",[x,E]=h(b,{forceMount:void 0}),A=e=>{let{__scopeDialog:n,forceMount:t,children:o,container:l}=e,i=j(b,n);return(0,y.jsx)(x,{scope:n,forceMount:t,children:r.Children.map(o,e=>(0,y.jsx)(f.C,{present:t||i.open,children:(0,y.jsx)(c.Z,{asChild:!0,container:l,children:e})}))})};A.displayName=b;var T="DialogOverlay",_=r.forwardRef((e,n)=>{let t=E(T,e.__scopeDialog),{forceMount:r=t.forceMount,...o}=e,l=j(T,e.__scopeDialog);return l.modal?(0,y.jsx)(f.C,{present:r||l.open,children:(0,y.jsx)(F,{...o,ref:n})}):null});_.displayName=T;var M=(0,N.TL)("DialogOverlay.RemoveScroll"),F=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,o=j(T,t);return(0,y.jsx)(g.A,{as:M,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(p.sG.div,{"data-state":H(o.open),...r,ref:n,style:{pointerEvents:"auto",...r.style}})})}),P="DialogContent",U=r.forwardRef((e,n)=>{let t=E(P,e.__scopeDialog),{forceMount:r=t.forceMount,...o}=e,l=j(P,e.__scopeDialog);return(0,y.jsx)(f.C,{present:r||l.open,children:l.modal?(0,y.jsx)(k,{...o,ref:n}):(0,y.jsx)(L,{...o,ref:n})})});U.displayName=P;var k=r.forwardRef((e,n)=>{let t=j(P,e.__scopeDialog),i=r.useRef(null),a=(0,l.s)(n,t.contentRef,i);return r.useEffect(()=>{let e=i.current;if(e)return(0,v.Eq)(e)},[]),(0,y.jsx)(S,{...e,ref:a,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var n;e.preventDefault(),null==(n=t.triggerRef.current)||n.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let n=e.detail.originalEvent,t=0===n.button&&!0===n.ctrlKey;(2===n.button||t)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),L=r.forwardRef((e,n)=>{let t=j(P,e.__scopeDialog),o=r.useRef(!1),l=r.useRef(!1);return(0,y.jsx)(S,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:n=>{var r,i;null==(r=e.onCloseAutoFocus)||r.call(e,n),n.defaultPrevented||(o.current||null==(i=t.triggerRef.current)||i.focus(),n.preventDefault()),o.current=!1,l.current=!1},onInteractOutside:n=>{var r,i;null==(r=e.onInteractOutside)||r.call(e,n),n.defaultPrevented||(o.current=!0,"pointerdown"===n.detail.originalEvent.type&&(l.current=!0));let a=n.target;(null==(i=t.triggerRef.current)?void 0:i.contains(a))&&n.preventDefault(),"focusin"===n.detail.originalEvent.type&&l.current&&n.preventDefault()}})}),S=r.forwardRef((e,n)=>{let{__scopeDialog:t,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:a,...s}=e,c=j(P,t),f=r.useRef(null),p=(0,l.s)(n,f);return(0,m.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(d.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:a,children:(0,y.jsx)(u.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":H(c.open),...s,ref:p,onDismiss:()=>c.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(z,{titleId:c.titleId}),(0,y.jsx)(Q,{contentRef:f,descriptionId:c.descriptionId})]})]})}),W="DialogTitle",B=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,o=j(W,t);return(0,y.jsx)(p.sG.h2,{id:o.titleId,...r,ref:n})});B.displayName=W;var G="DialogDescription",q=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,o=j(G,t);return(0,y.jsx)(p.sG.p,{id:o.descriptionId,...r,ref:n})});q.displayName=G;var V="DialogClose",Z=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,l=j(V,t);return(0,y.jsx)(p.sG.button,{type:"button",...r,ref:n,onClick:(0,o.m)(e.onClick,()=>l.onOpenChange(!1))})});function H(e){return e?"open":"closed"}Z.displayName=V;var J="DialogTitleWarning",[K,Y]=(0,i.q)(J,{contentName:P,titleName:W,docsSlug:"dialog"}),z=e=>{let{titleId:n}=e,t=Y(J),o="`".concat(t.contentName,"` requires a `").concat(t.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(t.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(t.docsSlug);return r.useEffect(()=>{n&&(document.getElementById(n)||console.error(o))},[o,n]),null},Q=e=>{let{contentRef:n,descriptionId:t}=e,o=Y("DialogDescriptionWarning"),l="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null==(e=n.current)?void 0:e.getAttribute("aria-describedby");t&&r&&(document.getElementById(t)||console.warn(l))},[l,n,t]),null},X=w,$=I,ee=A,en=_,et=U,er=B,eo=q,el=Z},28905:(e,n,t)=>{t.d(n,{C:()=>i});var r=t(12115),o=t(6101),l=t(52712),i=e=>{let{present:n,children:t}=e,i=function(e){var n,t;let[o,i]=r.useState(),s=r.useRef(null),u=r.useRef(e),d=r.useRef("none"),[c,f]=(n=e?"mounted":"unmounted",t={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,n)=>{let r=t[e][n];return null!=r?r:e},n));return r.useEffect(()=>{let e=a(s.current);d.current="mounted"===c?e:"none"},[c]),(0,l.N)(()=>{let n=s.current,t=u.current;if(t!==e){let r=d.current,o=a(n);e?f("MOUNT"):"none"===o||(null==n?void 0:n.display)==="none"?f("UNMOUNT"):t&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),u.current=e}},[e,f]),(0,l.N)(()=>{if(o){var e;let n,t=null!=(e=o.ownerDocument.defaultView)?e:window,r=e=>{let r=a(s.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!u.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",n=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},l=e=>{e.target===o&&(d.current=a(s.current))};return o.addEventListener("animationstart",l),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{t.clearTimeout(n),o.removeEventListener("animationstart",l),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:r.useCallback(e=>{s.current=e?getComputedStyle(e):null,i(e)},[])}}(n),s="function"==typeof t?t({present:i.isPresent}):r.Children.only(t),u=(0,o.s)(i.ref,function(e){var n,t;let r=null==(n=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:n.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(t=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:t.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(s));return"function"==typeof t||i.isPresent?r.cloneElement(s,{ref:u}):null};function a(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},47924:(e,n,t)=>{t.d(n,{A:()=>r});let r=(0,t(19946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}}]);