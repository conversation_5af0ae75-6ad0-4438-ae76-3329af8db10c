(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3698],{7958:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>ei});var t=s(95155),r=s(12115);s(40975);var i=s(66695),l=s(59434),n=s(51154);let d=r.forwardRef((e,a)=>{let{className:s,variant:r="primary",size:i="md",loading:d=!1,glow:o=!1,icon:c,children:m,disabled:x,...u}=e,h=x||d;return(0,t.jsxs)("button",{className:(0,l.cn)("relative overflow-hidden font-medium rounded-lg transition-all duration-300 ease-in-out transform focus:outline-none",{primary:"btn-primary",secondary:"btn-secondary",success:"btn-success",danger:"btn-danger",outline:"btn-outline",quick:"btn-quick"}[r],{sm:"btn-sm",md:"px-6 py-3",lg:"btn-lg"}[i],o&&"quick"!==r&&"btn-glow",h&&"opacity-50 cursor-not-allowed hover:shadow-none",s),disabled:h,ref:a,...u,children:[(0,t.jsx)("span",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent translate-x-[-100%] transition-transform duration-700 ease-in-out group-hover:translate-x-[100%]"}),(0,t.jsxs)("span",{className:"relative flex items-center justify-center gap-2",children:[d?(0,t.jsx)(n.A,{className:"h-4 w-4 animate-spin btn-icon"}):c?(0,t.jsx)("span",{className:"btn-icon",children:c}):null,m]})]})});d.displayName="AnimatedButton";let o=e=>(0,t.jsx)(d,{variant:"primary",...e}),c=e=>(0,t.jsx)(d,{variant:"secondary",...e}),m=e=>(0,t.jsx)(d,{variant:"danger",...e});var x=s(1243),u=s(54416),h=s(13717),p=s(14186),g=s(40646),b=s(62525),v=s(30285);function j(e){let{user:a,onEdit:s,onToggleStatus:i,onDelete:l}=e,[n,d]=(0,r.useState)(!1);return n?(0,t.jsxs)("div",{className:"flex items-center gap-1 bg-red-50 border border-red-200 rounded-md p-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(x.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)("span",{className:"text-xs text-red-700 font-medium",children:"Eliminare?"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-1 ml-2",children:[(0,t.jsx)(v.$,{size:"sm",variant:"destructive",onClick:e=>{e.preventDefault(),e.stopPropagation(),d(!1),l()},className:"h-6 px-2 text-xs",children:"S\xec"}),(0,t.jsx)(v.$,{size:"sm",variant:"outline",onClick:e=>{e.preventDefault(),e.stopPropagation(),d(!1)},className:"h-6 px-2 text-xs",children:(0,t.jsx)(u.A,{className:"h-3 w-3"})})]})]}):(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),s()},type:"button",className:"p-1.5 rounded-md hover:bg-blue-50 transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1","aria-label":"Modifica utente ".concat(a.username),children:(0,t.jsx)(h.A,{className:"h-4 w-4 text-blue-600 hover:text-blue-700"})}),(0,t.jsx)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-slate-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10",children:"Modifica utente"})]}),(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),(!a.abilitato||window.confirm("Sei sicuro di voler disabilitare l'utente \"".concat(a.username,"\"?\n\nL'utente non potr\xe0 pi\xf9 accedere al sistema.")))&&i()},disabled:"owner"===a.ruolo,type:"button",className:"p-1.5 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 ".concat("owner"===a.ruolo?"opacity-50 cursor-not-allowed":"hover:scale-105 hover:bg-slate-50 focus:ring-slate-500"),"aria-label":a.abilitato?"Disabilita utente ".concat(a.username):"Abilita utente ".concat(a.username),children:a.abilitato?(0,t.jsx)(p.A,{className:"h-4 w-4 text-red-500 hover:text-red-600"}):(0,t.jsx)(g.A,{className:"h-4 w-4 text-green-500 hover:text-green-600"})}),"owner"!==a.ruolo&&(0,t.jsx)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-slate-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10",children:a.abilitato?"Disabilita utente":"Abilita utente"})]}),(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),d(!0)},disabled:"owner"===a.ruolo,type:"button",className:"p-1.5 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 ".concat("owner"===a.ruolo?"opacity-50 cursor-not-allowed":"hover:scale-105 hover:bg-red-50 focus:ring-red-500"),"aria-label":"Elimina utente ".concat(a.username),children:(0,t.jsx)(b.A,{className:"h-4 w-4 text-red-500 hover:text-red-600"})}),"owner"!==a.ruolo&&(0,t.jsx)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-slate-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10",children:"Elimina utente"})]})]})}var f=s(26126),N=s(62523),w=s(63743),y=s(85127),A=s(17313),z=s(40283),S=s(35695),E=s(25731),T=s(85057),C=s(59409),R=s(47262),_=s(61610),k=s(85339),U=s(71007),I=s(75525),D=s(78749),L=s(92657),V=s(23227),O=s(28883),P=s(4516),G=s(381),Z=s(81284),B=s(4229);function F(e){let{user:a,onSave:s,onCancel:l}=e,[n,d]=(0,r.useState)({username:"",password:"",ruolo:"user",data_scadenza:"",abilitato:!0,ragione_sociale:"",indirizzo:"",nazione:"",email:"",vat:"",referente_aziendale:""}),[m,x]=(0,r.useState)({}),[b,j]=(0,r.useState)(!1),[w,y]=(0,r.useState)(""),[A,z]=(0,r.useState)(""),[S,F]=(0,r.useState)(!1),[M,X]=(0,r.useState)(0),[$,W]=(0,r.useState)({}),[J,q]=(0,r.useState)(!1),H=e=>{let a=0;return e.length>=8&&(a+=25),/[a-z]/.test(e)&&(a+=25),/[A-Z]/.test(e)&&(a+=25),/[0-9]/.test(e)&&(a+=25),/[^A-Za-z0-9]/.test(e)&&(a+=25),Math.min(a,100)},Q=(e,s)=>{let t="pending";switch(e){case"username":t=s&&s.length>=3?"valid":"invalid";break;case"password":t=a?!s||s.length>=8?"valid":"invalid":s&&s.length>=8?"valid":"invalid";break;case"ragione_sociale":t=s&&s.length>=2?"valid":"invalid";break;case"email":t=s?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(s)?"valid":"invalid":"valid";break;default:t="valid"}return W(a=>({...a,[e]:t})),t};(0,r.useEffect)(()=>{if(a){let e={username:a.username||"",password:"",ruolo:a.ruolo||"user",data_scadenza:a.data_scadenza?a.data_scadenza.split("T")[0]:"",abilitato:void 0===a.abilitato||a.abilitato,ragione_sociale:a.ragione_sociale||"",indirizzo:a.indirizzo||"",nazione:a.nazione||"",email:a.email||"",vat:a.vat||"",referente_aziendale:a.referente_aziendale||""};d(e),Object.entries(e).forEach(e=>{let[a,s]=e;Q(a,s)})}},[a]),(0,r.useEffect)(()=>{q((a?["username","ragione_sociale"]:["username","password","ragione_sociale"]).every(e=>"valid"===$[e]))},[$,a]);let Y=(e,a)=>{d(s=>({...s,[e]:a})),Q(e,a),"password"===e&&X(H(a)),m[e]&&x(a=>({...a,[e]:""})),A&&z(""),w&&y("")},K=()=>{let e=(0,_.GN)({username:n.username,password:a?void 0:n.password,ragione_sociale:n.ragione_sociale,email:n.email,vat:n.vat,indirizzo:n.indirizzo,nazione:n.nazione,referente_aziendale:n.referente_aziendale});return x(e.errors),e.isValid},ee=async e=>{e.preventDefault();let t="user-form-".concat((null==a?void 0:a.id_utente)||"new","-").concat(Date.now());if(!(0,_.Eb)(t,5,6e4))return void y("Troppi tentativi. Riprova tra un minuto.");if(K()){j(!0),y("");try{let e,t={...n};a||(t.ruolo="user"),a&&!t.password.trim()&&delete t.password,t.data_scadenza&&(t.data_scadenza=t.data_scadenza),e=a?await E.dG.updateUser(a.id_utente,t):await E.dG.createUser(t),z(a?"Utente aggiornato con successo!":"Nuovo utente creato con successo!"),setTimeout(()=>{s(e)},1500)}catch(e){var r,i;y((null==(i=e.response)||null==(r=i.data)?void 0:r.detail)||e.message||"Errore durante il salvataggio dell'utente")}finally{j(!1)}}},ea=e=>{let{status:a}=e;return"valid"===a?(0,t.jsx)(g.A,{className:"h-4 w-4 text-green-500"}):"invalid"===a?(0,t.jsx)(k.A,{className:"h-4 w-4 text-red-500"}):null};return(0,t.jsxs)(i.Zp,{className:"shadow-lg",children:[(0,t.jsx)(i.aR,{className:"bg-gradient-to-r from-blue-50 to-indigo-50 border-b",children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:a?(0,t.jsx)(h.A,{className:"h-5 w-5 text-blue-600"}):(0,t.jsx)(U.A,{className:"h-5 w-5 text-blue-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)(i.ZB,{className:"text-xl",children:a?"Modifica Utente: ".concat(a.username):"Crea Nuovo Utente Standard"}),(0,t.jsx)(i.BT,{children:a?"Aggiorna le informazioni dell'utente esistente":"Inserisci i dati per creare un nuovo utente nel sistema"})]})]})}),(0,t.jsxs)(i.Wu,{className:"p-6",children:[w&&(0,t.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6 flex items-center gap-3 animate-in slide-in-from-top-2",children:[(0,t.jsx)(k.A,{className:"h-5 w-5 text-red-600 flex-shrink-0"}),(0,t.jsx)("p",{className:"text-red-600",children:w})]}),A&&(0,t.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-6 flex items-center gap-3 animate-in slide-in-from-top-2",children:[(0,t.jsx)(g.A,{className:"h-5 w-5 text-green-600 flex-shrink-0"}),(0,t.jsx)("p",{className:"text-green-600",children:A})]}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm text-slate-600 mb-2",children:[(0,t.jsx)("span",{children:"Completamento form"}),(0,t.jsx)("span",{children:J?"✓ Completo":"In corso..."})]}),(0,t.jsx)("div",{className:"w-full bg-slate-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"h-2 rounded-full transition-all duration-500 ".concat(J?"bg-green-500":"bg-blue-500"),style:{width:"".concat(J?100:60,"%")}})})]}),(0,t.jsxs)("form",{onSubmit:ee,className:"space-y-8",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 pb-2 border-b border-slate-200",children:[(0,t.jsx)(I.A,{className:"h-5 w-5 text-blue-600"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-slate-900",children:"Credenziali di Accesso"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(T.J,{htmlFor:"username",className:"flex items-center gap-2",children:["Username *",(0,t.jsx)(ea,{status:$.username||"pending"})]}),(0,t.jsx)("div",{className:"relative",children:(0,t.jsx)(N.p,{id:"username",value:n.username,onChange:e=>Y("username",e.target.value),disabled:b,className:"".concat(m.username?"border-red-500":"valid"===$.username?"border-green-500":""," transition-colors duration-200"),placeholder:"Inserisci username univoco"})}),m.username&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(k.A,{className:"h-3 w-3"}),m.username]}),"valid"===$.username&&!m.username&&(0,t.jsxs)("p",{className:"text-sm text-green-600 flex items-center gap-1",children:[(0,t.jsx)(g.A,{className:"h-3 w-3"}),"Username valido"]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(T.J,{htmlFor:"password",className:"flex items-center gap-2",children:[a?"Nuova Password (lascia vuoto per non modificare)":"Password *",(0,t.jsx)(ea,{status:$.password||"pending"})]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(N.p,{id:"password",type:S?"text":"password",value:n.password,onChange:e=>Y("password",e.target.value),disabled:b,className:"".concat(m.password?"border-red-500":"valid"===$.password?"border-green-500":""," pr-10 transition-colors duration-200"),placeholder:a?"Lascia vuoto per mantenere la password attuale":"Inserisci password sicura"}),(0,t.jsx)(v.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>F(!S),disabled:b,children:S?(0,t.jsx)(D.A,{className:"h-4 w-4 text-gray-400"}):(0,t.jsx)(L.A,{className:"h-4 w-4 text-gray-400"})})]}),m.password&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(k.A,{className:"h-3 w-3"}),m.password]}),(0,t.jsx)(()=>n.password?(0,t.jsxs)("div",{className:"mt-2 space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,t.jsx)("span",{className:"text-slate-600",children:"Forza password:"}),(0,t.jsx)("span",{className:"font-medium ".concat(M<50?"text-red-600":M<75?"text-yellow-600":"text-green-600"),children:M<25?"Molto debole":M<50?"Debole":M<75?"Media":M<100?"Forte":"Molto forte"})]}),(0,t.jsx)("div",{className:"w-full bg-slate-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"h-2 rounded-full transition-all duration-300 ".concat(M<50?"bg-red-500":M<75?"bg-yellow-500":"bg-green-500"),style:{width:"".concat(M,"%")}})})]}):null,{})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 pb-2 border-b border-slate-200",children:[(0,t.jsx)(V.A,{className:"h-5 w-5 text-blue-600"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-slate-900",children:"Informazioni Aziendali"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(T.J,{htmlFor:"ragione_sociale",className:"flex items-center gap-2",children:["Ragione Sociale *",(0,t.jsx)(ea,{status:$.ragione_sociale||"pending"})]}),(0,t.jsx)(N.p,{id:"ragione_sociale",value:n.ragione_sociale,onChange:e=>Y("ragione_sociale",e.target.value),disabled:b,className:"".concat(m.ragione_sociale?"border-red-500":"valid"===$.ragione_sociale?"border-green-500":""," transition-colors duration-200"),placeholder:"Nome dell'azienda o organizzazione"}),m.ragione_sociale&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(k.A,{className:"h-3 w-3"}),m.ragione_sociale]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(T.J,{htmlFor:"email",className:"flex items-center gap-2",children:[(0,t.jsx)(O.A,{className:"h-4 w-4"}),"Email",(0,t.jsx)(ea,{status:$.email||"pending"})]}),(0,t.jsx)(N.p,{id:"email",type:"email",value:n.email,onChange:e=>Y("email",e.target.value),disabled:b,className:"".concat(m.email?"border-red-500":"valid"===$.email?"border-green-500":""," transition-colors duration-200"),placeholder:"<EMAIL>"}),m.email&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(k.A,{className:"h-3 w-3"}),m.email]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(T.J,{htmlFor:"indirizzo",className:"flex items-center gap-2",children:[(0,t.jsx)(P.A,{className:"h-4 w-4"}),"Indirizzo"]}),(0,t.jsx)(N.p,{id:"indirizzo",value:n.indirizzo,onChange:e=>Y("indirizzo",e.target.value),disabled:b,placeholder:"Via, numero civico, citt\xe0",className:"transition-colors duration-200"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(T.J,{htmlFor:"nazione",children:"Nazione"}),(0,t.jsx)(N.p,{id:"nazione",value:n.nazione,onChange:e=>Y("nazione",e.target.value),disabled:b,placeholder:"Italia",className:"transition-colors duration-200"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(T.J,{htmlFor:"vat",children:"Partita IVA"}),(0,t.jsx)(N.p,{id:"vat",value:n.vat,onChange:e=>Y("vat",e.target.value),disabled:b,placeholder:"*************",className:"transition-colors duration-200"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(T.J,{htmlFor:"referente_aziendale",children:"Referente Aziendale"}),(0,t.jsx)(N.p,{id:"referente_aziendale",value:n.referente_aziendale,onChange:e=>Y("referente_aziendale",e.target.value),disabled:b,placeholder:"Nome e cognome del referente",className:"transition-colors duration-200"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 pb-2 border-b border-slate-200",children:[(0,t.jsx)(G.A,{className:"h-5 w-5 text-blue-600"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-slate-900",children:"Configurazioni Account"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(T.J,{htmlFor:"data_scadenza",className:"flex items-center gap-2",children:[(0,t.jsx)(p.A,{className:"h-4 w-4"}),"Data Scadenza"]}),(0,t.jsx)(N.p,{id:"data_scadenza",type:"date",value:n.data_scadenza,onChange:e=>Y("data_scadenza",e.target.value),disabled:b,className:"transition-colors duration-200"}),(0,t.jsx)("p",{className:"text-xs text-slate-500",children:"Lascia vuoto per account senza scadenza"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(T.J,{htmlFor:"ruolo",children:"Ruolo Utente"}),a?(0,t.jsxs)(C.l6,{value:n.ruolo,onValueChange:e=>Y("ruolo",e),disabled:b,children:[(0,t.jsx)(C.bq,{className:"transition-colors duration-200",children:(0,t.jsx)(C.yv,{})}),(0,t.jsxs)(C.gC,{children:[(0,t.jsx)(C.eb,{value:"user",children:"User Standard"}),(0,t.jsx)(C.eb,{value:"cantieri_user",children:"Cantieri User"})]})]}):(0,t.jsxs)("div",{className:"px-3 py-2 bg-blue-50 border border-blue-200 rounded-md text-sm text-blue-700 flex items-center gap-2",children:[(0,t.jsx)(f.E,{variant:"outline",className:"bg-blue-100 text-blue-700",children:"User Standard"}),(0,t.jsx)("span",{children:"Ruolo predefinito per nuovi utenti"})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3 p-4 bg-slate-50 rounded-lg",children:[(0,t.jsx)(R.S,{id:"abilitato",checked:n.abilitato,onCheckedChange:e=>Y("abilitato",e),disabled:b||a&&"owner"===a.ruolo}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)(T.J,{htmlFor:"abilitato",className:"font-medium",children:"Account Abilitato"}),(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"L'utente pu\xf2 accedere al sistema e utilizzare le funzionalit\xe0"})]}),n.abilitato?(0,t.jsx)(f.E,{className:"bg-green-100 text-green-700",children:"Attivo"}):(0,t.jsx)(f.E,{variant:"outline",className:"bg-red-100 text-red-700",children:"Disabilitato"})]})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-center gap-4 pt-8 border-t border-slate-200",children:[(0,t.jsx)("div",{className:"text-sm text-slate-600",children:J?(0,t.jsxs)("span",{className:"flex items-center gap-1 text-green-600",children:[(0,t.jsx)(g.A,{className:"h-4 w-4"}),"Form completato correttamente"]}):(0,t.jsxs)("span",{className:"flex items-center gap-1",children:[(0,t.jsx)(Z.A,{className:"h-4 w-4"}),"Completa i campi obbligatori per continuare"]})}),(0,t.jsxs)("div",{className:"flex space-x-4",children:[(0,t.jsx)(c,{type:"button",onClick:l,disabled:b,icon:(0,t.jsx)(u.A,{className:"h-4 w-4"}),className:"min-w-[120px]",children:"Annulla"}),(0,t.jsx)(o,{type:"submit",loading:b,disabled:!J,icon:(0,t.jsx)(B.A,{className:"h-4 w-4"}),glow:J,className:"min-w-[120px]",children:b?"Salvataggio...":a?"Aggiorna Utente":"Crea Utente"})]})]})]})]})]})}var M=s(54213),X=s(53904);function $(){let[e,a]=(0,r.useState)(null),[s,l]=(0,r.useState)(!1),[d,c]=(0,r.useState)(""),m=async()=>{l(!0),c("");try{let e=await E.dG.getDatabaseData();a(e)}catch(a){var e,s;c((null==(s=a.response)||null==(e=s.data)?void 0:e.detail)||a.message||"Errore durante il caricamento dei dati del database")}finally{l(!1)}};(0,r.useEffect)(()=>{m()},[]);let x=(e,a,s)=>{if(!a||0===a.length)return(0,t.jsxs)("div",{className:"text-center py-4 text-slate-500 border rounded-lg",children:["Nessun dato disponibile per ",s]});let r=Object.keys(a[0]);return(0,t.jsxs)("div",{className:"border rounded-lg overflow-hidden mb-6",children:[(0,t.jsxs)("div",{className:"bg-slate-100 px-4 py-3 border-b",children:[(0,t.jsx)("h4",{className:"font-medium text-slate-900",children:s}),(0,t.jsxs)("p",{className:"text-sm text-slate-600",children:["Totale record: ",a.length]})]}),(0,t.jsx)("div",{className:"overflow-x-auto max-h-96",children:(0,t.jsxs)(y.XI,{children:[(0,t.jsx)(y.A0,{className:"sticky top-0 bg-slate-50",children:(0,t.jsx)(y.Hj,{children:r.map(e=>(0,t.jsx)(y.nd,{className:"font-medium",children:e},e))})}),(0,t.jsx)(y.BF,{children:a.map((e,a)=>(0,t.jsx)(y.Hj,{children:r.map(a=>(0,t.jsx)(y.nA,{className:"font-mono text-sm",children:null!==e[a]&&void 0!==e[a]?String(e[a]):(0,t.jsx)("span",{className:"text-slate-400",children:"NULL"})},a))},a))})]})})]})},u=[{key:"users",title:"Utenti",description:"Tutti gli utenti del sistema"},{key:"cantieri",title:"Cantieri",description:"Tutti i cantieri/progetti"},{key:"cavi",title:"Cavi",description:"Tutti i cavi installati"},{key:"parco_cavi",title:"Bobine",description:"Tutte le bobine del parco cavi"},{key:"strumenti_certificati",title:"Strumenti",description:"Strumenti certificati"},{key:"certificazioni_cavi",title:"Certificazioni",description:"Certificazioni dei cavi"}];return(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(M.A,{className:"h-5 w-5"}),"Visualizzazione Database Raw"]}),(0,t.jsx)(o,{size:"sm",onClick:m,loading:s,icon:(0,t.jsx)(X.A,{className:"h-4 w-4"}),children:"Aggiorna"})]})}),(0,t.jsxs)(i.Wu,{className:"space-y-6",children:[(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)(L.A,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-blue-900",children:"Visualizzazione Raw del Database"}),(0,t.jsx)("p",{className:"text-sm text-blue-700 mt-1",children:"Questa sezione mostra i dati grezzi delle tabelle del database. Utile per debugging e analisi dei dati."})]})]})}),s?(0,t.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,t.jsx)(n.A,{className:"h-8 w-8 animate-spin mr-3"}),(0,t.jsx)("span",{className:"text-lg",children:"Caricamento dati database..."})]}):d?(0,t.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6",children:[(0,t.jsx)("p",{className:"text-red-600 font-medium",children:"Errore durante il caricamento:"}),(0,t.jsx)("p",{className:"text-red-600",children:d})]}):e?(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,t.jsx)("p",{className:"text-sm text-blue-700",children:"Visualizzazione completa di tutte le tabelle del database. I dati sono mostrati in formato raw per debugging e analisi."})}),u.map(a=>e[a.key]&&(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-slate-900",children:a.title}),(0,t.jsx)("p",{className:"text-sm text-slate-600",children:a.description})]}),x(a.key,e[a.key],a.title)]},a.key)),(0,t.jsxs)("div",{className:"bg-slate-50 border border-slate-200 rounded-lg p-4",children:[(0,t.jsx)("h4",{className:"font-medium text-slate-900 mb-2",children:"Riepilogo Database"}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4 text-sm",children:u.map(a=>(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsxs)("span",{className:"text-slate-600",children:[a.title,":"]}),(0,t.jsxs)("span",{className:"font-medium",children:[e[a.key]?e[a.key].length:0," record"]})]},a.key))})]})]}):(0,t.jsx)("div",{className:"text-center py-12 text-slate-500",children:"Nessun dato disponibile"})]})]})}var W=s(40133);function J(){let{user:e}=(0,z.A)(),[a,s]=(0,r.useState)(""),[l,n]=(0,r.useState)(!1),[d,o]=(0,r.useState)(""),[u,h]=(0,r.useState)(!1),[g,j]=(0,r.useState)(!1),[f,w]=(0,r.useState)(""),[y,A]=(0,r.useState)(""),[S,C]=(0,r.useState)(10),[_,k]=(0,r.useState)(!1),[U,V]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e;return _&&S>0?e=setInterval(()=>{C(e=>e-1)},1e3):0===S&&(V(!0),k(!1)),()=>clearInterval(e)},[_,S]);let O=async()=>{if(!U)return void w("Devi completare il countdown di sicurezza");j(!0),w(""),A("");try{if(!d.trim())throw Error("Password amministratore richiesta");await E.dG.resetDatabase(),A("Database resettato con successo! Tutti i dati sono stati eliminati."),s(""),n(!1),o(""),V(!1),k(!1),C(10)}catch(s){var e,a;w((null==(a=s.response)||null==(e=a.data)?void 0:e.detail)||s.message||"Errore durante il reset del database")}finally{j(!1)}},P="RESET DATABASE"===a&&l&&d.trim()&&!g&&!_,G=U&&!g;return(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{children:(0,t.jsxs)(i.ZB,{className:"flex items-center gap-2 text-red-600",children:[(0,t.jsx)(W.A,{className:"h-5 w-5"}),"Reset Database"]})}),(0,t.jsxs)(i.Wu,{className:"space-y-6",children:[(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)(x.A,{className:"h-6 w-6 text-red-600 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-bold text-red-900 text-lg",children:"⚠️ ATTENZIONE - OPERAZIONE IRREVERSIBILE"}),(0,t.jsxs)("div",{className:"text-red-700 mt-2 space-y-2",children:[(0,t.jsx)("p",{className:"font-medium",children:"Questa operazione eliminer\xe0 PERMANENTEMENTE tutti i dati dal database:"}),(0,t.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-sm",children:[(0,t.jsx)("li",{children:"Tutti gli utenti (eccetto l'amministratore principale)"}),(0,t.jsx)("li",{children:"Tutti i cantieri e i progetti"}),(0,t.jsx)("li",{children:"Tutti i cavi installati"}),(0,t.jsx)("li",{children:"Tutte le bobine del parco cavi"}),(0,t.jsx)("li",{children:"Tutti i comandi e le certificazioni"}),(0,t.jsx)("li",{children:"Tutti i report e i dati di produttivit\xe0"})]}),(0,t.jsx)("p",{className:"font-bold text-red-800 mt-3",children:"NON \xc8 POSSIBILE RECUPERARE I DATI DOPO IL RESET!"})]})]})]})}),f&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,t.jsx)("p",{className:"text-red-600",children:f})}),y&&(0,t.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,t.jsx)("p",{className:"text-green-600",children:y})}),(0,t.jsxs)("div",{className:"space-y-4 border-t pt-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-slate-900 mb-4",children:"Conferma Reset Database"}),(0,t.jsx)("p",{className:"text-sm text-slate-600 mb-4",children:"Per procedere con il reset, devi confermare l'operazione seguendo questi passaggi:"})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(T.J,{htmlFor:"confirm-text",className:"text-sm font-medium",children:["1. Digita esattamente: ",(0,t.jsx)("code",{className:"bg-slate-100 px-2 py-1 rounded text-red-600 font-bold",children:"RESET DATABASE"})]}),(0,t.jsx)(N.p,{id:"confirm-text",value:a,onChange:e=>s(e.target.value),placeholder:"Digita: RESET DATABASE",disabled:g||_,className:"RESET DATABASE"===a?"border-green-500":""})]}),(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)(R.S,{id:"confirm-checkbox",checked:l,onCheckedChange:n,disabled:g||_}),(0,t.jsx)(T.J,{htmlFor:"confirm-checkbox",className:"text-sm leading-relaxed",children:"2. Confermo di aver compreso che questa operazione eliminer\xe0 TUTTI i dati dal database in modo PERMANENTE e IRREVERSIBILE. Ho effettuato un backup se necessario."})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(T.J,{htmlFor:"admin-password",className:"text-sm font-medium flex items-center gap-2",children:[(0,t.jsx)(I.A,{className:"h-4 w-4 text-blue-600"}),"3. Inserisci la tua password di amministratore per confermare l'identit\xe0"]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(N.p,{id:"admin-password",type:u?"text":"password",value:d,onChange:e=>o(e.target.value),placeholder:"Password amministratore",disabled:g||_,className:d.trim()?"border-green-500":""}),(0,t.jsx)(v.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0",onClick:()=>h(!u),disabled:g||_,children:u?(0,t.jsx)(D.A,{className:"h-4 w-4"}):(0,t.jsx)(L.A,{className:"h-4 w-4"})})]})]})]}),(0,t.jsxs)("div",{className:"bg-slate-50 border border-slate-200 rounded-lg p-4",children:[(0,t.jsx)("h5",{className:"font-medium text-slate-900 mb-3",children:"Stato Conferma:"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full ".concat("RESET DATABASE"===a?"bg-green-500":"bg-red-500")}),(0,t.jsxs)("span",{children:["Testo di conferma: ","RESET DATABASE"===a?"✓ Corretto":"✗ Richiesto"]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(l?"bg-green-500":"bg-red-500")}),(0,t.jsxs)("span",{children:["Checkbox confermata: ",l?"✓ S\xec":"✗ Richiesta"]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(d.trim()?"bg-green-500":"bg-red-500")}),(0,t.jsxs)("span",{children:["Password amministratore: ",d.trim()?"✓ Inserita":"✗ Richiesta"]})]}),_&&(0,t.jsxs)("div",{className:"flex items-center gap-2 mt-3 p-2 bg-orange-50 border border-orange-200 rounded",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 text-orange-600"}),(0,t.jsxs)("span",{className:"text-orange-700 font-medium",children:["Countdown di sicurezza: ",S," secondi"]})]}),U&&(0,t.jsxs)("div",{className:"flex items-center gap-2 mt-3 p-2 bg-red-50 border border-red-200 rounded",children:[(0,t.jsx)(x.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)("span",{className:"text-red-700 font-medium",children:"⚠️ Pronto per il reset - Ultima possibilit\xe0 di annullare"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[!U&&!_&&(0,t.jsx)(m,{onClick:()=>{"RESET DATABASE"===a&&l&&d.trim()?(k(!0),C(10),V(!1),w("")):w("Completa tutti i passaggi di conferma prima di procedere")},disabled:!P,className:"w-full",size:"lg",icon:(0,t.jsx)(p.A,{className:"h-5 w-5"}),children:"INIZIA COUNTDOWN DI SICUREZZA (10 secondi)"}),_&&(0,t.jsxs)("div",{className:"w-full p-4 bg-orange-50 border-2 border-orange-300 rounded-lg text-center",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[(0,t.jsx)(p.A,{className:"h-6 w-6 text-orange-600 animate-pulse"}),(0,t.jsxs)("span",{className:"text-lg font-bold text-orange-700",children:["Countdown: ",S," secondi"]})]}),(0,t.jsx)("p",{className:"text-sm text-orange-600 mt-2",children:"Il pulsante di reset si attiver\xe0 al termine del countdown"})]}),U&&(0,t.jsx)(m,{onClick:O,disabled:!G,className:"w-full animate-pulse",size:"lg",loading:g,icon:(0,t.jsx)(b.A,{className:"h-5 w-5"}),glow:!0,children:g?"RESET IN CORSO...":"\uD83D\uDEA8 RESET DATABASE - ELIMINA TUTTI I DATI \uD83D\uDEA8"}),!P&&!U&&!_&&(0,t.jsx)("p",{className:"text-center text-sm text-slate-500",children:"Completa tutti i passaggi di conferma per iniziare il countdown"}),U&&(0,t.jsx)(c,{onClick:()=>{V(!1),k(!1),C(10)},className:"w-full",size:"lg",disabled:g,children:"ANNULLA RESET"})]})]}),(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 text-sm",children:[(0,t.jsx)("h5",{className:"font-medium text-blue-900 mb-2",children:"Informazioni Tecniche:"}),(0,t.jsxs)("ul",{className:"text-blue-700 space-y-1",children:[(0,t.jsx)("li",{children:"• Il reset manterr\xe0 la struttura delle tabelle"}),(0,t.jsx)("li",{children:"• L'utente amministratore principale verr\xe0 ricreato"}),(0,t.jsx)("li",{children:"• Le configurazioni di sistema verranno ripristinate ai valori di default"}),(0,t.jsx)("li",{children:"• L'operazione pu\xf2 richiedere alcuni minuti per completarsi"})]})]})]})]})}var q=s(3493),H=s(43332),Q=s(48136),Y=s(57434),K=s(84616);function ee(){let[e,a]=(0,r.useState)("categorie");return(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{children:(0,t.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(q.A,{className:"h-5 w-5"}),"Database Tipologie Cavi"]})}),(0,t.jsxs)(i.Wu,{className:"space-y-6",children:[(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)(q.A,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-blue-900",children:"Database Enciclopedico Tipologie Cavi"}),(0,t.jsx)("p",{className:"text-sm text-blue-700 mt-1",children:"Gestisci il database delle tipologie di cavi organizzato per categorie, produttori, standard e tipologie specifiche. Questo database serve come riferimento per la classificazione e gestione dei cavi nei progetti."})]})]})}),(0,t.jsxs)(A.tU,{value:e,onValueChange:a,className:"w-full",children:[(0,t.jsxs)(A.j7,{className:"grid w-full grid-cols-4",children:[(0,t.jsxs)(A.Xi,{value:"categorie",className:"flex items-center gap-2",children:[(0,t.jsx)(H.A,{className:"h-4 w-4"}),"Categorie"]}),(0,t.jsxs)(A.Xi,{value:"produttori",className:"flex items-center gap-2",children:[(0,t.jsx)(Q.A,{className:"h-4 w-4"}),"Produttori"]}),(0,t.jsxs)(A.Xi,{value:"standard",className:"flex items-center gap-2",children:[(0,t.jsx)(Y.A,{className:"h-4 w-4"}),"Standard"]}),(0,t.jsxs)(A.Xi,{value:"tipologie",className:"flex items-center gap-2",children:[(0,t.jsx)(q.A,{className:"h-4 w-4"}),"Tipologie"]})]}),(0,t.jsxs)(A.av,{value:"categorie",className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"Categorie Cavi"}),(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci le categorie principali di cavi (es. Energia, Controllo, Strumentazione, ecc.)"})]}),(0,t.jsxs)(v.$,{children:[(0,t.jsx)(K.A,{className:"h-4 w-4 mr-2"}),"Nuova Categoria"]})]}),(0,t.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,t.jsx)(H.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,t.jsx)("p",{children:"Gestione categorie cavi - Da implementare"}),(0,t.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile creare, modificare ed eliminare le categorie di cavi"})]})]}),(0,t.jsxs)(A.av,{value:"produttori",className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"Produttori"}),(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci l'elenco dei produttori di cavi (es. Prysmian, Nexans, General Cable, ecc.)"})]}),(0,t.jsxs)(v.$,{children:[(0,t.jsx)(K.A,{className:"h-4 w-4 mr-2"}),"Nuovo Produttore"]})]}),(0,t.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,t.jsx)(Q.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,t.jsx)("p",{children:"Gestione produttori - Da implementare"}),(0,t.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile gestire l'anagrafica dei produttori di cavi"})]})]}),(0,t.jsxs)(A.av,{value:"standard",className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"Standard e Normative"}),(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci gli standard tecnici e le normative (es. CEI, IEC, EN, CENELEC, ecc.)"})]}),(0,t.jsxs)(v.$,{children:[(0,t.jsx)(K.A,{className:"h-4 w-4 mr-2"}),"Nuovo Standard"]})]}),(0,t.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,t.jsx)(Y.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,t.jsx)("p",{children:"Gestione standard - Da implementare"}),(0,t.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile gestire gli standard tecnici e le normative di riferimento"})]})]}),(0,t.jsxs)(A.av,{value:"tipologie",className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"Tipologie Specifiche"}),(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci le tipologie specifiche di cavi con tutte le caratteristiche tecniche"})]}),(0,t.jsxs)(v.$,{children:[(0,t.jsx)(K.A,{className:"h-4 w-4 mr-2"}),"Nuova Tipologia"]})]}),(0,t.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,t.jsx)(q.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,t.jsx)("p",{children:"Gestione tipologie - Da implementare"}),(0,t.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile gestire le tipologie specifiche con caratteristiche tecniche dettagliate"})]})]})]}),(0,t.jsxs)("div",{className:"bg-slate-50 border border-slate-200 rounded-lg p-4",children:[(0,t.jsx)("h5",{className:"font-medium text-slate-900 mb-2",children:"Struttura Database Tipologie:"}),(0,t.jsxs)("div",{className:"text-sm text-slate-600 space-y-1",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Categorie:"})," Classificazione principale (Energia, Controllo, Strumentazione, Dati, ecc.)"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Produttori:"})," Aziende produttrici con informazioni di contatto"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Standard:"})," Normative tecniche di riferimento (CEI, IEC, EN, CENELEC)"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Tipologie:"})," Specifiche tecniche dettagliate per ogni tipo di cavo"]})]})]})]})]})}var ea=s(17580),es=s(12318),et=s(47924),er=s(70306);function ei(){let e=(0,S.useRouter)(),[a,s]=(0,r.useState)("visualizza-utenti"),[l,d]=(0,r.useState)(""),[c,m]=(0,r.useState)([]),[x,u]=(0,r.useState)([]),[p,g]=(0,r.useState)(!0),[b,v]=(0,r.useState)(""),[T,C]=(0,r.useState)(null),[R,_]=(0,r.useState)({open:!1,message:"",severity:"success"}),{user:k,impersonateUser:U}=(0,z.A)();(0,r.useEffect)(()=>{I()},[a]);let I=async()=>{try{if(g(!0),v(""),"visualizza-utenti"===a||"crea-utente"===a||"accedi-come-utente"===a){let e=await E.dG.getUsers();m(e)}else if("cantieri"===a){let e=await E._I.getCantieri();u(e)}}catch(a){var e,s;v((null==(s=a.response)||null==(e=s.data)?void 0:e.detail)||a.message||"Errore durante il caricamento dei dati")}finally{g(!1)}},D=e=>{C(e),s("modifica-utente")},L=async e=>{try{await E.dG.toggleUserStatus(e),I()}catch(e){var a,s;v((null==(s=e.response)||null==(a=s.data)?void 0:a.detail)||"Errore durante la modifica dello stato utente")}},V=async e=>{if(confirm("Sei sicuro di voler eliminare questo utente?"))try{await E.dG.deleteUser(e),I()}catch(e){var a,s;v((null==(s=e.response)||null==(a=s.data)?void 0:a.detail)||"Errore durante l'eliminazione dell'utente")}},O=e=>{C(null),s("visualizza-utenti"),I()},P=()=>{C(null),s("visualizza-utenti")},G=async a=>{try{await U(a.id_utente),"user"===a.ruolo?e.push("/cantieri"):"cantieri_user"===a.ruolo?e.push("/cavi"):e.push("/")}catch(e){var s,t;v((null==(t=e.response)||null==(s=t.data)?void 0:s.detail)||e.message||"Errore durante l'impersonificazione")}},Z=e=>{let a="NEUTRAL";switch(e){case"owner":a="PROGRESS";break;case"user":a="INFO";break;case"cantieri_user":a="SUCCESS";break;default:a="NEUTRAL"}let s=(0,w.getSoftColorClasses)(a);return(0,t.jsx)(f.E,{className:s.badge,children:e})},B=(e,a)=>{let s="SUCCESS",r="Attivo",i="●";if(e)if(a){let e=new Date(a),t=new Date;e<t?(s="ERROR",r="Scaduto",i="⚠"):e.getTime()-t.getTime()<6048e5?(s="WARNING",r="In Scadenza",i="⏰"):i="✓"}else i="✓";else s="ERROR",r="Disabilitato",i="●";let l=(0,w.getSoftColorClasses)(s);return(0,t.jsxs)(f.E,{className:"".concat(l.badge," flex items-center gap-1"),children:[(0,t.jsx)("span",{className:"text-xs",role:"img","aria-hidden":"true",children:i}),(0,t.jsx)("span",{children:r})]})},X=c.filter(e=>{var a,s,t;return(null==(a=e.username)?void 0:a.toLowerCase().includes(l.toLowerCase()))||(null==(s=e.ragione_sociale)?void 0:s.toLowerCase().includes(l.toLowerCase()))||(null==(t=e.email)?void 0:t.toLowerCase().includes(l.toLowerCase()))});return k&&"owner"===k.ruolo?(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,t.jsx)("div",{className:"max-w-[90%] mx-auto space-y-6",children:(0,t.jsxs)(A.tU,{value:a,onValueChange:s,className:"w-full",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"bg-white rounded-lg border border-slate-200 shadow-sm p-1",children:(0,t.jsxs)(A.j7,{className:"grid w-full ".concat(T?"grid-cols-5":"grid-cols-4"," gap-1 h-auto bg-transparent p-0"),children:[(0,t.jsxs)(A.Xi,{value:"visualizza-utenti",className:"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent",children:[(0,t.jsx)(ea.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"font-medium",children:"Gestione Utenti"})]}),(0,t.jsxs)(A.Xi,{value:"crea-utente",className:"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent",children:[(0,t.jsx)(es.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"font-medium",children:"Crea Nuovo Utente"})]}),T&&(0,t.jsxs)(A.Xi,{value:"modifica-utente",className:"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent",children:[(0,t.jsx)(h.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"font-medium",children:"Modifica Utente"})]}),(0,t.jsxs)(A.Xi,{value:"database-tipologie-cavi",className:"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent",children:[(0,t.jsx)(q.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"font-medium",children:"Database Tipologie Cavi"})]}),(0,t.jsxs)(A.Xi,{value:"visualizza-database-raw",className:"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent",children:[(0,t.jsx)(M.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"font-medium",children:"Gestione Dati Avanzata"})]})]})}),(0,t.jsx)("div",{className:"bg-gradient-to-r from-red-50 to-orange-50 rounded-lg border border-red-200 shadow-sm p-1",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 px-4 py-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full animate-pulse"}),(0,t.jsx)("span",{className:"text-sm font-medium text-red-800",children:"Impostazioni Avanzate e Pericolose"})]}),(0,t.jsx)(A.j7,{className:"h-auto bg-transparent p-0",children:(0,t.jsxs)(A.Xi,{value:"reset-database",className:"admin-tab-danger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-red-100 data-[state=active]:text-red-700 data-[state=active]:border-red-300 data-[state=active]:shadow-sm hover:bg-red-100 hover:text-red-700 border border-transparent text-red-600 font-medium",children:[(0,t.jsx)(W.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Reset Database"})]})})]})})]}),(0,t.jsxs)(A.av,{value:"visualizza-utenti",className:"space-y-4",children:[b&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,t.jsx)("p",{className:"text-red-600",children:b})}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(ea.A,{className:"h-5 w-5 text-blue-600"}),"Lista Utenti"]}),(0,t.jsx)(i.BT,{children:"Gestisci tutti gli utenti del sistema CABLYS"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(et.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400"}),(0,t.jsx)(N.p,{placeholder:"Cerca per username, email o ragione sociale...",value:l,onChange:e=>d(e.target.value),className:"pl-10 w-80"})]}),(0,t.jsxs)(f.E,{variant:"outline",className:"text-xs",children:[X.length," utenti"]})]})]})}),(0,t.jsx)(i.Wu,{children:(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsx)("div",{className:"rounded-md border",children:(0,t.jsxs)(y.XI,{children:[(0,t.jsx)(y.A0,{children:(0,t.jsxs)(y.Hj,{children:[(0,t.jsx)(y.nd,{className:"w-[50px] text-center",children:"ID"}),(0,t.jsx)(y.nd,{className:"w-[100px]",children:"Username"}),(0,t.jsx)(y.nd,{className:"w-[80px] text-center",children:"Password"}),(0,t.jsx)(y.nd,{className:"w-[80px] text-center",children:"Ruolo"}),(0,t.jsx)(y.nd,{className:"w-[180px]",children:"Ragione Sociale"}),(0,t.jsx)(y.nd,{className:"w-[160px]",children:"Email"}),(0,t.jsx)(y.nd,{className:"w-[80px] text-center",children:"VAT"}),(0,t.jsx)(y.nd,{className:"w-[80px] text-center",children:"Nazione"}),(0,t.jsx)(y.nd,{className:"w-[120px]",children:"Referente"}),(0,t.jsx)(y.nd,{className:"w-[90px] text-center",children:"Scadenza"}),(0,t.jsx)(y.nd,{className:"w-[80px] text-center",children:"Stato"}),(0,t.jsx)(y.nd,{className:"w-[100px] text-center",children:"Azioni"})]})}),(0,t.jsx)(y.BF,{children:p?(0,t.jsx)(y.Hj,{children:(0,t.jsx)(y.nA,{colSpan:12,className:"text-center py-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,t.jsx)(n.A,{className:"h-4 w-4 animate-spin"}),"Caricamento..."]})})}):0===c.length?(0,t.jsx)(y.Hj,{children:(0,t.jsx)(y.nA,{colSpan:12,className:"text-center py-8 text-slate-500",children:"Nessun utente trovato"})}):X.map(e=>(0,t.jsxs)(y.Hj,{className:"users-table-row",children:[(0,t.jsx)(y.nA,{className:"text-center",children:(0,t.jsxs)(f.E,{variant:"outline",className:"text-xs font-mono",children:["#",e.id_utente]})}),(0,t.jsx)(y.nA,{className:"font-semibold text-slate-900",children:e.username}),(0,t.jsx)(y.nA,{className:"text-center",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-1",children:[(0,t.jsx)("div",{className:"flex gap-1",children:[...Array(8)].map((e,a)=>(0,t.jsx)("div",{className:"w-1.5 h-1.5 bg-slate-400 rounded-full"},a))}),(0,t.jsx)("div",{className:"w-2 h-2 rounded-full ml-2 ".concat(e.password_plain?"bg-green-500":"bg-red-500"),title:e.password_plain?"Password configurata":"Password non configurata"})]})}),(0,t.jsx)(y.nA,{className:"text-center",children:Z(e.ruolo)}),(0,t.jsx)(y.nA,{className:"max-w-[250px] truncate",title:e.ragione_sociale,children:(0,t.jsx)("span",{className:"text-slate-900",children:e.ragione_sociale||"-"})}),(0,t.jsx)(y.nA,{className:"max-w-[200px] truncate text-sm text-slate-600",title:e.email,children:e.email||"-"}),(0,t.jsx)(y.nA,{className:"text-center text-sm text-slate-600",children:e.vat||"-"}),(0,t.jsx)(y.nA,{className:"text-center text-sm text-slate-600",children:e.nazione||"-"}),(0,t.jsx)(y.nA,{className:"max-w-[150px] truncate text-sm text-slate-600",title:e.referente_aziendale,children:e.referente_aziendale||"-"}),(0,t.jsx)(y.nA,{className:"text-center text-sm text-slate-600",children:e.data_scadenza?new Date(e.data_scadenza).toLocaleDateString("it-IT"):"N/A"}),(0,t.jsx)(y.nA,{className:"text-center",children:B(e.abilitato,e.data_scadenza)}),(0,t.jsx)(y.nA,{className:"text-center",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,t.jsx)(j,{user:e,onEdit:()=>D(e),onToggleStatus:()=>L(e.id_utente),onDelete:()=>V(e.id_utente)}),(0,t.jsx)(o,{size:"sm",onClick:()=>G(e),disabled:"owner"===e.ruolo||!e.abilitato,className:"px-3 py-1.5 text-xs",icon:(0,t.jsx)(er.A,{className:"h-3.5 w-3.5"}),children:"Entra"})]})})]},e.id_utente))})]})})})})]})]}),(0,t.jsx)(A.av,{value:"crea-utente",className:"space-y-4",children:(0,t.jsx)(F,{user:null,onSave:O,onCancel:P})}),T&&(0,t.jsx)(A.av,{value:"modifica-utente",className:"space-y-4",children:(0,t.jsx)(F,{user:T,onSave:O,onCancel:P})}),(0,t.jsx)(A.av,{value:"database-tipologie-cavi",className:"space-y-4",children:(0,t.jsx)(ee,{})}),(0,t.jsx)(A.av,{value:"visualizza-database-raw",className:"space-y-4",children:(0,t.jsx)($,{})}),(0,t.jsx)(A.av,{value:"reset-database",className:"space-y-4",children:(0,t.jsx)(J,{})})]})})}):(window.location.replace("/login"),null)}},17313:(e,a,s)=>{"use strict";s.d(a,{Xi:()=>o,av:()=>c,j7:()=>d,tU:()=>n});var t=s(95155),r=s(12115),i=s(60704),l=s(59434);let n=i.bL,d=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(i.B8,{ref:a,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",s),...r})});d.displayName=i.B8.displayName;let o=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(i.l9,{ref:a,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",s),...r})});o.displayName=i.l9.displayName;let c=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(i.UC,{ref:a,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...r})});c.displayName=i.UC.displayName},26126:(e,a,s)=>{"use strict";s.d(a,{E:()=>d});var t=s(95155);s(12115);var r=s(99708),i=s(74466),l=s(59434);let n=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:a,variant:s,asChild:i=!1,...d}=e,o=i?r.DX:"span";return(0,t.jsx)(o,{"data-slot":"badge",className:(0,l.cn)(n({variant:s}),a),...d})}},30285:(e,a,s)=>{"use strict";s.d(a,{$:()=>d});var t=s(95155);s(12115);var r=s(99708),i=s(74466),l=s(59434);let n=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:a,variant:s,size:i,asChild:d=!1,...o}=e,c=d?r.DX:"button";return(0,t.jsx)(c,{"data-slot":"button",className:(0,l.cn)(n({variant:s,size:i,className:a})),...o})}},40975:()=>{},42221:(e,a,s)=>{Promise.resolve().then(s.bind(s,7958))},47262:(e,a,s)=>{"use strict";s.d(a,{S:()=>n});var t=s(95155);s(12115);var r=s(76981),i=s(5196),l=s(59434);function n(e){let{className:a,...s}=e;return(0,t.jsx)(r.bL,{"data-slot":"checkbox",className:(0,l.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...s,children:(0,t.jsx)(r.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,t.jsx)(i.A,{className:"size-3.5"})})})}},59409:(e,a,s)=>{"use strict";s.d(a,{bq:()=>m,eb:()=>u,gC:()=>x,l6:()=>o,yv:()=>c});var t=s(95155);s(12115);var r=s(38715),i=s(66474),l=s(5196),n=s(47863),d=s(59434);function o(e){let{...a}=e;return(0,t.jsx)(r.bL,{"data-slot":"select",...a})}function c(e){let{...a}=e;return(0,t.jsx)(r.WT,{"data-slot":"select-value",...a})}function m(e){let{className:a,size:s="default",children:l,...n}=e;return(0,t.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":s,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...n,children:[l,(0,t.jsx)(r.In,{asChild:!0,children:(0,t.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function x(e){let{className:a,children:s,position:i="popper",...l}=e;return(0,t.jsx)(r.ZL,{children:(0,t.jsxs)(r.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:i,...l,children:[(0,t.jsx)(h,{}),(0,t.jsx)(r.LM,{className:(0,d.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:s}),(0,t.jsx)(p,{})]})})}function u(e){let{className:a,children:s,...i}=e;return(0,t.jsxs)(r.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",a),...i,children:[(0,t.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,t.jsx)(r.VF,{children:(0,t.jsx)(l.A,{className:"size-4"})})}),(0,t.jsx)(r.p4,{children:s})]})}function h(e){let{className:a,...s}=e;return(0,t.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",a),...s,children:(0,t.jsx)(n.A,{className:"size-4"})})}function p(e){let{className:a,...s}=e;return(0,t.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",a),...s,children:(0,t.jsx)(i.A,{className:"size-4"})})}},59434:(e,a,s)=>{"use strict";s.d(a,{cn:()=>i});var t=s(52596),r=s(39688);function i(){for(var e=arguments.length,a=Array(e),s=0;s<e;s++)a[s]=arguments[s];return(0,r.QP)((0,t.$)(a))}},61610:(e,a,s)=>{"use strict";s.d(a,{Eb:()=>h,GN:()=>p});let t=/[<>\"'&\x00-\x1f\x7f-\x9f]/g,r=/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,i=/(<script|javascript:|vbscript:|onload|onerror|onclick)/gi,l=e=>"string"!=typeof e?"":e.trim().replace(t,"").replace(/\s+/g," ").substring(0,1e3),n=e=>{let a=l(e);return a.length<3?{isValid:!1,error:"Username deve essere almeno 3 caratteri"}:a.length>20?{isValid:!1,error:"Username non pu\xf2 superare 20 caratteri"}:/^[a-zA-Z0-9._-]+$/.test(a)?/^[._-]|[._-]$/.test(a)?{isValid:!1,error:"Username non pu\xf2 iniziare o finire con caratteri speciali"}:{isValid:!0}:{isValid:!1,error:"Username pu\xf2 contenere solo lettere, numeri, punti, underscore e trattini"}},d=e=>{if(!e||e.length<8)return{isValid:!1,error:"Password deve essere almeno 8 caratteri",strength:0};if(e.length>128)return{isValid:!1,error:"Password troppo lunga (max 128 caratteri)",strength:0};let a=0;return(/[a-z]/.test(e)&&a++,/[A-Z]/.test(e)&&a++,/[0-9]/.test(e)&&a++,/[^a-zA-Z0-9]/.test(e)&&a++,e.length>=12&&a++,a<3)?{isValid:!1,error:"Password deve contenere almeno: 1 minuscola, 1 maiuscola, 1 numero o 1 carattere speciale",strength:a}:["password","123456","admin","qwerty","letmein"].some(a=>e.toLowerCase().includes(a))?{isValid:!1,error:"Password troppo comune",strength:a}:{isValid:!0,strength:a}},o=e=>{let a=l(e);return a?a.length>254?{isValid:!1,error:"Email troppo lunga"}:/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(a)?{isValid:!0}:{isValid:!1,error:"Formato email non valido"}:{isValid:!1,error:"Email \xe8 obbligatoria"}},c=function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:255;return l(e).length>a?{isValid:!1,error:"Testo troppo lungo (max ".concat(a," caratteri)")}:i.test(e)||r.test(e)?{isValid:!1,error:"Contenuto non consentito rilevato"}:{isValid:!0}},m=e=>{let a=l(e);return a?a.length<2?{isValid:!1,error:"Ragione sociale troppo corta"}:a.length>100?{isValid:!1,error:"Ragione sociale troppo lunga (max 100 caratteri)"}:/^[a-zA-Z0-9\s\.\-&']+$/.test(a)?{isValid:!0}:{isValid:!1,error:"Ragione sociale contiene caratteri non consentiti"}:{isValid:!1,error:"Ragione sociale \xe8 obbligatoria"}},x=e=>{if(!e)return{isValid:!0};let a=l(e).replace(/\s/g,"");return a.length<8||a.length>15?{isValid:!1,error:"VAT deve essere tra 8 e 15 caratteri"}:/^[A-Z0-9]+$/i.test(a)?{isValid:!0}:{isValid:!1,error:"VAT pu\xf2 contenere solo lettere e numeri"}},u=new Map,h=(e,a,s)=>{let t=Date.now(),r=u.get(e);return!r||t>r.resetTime?(u.set(e,{count:1,resetTime:t+s}),!0):!(r.count>=a)&&(r.count++,!0)},p=e=>{let a={},s=n(e.username);if(s.isValid||(a.username=s.error),e.password){let s=d(e.password);s.isValid||(a.password=s.error)}let t=m(e.ragione_sociale);if(t.isValid||(a.ragione_sociale=t.error),e.email){let s=o(e.email);s.isValid||(a.email=s.error)}if(e.vat){let s=x(e.vat);s.isValid||(a.vat=s.error)}if(e.indirizzo){let s=c(e.indirizzo,200);s.isValid||(a.indirizzo=s.error)}if(e.nazione){let s=c(e.nazione,50);s.isValid||(a.nazione=s.error)}if(e.referente_aziendale){let s=c(e.referente_aziendale,100);s.isValid||(a.referente_aziendale=s.error)}return{isValid:0===Object.keys(a).length,errors:a}}},62523:(e,a,s)=>{"use strict";s.d(a,{p:()=>l});var t=s(95155),r=s(12115),i=s(59434);let l=r.forwardRef((e,a)=>{let{className:s,type:r,...l}=e;return(0,t.jsx)("input",{type:r,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),ref:a,...l})});l.displayName="Input"},63743:(e,a,s)=>{"use strict";s.d(a,{Fw:()=>c,NM:()=>o,Nj:()=>d,Tr:()=>l,mU:()=>t});let t={PRIMARY:{bg:"bg-blue-50",text:"text-blue-600",border:"border-blue-300",hover:"hover:bg-blue-50",active:"hover:border-blue-400",hex:"#007bff"},NEUTRAL:{text_dark:"text-gray-800",text_medium:"text-gray-600",text_light:"text-gray-500",bg_white:"bg-white",bg_light:"bg-gray-50",border:"border-gray-300",hex_dark:"#343A40",hex_medium:"#6c757d",hex_light:"#DEE2E6"},STATUS:{SUCCESS:{bg:"bg-green-50",text:"text-green-700",border:"border-green-200",hex:"#28A745"},WARNING:{bg:"bg-orange-50",text:"text-orange-700",border:"border-orange-200",hex:"#FD7E14"},ERROR:{bg:"bg-red-50",text:"text-red-700",border:"border-red-200",hex:"#DC3545"}}};t.STATUS.SUCCESS,t.STATUS.WARNING,t.NEUTRAL,t.STATUS.ERROR,t.NEUTRAL,t.STATUS.ERROR;let r={DA_INSTALLARE:t.NEUTRAL,INSTALLATO:t.STATUS.SUCCESS,COLLEGATO_PARTENZA:t.STATUS.WARNING,COLLEGATO_ARRIVO:t.STATUS.WARNING,COLLEGATO:t.STATUS.SUCCESS,CERTIFICATO:t.STATUS.SUCCESS,SPARE:t.STATUS.WARNING,ERRORE:t.STATUS.ERROR},i={ATTIVA:t.STATUS.SUCCESS,COMPLETATA:t.STATUS.SUCCESS,ANNULLATA:t.NEUTRAL,IN_CORSO:t.STATUS.WARNING,ERRORE:t.STATUS.ERROR},l=e=>{let a=r[null==e?void 0:e.toUpperCase().replace(/\s+/g,"_")]||r.ERRORE;return{badge:"".concat(a.bg," ").concat(a.text," rounded-full px-3 py-1 text-xs font-medium"),text:a.text,bg:a.bg,border:a.border,hex:a.hex}},n=()=>({button:"inline-flex items-center justify-center gap-1 px-3 py-2 text-xs font-medium bg-white text-gray-800 border-b-2 border-[#315cfd] rounded-full cursor-pointer min-w-[4rem] h-8 transition-colors duration-300 hover:border-2 hover:bg-[#315cfd] hover:text-white",text:"text-gray-800",border:"border-b-[#315cfd]",hover:"hover:bg-[#315cfd] hover:text-white hover:border-2"}),d=()=>n(),o=()=>({text:"inline-flex items-center gap-1 px-2 py-1 text-xs font-medium ".concat(t.NEUTRAL.text_light),color:t.NEUTRAL.text_light}),c=e=>{let a=i[null==e?void 0:e.toUpperCase().replace(/\s+/g,"_")]||i.ERRORE;return{badge:"".concat(a.bg," ").concat(a.text," ").concat(a.border),button:"".concat(a.bg," ").concat(a.text," ").concat(a.border," ").concat(a.hover),alert:"".concat(a.bg," ").concat(a.text," ").concat(a.border),text:a.text,bg:a.bg,border:a.border,hover:a.hover,hex:a.hex}};t.STATUS.ERROR,t.STATUS.WARNING,t.NEUTRAL,t.NEUTRAL},66695:(e,a,s)=>{"use strict";s.d(a,{BT:()=>d,Wu:()=>o,ZB:()=>n,Zp:()=>i,aR:()=>l});var t=s(95155);s(12115);var r=s(59434);function i(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...s})}function l(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...s})}function n(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",a),...s})}function d(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",a),...s})}function o(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",a),...s})}},85057:(e,a,s)=>{"use strict";s.d(a,{J:()=>l});var t=s(95155);s(12115);var r=s(40968),i=s(59434);function l(e){let{className:a,...s}=e;return(0,t.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...s})}},85127:(e,a,s)=>{"use strict";s.d(a,{A0:()=>l,BF:()=>n,Hj:()=>d,XI:()=>i,nA:()=>c,nd:()=>o});var t=s(95155);s(12115);var r=s(59434);function i(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,t.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm border-collapse",a),...s})})}function l(e){let{className:a,...s}=e;return(0,t.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",a),...s})}function n(e){let{className:a,...s}=e;return(0,t.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",a),...s})}function d(e){let{className:a,...s}=e;return(0,t.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("data-[state=selected]:bg-muted border-b",a),...s})}function o(e){let{className:a,...s}=e;return(0,t.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...s})}function c(e){let{className:a,...s}=e;return(0,t.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...s})}}},e=>{var a=a=>e(e.s=a);e.O(0,[8902,3455,3464,9816,9384,5171,3122,1568,283,8441,1684,7358],()=>a(42221)),_N_E=e.O()}]);