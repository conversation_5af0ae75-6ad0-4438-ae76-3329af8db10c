'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Cloud, Loader2, Settings, X } from 'lucide-react'
import { CertificazioneCavoCreate } from '@/types/certificazioni'

interface WeatherData {
  temperature: number
  humidity: number
  city?: string
  isDemo: boolean
  source: string
}

interface CondizioniAmbientaliProps {
  formData: Partial<CertificazioneCavoCreate>
  weatherData: WeatherData | null
  isLoadingWeather: boolean
  isWeatherOverride: boolean
  onInputChange: (field: string, value: any) => void
  onToggleWeatherOverride: () => void
}

export function CondizioniAmbientali({
  formData,
  weatherData,
  isLoadingWeather,
  isWeatherOverride,
  onInputChange,
  onToggleWeatherOverride
}: CondizioniAmbientaliProps) {
  if (!weatherData) return null

  return (
    <Card>
      <CardHeader className="pb-1 px-3 pt-2">
        <CardTitle className="flex items-center gap-2 text-sm font-semibold">
          <Cloud className="h-3 w-3" />
          Condizioni Ambientali
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0 px-3 pb-2">
        <div className={`p-3 rounded-lg border ${weatherData.isDemo ? 'bg-yellow-50 border-yellow-200' : 'bg-green-50 border-green-200'}`}>
          <div className="flex items-center gap-3">
            {isLoadingWeather ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Cloud className="h-4 w-4 text-blue-600" />
            )}
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <div className="text-sm font-medium">
                  {weatherData.city && `${weatherData.city} - `}
                  {weatherData.temperature}°C, {weatherData.humidity}% UR
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={onToggleWeatherOverride}
                  className="h-6 px-2 text-xs"
                >
                  {isWeatherOverride ? (
                    <>
                      <X className="h-3 w-3 mr-1" />
                      Auto
                    </>
                  ) : (
                    <>
                      <Settings className="h-3 w-3 mr-1" />
                      Modifica
                    </>
                  )}
                </Button>
              </div>
              <div className="text-xs text-gray-600 mt-1">
                {weatherData.source}
                {weatherData.isDemo && ' (Dati dimostrativi)'}
              </div>
            </div>
          </div>
        </div>

        {/* Override manuale */}
        {isWeatherOverride && (
          <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="text-sm font-medium text-blue-800 mb-2">
              Inserimento Manuale
            </div>
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-1">
                <Label htmlFor="temperatura_prova" className="text-sm">Temperatura (°C)</Label>
                <Input
                  id="temperatura_prova"
                  type="number"
                  value={formData.temperatura_prova || ''}
                  onChange={(e) => onInputChange('temperatura_prova', parseFloat(e.target.value))}
                  placeholder="20"
                  className="text-sm"
                />
              </div>
              <div className="space-y-1">
                <Label htmlFor="umidita_prova" className="text-sm">Umidità (%)</Label>
                <Input
                  id="umidita_prova"
                  type="number"
                  value={formData.umidita_prova || ''}
                  onChange={(e) => onInputChange('umidita_prova', parseFloat(e.target.value))}
                  placeholder="50"
                  className="text-sm"
                />
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
