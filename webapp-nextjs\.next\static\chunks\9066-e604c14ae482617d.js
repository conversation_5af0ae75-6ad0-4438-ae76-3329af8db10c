"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9066],{381:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1243:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},4229:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},13717:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},14186:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},16785:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},28905:(e,t,a)=>{a.d(t,{C:()=>l});var n=a(12115),r=a(6101),o=a(52712),l=e=>{let{present:t,children:a}=e,l=function(e){var t,a;let[r,l]=n.useState(),u=n.useRef(null),s=n.useRef(e),d=n.useRef("none"),[c,p]=(t=e?"mounted":"unmounted",a={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=a[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=i(u.current);d.current="mounted"===c?e:"none"},[c]),(0,o.N)(()=>{let t=u.current,a=s.current;if(a!==e){let n=d.current,r=i(t);e?p("MOUNT"):"none"===r||(null==t?void 0:t.display)==="none"?p("UNMOUNT"):a&&n!==r?p("ANIMATION_OUT"):p("UNMOUNT"),s.current=e}},[e,p]),(0,o.N)(()=>{if(r){var e;let t,a=null!=(e=r.ownerDocument.defaultView)?e:window,n=e=>{let n=i(u.current).includes(e.animationName);if(e.target===r&&n&&(p("ANIMATION_END"),!s.current)){let e=r.style.animationFillMode;r.style.animationFillMode="forwards",t=a.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=e)})}},o=e=>{e.target===r&&(d.current=i(u.current))};return r.addEventListener("animationstart",o),r.addEventListener("animationcancel",n),r.addEventListener("animationend",n),()=>{a.clearTimeout(t),r.removeEventListener("animationstart",o),r.removeEventListener("animationcancel",n),r.removeEventListener("animationend",n)}}p("ANIMATION_END")},[r,p]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:n.useCallback(e=>{u.current=e?getComputedStyle(e):null,l(e)},[])}}(t),u="function"==typeof a?a({present:l.isPresent}):n.Children.only(a),s=(0,r.s)(l.ref,function(e){var t,a;let n=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,r=n&&"isReactWarning"in n&&n.isReactWarning;return r?e.ref:(r=(n=null==(a=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:a.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof a||l.isPresent?n.cloneElement(u,{ref:s}):null};function i(e){return(null==e?void 0:e.animationName)||"none"}l.displayName="Presence"},33109:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},47924:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},50589:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("cloud",[["path",{d:"M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z",key:"p7xjir"}]])},53904:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},55863:(e,t,a)=>{a.d(t,{C1:()=>g,bL:()=>b});var n=a(12115),r=a(46081),o=a(63655),l=a(95155),i="Progress",[u,s]=(0,r.A)(i),[d,c]=u(i),p=n.forwardRef((e,t)=>{var a,n,r,i;let{__scopeProgress:u,value:s=null,max:c,getValueLabel:p=m,...v}=e;(c||0===c)&&!A(c)&&console.error((a="".concat(c),n="Progress","Invalid prop `max` of value `".concat(a,"` supplied to `").concat(n,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let f=A(c)?c:100;null===s||k(s,f)||console.error((r="".concat(s),i="Progress","Invalid prop `value` of value `".concat(r,"` supplied to `").concat(i,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let b=k(s,f)?s:null,g=h(b)?p(b,f):void 0;return(0,l.jsx)(d,{scope:u,value:b,max:f,children:(0,l.jsx)(o.sG.div,{"aria-valuemax":f,"aria-valuemin":0,"aria-valuenow":h(b)?b:void 0,"aria-valuetext":g,role:"progressbar","data-state":y(b,f),"data-value":null!=b?b:void 0,"data-max":f,...v,ref:t})})});p.displayName=i;var v="ProgressIndicator",f=n.forwardRef((e,t)=>{var a;let{__scopeProgress:n,...r}=e,i=c(v,n);return(0,l.jsx)(o.sG.div,{"data-state":y(i.value,i.max),"data-value":null!=(a=i.value)?a:void 0,"data-max":i.max,...r,ref:t})});function m(e,t){return"".concat(Math.round(e/t*100),"%")}function y(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function h(e){return"number"==typeof e}function A(e){return h(e)&&!isNaN(e)&&e>0}function k(e,t){return h(e)&&!isNaN(e)&&e<=t&&e>=0}f.displayName=v;var b=p,g=f},57434:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},60704:(e,t,a)=>{a.d(t,{B8:()=>j,UC:()=>E,bL:()=>T,l9:()=>D});var n=a(12115),r=a(85185),o=a(46081),l=a(89196),i=a(28905),u=a(63655),s=a(94315),d=a(5845),c=a(61285),p=a(95155),v="Tabs",[f,m]=(0,o.A)(v,[l.RG]),y=(0,l.RG)(),[h,A]=f(v),k=n.forwardRef((e,t)=>{let{__scopeTabs:a,value:n,onValueChange:r,defaultValue:o,orientation:l="horizontal",dir:i,activationMode:f="automatic",...m}=e,y=(0,s.jH)(i),[A,k]=(0,d.i)({prop:n,onChange:r,defaultProp:null!=o?o:"",caller:v});return(0,p.jsx)(h,{scope:a,baseId:(0,c.B)(),value:A,onValueChange:k,orientation:l,dir:y,activationMode:f,children:(0,p.jsx)(u.sG.div,{dir:y,"data-orientation":l,...m,ref:t})})});k.displayName=v;var b="TabsList",g=n.forwardRef((e,t)=>{let{__scopeTabs:a,loop:n=!0,...r}=e,o=A(b,a),i=y(a);return(0,p.jsx)(l.bL,{asChild:!0,...i,orientation:o.orientation,dir:o.dir,loop:n,children:(0,p.jsx)(u.sG.div,{role:"tablist","aria-orientation":o.orientation,...r,ref:t})})});g.displayName=b;var x="TabsTrigger",M=n.forwardRef((e,t)=>{let{__scopeTabs:a,value:n,disabled:o=!1,...i}=e,s=A(x,a),d=y(a),c=I(s.baseId,n),v=R(s.baseId,n),f=n===s.value;return(0,p.jsx)(l.q7,{asChild:!0,...d,focusable:!o,active:f,children:(0,p.jsx)(u.sG.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":v,"data-state":f?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:c,...i,ref:t,onMouseDown:(0,r.m)(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():s.onValueChange(n)}),onKeyDown:(0,r.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&s.onValueChange(n)}),onFocus:(0,r.m)(e.onFocus,()=>{let e="manual"!==s.activationMode;f||o||!e||s.onValueChange(n)})})})});M.displayName=x;var w="TabsContent",N=n.forwardRef((e,t)=>{let{__scopeTabs:a,value:r,forceMount:o,children:l,...s}=e,d=A(w,a),c=I(d.baseId,r),v=R(d.baseId,r),f=r===d.value,m=n.useRef(f);return n.useEffect(()=>{let e=requestAnimationFrame(()=>m.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(i.C,{present:o||f,children:a=>{let{present:n}=a;return(0,p.jsx)(u.sG.div,{"data-state":f?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":c,hidden:!n,id:v,tabIndex:0,...s,ref:t,style:{...e.style,animationDuration:m.current?"0s":void 0},children:n&&l})}})});function I(e,t){return"".concat(e,"-trigger-").concat(t)}function R(e,t){return"".concat(e,"-content-").concat(t)}N.displayName=w;var T=k,j=g,D=M,E=N},62525:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},69037:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},72713:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},84616:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},89196:(e,t,a)=>{a.d(t,{RG:()=>g,bL:()=>D,q7:()=>E});var n=a(12115),r=a(85185),o=a(37328),l=a(6101),i=a(46081),u=a(61285),s=a(63655),d=a(39033),c=a(5845),p=a(94315),v=a(95155),f="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},y="RovingFocusGroup",[h,A,k]=(0,o.N)(y),[b,g]=(0,i.A)(y,[k]),[x,M]=b(y),w=n.forwardRef((e,t)=>(0,v.jsx)(h.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,v.jsx)(h.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,v.jsx)(N,{...e,ref:t})})}));w.displayName=y;var N=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:a,orientation:o,loop:i=!1,dir:u,currentTabStopId:h,defaultCurrentTabStopId:k,onCurrentTabStopIdChange:b,onEntryFocus:g,preventScrollOnEntryFocus:M=!1,...w}=e,N=n.useRef(null),I=(0,l.s)(t,N),R=(0,p.jH)(u),[T,D]=(0,c.i)({prop:h,defaultProp:null!=k?k:null,onChange:b,caller:y}),[E,C]=n.useState(!1),F=(0,d.c)(g),L=A(a),O=n.useRef(!1),[G,P]=n.useState(0);return n.useEffect(()=>{let e=N.current;if(e)return e.addEventListener(f,F),()=>e.removeEventListener(f,F)},[F]),(0,v.jsx)(x,{scope:a,orientation:o,dir:R,loop:i,currentTabStopId:T,onItemFocus:n.useCallback(e=>D(e),[D]),onItemShiftTab:n.useCallback(()=>C(!0),[]),onFocusableItemAdd:n.useCallback(()=>P(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>P(e=>e-1),[]),children:(0,v.jsx)(s.sG.div,{tabIndex:E||0===G?-1:0,"data-orientation":o,...w,ref:I,style:{outline:"none",...e.style},onMouseDown:(0,r.m)(e.onMouseDown,()=>{O.current=!0}),onFocus:(0,r.m)(e.onFocus,e=>{let t=!O.current;if(e.target===e.currentTarget&&t&&!E){let t=new CustomEvent(f,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=L().filter(e=>e.focusable);j([e.find(e=>e.active),e.find(e=>e.id===T),...e].filter(Boolean).map(e=>e.ref.current),M)}}O.current=!1}),onBlur:(0,r.m)(e.onBlur,()=>C(!1))})})}),I="RovingFocusGroupItem",R=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:a,focusable:o=!0,active:l=!1,tabStopId:i,children:d,...c}=e,p=(0,u.B)(),f=i||p,m=M(I,a),y=m.currentTabStopId===f,k=A(a),{onFocusableItemAdd:b,onFocusableItemRemove:g,currentTabStopId:x}=m;return n.useEffect(()=>{if(o)return b(),()=>g()},[o,b,g]),(0,v.jsx)(h.ItemSlot,{scope:a,id:f,focusable:o,active:l,children:(0,v.jsx)(s.sG.span,{tabIndex:y?0:-1,"data-orientation":m.orientation,...c,ref:t,onMouseDown:(0,r.m)(e.onMouseDown,e=>{o?m.onItemFocus(f):e.preventDefault()}),onFocus:(0,r.m)(e.onFocus,()=>m.onItemFocus(f)),onKeyDown:(0,r.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void m.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,a){var n;let r=(n=e.key,"rtl"!==a?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(r))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(r)))return T[r]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let a=k().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)a.reverse();else if("prev"===t||"next"===t){"prev"===t&&a.reverse();let n=a.indexOf(e.currentTarget);a=m.loop?function(e,t){return e.map((a,n)=>e[(t+n)%e.length])}(a,n+1):a.slice(n+1)}setTimeout(()=>j(a))}}),children:"function"==typeof d?d({isCurrentTabStop:y,hasTabStop:null!=x}):d})})});R.displayName=I;var T={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function j(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=document.activeElement;for(let n of e)if(n===a||(n.focus({preventScroll:t}),document.activeElement!==a))return}var D=w,E=R},91788:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},92657:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}}]);