import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'

// Configurazione base per l'API
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

// Crea istanza axios con configurazione base
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Interceptor per aggiungere il token di autenticazione
apiClient.interceptors.request.use(
  (config) => {
    // Verifica se siamo nel browser prima di accedere a localStorage
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('token')
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Interceptor per gestire le risposte e gli errori
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401 && typeof window !== 'undefined') {
      // Token scaduto o non valido
      localStorage.removeItem('token')
      localStorage.removeItem('access_token')
      localStorage.removeItem('user_data')
      localStorage.removeItem('cantiere_data')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// Tipi per le risposte API
export interface ApiResponse<T = any> {
  data: T
  message?: string
  status: number
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  size: number
  pages: number
}

// Funzioni helper per le chiamate API
export const api = {
  // GET request
  get: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.get<T>(url, config)
    return response.data
  },

  // POST request
  post: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.post<T>(url, data, config)
    return response.data
  },

  // PUT request
  put: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.put<T>(url, data, config)
    return response.data
  },

  // PATCH request
  patch: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.patch<T>(url, data, config)
    return response.data
  },

  // DELETE request
  delete: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.delete<T>(url, config)
    return response.data
  },
}

// Servizi API specifici per CABLYS
export const authApi = {
  // Login utente - usa FormData per OAuth2PasswordRequestForm
  login: async (credentials: { username: string; password: string }) => {
    const formData = new FormData()
    formData.append('username', credentials.username)
    formData.append('password', credentials.password)

    const response = await apiClient.post('/api/auth/login', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    })
    return response.data
  },

  // Login cantiere - usa JSON per CantiereLogin
  loginCantiere: (credentials: { codice_cantiere: string; password_cantiere: string }) =>
    api.post<{ access_token: string; token_type: string; cantiere: any }>('/api/auth/login/cantiere', {
      codice_univoco: credentials.codice_cantiere,
      password: credentials.password_cantiere
    }),

  // Verifica token
  verifyToken: () =>
    api.post<{ user: any }>('/api/auth/test-token'),

  // Logout
  logout: () => {
    localStorage.removeItem('access_token')
    localStorage.removeItem('user_data')
    window.location.href = '/login'
  }
}

export const caviApi = {
  // Ottieni tutti i cavi
  getCavi: (cantiereId: number, params?: {
    tipo_cavo?: number,
    stato_installazione?: string,
    tipologia?: string,
    sort_by?: string,
    sort_order?: string
  }) =>
    api.get<any[]>(`/api/cavi/${cantiereId}`, { params }),

  // Ottieni cavo specifico
  getCavo: (cantiereId: number, idCavo: string) =>
    api.get<any>(`/api/cavi/${cantiereId}/${idCavo}`),

  // Verifica se cavo esiste
  checkCavo: (cantiereId: number, idCavo: string) =>
    api.get<any>(`/api/cavi/${cantiereId}/check/${idCavo}`),

  // Crea nuovo cavo
  createCavo: (cantiereId: number, cavo: any) =>
    api.post<any>(`/api/cavi/${cantiereId}`, cavo),

  // Aggiorna cavo
  updateCavo: (cantiereId: number, idCavo: string, updates: any) =>
    api.put<any>(`/api/cavi/${cantiereId}/${idCavo}`, updates),

  // Elimina cavo
  deleteCavo: (cantiereId: number, idCavo: string, options?: any) =>
    api.delete(`/api/cavi/${cantiereId}/${idCavo}`, { data: options }),

  // Aggiorna metri posati
  updateMetriPosati: (cantiereId: number, idCavo: string, metri: number, idBobina?: string, forceOver?: boolean) =>
    api.post<any>(`/api/cavi/${cantiereId}/${idCavo}/metri-posati`, {
      metri_posati: metri,
      id_bobina: idBobina,
      force_over: forceOver || false
    }),

  // Aggiorna bobina
  updateBobina: (cantiereId: number, idCavo: string, idBobina: string, forceOver?: boolean) =>
    api.post<any>(`/api/cavi/${cantiereId}/${idCavo}/bobina`, {
      id_bobina: idBobina,
      force_over: forceOver || false
    }),

  // Annulla installazione (resetta completamente il cavo)
  cancelInstallation: (cantiereId: number, idCavo: string) =>
    api.post<any>(`/api/cavi/${cantiereId}/${idCavo}/cancel-installation`),

  // Gestione collegamenti
  collegaCavo: (cantiereId: number, idCavo: string, lato: 'partenza' | 'arrivo', responsabile?: string) =>
    api.post<any>(`/api/cavi/${cantiereId}/${idCavo}/collegamento`, {
      lato,
      responsabile
    }),

  // Scollega cavo
  scollegaCavo: (cantiereId: number, idCavo: string, lato?: 'partenza' | 'arrivo') => {
    const config: any = {}
    if (lato) {
      config.data = { lato }
    }
    return api.delete(`/api/cavi/${cantiereId}/${idCavo}/collegamento`, config)
  },

  // Marca come spare
  markAsSpare: (cantiereId: number, idCavo: string, spare: boolean, force: boolean = true) =>
    spare
      ? api.post<any>(`/api/cavi/${cantiereId}/${idCavo}/mark-as-spare`, { force })
      : api.post<any>(`/api/cavi/${cantiereId}/${idCavo}/reactivate-spare`, {}),

  // Debug endpoints
  debugCavi: (cantiereId: number) =>
    api.get<any>(`/api/cavi/debug/${cantiereId}`),

  debugCaviRaw: (cantiereId: number) =>
    api.get<any>(`/api/cavi/debug/raw/${cantiereId}`),
}

export const parcoCaviApi = {
  // Ottieni tutte le bobine
  getBobine: (cantiereId: number, params?: {
    filtro?: string,
    tipologia?: string,
    sezione?: string,    // sezione nel DB = formazione sistema
    disponibili_only?: boolean
  }) =>
    api.get<any[]>(`/api/parco-cavi/${cantiereId}`, { params }),

  // Ottieni bobina specifica
  getBobina: (cantiereId: number, idBobina: string) =>
    api.get<any>(`/api/parco-cavi/${cantiereId}/${idBobina}`),

  // Ottieni bobine compatibili
  getBobineCompatibili: (cantiereId: number, params: {
    tipologia?: string,
    n_conduttori?: string,
    sezione?: string     // sezione nel DB = formazione sistema
  }) =>
    api.get<any[]>(`/api/parco-cavi/${cantiereId}/compatibili`, { params }),

  // Crea nuova bobina
  createBobina: (cantiereId: number, bobina: any) =>
    api.post<any>(`/api/parco-cavi/${cantiereId}`, bobina),

  // Aggiorna bobina esistente
  updateBobina: (cantiereId: number, bobinaNumero: string, bobina: any) =>
    api.put<any>(`/api/parco-cavi/${cantiereId}/${bobinaNumero}`, bobina),

  // Elimina bobina
  deleteBobina: (cantiereId: number, bobinaNumero: string) =>
    api.delete<any>(`/api/parco-cavi/${cantiereId}/${bobinaNumero}`),

  // Verifica se è il primo inserimento bobina
  isFirstBobinaInsertion: (cantiereId: number) =>
    api.get<{is_first_insertion: boolean, configurazione: string}>(`/api/parco-cavi/${cantiereId}/is-first-insertion`),

  // Aggiorna bobina
  updateBobina: (cantiereId: number, idBobina: string, updates: any) =>
    api.put<any>(`/api/parco-cavi/${cantiereId}/${idBobina}`, updates),

  // Elimina bobina
  deleteBobina: (cantiereId: number, idBobina: string) =>
    api.delete(`/api/parco-cavi/${cantiereId}/${idBobina}`),

  // Verifica disponibilità metri
  checkDisponibilita: (cantiereId: number, idBobina: string, metriRichiesti: number) =>
    api.get<any>(`/api/parco-cavi/${cantiereId}/${idBobina}/disponibilita`, {
      params: { metri_richiesti: metriRichiesti }
    }),
}

export const comandeApi = {
  // Ottieni tutte le comande
  getComande: (cantiereId: number) =>
    api.get<any[]>(`/api/comande/cantiere/${cantiereId}`),

  // Ottieni comanda specifica
  getComanda: (cantiereId: number, codiceComanda: string) =>
    api.get<any>(`/api/comande/${codiceComanda}`),

  // Ottieni cavi di una comanda
  getCaviComanda: (codiceComanda: string) =>
    api.get<any>(`/api/comande/${codiceComanda}/cavi`),

  // Crea nuova comanda
  createComanda: (cantiereId: number, comanda: any) =>
    api.post<any>(`/api/comande/cantiere/${cantiereId}`, comanda),

  // Crea comanda con cavi
  createComandaWithCavi: (cantiereId: number, comanda: any, caviIds: string[]) =>
    api.post<any>(`/api/comande/cantiere/${cantiereId}/crea-con-cavi`, comanda, {
      params: { lista_id_cavi: caviIds }
    }),

  // Aggiorna dati comanda (posa, collegamento, certificazione)
  updateDatiComanda: (codiceComanda: string, endpoint: string, data: any) =>
    api.put<any>(`/api/comande/${codiceComanda}/${endpoint}`, data),

  // Aggiorna comanda
  updateComanda: (cantiereId: number, codiceComanda: string, updates: any) =>
    api.put<any>(`/api/comande/cantiere/${cantiereId}/${codiceComanda}`, updates),

  // Elimina comanda
  deleteComanda: (cantiereId: number, codiceComanda: string) =>
    api.delete(`/api/comande/cantiere/${cantiereId}/${codiceComanda}`),

  // Assegna cavi a comanda
  assegnaCavi: (cantiereId: number, codiceComanda: string, caviIds: string[]) =>
    api.post<any>(`/api/comande/cantiere/${cantiereId}/${codiceComanda}/assegna-cavi`, { cavi_ids: caviIds }),

  // Rimuovi cavi da comanda
  rimuoviCavi: (cantiereId: number, codiceComanda: string, caviIds: string[]) =>
    api.delete(`/api/comande/cantiere/${cantiereId}/${codiceComanda}/rimuovi-cavi`, {
      data: { cavi_ids: caviIds }
    }),

  // Ottieni statistiche comande
  getStatistiche: (cantiereId: number) =>
    api.get<any>(`/api/comande/cantiere/${cantiereId}/statistiche`),

  // Cambia stato comanda
  cambiaStato: (cantiereId: number, codiceComanda: string, nuovoStato: string) =>
    api.put<any>(`/api/comande/cantiere/${cantiereId}/${codiceComanda}/stato`, {
      nuovo_stato: nuovoStato
    }),
}

export const responsabiliApi = {
  // Ottieni tutti i responsabili
  getResponsabili: (cantiereId: number) =>
    api.get<any[]>(`/api/responsabili/cantiere/${cantiereId}`),

  // Crea nuovo responsabile
  createResponsabile: (cantiereId: number, responsabile: any) =>
    api.post<any>(`/api/responsabili/${cantiereId}`, responsabile),

  // Aggiorna responsabile
  updateResponsabile: (cantiereId: number, id: number, updates: any) =>
    api.put<any>(`/api/responsabili/${cantiereId}/${id}`, updates),

  // Elimina responsabile
  deleteResponsabile: (cantiereId: number, id: number) =>
    api.delete(`/api/responsabili/${cantiereId}/${id}`),
}

export const certificazioniApi = {
  // Ottieni certificazioni
  getCertificazioni: (cantiereId: number, filtroCavo?: string) =>
    api.get<any[]>(`/api/cantieri/${cantiereId}/certificazioni`, {
      params: filtroCavo ? { filtro_cavo: filtroCavo } : {}
    }),

  // Crea certificazione
  createCertificazione: (cantiereId: number, certificazione: any) =>
    api.post<any>(`/api/cantieri/${cantiereId}/certificazioni`, certificazione),

  // Ottieni dettagli certificazione
  getCertificazione: (cantiereId: number, idCertificazione: number) =>
    api.get<any>(`/api/cantieri/${cantiereId}/certificazioni/${idCertificazione}`),

  // Aggiorna certificazione
  updateCertificazione: (cantiereId: number, idCertificazione: number, certificazione: any) =>
    api.put<any>(`/api/cantieri/${cantiereId}/certificazioni/${idCertificazione}`, certificazione),

  // Elimina certificazione
  deleteCertificazione: (cantiereId: number, idCertificazione: number) =>
    api.delete(`/api/cantieri/${cantiereId}/certificazioni/${idCertificazione}`),

  // Genera PDF certificato
  generatePDF: (cantiereId: number, idCertificazione: number) =>
    api.get(`/api/cantieri/${cantiereId}/certificazioni/${idCertificazione}/pdf`, {
      responseType: 'blob'
    }),

  // Ottieni statistiche certificazioni
  getStatistiche: (cantiereId: number) =>
    api.get<any>(`/api/cantieri/${cantiereId}/certificazioni/statistiche`),

  // Export certificazioni CSV
  exportCertificazioni: (cantiereId: number, filtri?: any) =>
    api.get(`/api/cantieri/${cantiereId}/certificazioni/export`, {
      params: filtri,
      responseType: 'blob'
    }),

  // Genera report completo
  generateReport: (cantiereId: number, tipoReport: string = 'completo') =>
    api.get<any>(`/api/cantieri/${cantiereId}/certificazioni/report/${tipoReport}`),

  // Operazioni bulk
  bulkDelete: (cantiereId: number, idCertificazioni: number[]) =>
    api.post<any>(`/api/cantieri/${cantiereId}/certificazioni/bulk-delete`, {
      ids: idCertificazioni
    }),

  // Genera PDF multipli
  generateBulkPdf: (cantiereId: number, idCertificazioni: number[]) =>
    api.post(`/api/cantieri/${cantiereId}/certificazioni/bulk-pdf`, {
      ids: idCertificazioni
    }, {
      responseType: 'blob'
    }),

  // Valida certificazione
  validateCertificazione: (cantiereId: number, certificazione: any) =>
    api.post<any>(`/api/cantieri/${cantiereId}/certificazioni/validate`, certificazione),
}

// API per Strumenti Certificati
export const strumentiApi = {
  // Ottieni strumenti
  getStrumenti: (cantiereId: number) =>
    api.get<any[]>(`/api/cantieri/${cantiereId}/strumenti`),

  // Crea strumento
  createStrumento: (cantiereId: number, strumento: any) =>
    api.post<any>(`/api/cantieri/${cantiereId}/strumenti`, strumento),

  // Aggiorna strumento
  updateStrumento: (cantiereId: number, idStrumento: number, strumento: any) =>
    api.put<any>(`/api/cantieri/${cantiereId}/strumenti/${idStrumento}`, strumento),

  // Elimina strumento
  deleteStrumento: (cantiereId: number, idStrumento: number) =>
    api.delete(`/api/cantieri/${cantiereId}/strumenti/${idStrumento}`),
}

// API per Rapporti Generali
export const rapportiGeneraliApi = {
  // Ottieni rapporti
  getRapporti: (cantiereId: number, skip: number = 0, limit: number = 100) =>
    api.get<any[]>(`/api/cantieri/${cantiereId}/rapporti`, {
      params: { skip, limit }
    }),

  // Crea rapporto
  createRapporto: (cantiereId: number, rapporto: any) =>
    api.post<any>(`/api/cantieri/${cantiereId}/rapporti`, rapporto),

  // Ottieni rapporto
  getRapporto: (cantiereId: number, rapportoId: number) =>
    api.get<any>(`/api/cantieri/${cantiereId}/rapporti/${rapportoId}`),

  // Aggiorna rapporto
  updateRapporto: (cantiereId: number, rapportoId: number, rapporto: any) =>
    api.put<any>(`/api/cantieri/${cantiereId}/rapporti/${rapportoId}`, rapporto),

  // Elimina rapporto
  deleteRapporto: (cantiereId: number, rapportoId: number) =>
    api.delete(`/api/cantieri/${cantiereId}/rapporti/${rapportoId}`),

  // Aggiorna statistiche rapporto
  aggiornaStatistiche: (cantiereId: number, rapportoId: number) =>
    api.post<any>(`/api/cantieri/${cantiereId}/rapporti/${rapportoId}/aggiorna-statistiche`),
}

// API per Non Conformità
export const nonConformitaApi = {
  // Ottieni non conformità
  getNonConformita: (cantiereId: number) =>
    api.get<any[]>(`/api/cantieri/${cantiereId}/non-conformita`),

  // Crea non conformità
  createNonConformita: (cantiereId: number, nc: any) =>
    api.post<any>(`/api/cantieri/${cantiereId}/non-conformita`, nc),

  // Aggiorna non conformità
  updateNonConformita: (cantiereId: number, ncId: number, nc: any) =>
    api.put<any>(`/api/cantieri/${cantiereId}/non-conformita/${ncId}`, nc),

  // Elimina non conformità
  deleteNonConformita: (cantiereId: number, ncId: number) =>
    api.delete(`/api/cantieri/${cantiereId}/non-conformita/${ncId}`),
}

export const excelApi = {
  // Import cavi da Excel
  importCavi: (cantiereId: number, file: File, revisione: string) => {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('revisione', revisione)
    return api.post<any>(`/api/excel/${cantiereId}/import-cavi`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },

  // Import bobine da Excel
  importBobine: (cantiereId: number, file: File) => {
    const formData = new FormData()
    formData.append('file', file)
    return api.post<any>(`/api/excel/${cantiereId}/import-parco-bobine`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },

  // Export cavi
  exportCavi: (cantiereId: number) =>
    api.get(`/api/excel/${cantiereId}/export-cavi`, {
      responseType: 'blob'
    }),

  // Export bobine
  exportBobine: (cantiereId: number) =>
    api.get(`/api/excel/${cantiereId}/export-parco-bobine`, {
      responseType: 'blob'
    }),
}

export const reportsApi = {
  // Report avanzamento
  getReportAvanzamento: (cantiereId: number) =>
    api.get<any>(`/api/reports/${cantiereId}/avanzamento`),

  // Report BOQ
  getReportBOQ: (cantiereId: number) =>
    api.get<any>(`/api/reports/${cantiereId}/boq`),

  // Report utilizzo bobine (storico bobine)
  getReportUtilizzoBobine: (cantiereId: number) =>
    api.get<any>(`/api/reports/${cantiereId}/storico-bobine`),

  // Report progress
  getReportProgress: (cantiereId: number) =>
    api.get<any>(`/api/reports/${cantiereId}/progress`),

  // Report posa per periodo
  getReportPosaPeriodo: (cantiereId: number, dataInizio?: string, dataFine?: string) => {
    const params = new URLSearchParams()
    if (dataInizio) params.append('data_inizio', dataInizio)
    if (dataFine) params.append('data_fine', dataFine)
    const queryString = params.toString()
    return api.get<any>(`/api/reports/${cantiereId}/posa-periodo${queryString ? `?${queryString}` : ''}`)
  },
}

export const cantieriApi = {
  // Ottieni tutti i cantieri
  getCantieri: () =>
    api.get<any[]>('/api/cantieri'),

  // Ottieni cantiere specifico
  getCantiere: (id: number) =>
    api.get<any>(`/api/cantieri/${id}`),

  // Crea nuovo cantiere
  createCantiere: (cantiere: any) =>
    api.post<any>('/api/cantieri', cantiere),

  // Aggiorna cantiere
  updateCantiere: (id: number, updates: any) =>
    api.put<any>(`/api/cantieri/${id}`, updates),

  // Ottieni statistiche cantiere
  getCantiereStatistics: (id: number) =>
    api.get<{
      cantiere_id: number
      total_cavi: number
      installati: number
      collegati: number
      certificati: number
      percentuale_avanzamento: number
      iap: number
    }>(`/api/cantieri/${id}/statistics`),
}

export const usersApi = {
  // Ottieni tutti gli utenti (solo admin)
  getUsers: () =>
    api.get<any[]>('/api/users'),

  // Ottieni utente specifico
  getUser: (id: number) =>
    api.get<any>(`/api/users/${id}`),

  // Crea nuovo utente
  createUser: (user: any) =>
    api.post<any>('/api/users', user),

  // Aggiorna utente
  updateUser: (id: number, updates: any) =>
    api.put<any>(`/api/users/${id}`, updates),

  // Elimina utente
  deleteUser: (id: number) =>
    api.delete(`/api/users/${id}`),

  // Abilita/Disabilita utente
  toggleUserStatus: (id: number) =>
    api.get<any>(`/api/users/toggle/${id}`),

  // Verifica utenti scaduti
  checkExpiredUsers: () =>
    api.get<any>('/api/users/check-expired'),

  // Impersona utente
  impersonateUser: (userId: number) =>
    api.post<any>('/api/auth/impersonate', { user_id: userId }),

  // Ottieni dati database raw
  getDatabaseData: () =>
    api.get<any>('/api/users/db-raw'),

  // Reset database
  resetDatabase: () =>
    api.post<any>('/api/admin/reset-database'),
}

export default apiClient
