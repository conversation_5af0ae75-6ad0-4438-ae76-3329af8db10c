"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9972],{6803:(e,a,t)=>{t.d(a,{A:()=>w});var s=t(95155),r=t(30285),i=t(55365),n=t(51154),l=t(57434),o=t(4229),d=t(85339),c=t(12115),u=t(25731),m=t(66695),x=t(85057),v=t(59409);function p(e){var a,t;let{formData:r,cavi:i,responsabili:n,strumenti:l,validationErrors:o,isCavoLocked:d,onInputChange:c}=e;return(0,s.jsxs)(m.Zp,{children:[(0,s.jsx)(m.aR,{className:"pb-1 px-3 pt-2",children:(0,s.jsx)(m.<PERSON><PERSON>,{className:"text-sm font-semibold",children:"Informazioni Base"})}),(0,s.jsx)(m.<PERSON>,{className:"space-y-2 pt-0 px-3 pb-2",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)(x.J,{htmlFor:"id_cavo",className:"text-sm",children:"Cavo *"}),(0,s.jsxs)(v.l6,{value:r.id_cavo,onValueChange:e=>c("id_cavo",e),disabled:d,children:[(0,s.jsx)(v.bq,{children:(0,s.jsx)(v.yv,{placeholder:"Seleziona cavo..."})}),(0,s.jsx)(v.gC,{children:i.map(e=>(0,s.jsxs)(v.eb,{value:e.id_cavo,children:[e.id_cavo," - ",e.descrizione]},e.id_cavo))})]}),o.id_cavo&&(0,s.jsx)("p",{className:"text-sm text-red-600",children:o.id_cavo})]}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)(x.J,{htmlFor:"id_operatore",className:"text-sm",children:"Operatore"}),(0,s.jsxs)(v.l6,{value:(null==(a=r.id_operatore)?void 0:a.toString())||"",onValueChange:e=>c("id_operatore",parseInt(e)),children:[(0,s.jsx)(v.bq,{children:(0,s.jsx)(v.yv,{placeholder:"Seleziona operatore..."})}),(0,s.jsx)(v.gC,{children:n.map(e=>(0,s.jsxs)(v.eb,{value:e.id_responsabile.toString(),children:[e.nome," ",e.cognome]},e.id_responsabile))})]})]}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)(x.J,{htmlFor:"id_strumento",className:"text-sm",children:"Strumento"}),(0,s.jsxs)(v.l6,{value:(null==(t=r.id_strumento)?void 0:t.toString())||"",onValueChange:e=>{let a=l.find(a=>a.id_strumento===parseInt(e));c("id_strumento",parseInt(e)),a&&c("strumento_utilizzato","".concat(a.marca," ").concat(a.modello))},children:[(0,s.jsx)(v.bq,{children:(0,s.jsx)(v.yv,{placeholder:"Seleziona strumento..."})}),(0,s.jsx)(v.gC,{children:l.map(e=>(0,s.jsxs)(v.eb,{value:e.id_strumento.toString(),children:[e.marca," ",e.modello," - ",e.numero_serie]},e.id_strumento))})]})]}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)(x.J,{htmlFor:"tipo_certificato",className:"text-sm",children:"Tipo Certificato"}),(0,s.jsxs)(v.l6,{value:r.tipo_certificato||"SINGOLO",onValueChange:e=>c("tipo_certificato",e),children:[(0,s.jsx)(v.bq,{children:(0,s.jsx)(v.yv,{})}),(0,s.jsxs)(v.gC,{children:[(0,s.jsx)(v.eb,{value:"SINGOLO",children:"Singolo"}),(0,s.jsx)(v.eb,{value:"GRUPPO",children:"Gruppo"})]})]})]})]})})]})}var h=t(62523),g=t(50589),f=t(54416),b=t(381);function j(e){let{formData:a,weatherData:t,isLoadingWeather:i,isWeatherOverride:l,onInputChange:o,onToggleWeatherOverride:d}=e;return t?(0,s.jsxs)(m.Zp,{children:[(0,s.jsx)(m.aR,{className:"pb-1 px-3 pt-2",children:(0,s.jsxs)(m.ZB,{className:"flex items-center gap-2 text-sm font-semibold",children:[(0,s.jsx)(g.A,{className:"h-3 w-3"}),"Condizioni Ambientali"]})}),(0,s.jsxs)(m.Wu,{className:"pt-0 px-3 pb-2",children:[(0,s.jsx)("div",{className:"p-3 rounded-lg border ".concat(t.isDemo?"bg-yellow-50 border-yellow-200":"bg-green-50 border-green-200"),children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[i?(0,s.jsx)(n.A,{className:"h-4 w-4 animate-spin"}):(0,s.jsx)(g.A,{className:"h-4 w-4 text-blue-600"}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"text-sm font-medium",children:[t.city&&"".concat(t.city," - "),t.temperature,"\xb0C, ",t.humidity,"% UR"]}),(0,s.jsx)(r.$,{type:"button",variant:"ghost",size:"sm",onClick:d,className:"h-6 px-2 text-xs",children:l?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(f.A,{className:"h-3 w-3 mr-1"}),"Auto"]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(b.A,{className:"h-3 w-3 mr-1"}),"Modifica"]})})]}),(0,s.jsxs)("div",{className:"text-xs text-gray-600 mt-1",children:[t.source,t.isDemo&&" (Dati dimostrativi)"]})]})]})}),l&&(0,s.jsxs)("div",{className:"mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-blue-800 mb-2",children:"Inserimento Manuale"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)(x.J,{htmlFor:"temperatura_prova",className:"text-sm",children:"Temperatura (\xb0C)"}),(0,s.jsx)(h.p,{id:"temperatura_prova",type:"number",value:a.temperatura_prova||"",onChange:e=>o("temperatura_prova",parseFloat(e.target.value)),placeholder:"20",className:"text-sm"})]}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)(x.J,{htmlFor:"umidita_prova",className:"text-sm",children:"Umidit\xe0 (%)"}),(0,s.jsx)(h.p,{id:"umidita_prova",type:"number",value:a.umidita_prova||"",onChange:e=>o("umidita_prova",parseFloat(e.target.value)),placeholder:"50",className:"text-sm"})]})]})]})]})]}):null}function N(e){let{formData:a,validationErrors:t,onInputChange:r}=e;return(0,s.jsxs)(m.Zp,{children:[(0,s.jsx)(m.aR,{className:"pb-1 px-3 pt-2",children:(0,s.jsxs)(m.ZB,{className:"flex items-center gap-2 text-sm font-semibold",children:[(0,s.jsx)(b.A,{className:"h-3 w-3"}),"Misurazioni e Test"]})}),(0,s.jsxs)(m.Wu,{className:"space-y-2 pt-0 px-3 pb-2",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3",children:[(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)(x.J,{htmlFor:"valore_isolamento",className:"text-sm",children:"Isolamento (MΩ) *"}),(0,s.jsx)(h.p,{id:"valore_isolamento",type:"number",step:"0.01",value:a.valore_isolamento||"",onChange:e=>r("valore_isolamento",parseFloat(e.target.value)),placeholder:"1000",className:"text-sm"}),t.valore_isolamento&&(0,s.jsx)("p",{className:"text-xs text-red-600",children:t.valore_isolamento})]}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)(x.J,{htmlFor:"valore_continuita",className:"text-sm",children:"Continuit\xe0 *"}),(0,s.jsxs)(v.l6,{value:a.valore_continuita,onValueChange:e=>r("valore_continuita",e),children:[(0,s.jsx)(v.bq,{className:"text-sm",children:(0,s.jsx)(v.yv,{placeholder:"Seleziona..."})}),(0,s.jsxs)(v.gC,{children:[(0,s.jsx)(v.eb,{value:"CONFORME",children:"✅ CONFORME"}),(0,s.jsx)(v.eb,{value:"NON_CONFORME",children:"❌ NON CONFORME"})]})]}),t.valore_continuita&&(0,s.jsx)("p",{className:"text-xs text-red-600",children:t.valore_continuita})]}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)(x.J,{htmlFor:"valore_resistenza",className:"text-sm",children:"Resistenza (Ω) *"}),(0,s.jsx)(h.p,{id:"valore_resistenza",type:"number",step:"0.01",value:a.valore_resistenza||"",onChange:e=>r("valore_resistenza",parseFloat(e.target.value)),placeholder:"0.5",className:"text-sm"}),t.valore_resistenza&&(0,s.jsx)("p",{className:"text-xs text-red-600",children:t.valore_resistenza})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)(x.J,{htmlFor:"tensione_prova_isolamento",className:"text-sm",children:"Tensione Prova (V)"}),(0,s.jsx)(h.p,{id:"tensione_prova_isolamento",type:"number",value:a.tensione_prova_isolamento||500,onChange:e=>r("tensione_prova_isolamento",parseInt(e.target.value)),className:"text-sm"})]}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)(x.J,{htmlFor:"durata_prova",className:"text-sm",children:"Durata Prova (min)"}),(0,s.jsx)(h.p,{id:"durata_prova",type:"number",value:a.durata_prova||"",onChange:e=>r("durata_prova",parseInt(e.target.value)),placeholder:"1",className:"text-sm"})]})]})]})]})}var _=t(88539);function y(e){let{formData:a,onInputChange:t}=e;return(0,s.jsxs)(m.Zp,{children:[(0,s.jsx)(m.aR,{className:"pb-1 px-3 pt-2",children:(0,s.jsx)(m.ZB,{className:"text-sm font-semibold",children:"Note e Stato"})}),(0,s.jsxs)(m.Wu,{className:"space-y-2 px-3 pb-2",children:[(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)(x.J,{htmlFor:"note",className:"text-sm",children:"Note"}),(0,s.jsx)(_.T,{id:"note",value:a.note,onChange:e=>t("note",e.target.value),placeholder:"Note aggiuntive...",rows:2,className:"text-sm"})]}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)(x.J,{htmlFor:"stato_certificato",className:"text-sm",children:"Stato Certificato"}),(0,s.jsxs)(v.l6,{value:a.stato_certificato||"BOZZA",onValueChange:e=>t("stato_certificato",e),children:[(0,s.jsx)(v.bq,{className:"text-sm",children:(0,s.jsx)(v.yv,{})}),(0,s.jsxs)(v.gC,{children:[(0,s.jsx)(v.eb,{value:"BOZZA",children:"Bozza"}),(0,s.jsx)(v.eb,{value:"CONFORME",children:"Conforme"}),(0,s.jsx)(v.eb,{value:"NON_CONFORME",children:"Non Conforme"}),(0,s.jsx)(v.eb,{value:"CONFORME_CON_RISERVA",children:"Conforme con Riserva"})]})]})]})]})]})}function w(e){let{cantiereId:a,certificazione:t,strumenti:m,preselectedCavoId:x,onSuccess:v,onCancel:h}=e,{formData:g,cavi:f,responsabili:b,weatherData:_,isLoading:w,isSaving:C,isLoadingWeather:z,error:k,validationErrors:O,isWeatherOverride:F,isEdit:S,isCavoLocked:A,handleInputChange:R,handleSubmit:E,setIsWeatherOverride:I,onCancel:Z}=function(e){let{cantiereId:a,certificazione:t,strumenti:s,preselectedCavoId:r,onSuccess:i,onCancel:n}=e,[l,o]=(0,c.useState)({id_cantiere:a,tipo_certificato:"SINGOLO",stato_certificato:"BOZZA",valore_continuita:"CONFORME",tensione_prova_isolamento:500,temperatura_prova:20,umidita_prova:50}),[d,m]=(0,c.useState)(!0),[x,v]=(0,c.useState)(!1),[p,h]=(0,c.useState)(!1),[g,f]=(0,c.useState)(""),[b,j]=(0,c.useState)({}),[N,_]=(0,c.useState)(!1),[y,w]=(0,c.useState)([]),[C,z]=(0,c.useState)([]),[k,O]=(0,c.useState)(null),F=!!t,S=!!r;async function A(){try{m(!0);let[e,t]=await Promise.all([u.At.getCavi(a),u.AR.getResponsabili(a)]);w(e.data||[]),z(t.data||[])}catch(e){console.error("Errore caricamento dati:",e),f("Errore nel caricamento dei dati iniziali")}finally{m(!1)}}async function R(){try{h(!0);let e=await u._I.getWeatherData(a);e.data&&O(e.data)}catch(e){console.error("Errore caricamento meteo:",e)}finally{h(!1)}}async function E(){if(!function(){let e={};return l.id_cavo||(e.id_cavo="Seleziona un cavo"),(!l.valore_isolamento||l.valore_isolamento<=0)&&(e.valore_isolamento="Inserisci un valore di isolamento valido"),l.valore_continuita||(e.valore_continuita="Seleziona il risultato della continuit\xe0"),l.valore_resistenza||(e.valore_resistenza="Inserisci un valore di resistenza"),j(e),0===Object.keys(e).length}())return void f("Correggi gli errori nel form prima di continuare");try{let e;v(!0),f("");let s=function(){let e={...l};return k&&!N&&(e.temperatura_prova=k.temperature,e.umidita_prova=k.humidity),e.id_cantiere=a,e.data_certificazione=e.data_certificazione||new Date().toISOString().split("T")[0],e}();(e=F&&t?await u.km.updateCertificazione(t.id_certificazione,s):await u.km.createCertificazione(s)).data&&i(e.data)}catch(a){var e,s;console.error("Errore salvataggio:",a),f((null==(s=a.response)||null==(e=s.data)?void 0:e.detail)||"Errore durante il salvataggio")}finally{v(!1)}}return(0,c.useEffect)(()=>{A(),R()},[a]),(0,c.useEffect)(()=>{t&&o({...t,id_cantiere:a})},[t,a]),(0,c.useEffect)(()=>{r&&!t&&o(e=>({...e,id_cavo:r}))},[r,t]),(0,c.useEffect)(()=>{!k||t||N||o(e=>({...e,temperatura_prova:k.temperature,umidita_prova:k.humidity}))},[k,t,N]),{formData:l,cavi:y,responsabili:C,strumenti:s,weatherData:k,isLoading:d,isSaving:x,isLoadingWeather:p,error:g,validationErrors:b,isWeatherOverride:N,isEdit:F,isCavoLocked:S,handleInputChange:function(e,a){o(t=>({...t,[e]:a})),b[e]&&j(a=>{let t={...a};return delete t[e],t})},handleSubmit:E,setIsWeatherOverride:_,onCancel:n}}({cantiereId:a,certificazione:t,strumenti:m,preselectedCavoId:x,onSuccess:v,onCancel:h});return w?(0,s.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,s.jsx)(n.A,{className:"h-8 w-8 animate-spin"}),(0,s.jsx)("span",{className:"ml-2",children:"Caricamento dati..."})]}):(0,s.jsxs)("div",{className:"h-full w-full",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 border-b bg-white",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(l.A,{className:"h-4 w-4 text-blue-600"}),(0,s.jsx)("h1",{className:"text-base font-semibold text-slate-900",children:S?"Modifica Certificazione":"Nuova Certificazione"})]}),(0,s.jsxs)(r.$,{onClick:E,disabled:C,size:"sm",children:[C?(0,s.jsx)(n.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,s.jsx)(o.A,{className:"h-4 w-4 mr-2"}),S?"Aggiorna":"Salva"]})]}),(0,s.jsxs)("div",{className:"p-3 h-[calc(100%-60px)] overflow-y-auto",children:[k&&(0,s.jsxs)(i.Fc,{variant:"destructive",className:"mb-2",children:[(0,s.jsx)(d.A,{className:"h-4 w-4"}),(0,s.jsx)(i.TN,{children:k})]}),(0,s.jsx)("form",{onSubmit:e=>{e.preventDefault(),E()},children:(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-3 h-full",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(p,{formData:g,cavi:f,responsabili:b,strumenti:m,validationErrors:O,isCavoLocked:A,onInputChange:R}),(0,s.jsx)(j,{formData:g,weatherData:_,isLoadingWeather:z,isWeatherOverride:F,onInputChange:R,onToggleWeatherOverride:()=>I(!F)})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(N,{formData:g,validationErrors:O,onInputChange:R}),(0,s.jsx)(y,{formData:g,onInputChange:R})]})]})})]})]})}},26126:(e,a,t)=>{t.d(a,{E:()=>o});var s=t(95155);t(12115);var r=t(99708),i=t(74466),n=t(59434);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:a,variant:t,asChild:i=!1,...o}=e,d=i?r.DX:"span";return(0,s.jsx)(d,{"data-slot":"badge",className:(0,n.cn)(l({variant:t}),a),...o})}},30285:(e,a,t)=>{t.d(a,{$:()=>o});var s=t(95155);t(12115);var r=t(99708),i=t(74466),n=t(59434);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:a,variant:t,size:i,asChild:o=!1,...d}=e,c=o?r.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,n.cn)(l({variant:t,size:i,className:a})),...d})}},55365:(e,a,t)=>{t.d(a,{Fc:()=>o,TN:()=>d});var s=t(95155),r=t(12115),i=t(74466),n=t(59434);let l=(0,i.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=r.forwardRef((e,a)=>{let{className:t,variant:r,...i}=e;return(0,s.jsx)("div",{ref:a,role:"alert",className:(0,n.cn)(l({variant:r}),t),...i})});o.displayName="Alert",r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("h5",{ref:a,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",t),...r})}).displayName="AlertTitle";let d=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("div",{ref:a,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",t),...r})});d.displayName="AlertDescription"},59409:(e,a,t)=>{t.d(a,{bq:()=>u,eb:()=>x,gC:()=>m,l6:()=>d,yv:()=>c});var s=t(95155);t(12115);var r=t(38715),i=t(66474),n=t(5196),l=t(47863),o=t(59434);function d(e){let{...a}=e;return(0,s.jsx)(r.bL,{"data-slot":"select",...a})}function c(e){let{...a}=e;return(0,s.jsx)(r.WT,{"data-slot":"select-value",...a})}function u(e){let{className:a,size:t="default",children:n,...l}=e;return(0,s.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":t,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...l,children:[n,(0,s.jsx)(r.In,{asChild:!0,children:(0,s.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function m(e){let{className:a,children:t,position:i="popper",...n}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:i,...n,children:[(0,s.jsx)(v,{}),(0,s.jsx)(r.LM,{className:(0,o.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,s.jsx)(p,{})]})})}function x(e){let{className:a,children:t,...i}=e;return(0,s.jsxs)(r.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",a),...i,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(n.A,{className:"size-4"})})}),(0,s.jsx)(r.p4,{children:t})]})}function v(e){let{className:a,...t}=e;return(0,s.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",a),...t,children:(0,s.jsx)(l.A,{className:"size-4"})})}function p(e){let{className:a,...t}=e;return(0,s.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",a),...t,children:(0,s.jsx)(i.A,{className:"size-4"})})}},59434:(e,a,t)=>{t.d(a,{cn:()=>i});var s=t(52596),r=t(39688);function i(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,r.QP)((0,s.$)(a))}},62523:(e,a,t)=>{t.d(a,{p:()=>n});var s=t(95155),r=t(12115),i=t(59434);let n=r.forwardRef((e,a)=>{let{className:t,type:r,...n}=e;return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),ref:a,...n})});n.displayName="Input"},66695:(e,a,t)=>{t.d(a,{BT:()=>o,Wu:()=>d,ZB:()=>l,Zp:()=>i,aR:()=>n});var s=t(95155);t(12115);var r=t(59434);function i(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...t})}function n(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...t})}function l(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",a),...t})}function o(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",a),...t})}function d(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",a),...t})}},85057:(e,a,t)=>{t.d(a,{J:()=>n});var s=t(95155);t(12115);var r=t(40968),i=t(59434);function n(e){let{className:a,...t}=e;return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...t})}},88539:(e,a,t)=>{t.d(a,{T:()=>n});var s=t(95155),r=t(12115),i=t(59434);let n=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:a,...r})});n.displayName="Textarea"}}]);