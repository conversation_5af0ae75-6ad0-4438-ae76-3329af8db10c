{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        data-slot=\"input\"\n        className={cn(\n          \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n          \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n          \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\n\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 211, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,mKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG,mKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 277, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm border-collapse\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"data-[state=selected]:bg-muted border-b\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;YAC9D,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2CACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 415, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 449, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 484, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 733, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/certificazioni/CertificazioneForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport { Input } from '@/components/ui/input'\nimport { Textarea } from '@/components/ui/textarea'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, Save, X, FileText, Settings, Cloud } from 'lucide-react'\nimport { CertificazioneCavo, CertificazioneCavoCreate, StrumentoCertificato } from '@/types/certificazioni'\nimport { certificazioniApi, caviApi, responsabiliApi, cantieriApi } from '@/lib/api'\n\ninterface CertificazioneFormProps {\n  cantiereId: number\n  certificazione?: CertificazioneCavo | null\n  strumenti: StrumentoCertificato[]\n  onSuccess: () => void\n  onCancel: () => void\n}\n\ninterface Cavo {\n  id_cavo: string\n  tipologia?: string\n  sezione?: string\n  lunghezza?: number\n  stato_installazione?: string\n}\n\ninterface Responsabile {\n  id_responsabile: number\n  nome_responsabile: string\n}\n\ninterface WeatherData {\n  temperature: number\n  humidity: number\n  pressure?: number\n  description?: string\n  city?: string\n  country?: string\n  timestamp?: string\n  isDemo?: boolean\n  source?: string\n}\n\nexport default function CertificazioneForm({\n  cantiereId,\n  certificazione,\n  strumenti,\n  onSuccess,\n  onCancel\n}: CertificazioneFormProps) {\n  const [formData, setFormData] = useState<CertificazioneCavoCreate>({\n    id_cavo: '',\n    id_operatore: undefined,\n    strumento_utilizzato: '',\n    id_strumento: undefined,\n    lunghezza_misurata: undefined,\n    valore_continuita: '',\n    valore_isolamento: '',\n    valore_resistenza: '',\n    note: '',\n    tipo_certificato: 'SINGOLO',\n    stato_certificato: 'BOZZA',\n    designazione_funzionale: '',\n    tensione_nominale: '230V',\n    tensione_prova_isolamento: 500,\n    durata_prova_isolamento: 60,\n    valore_minimo_isolamento: 500,\n    temperatura_prova: 20,\n    umidita_prova: 50,\n    esito_complessivo: ''\n  })\n\n  const [cavi, setCavi] = useState<Cavo[]>([])\n  const [responsabili, setResponsabili] = useState<Responsabile[]>([])\n  const [weatherData, setWeatherData] = useState<WeatherData | null>(null)\n  const [isLoading, setIsLoading] = useState(false)\n  const [isSaving, setIsSaving] = useState(false)\n  const [isLoadingWeather, setIsLoadingWeather] = useState(false)\n  const [error, setError] = useState('')\n  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})\n\n  const isEdit = !!certificazione\n\n  useEffect(() => {\n    loadInitialData()\n    loadWeatherData()\n    if (certificazione) {\n      setFormData({\n        id_cavo: certificazione.id_cavo,\n        id_operatore: certificazione.id_operatore,\n        strumento_utilizzato: certificazione.strumento_utilizzato || '',\n        id_strumento: certificazione.id_strumento,\n        lunghezza_misurata: certificazione.lunghezza_misurata,\n        valore_continuita: certificazione.valore_continuita || '',\n        valore_isolamento: certificazione.valore_isolamento || '',\n        valore_resistenza: certificazione.valore_resistenza || '',\n        note: certificazione.note || '',\n        tipo_certificato: certificazione.tipo_certificato || 'SINGOLO',\n        stato_certificato: certificazione.stato_certificato || 'BOZZA',\n        designazione_funzionale: certificazione.designazione_funzionale || '',\n        tensione_nominale: certificazione.tensione_nominale || '230V',\n        tensione_prova_isolamento: certificazione.tensione_prova_isolamento || 500,\n        durata_prova_isolamento: certificazione.durata_prova_isolamento || 60,\n        valore_minimo_isolamento: certificazione.valore_minimo_isolamento || 500,\n        temperatura_prova: certificazione.temperatura_prova || weatherData?.temperature || 20,\n        umidita_prova: certificazione.umidita_prova || weatherData?.humidity || 50,\n        esito_complessivo: certificazione.esito_complessivo || ''\n      })\n    }\n  }, [certificazione, weatherData])\n\n  const loadInitialData = async () => {\n    try {\n      setIsLoading(true)\n      const [caviData, responsabiliData] = await Promise.all([\n        caviApi.getCavi(cantiereId),\n        responsabiliApi.getResponsabili(cantiereId)\n      ])\n      setCavi(caviData)\n      setResponsabili(responsabiliData)\n    } catch (error: any) {\n      setError('Errore durante il caricamento dei dati')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const loadWeatherData = async () => {\n    try {\n      setIsLoadingWeather(true)\n      const response = await cantieriApi.getWeatherData(cantiereId)\n      if (response.success && response.data) {\n        setWeatherData(response.data)\n        // Aggiorna automaticamente i valori nel form se non sono già impostati\n        if (!certificazione) {\n          setFormData(prev => ({\n            ...prev,\n            temperatura_prova: response.data.temperature || 20,\n            umidita_prova: response.data.humidity || 50\n          }))\n        }\n      }\n    } catch (error: any) {\n      console.warn('Impossibile caricare dati meteorologici:', error)\n      // Non mostrare errore all'utente, usa valori predefiniti\n    } finally {\n      setIsLoadingWeather(false)\n    }\n  }\n\n  // Funzione per collegare automaticamente un cavo (CEI 64-8 compliance)\n  const collegaCavoAutomatico = async (cavoId: string, responsabile: string = 'cantiere') => {\n    try {\n      let partenzaCollegata = false\n      let arrivoCollegato = false\n\n      // Prova a collegare il lato partenza\n      try {\n        await caviApi.collegaCavo(cantiereId, cavoId, 'partenza', responsabile)\n        partenzaCollegata = true\n      } catch (error: any) {\n        if (error.response?.data?.detail?.includes('già collegato')) {\n          partenzaCollegata = true\n        } else {\n          throw error\n        }\n      }\n\n      // Prova a collegare il lato arrivo\n      try {\n        await caviApi.collegaCavo(cantiereId, cavoId, 'arrivo', responsabile)\n        arrivoCollegato = true\n      } catch (error: any) {\n        if (error.response?.data?.detail?.includes('già collegato')) {\n          arrivoCollegato = true\n        } else {\n          throw error\n        }\n      }\n\n      return partenzaCollegata && arrivoCollegato\n    } catch (error) {\n      console.error('Errore nel collegamento automatico:', error)\n      throw error\n    }\n  }\n\n  const validateForm = (): boolean => {\n    const errors: Record<string, string> = {}\n\n    if (!formData.id_cavo) {\n      errors.id_cavo = 'Seleziona un cavo'\n    }\n\n    if (!formData.valore_isolamento) {\n      errors.valore_isolamento = 'Inserisci il valore di isolamento'\n    } else if (parseFloat(formData.valore_isolamento) < 0) {\n      errors.valore_isolamento = 'Il valore deve essere positivo'\n    }\n\n    if (formData.tensione_prova_isolamento && formData.tensione_prova_isolamento < 0) {\n      errors.tensione_prova_isolamento = 'La tensione deve essere positiva'\n    }\n\n    if (formData.temperatura_prova && (formData.temperatura_prova < -40 || formData.temperatura_prova > 80)) {\n      errors.temperatura_prova = 'Temperatura deve essere tra -40°C e 80°C'\n    }\n\n    if (formData.umidita_prova && (formData.umidita_prova < 0 || formData.umidita_prova > 100)) {\n      errors.umidita_prova = 'Umidità deve essere tra 0% e 100%'\n    }\n\n    setValidationErrors(errors)\n    return Object.keys(errors).length === 0\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!validateForm()) {\n      return\n    }\n\n    try {\n      setIsSaving(true)\n      setError('')\n\n      // Verifica se il cavo è completamente collegato (CEI 64-8 compliance)\n      if (!isEdit && formData.id_cavo) {\n        const cavo = cavi.find(c => c.id_cavo === formData.id_cavo)\n        const isCompletelyConnected = cavo && (cavo.collegamenti || 0) === 3\n\n        if (!isCompletelyConnected) {\n          const statoCollegamenti = (cavo?.collegamenti || 0) === 0 ? 'Non collegato' :\n                                   (cavo?.collegamenti || 0) === 1 ? 'Solo partenza collegata' :\n                                   (cavo?.collegamenti || 0) === 2 ? 'Solo arrivo collegato' :\n                                   'Stato sconosciuto'\n\n          const conferma = window.confirm(\n            `ATTENZIONE: Il cavo ${formData.id_cavo} non risulta completamente collegato.\\n\\n` +\n            `Stato collegamenti: ${statoCollegamenti}\\n\\n` +\n            `Vuoi collegare automaticamente entrambi i lati del cavo e procedere con la certificazione CEI 64-8?\\n\\n` +\n            `(Il sistema collegherà automaticamente il cavo a \"cantiere\" su entrambi i lati)`\n          )\n\n          if (!conferma) {\n            setIsSaving(false)\n            return\n          }\n\n          // Collega automaticamente il cavo\n          try {\n            await collegaCavoAutomatico(formData.id_cavo, 'cantiere')\n            // Ricarica i cavi per aggiornare lo stato\n            const updatedCavi = await caviApi.getCavi(cantiereId)\n            setCavi(updatedCavi)\n          } catch (error: any) {\n            setError(`Errore nel collegamento automatico: ${error.response?.data?.detail || error.message}`)\n            setIsSaving(false)\n            return\n          }\n        }\n      }\n\n      // Determina automaticamente lo stato in base ai valori\n      const isolamento = parseFloat(formData.valore_isolamento || '0')\n      const statoAutomatico = isolamento >= (formData.valore_minimo_isolamento || 500) ? 'CONFORME' : 'NON_CONFORME'\n      \n      const dataToSubmit = {\n        ...formData,\n        stato_certificato: formData.stato_certificato === 'BOZZA' ? statoAutomatico : formData.stato_certificato,\n        esito_complessivo: isolamento >= (formData.valore_minimo_isolamento || 500) ? 'CONFORME' : 'NON CONFORME',\n        // Includi i dati meteorologici automatici\n        temperatura_prova: formData.temperatura_prova || weatherData?.temperature || 20,\n        umidita_prova: formData.umidita_prova || weatherData?.humidity || 50\n      }\n\n      if (isEdit && certificazione) {\n        await certificazioniApi.updateCertificazione(cantiereId, certificazione.id_certificazione, dataToSubmit)\n      } else {\n        await certificazioniApi.createCertificazione(cantiereId, dataToSubmit)\n      }\n\n      onSuccess()\n    } catch (error: any) {\n      setError(error.response?.data?.detail || 'Errore durante il salvataggio')\n    } finally {\n      setIsSaving(false)\n    }\n  }\n\n  const handleInputChange = (field: keyof CertificazioneCavoCreate, value: any) => {\n    setFormData(prev => ({ ...prev, [field]: value }))\n    \n    // Rimuovi errore di validazione se presente\n    if (validationErrors[field]) {\n      setValidationErrors(prev => {\n        const newErrors = { ...prev }\n        delete newErrors[field]\n        return newErrors\n      })\n    }\n  }\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center p-8\">\n        <div className=\"flex items-center gap-2\">\n          <Loader2 className=\"h-4 w-4 animate-spin\" />\n          Caricamento dati...\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-slate-900 flex items-center gap-3\">\n            <FileText className=\"h-6 w-6 text-blue-600\" />\n            {isEdit ? 'Modifica Certificazione' : 'Nuova Certificazione'}\n          </h1>\n          <p className=\"text-slate-600 mt-1\">\n            {isEdit ? 'Modifica i dati della certificazione esistente' : 'Crea una nuova certificazione CEI 64-8'}\n          </p>\n        </div>\n        \n        <div className=\"flex gap-2\">\n          <Button variant=\"outline\" onClick={onCancel}>\n            <X className=\"h-4 w-4 mr-2\" />\n            Annulla\n          </Button>\n          <Button onClick={handleSubmit} disabled={isSaving}>\n            {isSaving ? <Loader2 className=\"h-4 w-4 animate-spin mr-2\" /> : <Save className=\"h-4 w-4 mr-2\" />}\n            {isEdit ? 'Aggiorna' : 'Salva'}\n          </Button>\n        </div>\n      </div>\n\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        {/* Informazioni Base */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Informazioni Base</CardTitle>\n            <CardDescription>Dati principali della certificazione</CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"id_cavo\">Cavo *</Label>\n                <Select\n                  value={formData.id_cavo}\n                  onValueChange={(value) => handleInputChange('id_cavo', value)}\n                  disabled={isEdit}\n                >\n                  <SelectTrigger className={validationErrors.id_cavo ? 'border-red-500' : ''}>\n                    <SelectValue placeholder=\"Seleziona cavo...\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {cavi.map((cavo) => (\n                      <SelectItem key={cavo.id_cavo} value={cavo.id_cavo}>\n                        {cavo.id_cavo} - {cavo.tipologia} {cavo.sezione}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n                {validationErrors.id_cavo && (\n                  <p className=\"text-sm text-red-600\">{validationErrors.id_cavo}</p>\n                )}\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"id_operatore\">Operatore</Label>\n                <Select\n                  value={formData.id_operatore?.toString() || ''}\n                  onValueChange={(value) => handleInputChange('id_operatore', parseInt(value))}\n                >\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Seleziona operatore...\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {responsabili.map((resp) => (\n                      <SelectItem key={resp.id_responsabile} value={resp.id_responsabile.toString()}>\n                        {resp.nome_responsabile}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"id_strumento\">Strumento</Label>\n                <Select\n                  value={formData.id_strumento?.toString() || ''}\n                  onValueChange={(value) => {\n                    const strumento = strumenti.find(s => s.id_strumento === parseInt(value))\n                    handleInputChange('id_strumento', parseInt(value))\n                    if (strumento) {\n                      handleInputChange('strumento_utilizzato', `${strumento.marca} ${strumento.modello}`)\n                    }\n                  }}\n                >\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Seleziona strumento...\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {strumenti.map((strumento) => (\n                      <SelectItem key={strumento.id_strumento} value={strumento.id_strumento.toString()}>\n                        {strumento.nome} - {strumento.marca} {strumento.modello}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"tipo_certificato\">Tipo Certificato</Label>\n                <Select\n                  value={formData.tipo_certificato || 'SINGOLO'}\n                  onValueChange={(value) => handleInputChange('tipo_certificato', value)}\n                >\n                  <SelectTrigger>\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"SINGOLO\">Singolo</SelectItem>\n                    <SelectItem value=\"GRUPPO\">Gruppo</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Condizioni Ambientali Automatiche */}\n        {weatherData && (\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                <Cloud className=\"h-5 w-5\" />\n                Condizioni Ambientali (Rilevate Automaticamente)\n              </CardTitle>\n              <CardDescription>Dati meteorologici per il cantiere</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className={`p-4 rounded-lg border ${weatherData.isDemo ? 'bg-yellow-50 border-yellow-200' : 'bg-green-50 border-green-200'}`}>\n                <div className=\"flex items-center gap-4\">\n                  {isLoadingWeather ? (\n                    <Loader2 className=\"h-5 w-5 animate-spin\" />\n                  ) : (\n                    <span className=\"text-2xl\">🌤️</span>\n                  )}\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center gap-6\">\n                      <div>\n                        <span className=\"text-sm text-slate-600\">Temperatura:</span>\n                        <span className=\"ml-2 font-semibold\">{weatherData.temperature}°C</span>\n                      </div>\n                      <div>\n                        <span className=\"text-sm text-slate-600\">Umidità:</span>\n                        <span className=\"ml-2 font-semibold\">{weatherData.humidity}%</span>\n                      </div>\n                      {weatherData.city && (\n                        <div>\n                          <span className=\"text-sm text-slate-600\">Località:</span>\n                          <span className=\"ml-2 font-semibold\">{weatherData.city}</span>\n                        </div>\n                      )}\n                    </div>\n                    <div className=\"text-xs text-slate-500 mt-1\">\n                      {weatherData.isDemo ? 'Dati demo' : 'Dati live'} -\n                      {weatherData.source === 'cantiere_database' ? ' Database cantiere' : ' API meteo'}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        )}\n\n        {/* Misurazioni */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Settings className=\"h-5 w-5\" />\n              Misurazioni e Test\n            </CardTitle>\n            <CardDescription>Risultati delle prove elettriche</CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"valore_isolamento\">Isolamento (MΩ) *</Label>\n                <Input\n                  id=\"valore_isolamento\"\n                  type=\"number\"\n                  step=\"0.1\"\n                  value={formData.valore_isolamento}\n                  onChange={(e) => handleInputChange('valore_isolamento', e.target.value)}\n                  className={validationErrors.valore_isolamento ? 'border-red-500' : ''}\n                  placeholder=\"500\"\n                />\n                {validationErrors.valore_isolamento && (\n                  <p className=\"text-sm text-red-600\">{validationErrors.valore_isolamento}</p>\n                )}\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"valore_continuita\">Continuità</Label>\n                <Input\n                  id=\"valore_continuita\"\n                  value={formData.valore_continuita}\n                  onChange={(e) => handleInputChange('valore_continuita', e.target.value)}\n                  placeholder=\"OK\"\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"valore_resistenza\">Resistenza</Label>\n                <Input\n                  id=\"valore_resistenza\"\n                  value={formData.valore_resistenza}\n                  onChange={(e) => handleInputChange('valore_resistenza', e.target.value)}\n                  placeholder=\"OK\"\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"tensione_prova_isolamento\">Tensione Prova (V)</Label>\n                <Input\n                  id=\"tensione_prova_isolamento\"\n                  type=\"number\"\n                  value={formData.tensione_prova_isolamento}\n                  onChange={(e) => handleInputChange('tensione_prova_isolamento', parseInt(e.target.value))}\n                  placeholder=\"500\"\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"temperatura_prova\">Temperatura (°C)</Label>\n                <Input\n                  id=\"temperatura_prova\"\n                  type=\"number\"\n                  value={formData.temperatura_prova}\n                  onChange={(e) => handleInputChange('temperatura_prova', parseInt(e.target.value))}\n                  placeholder=\"20\"\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"umidita_prova\">Umidità (%)</Label>\n                <Input\n                  id=\"umidita_prova\"\n                  type=\"number\"\n                  min=\"0\"\n                  max=\"100\"\n                  value={formData.umidita_prova}\n                  onChange={(e) => handleInputChange('umidita_prova', parseInt(e.target.value))}\n                  placeholder=\"50\"\n                />\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Note e Stato */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Note e Stato</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"note\">Note</Label>\n              <Textarea\n                id=\"note\"\n                value={formData.note}\n                onChange={(e) => handleInputChange('note', e.target.value)}\n                placeholder=\"Note aggiuntive sulla certificazione...\"\n                rows={3}\n              />\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"stato_certificato\">Stato Certificato</Label>\n              <Select\n                value={formData.stato_certificato || 'BOZZA'}\n                onValueChange={(value) => handleInputChange('stato_certificato', value)}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"BOZZA\">Bozza</SelectItem>\n                  <SelectItem value=\"CONFORME\">Conforme</SelectItem>\n                  <SelectItem value=\"NON_CONFORME\">Non Conforme</SelectItem>\n                  <SelectItem value=\"CONFORME_CON_RISERVA\">Conforme con Riserva</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </CardContent>\n        </Card>\n\n        {error && (\n          <Alert className=\"border-red-200 bg-red-50\">\n            <AlertCircle className=\"h-4 w-4 text-red-600\" />\n            <AlertDescription className=\"text-red-800\">{error}</AlertDescription>\n          </Alert>\n        )}\n      </form>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;;;AAlBA;;;;;;;;;;;AAqDe,SAAS,mBAAmB,EACzC,UAAU,EACV,cAAc,EACd,SAAS,EACT,SAAS,EACT,QAAQ,EACgB;;IACxB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;QACjE,SAAS;QACT,cAAc;QACd,sBAAsB;QACtB,cAAc;QACd,oBAAoB;QACpB,mBAAmB;QACnB,mBAAmB;QACnB,mBAAmB;QACnB,MAAM;QACN,kBAAkB;QAClB,mBAAmB;QACnB,yBAAyB;QACzB,mBAAmB;QACnB,2BAA2B;QAC3B,yBAAyB;QACzB,0BAA0B;QAC1B,mBAAmB;QACnB,eAAe;QACf,mBAAmB;IACrB;IAEA,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAElF,MAAM,SAAS,CAAC,CAAC;IAEjB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR;YACA;YACA,IAAI,gBAAgB;gBAClB,YAAY;oBACV,SAAS,eAAe,OAAO;oBAC/B,cAAc,eAAe,YAAY;oBACzC,sBAAsB,eAAe,oBAAoB,IAAI;oBAC7D,cAAc,eAAe,YAAY;oBACzC,oBAAoB,eAAe,kBAAkB;oBACrD,mBAAmB,eAAe,iBAAiB,IAAI;oBACvD,mBAAmB,eAAe,iBAAiB,IAAI;oBACvD,mBAAmB,eAAe,iBAAiB,IAAI;oBACvD,MAAM,eAAe,IAAI,IAAI;oBAC7B,kBAAkB,eAAe,gBAAgB,IAAI;oBACrD,mBAAmB,eAAe,iBAAiB,IAAI;oBACvD,yBAAyB,eAAe,uBAAuB,IAAI;oBACnE,mBAAmB,eAAe,iBAAiB,IAAI;oBACvD,2BAA2B,eAAe,yBAAyB,IAAI;oBACvE,yBAAyB,eAAe,uBAAuB,IAAI;oBACnE,0BAA0B,eAAe,wBAAwB,IAAI;oBACrE,mBAAmB,eAAe,iBAAiB,IAAI,aAAa,eAAe;oBACnF,eAAe,eAAe,aAAa,IAAI,aAAa,YAAY;oBACxE,mBAAmB,eAAe,iBAAiB,IAAI;gBACzD;YACF;QACF;uCAAG;QAAC;QAAgB;KAAY;IAEhC,MAAM,kBAAkB;QACtB,IAAI;YACF,aAAa;YACb,MAAM,CAAC,UAAU,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACrD,oHAAA,CAAA,UAAO,CAAC,OAAO,CAAC;gBAChB,oHAAA,CAAA,kBAAe,CAAC,eAAe,CAAC;aACjC;YACD,QAAQ;YACR,gBAAgB;QAClB,EAAE,OAAO,OAAY;YACnB,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI;YACF,oBAAoB;YACpB,MAAM,WAAW,MAAM,oHAAA,CAAA,cAAW,CAAC,cAAc,CAAC;YAClD,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,eAAe,SAAS,IAAI;gBAC5B,uEAAuE;gBACvE,IAAI,CAAC,gBAAgB;oBACnB,YAAY,CAAA,OAAQ,CAAC;4BACnB,GAAG,IAAI;4BACP,mBAAmB,SAAS,IAAI,CAAC,WAAW,IAAI;4BAChD,eAAe,SAAS,IAAI,CAAC,QAAQ,IAAI;wBAC3C,CAAC;gBACH;YACF;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,IAAI,CAAC,4CAA4C;QACzD,yDAAyD;QAC3D,SAAU;YACR,oBAAoB;QACtB;IACF;IAEA,uEAAuE;IACvE,MAAM,wBAAwB,OAAO,QAAgB,eAAuB,UAAU;QACpF,IAAI;YACF,IAAI,oBAAoB;YACxB,IAAI,kBAAkB;YAEtB,qCAAqC;YACrC,IAAI;gBACF,MAAM,oHAAA,CAAA,UAAO,CAAC,WAAW,CAAC,YAAY,QAAQ,YAAY;gBAC1D,oBAAoB;YACtB,EAAE,OAAO,OAAY;gBACnB,IAAI,MAAM,QAAQ,EAAE,MAAM,QAAQ,SAAS,kBAAkB;oBAC3D,oBAAoB;gBACtB,OAAO;oBACL,MAAM;gBACR;YACF;YAEA,mCAAmC;YACnC,IAAI;gBACF,MAAM,oHAAA,CAAA,UAAO,CAAC,WAAW,CAAC,YAAY,QAAQ,UAAU;gBACxD,kBAAkB;YACpB,EAAE,OAAO,OAAY;gBACnB,IAAI,MAAM,QAAQ,EAAE,MAAM,QAAQ,SAAS,kBAAkB;oBAC3D,kBAAkB;gBACpB,OAAO;oBACL,MAAM;gBACR;YACF;YAEA,OAAO,qBAAqB;QAC9B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,MAAM;QACR;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,SAAiC,CAAC;QAExC,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,OAAO,OAAO,GAAG;QACnB;QAEA,IAAI,CAAC,SAAS,iBAAiB,EAAE;YAC/B,OAAO,iBAAiB,GAAG;QAC7B,OAAO,IAAI,WAAW,SAAS,iBAAiB,IAAI,GAAG;YACrD,OAAO,iBAAiB,GAAG;QAC7B;QAEA,IAAI,SAAS,yBAAyB,IAAI,SAAS,yBAAyB,GAAG,GAAG;YAChF,OAAO,yBAAyB,GAAG;QACrC;QAEA,IAAI,SAAS,iBAAiB,IAAI,CAAC,SAAS,iBAAiB,GAAG,CAAC,MAAM,SAAS,iBAAiB,GAAG,EAAE,GAAG;YACvG,OAAO,iBAAiB,GAAG;QAC7B;QAEA,IAAI,SAAS,aAAa,IAAI,CAAC,SAAS,aAAa,GAAG,KAAK,SAAS,aAAa,GAAG,GAAG,GAAG;YAC1F,OAAO,aAAa,GAAG;QACzB;QAEA,oBAAoB;QACpB,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;IACxC;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,IAAI;YACF,YAAY;YACZ,SAAS;YAET,sEAAsE;YACtE,IAAI,CAAC,UAAU,SAAS,OAAO,EAAE;gBAC/B,MAAM,OAAO,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,SAAS,OAAO;gBAC1D,MAAM,wBAAwB,QAAQ,CAAC,KAAK,YAAY,IAAI,CAAC,MAAM;gBAEnE,IAAI,CAAC,uBAAuB;oBAC1B,MAAM,oBAAoB,CAAC,MAAM,gBAAgB,CAAC,MAAM,IAAI,kBACnC,CAAC,MAAM,gBAAgB,CAAC,MAAM,IAAI,4BAClC,CAAC,MAAM,gBAAgB,CAAC,MAAM,IAAI,0BAClC;oBAEzB,MAAM,WAAW,OAAO,OAAO,CAC7B,CAAC,oBAAoB,EAAE,SAAS,OAAO,CAAC,yCAAyC,CAAC,GAClF,CAAC,oBAAoB,EAAE,kBAAkB,IAAI,CAAC,GAC9C,CAAC,uGAAuG,CAAC,GACzG,CAAC,+EAA+E,CAAC;oBAGnF,IAAI,CAAC,UAAU;wBACb,YAAY;wBACZ;oBACF;oBAEA,kCAAkC;oBAClC,IAAI;wBACF,MAAM,sBAAsB,SAAS,OAAO,EAAE;wBAC9C,0CAA0C;wBAC1C,MAAM,cAAc,MAAM,oHAAA,CAAA,UAAO,CAAC,OAAO,CAAC;wBAC1C,QAAQ;oBACV,EAAE,OAAO,OAAY;wBACnB,SAAS,CAAC,oCAAoC,EAAE,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,EAAE;wBAC/F,YAAY;wBACZ;oBACF;gBACF;YACF;YAEA,uDAAuD;YACvD,MAAM,aAAa,WAAW,SAAS,iBAAiB,IAAI;YAC5D,MAAM,kBAAkB,cAAc,CAAC,SAAS,wBAAwB,IAAI,GAAG,IAAI,aAAa;YAEhG,MAAM,eAAe;gBACnB,GAAG,QAAQ;gBACX,mBAAmB,SAAS,iBAAiB,KAAK,UAAU,kBAAkB,SAAS,iBAAiB;gBACxG,mBAAmB,cAAc,CAAC,SAAS,wBAAwB,IAAI,GAAG,IAAI,aAAa;gBAC3F,0CAA0C;gBAC1C,mBAAmB,SAAS,iBAAiB,IAAI,aAAa,eAAe;gBAC7E,eAAe,SAAS,aAAa,IAAI,aAAa,YAAY;YACpE;YAEA,IAAI,UAAU,gBAAgB;gBAC5B,MAAM,oHAAA,CAAA,oBAAiB,CAAC,oBAAoB,CAAC,YAAY,eAAe,iBAAiB,EAAE;YAC7F,OAAO;gBACL,MAAM,oHAAA,CAAA,oBAAiB,CAAC,oBAAoB,CAAC,YAAY;YAC3D;YAEA;QACF,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,QAAQ,EAAE,MAAM,UAAU;QAC3C,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,oBAAoB,CAAC,OAAuC;QAChE,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAEhD,4CAA4C;QAC5C,IAAI,gBAAgB,CAAC,MAAM,EAAE;YAC3B,oBAAoB,CAAA;gBAClB,MAAM,YAAY;oBAAE,GAAG,IAAI;gBAAC;gBAC5B,OAAO,SAAS,CAAC,MAAM;gBACvB,OAAO;YACT;QACF;IACF;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;oBAAyB;;;;;;;;;;;;IAKpD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCACnB,SAAS,4BAA4B;;;;;;;0CAExC,6LAAC;gCAAE,WAAU;0CACV,SAAS,mDAAmD;;;;;;;;;;;;kCAIjE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;;kDACjC,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGhC,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS;gCAAc,UAAU;;oCACtC,yBAAW,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAAiC,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAC/E,SAAS,aAAa;;;;;;;;;;;;;;;;;;;0BAK7B,6LAAC;gBAAK,UAAU;gBAAc,WAAU;;kCAEtC,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAEnB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;8DACzB,6LAAC,qIAAA,CAAA,SAAM;oDACL,OAAO,SAAS,OAAO;oDACvB,eAAe,CAAC,QAAU,kBAAkB,WAAW;oDACvD,UAAU;;sEAEV,6LAAC,qIAAA,CAAA,gBAAa;4DAAC,WAAW,iBAAiB,OAAO,GAAG,mBAAmB;sEACtE,cAAA,6LAAC,qIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,6LAAC,qIAAA,CAAA,gBAAa;sEACX,KAAK,GAAG,CAAC,CAAC,qBACT,6LAAC,qIAAA,CAAA,aAAU;oEAAoB,OAAO,KAAK,OAAO;;wEAC/C,KAAK,OAAO;wEAAC;wEAAI,KAAK,SAAS;wEAAC;wEAAE,KAAK,OAAO;;mEADhC,KAAK,OAAO;;;;;;;;;;;;;;;;gDAMlC,iBAAiB,OAAO,kBACvB,6LAAC;oDAAE,WAAU;8DAAwB,iBAAiB,OAAO;;;;;;;;;;;;sDAIjE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAe;;;;;;8DAC9B,6LAAC,qIAAA,CAAA,SAAM;oDACL,OAAO,SAAS,YAAY,EAAE,cAAc;oDAC5C,eAAe,CAAC,QAAU,kBAAkB,gBAAgB,SAAS;;sEAErE,6LAAC,qIAAA,CAAA,gBAAa;sEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,6LAAC,qIAAA,CAAA,gBAAa;sEACX,aAAa,GAAG,CAAC,CAAC,qBACjB,6LAAC,qIAAA,CAAA,aAAU;oEAA4B,OAAO,KAAK,eAAe,CAAC,QAAQ;8EACxE,KAAK,iBAAiB;mEADR,KAAK,eAAe;;;;;;;;;;;;;;;;;;;;;;sDAQ7C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAe;;;;;;8DAC9B,6LAAC,qIAAA,CAAA,SAAM;oDACL,OAAO,SAAS,YAAY,EAAE,cAAc;oDAC5C,eAAe,CAAC;wDACd,MAAM,YAAY,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK,SAAS;wDAClE,kBAAkB,gBAAgB,SAAS;wDAC3C,IAAI,WAAW;4DACb,kBAAkB,wBAAwB,GAAG,UAAU,KAAK,CAAC,CAAC,EAAE,UAAU,OAAO,EAAE;wDACrF;oDACF;;sEAEA,6LAAC,qIAAA,CAAA,gBAAa;sEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,6LAAC,qIAAA,CAAA,gBAAa;sEACX,UAAU,GAAG,CAAC,CAAC,0BACd,6LAAC,qIAAA,CAAA,aAAU;oEAA8B,OAAO,UAAU,YAAY,CAAC,QAAQ;;wEAC5E,UAAU,IAAI;wEAAC;wEAAI,UAAU,KAAK;wEAAC;wEAAE,UAAU,OAAO;;mEADxC,UAAU,YAAY;;;;;;;;;;;;;;;;;;;;;;sDAQ/C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAmB;;;;;;8DAClC,6LAAC,qIAAA,CAAA,SAAM;oDACL,OAAO,SAAS,gBAAgB,IAAI;oDACpC,eAAe,CAAC,QAAU,kBAAkB,oBAAoB;;sEAEhE,6LAAC,qIAAA,CAAA,gBAAa;sEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;sEAEd,6LAAC,qIAAA,CAAA,gBAAa;;8EACZ,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAU;;;;;;8EAC5B,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAStC,6BACC,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAG/B,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAEnB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAW,CAAC,sBAAsB,EAAE,YAAY,MAAM,GAAG,mCAAmC,gCAAgC;8CAC/H,cAAA,6LAAC;wCAAI,WAAU;;4CACZ,iCACC,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;qEAEnB,6LAAC;gDAAK,WAAU;0DAAW;;;;;;0DAE7B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAAyB;;;;;;kFACzC,6LAAC;wEAAK,WAAU;;4EAAsB,YAAY,WAAW;4EAAC;;;;;;;;;;;;;0EAEhE,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAAyB;;;;;;kFACzC,6LAAC;wEAAK,WAAU;;4EAAsB,YAAY,QAAQ;4EAAC;;;;;;;;;;;;;4DAE5D,YAAY,IAAI,kBACf,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAAyB;;;;;;kFACzC,6LAAC;wEAAK,WAAU;kFAAsB,YAAY,IAAI;;;;;;;;;;;;;;;;;;kEAI5D,6LAAC;wDAAI,WAAU;;4DACZ,YAAY,MAAM,GAAG,cAAc;4DAAY;4DAC/C,YAAY,MAAM,KAAK,sBAAsB,uBAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUnF,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAGlC,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAEnB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAoB;;;;;;8DACnC,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,iBAAiB;oDACjC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;oDACtE,WAAW,iBAAiB,iBAAiB,GAAG,mBAAmB;oDACnE,aAAY;;;;;;gDAEb,iBAAiB,iBAAiB,kBACjC,6LAAC;oDAAE,WAAU;8DAAwB,iBAAiB,iBAAiB;;;;;;;;;;;;sDAI3E,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAoB;;;;;;8DACnC,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,iBAAiB;oDACjC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;oDACtE,aAAY;;;;;;;;;;;;sDAIhB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAoB;;;;;;8DACnC,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,iBAAiB;oDACjC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;oDACtE,aAAY;;;;;;;;;;;;sDAIhB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAA4B;;;;;;8DAC3C,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,yBAAyB;oDACzC,UAAU,CAAC,IAAM,kBAAkB,6BAA6B,SAAS,EAAE,MAAM,CAAC,KAAK;oDACvF,aAAY;;;;;;;;;;;;sDAIhB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAoB;;;;;;8DACnC,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,iBAAiB;oDACjC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,SAAS,EAAE,MAAM,CAAC,KAAK;oDAC/E,aAAY;;;;;;;;;;;;sDAIhB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAgB;;;;;;8DAC/B,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,KAAI;oDACJ,KAAI;oDACJ,OAAO,SAAS,aAAa;oDAC7B,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,SAAS,EAAE,MAAM,CAAC,KAAK;oDAC3E,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQtB,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;8CAAC;;;;;;;;;;;0CAEb,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAO;;;;;;0DACtB,6LAAC,uIAAA,CAAA,WAAQ;gDACP,IAAG;gDACH,OAAO,SAAS,IAAI;gDACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;gDACzD,aAAY;gDACZ,MAAM;;;;;;;;;;;;kDAIV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAoB;;;;;;0DACnC,6LAAC,qIAAA,CAAA,SAAM;gDACL,OAAO,SAAS,iBAAiB,IAAI;gDACrC,eAAe,CAAC,QAAU,kBAAkB,qBAAqB;;kEAEjE,6LAAC,qIAAA,CAAA,gBAAa;kEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,6LAAC,qIAAA,CAAA,gBAAa;;0EACZ,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAQ;;;;;;0EAC1B,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAW;;;;;;0EAC7B,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAe;;;;;;0EACjC,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAOlD,uBACC,6LAAC,oIAAA,CAAA,QAAK;wBAAC,WAAU;;0CACf,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC,oIAAA,CAAA,mBAAgB;gCAAC,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;AAMxD;GA1jBwB;KAAA", "debugId": null}}, {"offset": {"line": 2021, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/certificazioni/StrumentoForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport { Input } from '@/components/ui/input'\nimport { Textarea } from '@/components/ui/textarea'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, Save, X, Settings } from 'lucide-react'\nimport { StrumentoCertificato, StrumentoCertificatoCreate } from '@/types/certificazioni'\nimport { strumentiApi } from '@/lib/api'\n\ninterface StrumentoFormProps {\n  cantiereId: number\n  strumento?: StrumentoCertificato | null\n  onSuccess: () => void\n  onCancel: () => void\n}\n\nexport default function StrumentoForm({\n  cantiereId,\n  strumento,\n  onSuccess,\n  onCancel\n}: StrumentoFormProps) {\n  const [formData, setFormData] = useState<StrumentoCertificatoCreate>({\n    nome: '',\n    marca: '',\n    modello: '',\n    numero_serie: '',\n    data_calibrazione: '',\n    data_scadenza_calibrazione: '',\n    note: '',\n    tipo_strumento: 'MEGGER',\n    ente_certificatore: '',\n    numero_certificato_calibrazione: '',\n    range_misura: '',\n    precisione: '',\n    stato_strumento: 'ATTIVO'\n  })\n\n  const [isSaving, setIsSaving] = useState(false)\n  const [error, setError] = useState('')\n  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})\n\n  const isEdit = !!strumento\n\n  useEffect(() => {\n    if (strumento) {\n      setFormData({\n        nome: strumento.nome,\n        marca: strumento.marca,\n        modello: strumento.modello,\n        numero_serie: strumento.numero_serie,\n        data_calibrazione: strumento.data_calibrazione.split('T')[0], // Format for input[type=\"date\"]\n        data_scadenza_calibrazione: strumento.data_scadenza_calibrazione.split('T')[0],\n        note: strumento.note || '',\n        tipo_strumento: strumento.tipo_strumento || 'MEGGER',\n        ente_certificatore: strumento.ente_certificatore || '',\n        numero_certificato_calibrazione: strumento.numero_certificato_calibrazione || '',\n        range_misura: strumento.range_misura || '',\n        precisione: strumento.precisione || '',\n        stato_strumento: strumento.stato_strumento || 'ATTIVO'\n      })\n    }\n  }, [strumento])\n\n  const validateForm = (): boolean => {\n    const errors: Record<string, string> = {}\n\n    if (!formData.nome.trim()) {\n      errors.nome = 'Il nome è obbligatorio'\n    }\n\n    if (!formData.marca.trim()) {\n      errors.marca = 'La marca è obbligatoria'\n    }\n\n    if (!formData.modello.trim()) {\n      errors.modello = 'Il modello è obbligatorio'\n    }\n\n    if (!formData.numero_serie.trim()) {\n      errors.numero_serie = 'Il numero di serie è obbligatorio'\n    }\n\n    if (!formData.data_calibrazione) {\n      errors.data_calibrazione = 'La data di calibrazione è obbligatoria'\n    }\n\n    if (!formData.data_scadenza_calibrazione) {\n      errors.data_scadenza_calibrazione = 'La data di scadenza è obbligatoria'\n    }\n\n    if (formData.data_calibrazione && formData.data_scadenza_calibrazione) {\n      const calibrazione = new Date(formData.data_calibrazione)\n      const scadenza = new Date(formData.data_scadenza_calibrazione)\n      \n      if (scadenza <= calibrazione) {\n        errors.data_scadenza_calibrazione = 'La data di scadenza deve essere successiva alla calibrazione'\n      }\n    }\n\n    setValidationErrors(errors)\n    return Object.keys(errors).length === 0\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!validateForm()) {\n      return\n    }\n\n    try {\n      setIsSaving(true)\n      setError('')\n\n      if (isEdit && strumento) {\n        await strumentiApi.updateStrumento(cantiereId, strumento.id_strumento, formData)\n      } else {\n        await strumentiApi.createStrumento(cantiereId, formData)\n      }\n\n      onSuccess()\n    } catch (error: any) {\n      setError(error.response?.data?.detail || 'Errore durante il salvataggio')\n    } finally {\n      setIsSaving(false)\n    }\n  }\n\n  const handleInputChange = (field: keyof StrumentoCertificatoCreate, value: any) => {\n    setFormData(prev => ({ ...prev, [field]: value }))\n    \n    // Rimuovi errore di validazione se presente\n    if (validationErrors[field]) {\n      setValidationErrors(prev => {\n        const newErrors = { ...prev }\n        delete newErrors[field]\n        return newErrors\n      })\n    }\n  }\n\n  // Calcola automaticamente la data di scadenza (1 anno dalla calibrazione)\n  const handleCalibrazioneChange = (value: string) => {\n    handleInputChange('data_calibrazione', value)\n    \n    if (value && !formData.data_scadenza_calibrazione) {\n      const calibrazione = new Date(value)\n      const scadenza = new Date(calibrazione)\n      scadenza.setFullYear(scadenza.getFullYear() + 1)\n      handleInputChange('data_scadenza_calibrazione', scadenza.toISOString().split('T')[0])\n    }\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h2 className=\"text-2xl font-bold text-slate-900 flex items-center gap-3\">\n            <Settings className=\"h-6 w-6 text-blue-600\" />\n            {isEdit ? 'Modifica Strumento' : 'Nuovo Strumento'}\n          </h2>\n          <p className=\"text-slate-600 mt-1\">\n            {isEdit ? 'Modifica i dati dello strumento esistente' : 'Aggiungi un nuovo strumento di misura'}\n          </p>\n        </div>\n        \n        <div className=\"flex gap-2\">\n          <Button variant=\"outline\" onClick={onCancel}>\n            <X className=\"h-4 w-4 mr-2\" />\n            Annulla\n          </Button>\n          <Button onClick={handleSubmit} disabled={isSaving}>\n            {isSaving ? <Loader2 className=\"h-4 w-4 animate-spin mr-2\" /> : <Save className=\"h-4 w-4 mr-2\" />}\n            {isEdit ? 'Aggiorna' : 'Salva'}\n          </Button>\n        </div>\n      </div>\n\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        {/* Informazioni Base */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Informazioni Base</CardTitle>\n            <CardDescription>Dati identificativi dello strumento</CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"nome\">Nome Strumento *</Label>\n                <Input\n                  id=\"nome\"\n                  value={formData.nome}\n                  onChange={(e) => handleInputChange('nome', e.target.value)}\n                  className={validationErrors.nome ? 'border-red-500' : ''}\n                  placeholder=\"es. Megger MFT1741\"\n                />\n                {validationErrors.nome && (\n                  <p className=\"text-sm text-red-600\">{validationErrors.nome}</p>\n                )}\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"tipo_strumento\">Tipo Strumento</Label>\n                <Select\n                  value={formData.tipo_strumento}\n                  onValueChange={(value) => handleInputChange('tipo_strumento', value)}\n                >\n                  <SelectTrigger>\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"MEGGER\">Megger</SelectItem>\n                    <SelectItem value=\"MULTIMETRO\">Multimetro</SelectItem>\n                    <SelectItem value=\"OSCILLOSCOPIO\">Oscilloscopio</SelectItem>\n                    <SelectItem value=\"ALTRO\">Altro</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"marca\">Marca *</Label>\n                <Input\n                  id=\"marca\"\n                  value={formData.marca}\n                  onChange={(e) => handleInputChange('marca', e.target.value)}\n                  className={validationErrors.marca ? 'border-red-500' : ''}\n                  placeholder=\"es. Fluke, Megger, Keysight\"\n                />\n                {validationErrors.marca && (\n                  <p className=\"text-sm text-red-600\">{validationErrors.marca}</p>\n                )}\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"modello\">Modello *</Label>\n                <Input\n                  id=\"modello\"\n                  value={formData.modello}\n                  onChange={(e) => handleInputChange('modello', e.target.value)}\n                  className={validationErrors.modello ? 'border-red-500' : ''}\n                  placeholder=\"es. MFT1741, 87V\"\n                />\n                {validationErrors.modello && (\n                  <p className=\"text-sm text-red-600\">{validationErrors.modello}</p>\n                )}\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"numero_serie\">Numero Serie *</Label>\n                <Input\n                  id=\"numero_serie\"\n                  value={formData.numero_serie}\n                  onChange={(e) => handleInputChange('numero_serie', e.target.value)}\n                  className={validationErrors.numero_serie ? 'border-red-500' : ''}\n                  placeholder=\"Numero di serie univoco\"\n                />\n                {validationErrors.numero_serie && (\n                  <p className=\"text-sm text-red-600\">{validationErrors.numero_serie}</p>\n                )}\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"stato_strumento\">Stato</Label>\n                <Select\n                  value={formData.stato_strumento}\n                  onValueChange={(value) => handleInputChange('stato_strumento', value)}\n                >\n                  <SelectTrigger>\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"ATTIVO\">Attivo</SelectItem>\n                    <SelectItem value=\"SCADUTO\">Scaduto</SelectItem>\n                    <SelectItem value=\"FUORI_SERVIZIO\">Fuori Servizio</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Calibrazione */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Calibrazione</CardTitle>\n            <CardDescription>Informazioni sulla calibrazione dello strumento</CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"data_calibrazione\">Data Calibrazione *</Label>\n                <Input\n                  id=\"data_calibrazione\"\n                  type=\"date\"\n                  value={formData.data_calibrazione}\n                  onChange={(e) => handleCalibrazioneChange(e.target.value)}\n                  className={validationErrors.data_calibrazione ? 'border-red-500' : ''}\n                />\n                {validationErrors.data_calibrazione && (\n                  <p className=\"text-sm text-red-600\">{validationErrors.data_calibrazione}</p>\n                )}\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"data_scadenza_calibrazione\">Data Scadenza *</Label>\n                <Input\n                  id=\"data_scadenza_calibrazione\"\n                  type=\"date\"\n                  value={formData.data_scadenza_calibrazione}\n                  onChange={(e) => handleInputChange('data_scadenza_calibrazione', e.target.value)}\n                  className={validationErrors.data_scadenza_calibrazione ? 'border-red-500' : ''}\n                />\n                {validationErrors.data_scadenza_calibrazione && (\n                  <p className=\"text-sm text-red-600\">{validationErrors.data_scadenza_calibrazione}</p>\n                )}\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"ente_certificatore\">Ente Certificatore</Label>\n                <Input\n                  id=\"ente_certificatore\"\n                  value={formData.ente_certificatore}\n                  onChange={(e) => handleInputChange('ente_certificatore', e.target.value)}\n                  placeholder=\"es. LAT 123, ACCREDIA\"\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"numero_certificato_calibrazione\">Numero Certificato</Label>\n                <Input\n                  id=\"numero_certificato_calibrazione\"\n                  value={formData.numero_certificato_calibrazione}\n                  onChange={(e) => handleInputChange('numero_certificato_calibrazione', e.target.value)}\n                  placeholder=\"Numero del certificato di calibrazione\"\n                />\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Specifiche Tecniche */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Specifiche Tecniche</CardTitle>\n            <CardDescription>Caratteristiche tecniche dello strumento</CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"range_misura\">Range di Misura</Label>\n                <Input\n                  id=\"range_misura\"\n                  value={formData.range_misura}\n                  onChange={(e) => handleInputChange('range_misura', e.target.value)}\n                  placeholder=\"es. 0-1000V, 0-20A\"\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"precisione\">Precisione</Label>\n                <Input\n                  id=\"precisione\"\n                  value={formData.precisione}\n                  onChange={(e) => handleInputChange('precisione', e.target.value)}\n                  placeholder=\"es. ±0.1%, ±2 digit\"\n                />\n              </div>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"note\">Note</Label>\n              <Textarea\n                id=\"note\"\n                value={formData.note}\n                onChange={(e) => handleInputChange('note', e.target.value)}\n                placeholder=\"Note aggiuntive sullo strumento...\"\n                rows={3}\n              />\n            </div>\n          </CardContent>\n        </Card>\n\n        {error && (\n          <Alert className=\"border-red-200 bg-red-50\">\n            <AlertCircle className=\"h-4 w-4 text-red-600\" />\n            <AlertDescription className=\"text-red-800\">{error}</AlertDescription>\n          </Alert>\n        )}\n      </form>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;;;AAlBA;;;;;;;;;;;AA2Be,SAAS,cAAc,EACpC,UAAU,EACV,SAAS,EACT,SAAS,EACT,QAAQ,EACW;;IACnB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B;QACnE,MAAM;QACN,OAAO;QACP,SAAS;QACT,cAAc;QACd,mBAAmB;QACnB,4BAA4B;QAC5B,MAAM;QACN,gBAAgB;QAChB,oBAAoB;QACpB,iCAAiC;QACjC,cAAc;QACd,YAAY;QACZ,iBAAiB;IACnB;IAEA,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAElF,MAAM,SAAS,CAAC,CAAC;IAEjB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,WAAW;gBACb,YAAY;oBACV,MAAM,UAAU,IAAI;oBACpB,OAAO,UAAU,KAAK;oBACtB,SAAS,UAAU,OAAO;oBAC1B,cAAc,UAAU,YAAY;oBACpC,mBAAmB,UAAU,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;oBAC5D,4BAA4B,UAAU,0BAA0B,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;oBAC9E,MAAM,UAAU,IAAI,IAAI;oBACxB,gBAAgB,UAAU,cAAc,IAAI;oBAC5C,oBAAoB,UAAU,kBAAkB,IAAI;oBACpD,iCAAiC,UAAU,+BAA+B,IAAI;oBAC9E,cAAc,UAAU,YAAY,IAAI;oBACxC,YAAY,UAAU,UAAU,IAAI;oBACpC,iBAAiB,UAAU,eAAe,IAAI;gBAChD;YACF;QACF;kCAAG;QAAC;KAAU;IAEd,MAAM,eAAe;QACnB,MAAM,SAAiC,CAAC;QAExC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,OAAO,IAAI,GAAG;QAChB;QAEA,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,OAAO,KAAK,GAAG;QACjB;QAEA,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,OAAO,OAAO,GAAG;QACnB;QAEA,IAAI,CAAC,SAAS,YAAY,CAAC,IAAI,IAAI;YACjC,OAAO,YAAY,GAAG;QACxB;QAEA,IAAI,CAAC,SAAS,iBAAiB,EAAE;YAC/B,OAAO,iBAAiB,GAAG;QAC7B;QAEA,IAAI,CAAC,SAAS,0BAA0B,EAAE;YACxC,OAAO,0BAA0B,GAAG;QACtC;QAEA,IAAI,SAAS,iBAAiB,IAAI,SAAS,0BAA0B,EAAE;YACrE,MAAM,eAAe,IAAI,KAAK,SAAS,iBAAiB;YACxD,MAAM,WAAW,IAAI,KAAK,SAAS,0BAA0B;YAE7D,IAAI,YAAY,cAAc;gBAC5B,OAAO,0BAA0B,GAAG;YACtC;QACF;QAEA,oBAAoB;QACpB,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;IACxC;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,IAAI;YACF,YAAY;YACZ,SAAS;YAET,IAAI,UAAU,WAAW;gBACvB,MAAM,oHAAA,CAAA,eAAY,CAAC,eAAe,CAAC,YAAY,UAAU,YAAY,EAAE;YACzE,OAAO;gBACL,MAAM,oHAAA,CAAA,eAAY,CAAC,eAAe,CAAC,YAAY;YACjD;YAEA;QACF,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,QAAQ,EAAE,MAAM,UAAU;QAC3C,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,oBAAoB,CAAC,OAAyC;QAClE,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAEhD,4CAA4C;QAC5C,IAAI,gBAAgB,CAAC,MAAM,EAAE;YAC3B,oBAAoB,CAAA;gBAClB,MAAM,YAAY;oBAAE,GAAG,IAAI;gBAAC;gBAC5B,OAAO,SAAS,CAAC,MAAM;gBACvB,OAAO;YACT;QACF;IACF;IAEA,0EAA0E;IAC1E,MAAM,2BAA2B,CAAC;QAChC,kBAAkB,qBAAqB;QAEvC,IAAI,SAAS,CAAC,SAAS,0BAA0B,EAAE;YACjD,MAAM,eAAe,IAAI,KAAK;YAC9B,MAAM,WAAW,IAAI,KAAK;YAC1B,SAAS,WAAW,CAAC,SAAS,WAAW,KAAK;YAC9C,kBAAkB,8BAA8B,SAAS,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACtF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCACnB,SAAS,uBAAuB;;;;;;;0CAEnC,6LAAC;gCAAE,WAAU;0CACV,SAAS,8CAA8C;;;;;;;;;;;;kCAI5D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;;kDACjC,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGhC,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS;gCAAc,UAAU;;oCACtC,yBAAW,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAAiC,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAC/E,SAAS,aAAa;;;;;;;;;;;;;;;;;;;0BAK7B,6LAAC;gBAAK,UAAU;gBAAc,WAAU;;kCAEtC,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAEnB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAO;;;;;;8DACtB,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,IAAI;oDACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;oDACzD,WAAW,iBAAiB,IAAI,GAAG,mBAAmB;oDACtD,aAAY;;;;;;gDAEb,iBAAiB,IAAI,kBACpB,6LAAC;oDAAE,WAAU;8DAAwB,iBAAiB,IAAI;;;;;;;;;;;;sDAI9D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAiB;;;;;;8DAChC,6LAAC,qIAAA,CAAA,SAAM;oDACL,OAAO,SAAS,cAAc;oDAC9B,eAAe,CAAC,QAAU,kBAAkB,kBAAkB;;sEAE9D,6LAAC,qIAAA,CAAA,gBAAa;sEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;sEAEd,6LAAC,qIAAA,CAAA,gBAAa;;8EACZ,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAS;;;;;;8EAC3B,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAa;;;;;;8EAC/B,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAgB;;;;;;8EAClC,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAQ;;;;;;;;;;;;;;;;;;;;;;;;sDAKhC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAQ;;;;;;8DACvB,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,KAAK;oDACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;oDAC1D,WAAW,iBAAiB,KAAK,GAAG,mBAAmB;oDACvD,aAAY;;;;;;gDAEb,iBAAiB,KAAK,kBACrB,6LAAC;oDAAE,WAAU;8DAAwB,iBAAiB,KAAK;;;;;;;;;;;;sDAI/D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;8DACzB,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,OAAO;oDACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oDAC5D,WAAW,iBAAiB,OAAO,GAAG,mBAAmB;oDACzD,aAAY;;;;;;gDAEb,iBAAiB,OAAO,kBACvB,6LAAC;oDAAE,WAAU;8DAAwB,iBAAiB,OAAO;;;;;;;;;;;;sDAIjE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAe;;;;;;8DAC9B,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,YAAY;oDAC5B,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;oDACjE,WAAW,iBAAiB,YAAY,GAAG,mBAAmB;oDAC9D,aAAY;;;;;;gDAEb,iBAAiB,YAAY,kBAC5B,6LAAC;oDAAE,WAAU;8DAAwB,iBAAiB,YAAY;;;;;;;;;;;;sDAItE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAkB;;;;;;8DACjC,6LAAC,qIAAA,CAAA,SAAM;oDACL,OAAO,SAAS,eAAe;oDAC/B,eAAe,CAAC,QAAU,kBAAkB,mBAAmB;;sEAE/D,6LAAC,qIAAA,CAAA,gBAAa;sEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;sEAEd,6LAAC,qIAAA,CAAA,gBAAa;;8EACZ,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAS;;;;;;8EAC3B,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAU;;;;;;8EAC5B,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS/C,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAEnB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAoB;;;;;;8DACnC,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,iBAAiB;oDACjC,UAAU,CAAC,IAAM,yBAAyB,EAAE,MAAM,CAAC,KAAK;oDACxD,WAAW,iBAAiB,iBAAiB,GAAG,mBAAmB;;;;;;gDAEpE,iBAAiB,iBAAiB,kBACjC,6LAAC;oDAAE,WAAU;8DAAwB,iBAAiB,iBAAiB;;;;;;;;;;;;sDAI3E,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAA6B;;;;;;8DAC5C,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,0BAA0B;oDAC1C,UAAU,CAAC,IAAM,kBAAkB,8BAA8B,EAAE,MAAM,CAAC,KAAK;oDAC/E,WAAW,iBAAiB,0BAA0B,GAAG,mBAAmB;;;;;;gDAE7E,iBAAiB,0BAA0B,kBAC1C,6LAAC;oDAAE,WAAU;8DAAwB,iBAAiB,0BAA0B;;;;;;;;;;;;sDAIpF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAqB;;;;;;8DACpC,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,kBAAkB;oDAClC,UAAU,CAAC,IAAM,kBAAkB,sBAAsB,EAAE,MAAM,CAAC,KAAK;oDACvE,aAAY;;;;;;;;;;;;sDAIhB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAkC;;;;;;8DACjD,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,+BAA+B;oDAC/C,UAAU,CAAC,IAAM,kBAAkB,mCAAmC,EAAE,MAAM,CAAC,KAAK;oDACpF,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQtB,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAEnB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAe;;;;;;kEAC9B,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,SAAS,YAAY;wDAC5B,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;wDACjE,aAAY;;;;;;;;;;;;0DAIhB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAa;;;;;;kEAC5B,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,SAAS,UAAU;wDAC1B,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC/D,aAAY;;;;;;;;;;;;;;;;;;kDAKlB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAO;;;;;;0DACtB,6LAAC,uIAAA,CAAA,WAAQ;gDACP,IAAG;gDACH,OAAO,SAAS,IAAI;gDACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;gDACzD,aAAY;gDACZ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;oBAMb,uBACC,6LAAC,oIAAA,CAAA,QAAK;wBAAC,WAAU;;0CACf,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC,oIAAA,CAAA,mBAAgB;gCAAC,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;AAMxD;GAzXwB;KAAA", "debugId": null}}, {"offset": {"line": 2962, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/certificazioni/StrumentiManager.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Input } from '@/components/ui/input'\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { \n  Settings, \n  Search, \n  Plus, \n  Edit, \n  Trash2, \n  AlertCircle,\n  CheckCircle,\n  Clock,\n  Loader2\n} from 'lucide-react'\nimport { StrumentoCertificato } from '@/types/certificazioni'\nimport { strumentiApi } from '@/lib/api'\nimport StrumentoForm from './StrumentoForm'\n\ninterface StrumentiManagerProps {\n  cantiereId: number\n  strumenti: StrumentoCertificato[]\n  onUpdate: () => void\n}\n\nexport default function StrumentiManager({ cantiereId, strumenti, onUpdate }: StrumentiManagerProps) {\n  const [searchTerm, setSearchTerm] = useState('')\n  const [showForm, setShowForm] = useState(false)\n  const [selectedStrumento, setSelectedStrumento] = useState<StrumentoCertificato | null>(null)\n  const [isLoading, setIsLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const handleCreateStrumento = () => {\n    setSelectedStrumento(null)\n    setShowForm(true)\n  }\n\n  const handleEditStrumento = (strumento: StrumentoCertificato) => {\n    setSelectedStrumento(strumento)\n    setShowForm(true)\n  }\n\n  const handleDeleteStrumento = async (id: number) => {\n    if (!confirm('Sei sicuro di voler eliminare questo strumento?')) return\n    \n    try {\n      setIsLoading(true)\n      await strumentiApi.deleteStrumento(cantiereId, id)\n      onUpdate()\n    } catch (error: any) {\n      setError(error.response?.data?.detail || 'Errore durante l\\'eliminazione')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const getStatoBadge = (strumento: StrumentoCertificato) => {\n    const oggi = new Date()\n    const scadenza = new Date(strumento.data_scadenza_calibrazione)\n    const giorniRimanenti = Math.ceil((scadenza.getTime() - oggi.getTime()) / (1000 * 60 * 60 * 24))\n\n    if (strumento.stato_strumento === 'FUORI_SERVIZIO') {\n      return <Badge className=\"bg-gray-100 text-gray-800 border-gray-200\">Fuori Servizio</Badge>\n    }\n\n    if (giorniRimanenti < 0) {\n      return <Badge className=\"bg-red-100 text-red-800 border-red-200\">Scaduto</Badge>\n    }\n\n    if (giorniRimanenti <= 30) {\n      return <Badge className=\"bg-yellow-100 text-yellow-800 border-yellow-200\">In Scadenza</Badge>\n    }\n\n    return <Badge className=\"bg-green-100 text-green-800 border-green-200\">Attivo</Badge>\n  }\n\n  const getStatoIcon = (strumento: StrumentoCertificato) => {\n    const oggi = new Date()\n    const scadenza = new Date(strumento.data_scadenza_calibrazione)\n    const giorniRimanenti = Math.ceil((scadenza.getTime() - oggi.getTime()) / (1000 * 60 * 60 * 24))\n\n    if (strumento.stato_strumento === 'FUORI_SERVIZIO' || giorniRimanenti < 0) {\n      return <AlertCircle className=\"h-4 w-4 text-red-500\" />\n    }\n\n    if (giorniRimanenti <= 30) {\n      return <Clock className=\"h-4 w-4 text-yellow-500\" />\n    }\n\n    return <CheckCircle className=\"h-4 w-4 text-green-500\" />\n  }\n\n  const filteredStrumenti = strumenti.filter(strumento => {\n    const searchLower = searchTerm.toLowerCase()\n    return (\n      strumento.nome?.toLowerCase().includes(searchLower) ||\n      strumento.marca?.toLowerCase().includes(searchLower) ||\n      strumento.modello?.toLowerCase().includes(searchLower) ||\n      strumento.numero_serie?.toLowerCase().includes(searchLower)\n    )\n  })\n\n  const stats = {\n    totali: strumenti.length,\n    attivi: strumenti.filter(s => {\n      const oggi = new Date()\n      const scadenza = new Date(s.data_scadenza_calibrazione)\n      return s.stato_strumento === 'ATTIVO' && scadenza > oggi\n    }).length,\n    scaduti: strumenti.filter(s => {\n      const oggi = new Date()\n      const scadenza = new Date(s.data_scadenza_calibrazione)\n      return scadenza <= oggi\n    }).length,\n    in_scadenza: strumenti.filter(s => {\n      const oggi = new Date()\n      const scadenza = new Date(s.data_scadenza_calibrazione)\n      const giorniRimanenti = Math.ceil((scadenza.getTime() - oggi.getTime()) / (1000 * 60 * 60 * 24))\n      return giorniRimanenti > 0 && giorniRimanenti <= 30\n    }).length\n  }\n\n  if (showForm) {\n    return (\n      <StrumentoForm\n        cantiereId={cantiereId}\n        strumento={selectedStrumento}\n        onSuccess={() => {\n          setShowForm(false)\n          onUpdate()\n        }}\n        onCancel={() => setShowForm(false)}\n      />\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h2 className=\"text-2xl font-bold text-slate-900 flex items-center gap-3\">\n            <Settings className=\"h-6 w-6 text-blue-600\" />\n            Gestione Strumenti\n          </h2>\n          <p className=\"text-slate-600 mt-1\">Strumenti di misura e calibrazione</p>\n        </div>\n        \n        <Button onClick={handleCreateStrumento}>\n          <Plus className=\"h-4 w-4 mr-2\" />\n          Nuovo Strumento\n        </Button>\n      </div>\n\n      {/* Statistics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-slate-600\">Totali</p>\n                <p className=\"text-2xl font-bold text-slate-900\">{stats.totali}</p>\n              </div>\n              <Settings className=\"h-8 w-8 text-blue-500\" />\n            </div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-slate-600\">Attivi</p>\n                <p className=\"text-2xl font-bold text-green-600\">{stats.attivi}</p>\n              </div>\n              <CheckCircle className=\"h-8 w-8 text-green-500\" />\n            </div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-slate-600\">In Scadenza</p>\n                <p className=\"text-2xl font-bold text-yellow-600\">{stats.in_scadenza}</p>\n              </div>\n              <Clock className=\"h-8 w-8 text-yellow-500\" />\n            </div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-slate-600\">Scaduti</p>\n                <p className=\"text-2xl font-bold text-red-600\">{stats.scaduti}</p>\n              </div>\n              <AlertCircle className=\"h-8 w-8 text-red-500\" />\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Search */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Search className=\"h-5 w-5\" />\n            Ricerca Strumenti\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <Input\n            placeholder=\"Cerca per nome, marca, modello o numero serie...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n          />\n        </CardContent>\n      </Card>\n\n      {/* Strumenti Table */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Elenco Strumenti ({filteredStrumenti.length})</CardTitle>\n          <CardDescription>\n            Gestione strumenti di misura e certificazione\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"rounded-md border\">\n            <Table>\n              <TableHeader>\n                <TableRow>\n                  <TableHead>Nome</TableHead>\n                  <TableHead>Marca/Modello</TableHead>\n                  <TableHead>Numero Serie</TableHead>\n                  <TableHead>Tipo</TableHead>\n                  <TableHead>Calibrazione</TableHead>\n                  <TableHead>Scadenza</TableHead>\n                  <TableHead>Stato</TableHead>\n                  <TableHead>Azioni</TableHead>\n                </TableRow>\n              </TableHeader>\n              <TableBody>\n                {isLoading ? (\n                  <TableRow>\n                    <TableCell colSpan={8} className=\"text-center py-8\">\n                      <div className=\"flex items-center justify-center gap-2\">\n                        <Loader2 className=\"h-4 w-4 animate-spin\" />\n                        Caricamento...\n                      </div>\n                    </TableCell>\n                  </TableRow>\n                ) : filteredStrumenti.length === 0 ? (\n                  <TableRow>\n                    <TableCell colSpan={8} className=\"text-center py-8 text-slate-500\">\n                      Nessuno strumento trovato\n                    </TableCell>\n                  </TableRow>\n                ) : (\n                  filteredStrumenti.map((strumento) => {\n                    const oggi = new Date()\n                    const scadenza = new Date(strumento.data_scadenza_calibrazione)\n                    const giorniRimanenti = Math.ceil((scadenza.getTime() - oggi.getTime()) / (1000 * 60 * 60 * 24))\n\n                    return (\n                      <TableRow key={strumento.id_strumento}>\n                        <TableCell className=\"font-medium\">\n                          <div className=\"flex items-center gap-2\">\n                            {getStatoIcon(strumento)}\n                            {strumento.nome}\n                          </div>\n                        </TableCell>\n                        <TableCell>\n                          <div>\n                            <div className=\"font-medium\">{strumento.marca}</div>\n                            <div className=\"text-sm text-slate-500\">{strumento.modello}</div>\n                          </div>\n                        </TableCell>\n                        <TableCell className=\"font-mono text-sm\">{strumento.numero_serie}</TableCell>\n                        <TableCell>\n                          <Badge variant=\"outline\">\n                            {strumento.tipo_strumento || 'N/A'}\n                          </Badge>\n                        </TableCell>\n                        <TableCell>\n                          {new Date(strumento.data_calibrazione).toLocaleDateString('it-IT')}\n                        </TableCell>\n                        <TableCell>\n                          <div>\n                            <div>{new Date(strumento.data_scadenza_calibrazione).toLocaleDateString('it-IT')}</div>\n                            {giorniRimanenti > 0 && giorniRimanenti <= 30 && (\n                              <div className=\"text-xs text-yellow-600\">\n                                {giorniRimanenti} giorni rimanenti\n                              </div>\n                            )}\n                            {giorniRimanenti < 0 && (\n                              <div className=\"text-xs text-red-600\">\n                                Scaduto da {Math.abs(giorniRimanenti)} giorni\n                              </div>\n                            )}\n                          </div>\n                        </TableCell>\n                        <TableCell>{getStatoBadge(strumento)}</TableCell>\n                        <TableCell>\n                          <div className=\"flex gap-1\">\n                            <Button \n                              variant=\"ghost\" \n                              size=\"sm\"\n                              onClick={() => handleEditStrumento(strumento)}\n                            >\n                              <Edit className=\"h-4 w-4\" />\n                            </Button>\n                            <Button \n                              variant=\"ghost\" \n                              size=\"sm\"\n                              onClick={() => handleDeleteStrumento(strumento.id_strumento)}\n                            >\n                              <Trash2 className=\"h-4 w-4\" />\n                            </Button>\n                          </div>\n                        </TableCell>\n                      </TableRow>\n                    )\n                  })\n                )}\n              </TableBody>\n            </Table>\n          </div>\n        </CardContent>\n      </Card>\n\n      {error && (\n        <Alert className=\"border-red-200 bg-red-50\">\n          <AlertCircle className=\"h-4 w-4 text-red-600\" />\n          <AlertDescription className=\"text-red-800\">{error}</AlertDescription>\n        </Alert>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;;;AAtBA;;;;;;;;;;;AA8Be,SAAS,iBAAiB,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAyB;;IACjG,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA+B;IACxF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,wBAAwB;QAC5B,qBAAqB;QACrB,YAAY;IACd;IAEA,MAAM,sBAAsB,CAAC;QAC3B,qBAAqB;QACrB,YAAY;IACd;IAEA,MAAM,wBAAwB,OAAO;QACnC,IAAI,CAAC,QAAQ,oDAAoD;QAEjE,IAAI;YACF,aAAa;YACb,MAAM,oHAAA,CAAA,eAAY,CAAC,eAAe,CAAC,YAAY;YAC/C;QACF,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,QAAQ,EAAE,MAAM,UAAU;QAC3C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,OAAO,IAAI;QACjB,MAAM,WAAW,IAAI,KAAK,UAAU,0BAA0B;QAC9D,MAAM,kBAAkB,KAAK,IAAI,CAAC,CAAC,SAAS,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;QAE9F,IAAI,UAAU,eAAe,KAAK,kBAAkB;YAClD,qBAAO,6LAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;0BAA4C;;;;;;QACtE;QAEA,IAAI,kBAAkB,GAAG;YACvB,qBAAO,6LAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;0BAAyC;;;;;;QACnE;QAEA,IAAI,mBAAmB,IAAI;YACzB,qBAAO,6LAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;0BAAkD;;;;;;QAC5E;QAEA,qBAAO,6LAAC,oIAAA,CAAA,QAAK;YAAC,WAAU;sBAA+C;;;;;;IACzE;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,OAAO,IAAI;QACjB,MAAM,WAAW,IAAI,KAAK,UAAU,0BAA0B;QAC9D,MAAM,kBAAkB,KAAK,IAAI,CAAC,CAAC,SAAS,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;QAE9F,IAAI,UAAU,eAAe,KAAK,oBAAoB,kBAAkB,GAAG;YACzE,qBAAO,6LAAC,uNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;QAChC;QAEA,IAAI,mBAAmB,IAAI;YACzB,qBAAO,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QAC1B;QAEA,qBAAO,6LAAC,8NAAA,CAAA,cAAW;YAAC,WAAU;;;;;;IAChC;IAEA,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAA;QACzC,MAAM,cAAc,WAAW,WAAW;QAC1C,OACE,UAAU,IAAI,EAAE,cAAc,SAAS,gBACvC,UAAU,KAAK,EAAE,cAAc,SAAS,gBACxC,UAAU,OAAO,EAAE,cAAc,SAAS,gBAC1C,UAAU,YAAY,EAAE,cAAc,SAAS;IAEnD;IAEA,MAAM,QAAQ;QACZ,QAAQ,UAAU,MAAM;QACxB,QAAQ,UAAU,MAAM,CAAC,CAAA;YACvB,MAAM,OAAO,IAAI;YACjB,MAAM,WAAW,IAAI,KAAK,EAAE,0BAA0B;YACtD,OAAO,EAAE,eAAe,KAAK,YAAY,WAAW;QACtD,GAAG,MAAM;QACT,SAAS,UAAU,MAAM,CAAC,CAAA;YACxB,MAAM,OAAO,IAAI;YACjB,MAAM,WAAW,IAAI,KAAK,EAAE,0BAA0B;YACtD,OAAO,YAAY;QACrB,GAAG,MAAM;QACT,aAAa,UAAU,MAAM,CAAC,CAAA;YAC5B,MAAM,OAAO,IAAI;YACjB,MAAM,WAAW,IAAI,KAAK,EAAE,0BAA0B;YACtD,MAAM,kBAAkB,KAAK,IAAI,CAAC,CAAC,SAAS,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;YAC9F,OAAO,kBAAkB,KAAK,mBAAmB;QACnD,GAAG,MAAM;IACX;IAEA,IAAI,UAAU;QACZ,qBACE,6LAAC,wJAAA,CAAA,UAAa;YACZ,YAAY;YACZ,WAAW;YACX,WAAW;gBACT,YAAY;gBACZ;YACF;YACA,UAAU,IAAM,YAAY;;;;;;IAGlC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAA0B;;;;;;;0CAGhD,6LAAC;gCAAE,WAAU;0CAAsB;;;;;;;;;;;;kCAGrC,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS;;0CACf,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAyB;;;;;;0DACtC,6LAAC;gDAAE,WAAU;0DAAqC,MAAM,MAAM;;;;;;;;;;;;kDAEhE,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK1B,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAyB;;;;;;0DACtC,6LAAC;gDAAE,WAAU;0DAAqC,MAAM,MAAM;;;;;;;;;;;;kDAEhE,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK7B,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAyB;;;;;;0DACtC,6LAAC;gDAAE,WAAU;0DAAsC,MAAM,WAAW;;;;;;;;;;;;kDAEtE,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAKvB,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAyB;;;;;;0DACtC,6LAAC;gDAAE,WAAU;0DAAmC,MAAM,OAAO;;;;;;;;;;;;kDAE/D,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO/B,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIlC,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;4BACJ,aAAY;4BACZ,OAAO;4BACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;0BAMnD,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;;oCAAC;oCAAmB,kBAAkB,MAAM;oCAAC;;;;;;;0CACvD,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;;kDACJ,6LAAC,oIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;8DACP,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;;;;;;;;;;;;kDAGf,6LAAC,oIAAA,CAAA,YAAS;kDACP,0BACC,6LAAC,oIAAA,CAAA,WAAQ;sDACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;gDAAC,SAAS;gDAAG,WAAU;0DAC/B,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;wDAAyB;;;;;;;;;;;;;;;;mDAKhD,kBAAkB,MAAM,KAAK,kBAC/B,6LAAC,oIAAA,CAAA,WAAQ;sDACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;gDAAC,SAAS;gDAAG,WAAU;0DAAkC;;;;;;;;;;mDAKrE,kBAAkB,GAAG,CAAC,CAAC;4CACrB,MAAM,OAAO,IAAI;4CACjB,MAAM,WAAW,IAAI,KAAK,UAAU,0BAA0B;4CAC9D,MAAM,kBAAkB,KAAK,IAAI,CAAC,CAAC,SAAS,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;4CAE9F,qBACE,6LAAC,oIAAA,CAAA,WAAQ;;kEACP,6LAAC,oIAAA,CAAA,YAAS;wDAAC,WAAU;kEACnB,cAAA,6LAAC;4DAAI,WAAU;;gEACZ,aAAa;gEACb,UAAU,IAAI;;;;;;;;;;;;kEAGnB,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EAAe,UAAU,KAAK;;;;;;8EAC7C,6LAAC;oEAAI,WAAU;8EAA0B,UAAU,OAAO;;;;;;;;;;;;;;;;;kEAG9D,6LAAC,oIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAqB,UAAU,YAAY;;;;;;kEAChE,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEACZ,UAAU,cAAc,IAAI;;;;;;;;;;;kEAGjC,6LAAC,oIAAA,CAAA,YAAS;kEACP,IAAI,KAAK,UAAU,iBAAiB,EAAE,kBAAkB,CAAC;;;;;;kEAE5D,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;;8EACC,6LAAC;8EAAK,IAAI,KAAK,UAAU,0BAA0B,EAAE,kBAAkB,CAAC;;;;;;gEACvE,kBAAkB,KAAK,mBAAmB,oBACzC,6LAAC;oEAAI,WAAU;;wEACZ;wEAAgB;;;;;;;gEAGpB,kBAAkB,mBACjB,6LAAC;oEAAI,WAAU;;wEAAuB;wEACxB,KAAK,GAAG,CAAC;wEAAiB;;;;;;;;;;;;;;;;;;kEAK9C,6LAAC,oIAAA,CAAA,YAAS;kEAAE,cAAc;;;;;;kEAC1B,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,oBAAoB;8EAEnC,cAAA,6LAAC,8MAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;8EAElB,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,sBAAsB,UAAU,YAAY;8EAE3D,cAAA,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+CApDX,UAAU,YAAY;;;;;wCA0DzC;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQX,uBACC,6LAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;;kCACf,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC,oIAAA,CAAA,mBAAgB;wBAAC,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;AAKtD;GA7TwB;KAAA", "debugId": null}}, {"offset": {"line": 3871, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/certificazioni/RapportiGeneraliManager.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Input } from '@/components/ui/input'\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { \n  BarChart3, \n  Search, \n  Plus, \n  Edit, \n  Trash2, \n  AlertCircle,\n  CheckCircle,\n  FileText,\n  Loader2,\n  Download,\n  RefreshCw\n} from 'lucide-react'\nimport { RapportoGeneraleCollaudo } from '@/types/certificazioni'\nimport { rapportiGeneraliApi } from '@/lib/api'\n\ninterface RapportiGeneraliManagerProps {\n  cantiereId: number\n  rapporti: RapportoGeneraleCollaudo[]\n  onUpdate: () => void\n}\n\nexport default function RapportiGeneraliManager({ cantiereId, rapporti, onUpdate }: RapportiGeneraliManagerProps) {\n  const [searchTerm, setSearchTerm] = useState('')\n  const [isLoading, setIsLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const handleCreateRapporto = () => {\n    // TODO: Implementare form per nuovo rapporto\n    console.log('Crea nuovo rapporto')\n  }\n\n  const handleEditRapporto = (rapporto: RapportoGeneraleCollaudo) => {\n    // TODO: Implementare form per modifica rapporto\n    console.log('Modifica rapporto', rapporto)\n  }\n\n  const handleDeleteRapporto = async (id: number) => {\n    if (!confirm('Sei sicuro di voler eliminare questo rapporto?')) return\n    \n    try {\n      setIsLoading(true)\n      await rapportiGeneraliApi.deleteRapporto(cantiereId, id)\n      onUpdate()\n    } catch (error: any) {\n      setError(error.response?.data?.detail || 'Errore durante l\\'eliminazione')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleAggiornaStatistiche = async (id: number) => {\n    try {\n      setIsLoading(true)\n      await rapportiGeneraliApi.aggiornaStatistiche(cantiereId, id)\n      onUpdate()\n    } catch (error: any) {\n      setError(error.response?.data?.detail || 'Errore durante l\\'aggiornamento')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const getStatoBadge = (stato: string) => {\n    switch (stato?.toLowerCase()) {\n      case 'completato':\n        return <Badge className=\"bg-green-100 text-green-800 border-green-200\">Completato</Badge>\n      case 'approvato':\n        return <Badge className=\"bg-blue-100 text-blue-800 border-blue-200\">Approvato</Badge>\n      case 'bozza':\n        return <Badge className=\"bg-gray-100 text-gray-800 border-gray-200\">Bozza</Badge>\n      default:\n        return <Badge className=\"bg-gray-100 text-gray-800 border-gray-200\">Da Verificare</Badge>\n    }\n  }\n\n  const getConformitaBadge = (rapporto: RapportoGeneraleCollaudo) => {\n    if (rapporto.numero_cavi_totali === 0) {\n      return <Badge variant=\"outline\">Nessun Cavo</Badge>\n    }\n\n    const percentuale = (rapporto.numero_cavi_conformi / rapporto.numero_cavi_totali) * 100\n\n    if (percentuale === 100) {\n      return <Badge className=\"bg-green-100 text-green-800 border-green-200\">100% Conforme</Badge>\n    } else if (percentuale >= 90) {\n      return <Badge className=\"bg-yellow-100 text-yellow-800 border-yellow-200\">{percentuale.toFixed(1)}% Conforme</Badge>\n    } else {\n      return <Badge className=\"bg-red-100 text-red-800 border-red-200\">{percentuale.toFixed(1)}% Conforme</Badge>\n    }\n  }\n\n  const filteredRapporti = rapporti.filter(rapporto => {\n    const searchLower = searchTerm.toLowerCase()\n    return (\n      rapporto.numero_rapporto?.toLowerCase().includes(searchLower) ||\n      rapporto.nome_progetto?.toLowerCase().includes(searchLower) ||\n      rapporto.cliente_finale?.toLowerCase().includes(searchLower)\n    )\n  })\n\n  const stats = {\n    totali: rapporti.length,\n    completati: rapporti.filter(r => r.stato_rapporto === 'COMPLETATO').length,\n    approvati: rapporti.filter(r => r.stato_rapporto === 'APPROVATO').length,\n    bozze: rapporti.filter(r => r.stato_rapporto === 'BOZZA').length,\n    cavi_totali: rapporti.reduce((sum, r) => sum + r.numero_cavi_totali, 0),\n    cavi_conformi: rapporti.reduce((sum, r) => sum + r.numero_cavi_conformi, 0)\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h2 className=\"text-2xl font-bold text-slate-900 flex items-center gap-3\">\n            <BarChart3 className=\"h-6 w-6 text-blue-600\" />\n            Rapporti Generali di Collaudo\n          </h2>\n          <p className=\"text-slate-600 mt-1\">Gestione rapporti generali CEI 64-8</p>\n        </div>\n        \n        <Button onClick={handleCreateRapporto}>\n          <Plus className=\"h-4 w-4 mr-2\" />\n          Nuovo Rapporto\n        </Button>\n      </div>\n\n      {/* Statistics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-5 gap-4\">\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-slate-600\">Totali</p>\n                <p className=\"text-2xl font-bold text-slate-900\">{stats.totali}</p>\n              </div>\n              <FileText className=\"h-8 w-8 text-blue-500\" />\n            </div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-slate-600\">Completati</p>\n                <p className=\"text-2xl font-bold text-green-600\">{stats.completati}</p>\n              </div>\n              <CheckCircle className=\"h-8 w-8 text-green-500\" />\n            </div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-slate-600\">Approvati</p>\n                <p className=\"text-2xl font-bold text-blue-600\">{stats.approvati}</p>\n              </div>\n              <BarChart3 className=\"h-8 w-8 text-blue-500\" />\n            </div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-slate-600\">Cavi Totali</p>\n                <p className=\"text-2xl font-bold text-slate-900\">{stats.cavi_totali}</p>\n              </div>\n              <FileText className=\"h-8 w-8 text-slate-500\" />\n            </div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-slate-600\">Conformità</p>\n                <p className=\"text-2xl font-bold text-green-600\">\n                  {stats.cavi_totali > 0 ? ((stats.cavi_conformi / stats.cavi_totali) * 100).toFixed(1) : 0}%\n                </p>\n              </div>\n              <CheckCircle className=\"h-8 w-8 text-green-500\" />\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Search */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Search className=\"h-5 w-5\" />\n            Ricerca Rapporti\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <Input\n            placeholder=\"Cerca per numero rapporto, progetto o cliente...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n          />\n        </CardContent>\n      </Card>\n\n      {/* Rapporti Table */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Elenco Rapporti ({filteredRapporti.length})</CardTitle>\n          <CardDescription>\n            Gestione rapporti generali di collaudo\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"rounded-md border\">\n            <Table>\n              <TableHeader>\n                <TableRow>\n                  <TableHead>Numero Rapporto</TableHead>\n                  <TableHead>Progetto</TableHead>\n                  <TableHead>Cliente</TableHead>\n                  <TableHead>Data</TableHead>\n                  <TableHead>Cavi</TableHead>\n                  <TableHead>Conformità</TableHead>\n                  <TableHead>Stato</TableHead>\n                  <TableHead>Azioni</TableHead>\n                </TableRow>\n              </TableHeader>\n              <TableBody>\n                {isLoading ? (\n                  <TableRow>\n                    <TableCell colSpan={8} className=\"text-center py-8\">\n                      <div className=\"flex items-center justify-center gap-2\">\n                        <Loader2 className=\"h-4 w-4 animate-spin\" />\n                        Caricamento...\n                      </div>\n                    </TableCell>\n                  </TableRow>\n                ) : filteredRapporti.length === 0 ? (\n                  <TableRow>\n                    <TableCell colSpan={8} className=\"text-center py-8 text-slate-500\">\n                      Nessun rapporto trovato\n                    </TableCell>\n                  </TableRow>\n                ) : (\n                  filteredRapporti.map((rapporto) => (\n                    <TableRow key={rapporto.id_rapporto}>\n                      <TableCell className=\"font-medium\">{rapporto.numero_rapporto}</TableCell>\n                      <TableCell>\n                        <div>\n                          <div className=\"font-medium\">{rapporto.nome_progetto || '-'}</div>\n                          <div className=\"text-sm text-slate-500\">{rapporto.societa_installatrice || '-'}</div>\n                        </div>\n                      </TableCell>\n                      <TableCell>{rapporto.cliente_finale || '-'}</TableCell>\n                      <TableCell>\n                        {new Date(rapporto.data_rapporto).toLocaleDateString('it-IT')}\n                      </TableCell>\n                      <TableCell>\n                        <div className=\"text-center\">\n                          <div className=\"font-medium\">{rapporto.numero_cavi_totali}</div>\n                          <div className=\"text-xs text-slate-500\">\n                            {rapporto.numero_cavi_conformi}C / {rapporto.numero_cavi_non_conformi}NC\n                          </div>\n                        </div>\n                      </TableCell>\n                      <TableCell>{getConformitaBadge(rapporto)}</TableCell>\n                      <TableCell>{getStatoBadge(rapporto.stato_rapporto)}</TableCell>\n                      <TableCell>\n                        <div className=\"flex gap-1\">\n                          <Button \n                            variant=\"ghost\" \n                            size=\"sm\"\n                            onClick={() => handleEditRapporto(rapporto)}\n                            title=\"Visualizza/Modifica\"\n                          >\n                            <Edit className=\"h-4 w-4\" />\n                          </Button>\n                          <Button \n                            variant=\"ghost\" \n                            size=\"sm\"\n                            onClick={() => handleAggiornaStatistiche(rapporto.id_rapporto)}\n                            title=\"Aggiorna Statistiche\"\n                          >\n                            <RefreshCw className=\"h-4 w-4\" />\n                          </Button>\n                          <Button \n                            variant=\"ghost\" \n                            size=\"sm\"\n                            onClick={() => {}}\n                            title=\"Genera PDF\"\n                          >\n                            <Download className=\"h-4 w-4\" />\n                          </Button>\n                          <Button \n                            variant=\"ghost\" \n                            size=\"sm\"\n                            onClick={() => handleDeleteRapporto(rapporto.id_rapporto)}\n                            title=\"Elimina\"\n                          >\n                            <Trash2 className=\"h-4 w-4\" />\n                          </Button>\n                        </div>\n                      </TableCell>\n                    </TableRow>\n                  ))\n                )}\n              </TableBody>\n            </Table>\n          </div>\n        </CardContent>\n      </Card>\n\n      {error && (\n        <Alert className=\"border-red-200 bg-red-50\">\n          <AlertCircle className=\"h-4 w-4 text-red-600\" />\n          <AlertDescription className=\"text-red-800\">{error}</AlertDescription>\n        </Alert>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;;;AAvBA;;;;;;;;;;AA+Be,SAAS,wBAAwB,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAgC;;IAC9G,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,uBAAuB;QAC3B,6CAA6C;QAC7C,QAAQ,GAAG,CAAC;IACd;IAEA,MAAM,qBAAqB,CAAC;QAC1B,gDAAgD;QAChD,QAAQ,GAAG,CAAC,qBAAqB;IACnC;IAEA,MAAM,uBAAuB,OAAO;QAClC,IAAI,CAAC,QAAQ,mDAAmD;QAEhE,IAAI;YACF,aAAa;YACb,MAAM,oHAAA,CAAA,sBAAmB,CAAC,cAAc,CAAC,YAAY;YACrD;QACF,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,QAAQ,EAAE,MAAM,UAAU;QAC3C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,4BAA4B,OAAO;QACvC,IAAI;YACF,aAAa;YACb,MAAM,oHAAA,CAAA,sBAAmB,CAAC,mBAAmB,CAAC,YAAY;YAC1D;QACF,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,QAAQ,EAAE,MAAM,UAAU;QAC3C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ,OAAO;YACb,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA+C;;;;;;YACzE,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA4C;;;;;;YACtE,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA4C;;;;;;YACtE;gBACE,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA4C;;;;;;QACxE;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,SAAS,kBAAkB,KAAK,GAAG;YACrC,qBAAO,6LAAC,oIAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAU;;;;;;QAClC;QAEA,MAAM,cAAc,AAAC,SAAS,oBAAoB,GAAG,SAAS,kBAAkB,GAAI;QAEpF,IAAI,gBAAgB,KAAK;YACvB,qBAAO,6LAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;0BAA+C;;;;;;QACzE,OAAO,IAAI,eAAe,IAAI;YAC5B,qBAAO,6LAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;;oBAAmD,YAAY,OAAO,CAAC;oBAAG;;;;;;;QACpG,OAAO;YACL,qBAAO,6LAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;;oBAA0C,YAAY,OAAO,CAAC;oBAAG;;;;;;;QAC3F;IACF;IAEA,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA;QACvC,MAAM,cAAc,WAAW,WAAW;QAC1C,OACE,SAAS,eAAe,EAAE,cAAc,SAAS,gBACjD,SAAS,aAAa,EAAE,cAAc,SAAS,gBAC/C,SAAS,cAAc,EAAE,cAAc,SAAS;IAEpD;IAEA,MAAM,QAAQ;QACZ,QAAQ,SAAS,MAAM;QACvB,YAAY,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,cAAc,KAAK,cAAc,MAAM;QAC1E,WAAW,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,cAAc,KAAK,aAAa,MAAM;QACxE,OAAO,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,cAAc,KAAK,SAAS,MAAM;QAChE,aAAa,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,kBAAkB,EAAE;QACrE,eAAe,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,oBAAoB,EAAE;IAC3E;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,qNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAA0B;;;;;;;0CAGjD,6LAAC;gCAAE,WAAU;0CAAsB;;;;;;;;;;;;kCAGrC,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS;;0CACf,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAyB;;;;;;0DACtC,6LAAC;gDAAE,WAAU;0DAAqC,MAAM,MAAM;;;;;;;;;;;;kDAEhE,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK1B,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAyB;;;;;;0DACtC,6LAAC;gDAAE,WAAU;0DAAqC,MAAM,UAAU;;;;;;;;;;;;kDAEpE,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK7B,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAyB;;;;;;0DACtC,6LAAC;gDAAE,WAAU;0DAAoC,MAAM,SAAS;;;;;;;;;;;;kDAElE,6LAAC,qNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK3B,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAyB;;;;;;0DACtC,6LAAC;gDAAE,WAAU;0DAAqC,MAAM,WAAW;;;;;;;;;;;;kDAErE,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK1B,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAyB;;;;;;0DACtC,6LAAC;gDAAE,WAAU;;oDACV,MAAM,WAAW,GAAG,IAAI,CAAC,AAAC,MAAM,aAAa,GAAG,MAAM,WAAW,GAAI,GAAG,EAAE,OAAO,CAAC,KAAK;oDAAE;;;;;;;;;;;;;kDAG9F,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO/B,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIlC,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;4BACJ,aAAY;4BACZ,OAAO;4BACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;0BAMnD,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;;oCAAC;oCAAkB,iBAAiB,MAAM;oCAAC;;;;;;;0CACrD,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;;kDACJ,6LAAC,oIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;8DACP,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;;;;;;;;;;;;kDAGf,6LAAC,oIAAA,CAAA,YAAS;kDACP,0BACC,6LAAC,oIAAA,CAAA,WAAQ;sDACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;gDAAC,SAAS;gDAAG,WAAU;0DAC/B,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;wDAAyB;;;;;;;;;;;;;;;;mDAKhD,iBAAiB,MAAM,KAAK,kBAC9B,6LAAC,oIAAA,CAAA,WAAQ;sDACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;gDAAC,SAAS;gDAAG,WAAU;0DAAkC;;;;;;;;;;mDAKrE,iBAAiB,GAAG,CAAC,CAAC,yBACpB,6LAAC,oIAAA,CAAA,WAAQ;;kEACP,6LAAC,oIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAe,SAAS,eAAe;;;;;;kEAC5D,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EAAe,SAAS,aAAa,IAAI;;;;;;8EACxD,6LAAC;oEAAI,WAAU;8EAA0B,SAAS,qBAAqB,IAAI;;;;;;;;;;;;;;;;;kEAG/E,6LAAC,oIAAA,CAAA,YAAS;kEAAE,SAAS,cAAc,IAAI;;;;;;kEACvC,6LAAC,oIAAA,CAAA,YAAS;kEACP,IAAI,KAAK,SAAS,aAAa,EAAE,kBAAkB,CAAC;;;;;;kEAEvD,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAe,SAAS,kBAAkB;;;;;;8EACzD,6LAAC;oEAAI,WAAU;;wEACZ,SAAS,oBAAoB;wEAAC;wEAAK,SAAS,wBAAwB;wEAAC;;;;;;;;;;;;;;;;;;kEAI5E,6LAAC,oIAAA,CAAA,YAAS;kEAAE,mBAAmB;;;;;;kEAC/B,6LAAC,oIAAA,CAAA,YAAS;kEAAE,cAAc,SAAS,cAAc;;;;;;kEACjD,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,mBAAmB;oEAClC,OAAM;8EAEN,cAAA,6LAAC,8MAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;8EAElB,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,0BAA0B,SAAS,WAAW;oEAC7D,OAAM;8EAEN,cAAA,6LAAC,mNAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;;;;;;8EAEvB,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,KAAO;oEAChB,OAAM;8EAEN,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;;;;;8EAEtB,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,qBAAqB,SAAS,WAAW;oEACxD,OAAM;8EAEN,cAAA,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+CAtDX,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAmEhD,uBACC,6LAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;;kCACf,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC,oIAAA,CAAA,mBAAgB;wBAAC,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;AAKtD;GAhTwB;KAAA", "debugId": null}}, {"offset": {"line": 4838, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE;KArBS", "debugId": null}}, {"offset": {"line": 4883, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/certificazioni/CertificazioniStatistics.tsx"], "sourcesContent": ["'use client'\n\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Progress } from '@/components/ui/progress'\nimport { Badge } from '@/components/ui/badge'\nimport { \n  FileText, \n  CheckCircle, \n  AlertCircle, \n  Clock, \n  Award,\n  TrendingUp,\n  Target,\n  AlertTriangle\n} from 'lucide-react'\n\ninterface StatisticsData {\n  totali: number\n  conformi: number\n  non_conformi: number\n  bozze: number\n  con_riserva: number\n}\n\ninterface CertificazioniStatisticsProps {\n  stats: StatisticsData\n  detailed?: boolean\n}\n\nexport default function CertificazioniStatistics({ stats, detailed = false }: CertificazioniStatisticsProps) {\n  const percentualeConformita = stats.totali > 0 ? (stats.conformi / stats.totali) * 100 : 0\n  const percentualeCompletamento = stats.totali > 0 ? ((stats.conformi + stats.non_conformi + stats.con_riserva) / stats.totali) * 100 : 0\n\n  const getConformitaColor = (percentage: number) => {\n    if (percentage >= 95) return 'text-green-600'\n    if (percentage >= 85) return 'text-yellow-600'\n    return 'text-red-600'\n  }\n\n  const getConformitaIcon = (percentage: number) => {\n    if (percentage >= 95) return <CheckCircle className=\"h-5 w-5 text-green-600\" />\n    if (percentage >= 85) return <AlertTriangle className=\"h-5 w-5 text-yellow-600\" />\n    return <AlertCircle className=\"h-5 w-5 text-red-600\" />\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Cards Statistiche Principali */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-slate-600\">Totali</p>\n                <p className=\"text-2xl font-bold text-slate-900\">{stats.totali}</p>\n              </div>\n              <FileText className=\"h-8 w-8 text-blue-500\" />\n            </div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-slate-600\">Conformi</p>\n                <p className=\"text-2xl font-bold text-green-600\">{stats.conformi}</p>\n                <p className=\"text-xs text-slate-500\">\n                  {stats.totali > 0 ? `${((stats.conformi / stats.totali) * 100).toFixed(1)}%` : '0%'}\n                </p>\n              </div>\n              <CheckCircle className=\"h-8 w-8 text-green-500\" />\n            </div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-slate-600\">Non Conformi</p>\n                <p className=\"text-2xl font-bold text-red-600\">{stats.non_conformi}</p>\n                <p className=\"text-xs text-slate-500\">\n                  {stats.totali > 0 ? `${((stats.non_conformi / stats.totali) * 100).toFixed(1)}%` : '0%'}\n                </p>\n              </div>\n              <AlertCircle className=\"h-8 w-8 text-red-500\" />\n            </div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-slate-600\">Bozze</p>\n                <p className=\"text-2xl font-bold text-yellow-600\">{stats.bozze}</p>\n                <p className=\"text-xs text-slate-500\">\n                  {stats.totali > 0 ? `${((stats.bozze / stats.totali) * 100).toFixed(1)}%` : '0%'}\n                </p>\n              </div>\n              <Clock className=\"h-8 w-8 text-yellow-500\" />\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Indicatori di Performance */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Target className=\"h-5 w-5 text-blue-600\" />\n              Tasso di Conformità\n            </CardTitle>\n            <CardDescription>\n              Percentuale di certificazioni conformi sul totale\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm font-medium\">Conformità</span>\n                <div className=\"flex items-center gap-2\">\n                  {getConformitaIcon(percentualeConformita)}\n                  <span className={`text-lg font-bold ${getConformitaColor(percentualeConformita)}`}>\n                    {percentualeConformita.toFixed(1)}%\n                  </span>\n                </div>\n              </div>\n              <Progress \n                value={percentualeConformita} \n                className=\"h-2\"\n              />\n              <div className=\"flex justify-between text-xs text-slate-500\">\n                <span>Target: 95%</span>\n                <span>{stats.conformi} / {stats.totali}</span>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <TrendingUp className=\"h-5 w-5 text-green-600\" />\n              Completamento Certificazioni\n            </CardTitle>\n            <CardDescription>\n              Percentuale di certificazioni completate (non bozze)\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm font-medium\">Completamento</span>\n                <span className=\"text-lg font-bold text-blue-600\">\n                  {percentualeCompletamento.toFixed(1)}%\n                </span>\n              </div>\n              <Progress \n                value={percentualeCompletamento} \n                className=\"h-2\"\n              />\n              <div className=\"flex justify-between text-xs text-slate-500\">\n                <span>Completate: {stats.conformi + stats.non_conformi + stats.con_riserva}</span>\n                <span>Bozze: {stats.bozze}</span>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {detailed && (\n        <>\n          {/* Dettagli Aggiuntivi */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"text-lg\">Distribuzione Stati</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center gap-2\">\n                      <div className=\"w-3 h-3 bg-green-500 rounded-full\"></div>\n                      <span className=\"text-sm\">Conformi</span>\n                    </div>\n                    <Badge variant=\"outline\" className=\"text-green-600 border-green-200\">\n                      {stats.conformi}\n                    </Badge>\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center gap-2\">\n                      <div className=\"w-3 h-3 bg-red-500 rounded-full\"></div>\n                      <span className=\"text-sm\">Non Conformi</span>\n                    </div>\n                    <Badge variant=\"outline\" className=\"text-red-600 border-red-200\">\n                      {stats.non_conformi}\n                    </Badge>\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center gap-2\">\n                      <div className=\"w-3 h-3 bg-yellow-500 rounded-full\"></div>\n                      <span className=\"text-sm\">Con Riserva</span>\n                    </div>\n                    <Badge variant=\"outline\" className=\"text-yellow-600 border-yellow-200\">\n                      {stats.con_riserva}\n                    </Badge>\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center gap-2\">\n                      <div className=\"w-3 h-3 bg-gray-500 rounded-full\"></div>\n                      <span className=\"text-sm\">Bozze</span>\n                    </div>\n                    <Badge variant=\"outline\" className=\"text-gray-600 border-gray-200\">\n                      {stats.bozze}\n                    </Badge>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"text-lg\">Indicatori Qualità</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  <div>\n                    <div className=\"flex justify-between text-sm mb-1\">\n                      <span>Tasso Successo</span>\n                      <span className=\"font-medium\">\n                        {stats.totali > 0 ? (((stats.conformi + stats.con_riserva) / stats.totali) * 100).toFixed(1) : 0}%\n                      </span>\n                    </div>\n                    <Progress \n                      value={stats.totali > 0 ? ((stats.conformi + stats.con_riserva) / stats.totali) * 100 : 0} \n                      className=\"h-2\"\n                    />\n                  </div>\n                  <div>\n                    <div className=\"flex justify-between text-sm mb-1\">\n                      <span>Tasso Fallimento</span>\n                      <span className=\"font-medium text-red-600\">\n                        {stats.totali > 0 ? ((stats.non_conformi / stats.totali) * 100).toFixed(1) : 0}%\n                      </span>\n                    </div>\n                    <Progress \n                      value={stats.totali > 0 ? (stats.non_conformi / stats.totali) * 100 : 0} \n                      className=\"h-2\"\n                    />\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"text-lg flex items-center gap-2\">\n                  <Award className=\"h-5 w-5 text-yellow-500\" />\n                  Valutazione Complessiva\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-center space-y-2\">\n                  <div className=\"text-3xl font-bold\">\n                    {percentualeConformita >= 95 ? '🏆' : \n                     percentualeConformita >= 85 ? '⭐' : \n                     percentualeConformita >= 70 ? '👍' : '⚠️'}\n                  </div>\n                  <div className=\"text-lg font-semibold\">\n                    {percentualeConformita >= 95 ? 'Eccellente' : \n                     percentualeConformita >= 85 ? 'Buono' : \n                     percentualeConformita >= 70 ? 'Sufficiente' : 'Da Migliorare'}\n                  </div>\n                  <div className=\"text-sm text-slate-600\">\n                    {percentualeConformita >= 95 ? 'Qualità certificazioni ottimale' : \n                     percentualeConformita >= 85 ? 'Qualità certificazioni buona' : \n                     percentualeConformita >= 70 ? 'Qualità certificazioni accettabile' : 'Necessario miglioramento qualità'}\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Raccomandazioni */}\n          {(percentualeConformita < 95 || stats.bozze > 0) && (\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center gap-2\">\n                  <AlertTriangle className=\"h-5 w-5 text-yellow-600\" />\n                  Raccomandazioni\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-2\">\n                  {percentualeConformita < 95 && (\n                    <div className=\"flex items-start gap-2 text-sm\">\n                      <div className=\"w-2 h-2 bg-yellow-500 rounded-full mt-2\"></div>\n                      <span>\n                        Il tasso di conformità è del {percentualeConformita.toFixed(1)}%. \n                        Obiettivo raccomandato: 95%. Verificare procedure di test e qualità installazioni.\n                      </span>\n                    </div>\n                  )}\n                  {stats.bozze > 0 && (\n                    <div className=\"flex items-start gap-2 text-sm\">\n                      <div className=\"w-2 h-2 bg-blue-500 rounded-full mt-2\"></div>\n                      <span>\n                        Ci sono {stats.bozze} certificazioni in bozza. \n                        Completare le certificazioni per avere dati accurati.\n                      </span>\n                    </div>\n                  )}\n                  {stats.non_conformi > stats.conformi && (\n                    <div className=\"flex items-start gap-2 text-sm\">\n                      <div className=\"w-2 h-2 bg-red-500 rounded-full mt-2\"></div>\n                      <span>\n                        Il numero di certificazioni non conformi supera quelle conformi. \n                        Rivedere urgentemente le procedure di installazione e test.\n                      </span>\n                    </div>\n                  )}\n                </div>\n              </CardContent>\n            </Card>\n          )}\n        </>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AA6Be,SAAS,yBAAyB,EAAE,KAAK,EAAE,WAAW,KAAK,EAAiC;IACzG,MAAM,wBAAwB,MAAM,MAAM,GAAG,IAAI,AAAC,MAAM,QAAQ,GAAG,MAAM,MAAM,GAAI,MAAM;IACzF,MAAM,2BAA2B,MAAM,MAAM,GAAG,IAAI,AAAC,CAAC,MAAM,QAAQ,GAAG,MAAM,YAAY,GAAG,MAAM,WAAW,IAAI,MAAM,MAAM,GAAI,MAAM;IAEvI,MAAM,qBAAqB,CAAC;QAC1B,IAAI,cAAc,IAAI,OAAO;QAC7B,IAAI,cAAc,IAAI,OAAO;QAC7B,OAAO;IACT;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,cAAc,IAAI,qBAAO,6LAAC,8NAAA,CAAA,cAAW;YAAC,WAAU;;;;;;QACpD,IAAI,cAAc,IAAI,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;QACtD,qBAAO,6LAAC,uNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;IAChC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAyB;;;;;;0DACtC,6LAAC;gDAAE,WAAU;0DAAqC,MAAM,MAAM;;;;;;;;;;;;kDAEhE,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK1B,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAyB;;;;;;0DACtC,6LAAC;gDAAE,WAAU;0DAAqC,MAAM,QAAQ;;;;;;0DAChE,6LAAC;gDAAE,WAAU;0DACV,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,AAAC,MAAM,QAAQ,GAAG,MAAM,MAAM,GAAI,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG;;;;;;;;;;;;kDAGnF,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK7B,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAyB;;;;;;0DACtC,6LAAC;gDAAE,WAAU;0DAAmC,MAAM,YAAY;;;;;;0DAClE,6LAAC;gDAAE,WAAU;0DACV,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,AAAC,MAAM,YAAY,GAAG,MAAM,MAAM,GAAI,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG;;;;;;;;;;;;kDAGvF,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK7B,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAyB;;;;;;0DACtC,6LAAC;gDAAE,WAAU;0DAAsC,MAAM,KAAK;;;;;;0DAC9D,6LAAC;gDAAE,WAAU;0DACV,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,AAAC,MAAM,KAAK,GAAG,MAAM,MAAM,GAAI,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG;;;;;;;;;;;;kDAGhF,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOzB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAA0B;;;;;;;kDAG9C,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAsB;;;;;;8DACtC,6LAAC;oDAAI,WAAU;;wDACZ,kBAAkB;sEACnB,6LAAC;4DAAK,WAAW,CAAC,kBAAkB,EAAE,mBAAmB,wBAAwB;;gEAC9E,sBAAsB,OAAO,CAAC;gEAAG;;;;;;;;;;;;;;;;;;;sDAIxC,6LAAC,uIAAA,CAAA,WAAQ;4CACP,OAAO;4CACP,WAAU;;;;;;sDAEZ,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;;wDAAM,MAAM,QAAQ;wDAAC;wDAAI,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM9C,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAA2B;;;;;;;kDAGnD,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAsB;;;;;;8DACtC,6LAAC;oDAAK,WAAU;;wDACb,yBAAyB,OAAO,CAAC;wDAAG;;;;;;;;;;;;;sDAGzC,6LAAC,uIAAA,CAAA,WAAQ;4CACP,OAAO;4CACP,WAAU;;;;;;sDAEZ,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;wDAAK;wDAAa,MAAM,QAAQ,GAAG,MAAM,YAAY,GAAG,MAAM,WAAW;;;;;;;8DAC1E,6LAAC;;wDAAK;wDAAQ,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOlC,0BACC;;kCAEE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAU;;;;;;;;;;;kDAEjC,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;sEAE5B,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAChC,MAAM,QAAQ;;;;;;;;;;;;8DAGnB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;sEAE5B,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAChC,MAAM,YAAY;;;;;;;;;;;;8DAGvB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;sEAE5B,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAChC,MAAM,WAAW;;;;;;;;;;;;8DAGtB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;sEAE5B,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAChC,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOtB,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAU;;;;;;;;;;;kDAEjC,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EAAK;;;;;;8EACN,6LAAC;oEAAK,WAAU;;wEACb,MAAM,MAAM,GAAG,IAAI,CAAC,AAAC,CAAC,MAAM,QAAQ,GAAG,MAAM,WAAW,IAAI,MAAM,MAAM,GAAI,GAAG,EAAE,OAAO,CAAC,KAAK;wEAAE;;;;;;;;;;;;;sEAGrG,6LAAC,uIAAA,CAAA,WAAQ;4DACP,OAAO,MAAM,MAAM,GAAG,IAAI,AAAC,CAAC,MAAM,QAAQ,GAAG,MAAM,WAAW,IAAI,MAAM,MAAM,GAAI,MAAM;4DACxF,WAAU;;;;;;;;;;;;8DAGd,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EAAK;;;;;;8EACN,6LAAC;oEAAK,WAAU;;wEACb,MAAM,MAAM,GAAG,IAAI,CAAC,AAAC,MAAM,YAAY,GAAG,MAAM,MAAM,GAAI,GAAG,EAAE,OAAO,CAAC,KAAK;wEAAE;;;;;;;;;;;;;sEAGnF,6LAAC,uIAAA,CAAA,WAAQ;4DACP,OAAO,MAAM,MAAM,GAAG,IAAI,AAAC,MAAM,YAAY,GAAG,MAAM,MAAM,GAAI,MAAM;4DACtE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOpB,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAA4B;;;;;;;;;;;;kDAIjD,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ,yBAAyB,KAAK,OAC9B,yBAAyB,KAAK,MAC9B,yBAAyB,KAAK,OAAO;;;;;;8DAExC,6LAAC;oDAAI,WAAU;8DACZ,yBAAyB,KAAK,eAC9B,yBAAyB,KAAK,UAC9B,yBAAyB,KAAK,gBAAgB;;;;;;8DAEjD,6LAAC;oDAAI,WAAU;8DACZ,yBAAyB,KAAK,oCAC9B,yBAAyB,KAAK,iCAC9B,yBAAyB,KAAK,uCAAuC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQ/E,CAAC,wBAAwB,MAAM,MAAM,KAAK,GAAG,CAAC,mBAC7C,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;wCAA4B;;;;;;;;;;;;0CAIzD,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;;wCACZ,wBAAwB,oBACvB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;;wDAAK;wDAC0B,sBAAsB,OAAO,CAAC;wDAAG;;;;;;;;;;;;;wCAKpE,MAAM,KAAK,GAAG,mBACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;;wDAAK;wDACK,MAAM,KAAK;wDAAC;;;;;;;;;;;;;wCAK1B,MAAM,YAAY,GAAG,MAAM,QAAQ,kBAClC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAc5B;KA/SwB", "debugId": null}}, {"offset": {"line": 5993, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/certificazioni/NonConformitaManager.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Input } from '@/components/ui/input'\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { \n  AlertTriangle, \n  Search, \n  Plus, \n  Edit, \n  Trash2, \n  AlertCircle,\n  CheckCircle,\n  Clock,\n  Loader2,\n  Eye,\n  FileText\n} from 'lucide-react'\nimport { NonConformita } from '@/types/certificazioni'\nimport { nonConformitaApi } from '@/lib/api'\n\ninterface NonConformitaManagerProps {\n  cantiereId: number\n  nonConformita: NonConformita[]\n  onUpdate: () => void\n}\n\nexport default function NonConformitaManager({ cantiereId, nonConformita, onUpdate }: NonConformitaManagerProps) {\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedStatus, setSelectedStatus] = useState('all')\n  const [isLoading, setIsLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const handleCreateNonConformita = () => {\n    // TODO: Implementare form per nuova non conformità\n    console.log('Crea nuova non conformità')\n  }\n\n  const handleEditNonConformita = (nc: NonConformita) => {\n    // TODO: Implementare form per modifica non conformità\n    console.log('Modifica non conformità', nc)\n  }\n\n  const handleDeleteNonConformita = async (id: number) => {\n    if (!confirm('Sei sicuro di voler eliminare questa non conformità?')) return\n    \n    try {\n      setIsLoading(true)\n      await nonConformitaApi.deleteNonConformita(cantiereId, id)\n      onUpdate()\n    } catch (error: any) {\n      setError(error.response?.data?.detail || 'Errore durante l\\'eliminazione')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const getStatoBadge = (stato: string) => {\n    switch (stato?.toLowerCase()) {\n      case 'aperta':\n        return <Badge className=\"bg-red-100 text-red-800 border-red-200\">Aperta</Badge>\n      case 'in_risoluzione':\n        return <Badge className=\"bg-yellow-100 text-yellow-800 border-yellow-200\">In Risoluzione</Badge>\n      case 'risolta':\n        return <Badge className=\"bg-green-100 text-green-800 border-green-200\">Risolta</Badge>\n      case 'chiusa':\n        return <Badge className=\"bg-gray-100 text-gray-800 border-gray-200\">Chiusa</Badge>\n      default:\n        return <Badge className=\"bg-gray-100 text-gray-800 border-gray-200\">Da Verificare</Badge>\n    }\n  }\n\n  const getSeveritaBadge = (severita: string) => {\n    switch (severita?.toLowerCase()) {\n      case 'critica':\n        return <Badge className=\"bg-red-100 text-red-800 border-red-200\">Critica</Badge>\n      case 'alta':\n        return <Badge className=\"bg-orange-100 text-orange-800 border-orange-200\">Alta</Badge>\n      case 'media':\n        return <Badge className=\"bg-yellow-100 text-yellow-800 border-yellow-200\">Media</Badge>\n      case 'bassa':\n        return <Badge className=\"bg-blue-100 text-blue-800 border-blue-200\">Bassa</Badge>\n      default:\n        return <Badge variant=\"outline\">Non Specificata</Badge>\n    }\n  }\n\n  const getStatoIcon = (stato: string) => {\n    switch (stato?.toLowerCase()) {\n      case 'aperta':\n        return <AlertCircle className=\"h-4 w-4 text-red-500\" />\n      case 'in_risoluzione':\n        return <Clock className=\"h-4 w-4 text-yellow-500\" />\n      case 'risolta':\n      case 'chiusa':\n        return <CheckCircle className=\"h-4 w-4 text-green-500\" />\n      default:\n        return <AlertTriangle className=\"h-4 w-4 text-gray-500\" />\n    }\n  }\n\n  const filteredNonConformita = nonConformita.filter(nc => {\n    const searchLower = searchTerm.toLowerCase()\n    const matchesSearch = (\n      nc.id_cavo?.toLowerCase().includes(searchLower) ||\n      nc.descrizione?.toLowerCase().includes(searchLower) ||\n      nc.tipo_non_conformita?.toLowerCase().includes(searchLower) ||\n      nc.responsabile_rilevazione?.toLowerCase().includes(searchLower)\n    )\n    \n    let matchesStatus = true\n    if (selectedStatus !== 'all') {\n      matchesStatus = nc.stato?.toLowerCase() === selectedStatus\n    }\n    \n    return matchesSearch && matchesStatus\n  })\n\n  const stats = {\n    totali: nonConformita.length,\n    aperte: nonConformita.filter(nc => nc.stato === 'APERTA').length,\n    in_risoluzione: nonConformita.filter(nc => nc.stato === 'IN_RISOLUZIONE').length,\n    risolte: nonConformita.filter(nc => nc.stato === 'RISOLTA' || nc.stato === 'CHIUSA').length,\n    critiche: nonConformita.filter(nc => nc.severita === 'CRITICA').length\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h2 className=\"text-2xl font-bold text-slate-900 flex items-center gap-3\">\n            <AlertTriangle className=\"h-6 w-6 text-red-600\" />\n            Gestione Non Conformità\n          </h2>\n          <p className=\"text-slate-600 mt-1\">Tracciamento e risoluzione delle non conformità</p>\n        </div>\n        \n        <Button onClick={handleCreateNonConformita}>\n          <Plus className=\"h-4 w-4 mr-2\" />\n          Nuova Non Conformità\n        </Button>\n      </div>\n\n      {/* Statistics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-5 gap-4\">\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-slate-600\">Totali</p>\n                <p className=\"text-2xl font-bold text-slate-900\">{stats.totali}</p>\n              </div>\n              <FileText className=\"h-8 w-8 text-blue-500\" />\n            </div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-slate-600\">Aperte</p>\n                <p className=\"text-2xl font-bold text-red-600\">{stats.aperte}</p>\n              </div>\n              <AlertCircle className=\"h-8 w-8 text-red-500\" />\n            </div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-slate-600\">In Risoluzione</p>\n                <p className=\"text-2xl font-bold text-yellow-600\">{stats.in_risoluzione}</p>\n              </div>\n              <Clock className=\"h-8 w-8 text-yellow-500\" />\n            </div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-slate-600\">Risolte</p>\n                <p className=\"text-2xl font-bold text-green-600\">{stats.risolte}</p>\n              </div>\n              <CheckCircle className=\"h-8 w-8 text-green-500\" />\n            </div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-slate-600\">Critiche</p>\n                <p className=\"text-2xl font-bold text-red-600\">{stats.critiche}</p>\n              </div>\n              <AlertTriangle className=\"h-8 w-8 text-red-500\" />\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Search and Filters */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Search className=\"h-5 w-5\" />\n            Ricerca e Filtri\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"flex gap-4\">\n            <div className=\"flex-1\">\n              <Input\n                placeholder=\"Cerca per ID cavo, descrizione, tipo o responsabile...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n              />\n            </div>\n            <div className=\"flex gap-2\">\n              {[\n                { value: 'all', label: 'Tutte' },\n                { value: 'aperta', label: 'Aperte' },\n                { value: 'in_risoluzione', label: 'In Risoluzione' },\n                { value: 'risolta', label: 'Risolte' },\n                { value: 'chiusa', label: 'Chiuse' }\n              ].map((status) => (\n                <Button\n                  key={status.value}\n                  variant={selectedStatus === status.value ? 'default' : 'outline'}\n                  size=\"sm\"\n                  onClick={() => setSelectedStatus(status.value)}\n                >\n                  {status.label}\n                </Button>\n              ))}\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Non Conformità Table */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Elenco Non Conformità ({filteredNonConformita.length})</CardTitle>\n          <CardDescription>\n            Gestione delle non conformità rilevate durante le certificazioni\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"rounded-md border\">\n            <Table>\n              <TableHeader>\n                <TableRow>\n                  <TableHead>ID Cavo</TableHead>\n                  <TableHead>Tipo</TableHead>\n                  <TableHead>Descrizione</TableHead>\n                  <TableHead>Severità</TableHead>\n                  <TableHead>Data Rilevazione</TableHead>\n                  <TableHead>Responsabile</TableHead>\n                  <TableHead>Stato</TableHead>\n                  <TableHead>Azioni</TableHead>\n                </TableRow>\n              </TableHeader>\n              <TableBody>\n                {isLoading ? (\n                  <TableRow>\n                    <TableCell colSpan={8} className=\"text-center py-8\">\n                      <div className=\"flex items-center justify-center gap-2\">\n                        <Loader2 className=\"h-4 w-4 animate-spin\" />\n                        Caricamento...\n                      </div>\n                    </TableCell>\n                  </TableRow>\n                ) : filteredNonConformita.length === 0 ? (\n                  <TableRow>\n                    <TableCell colSpan={8} className=\"text-center py-8 text-slate-500\">\n                      Nessuna non conformità trovata\n                    </TableCell>\n                  </TableRow>\n                ) : (\n                  filteredNonConformita.map((nc) => (\n                    <TableRow key={nc.id_non_conformita}>\n                      <TableCell className=\"font-medium\">\n                        <div className=\"flex items-center gap-2\">\n                          {getStatoIcon(nc.stato)}\n                          {nc.id_cavo}\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        <Badge variant=\"outline\">\n                          {nc.tipo_non_conformita || 'Non Specificato'}\n                        </Badge>\n                      </TableCell>\n                      <TableCell>\n                        <div className=\"max-w-xs\">\n                          <div className=\"font-medium truncate\" title={nc.descrizione}>\n                            {nc.descrizione}\n                          </div>\n                          {nc.azione_correttiva && (\n                            <div className=\"text-xs text-slate-500 truncate\" title={nc.azione_correttiva}>\n                              Azione: {nc.azione_correttiva}\n                            </div>\n                          )}\n                        </div>\n                      </TableCell>\n                      <TableCell>{getSeveritaBadge(nc.severita)}</TableCell>\n                      <TableCell>\n                        {new Date(nc.data_rilevazione).toLocaleDateString('it-IT')}\n                      </TableCell>\n                      <TableCell>{nc.responsabile_rilevazione || '-'}</TableCell>\n                      <TableCell>{getStatoBadge(nc.stato)}</TableCell>\n                      <TableCell>\n                        <div className=\"flex gap-1\">\n                          <Button \n                            variant=\"ghost\" \n                            size=\"sm\"\n                            onClick={() => {}}\n                            title=\"Visualizza Dettagli\"\n                          >\n                            <Eye className=\"h-4 w-4\" />\n                          </Button>\n                          <Button \n                            variant=\"ghost\" \n                            size=\"sm\"\n                            onClick={() => handleEditNonConformita(nc)}\n                            title=\"Modifica\"\n                          >\n                            <Edit className=\"h-4 w-4\" />\n                          </Button>\n                          <Button \n                            variant=\"ghost\" \n                            size=\"sm\"\n                            onClick={() => handleDeleteNonConformita(nc.id_non_conformita)}\n                            title=\"Elimina\"\n                          >\n                            <Trash2 className=\"h-4 w-4\" />\n                          </Button>\n                        </div>\n                      </TableCell>\n                    </TableRow>\n                  ))\n                )}\n              </TableBody>\n            </Table>\n          </div>\n        </CardContent>\n      </Card>\n\n      {error && (\n        <Alert className=\"border-red-200 bg-red-50\">\n          <AlertCircle className=\"h-4 w-4 text-red-600\" />\n          <AlertDescription className=\"text-red-800\">{error}</AlertDescription>\n        </Alert>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;;;AAvBA;;;;;;;;;;AA+Be,SAAS,qBAAqB,EAAE,UAAU,EAAE,aAAa,EAAE,QAAQ,EAA6B;;IAC7G,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,4BAA4B;QAChC,mDAAmD;QACnD,QAAQ,GAAG,CAAC;IACd;IAEA,MAAM,0BAA0B,CAAC;QAC/B,sDAAsD;QACtD,QAAQ,GAAG,CAAC,2BAA2B;IACzC;IAEA,MAAM,4BAA4B,OAAO;QACvC,IAAI,CAAC,QAAQ,yDAAyD;QAEtE,IAAI;YACF,aAAa;YACb,MAAM,oHAAA,CAAA,mBAAgB,CAAC,mBAAmB,CAAC,YAAY;YACvD;QACF,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,QAAQ,EAAE,MAAM,UAAU;QAC3C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ,OAAO;YACb,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAyC;;;;;;YACnE,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAkD;;;;;;YAC5E,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA+C;;;;;;YACzE,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA4C;;;;;;YACtE;gBACE,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA4C;;;;;;QACxE;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ,UAAU;YAChB,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAyC;;;;;;YACnE,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAkD;;;;;;YAC5E,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAkD;;;;;;YAC5E,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA4C;;;;;;YACtE;gBACE,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAU;;;;;;QACpC;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ,OAAO;YACb,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;YACL,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;QACpC;IACF;IAEA,MAAM,wBAAwB,cAAc,MAAM,CAAC,CAAA;QACjD,MAAM,cAAc,WAAW,WAAW;QAC1C,MAAM,gBACJ,GAAG,OAAO,EAAE,cAAc,SAAS,gBACnC,GAAG,WAAW,EAAE,cAAc,SAAS,gBACvC,GAAG,mBAAmB,EAAE,cAAc,SAAS,gBAC/C,GAAG,wBAAwB,EAAE,cAAc,SAAS;QAGtD,IAAI,gBAAgB;QACpB,IAAI,mBAAmB,OAAO;YAC5B,gBAAgB,GAAG,KAAK,EAAE,kBAAkB;QAC9C;QAEA,OAAO,iBAAiB;IAC1B;IAEA,MAAM,QAAQ;QACZ,QAAQ,cAAc,MAAM;QAC5B,QAAQ,cAAc,MAAM,CAAC,CAAA,KAAM,GAAG,KAAK,KAAK,UAAU,MAAM;QAChE,gBAAgB,cAAc,MAAM,CAAC,CAAA,KAAM,GAAG,KAAK,KAAK,kBAAkB,MAAM;QAChF,SAAS,cAAc,MAAM,CAAC,CAAA,KAAM,GAAG,KAAK,KAAK,aAAa,GAAG,KAAK,KAAK,UAAU,MAAM;QAC3F,UAAU,cAAc,MAAM,CAAC,CAAA,KAAM,GAAG,QAAQ,KAAK,WAAW,MAAM;IACxE;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCAAyB;;;;;;;0CAGpD,6LAAC;gCAAE,WAAU;0CAAsB;;;;;;;;;;;;kCAGrC,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS;;0CACf,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAyB;;;;;;0DACtC,6LAAC;gDAAE,WAAU;0DAAqC,MAAM,MAAM;;;;;;;;;;;;kDAEhE,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK1B,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAyB;;;;;;0DACtC,6LAAC;gDAAE,WAAU;0DAAmC,MAAM,MAAM;;;;;;;;;;;;kDAE9D,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK7B,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAyB;;;;;;0DACtC,6LAAC;gDAAE,WAAU;0DAAsC,MAAM,cAAc;;;;;;;;;;;;kDAEzE,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAKvB,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAyB;;;;;;0DACtC,6LAAC;gDAAE,WAAU;0DAAqC,MAAM,OAAO;;;;;;;;;;;;kDAEjE,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK7B,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAyB;;;;;;0DACtC,6LAAC;gDAAE,WAAU;0DAAmC,MAAM,QAAQ;;;;;;;;;;;;kDAEhE,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOjC,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIlC,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;8CAGjD,6LAAC;oCAAI,WAAU;8CACZ;wCACC;4CAAE,OAAO;4CAAO,OAAO;wCAAQ;wCAC/B;4CAAE,OAAO;4CAAU,OAAO;wCAAS;wCACnC;4CAAE,OAAO;4CAAkB,OAAO;wCAAiB;wCACnD;4CAAE,OAAO;4CAAW,OAAO;wCAAU;wCACrC;4CAAE,OAAO;4CAAU,OAAO;wCAAS;qCACpC,CAAC,GAAG,CAAC,CAAC,uBACL,6LAAC,qIAAA,CAAA,SAAM;4CAEL,SAAS,mBAAmB,OAAO,KAAK,GAAG,YAAY;4CACvD,MAAK;4CACL,SAAS,IAAM,kBAAkB,OAAO,KAAK;sDAE5C,OAAO,KAAK;2CALR,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAc7B,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;;oCAAC;oCAAwB,sBAAsB,MAAM;oCAAC;;;;;;;0CAChE,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;;kDACJ,6LAAC,oIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;8DACP,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;;;;;;;;;;;;kDAGf,6LAAC,oIAAA,CAAA,YAAS;kDACP,0BACC,6LAAC,oIAAA,CAAA,WAAQ;sDACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;gDAAC,SAAS;gDAAG,WAAU;0DAC/B,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;wDAAyB;;;;;;;;;;;;;;;;mDAKhD,sBAAsB,MAAM,KAAK,kBACnC,6LAAC,oIAAA,CAAA,WAAQ;sDACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;gDAAC,SAAS;gDAAG,WAAU;0DAAkC;;;;;;;;;;mDAKrE,sBAAsB,GAAG,CAAC,CAAC,mBACzB,6LAAC,oIAAA,CAAA,WAAQ;;kEACP,6LAAC,oIAAA,CAAA,YAAS;wDAAC,WAAU;kEACnB,cAAA,6LAAC;4DAAI,WAAU;;gEACZ,aAAa,GAAG,KAAK;gEACrB,GAAG,OAAO;;;;;;;;;;;;kEAGf,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEACZ,GAAG,mBAAmB,IAAI;;;;;;;;;;;kEAG/B,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;oEAAuB,OAAO,GAAG,WAAW;8EACxD,GAAG,WAAW;;;;;;gEAEhB,GAAG,iBAAiB,kBACnB,6LAAC;oEAAI,WAAU;oEAAkC,OAAO,GAAG,iBAAiB;;wEAAE;wEACnE,GAAG,iBAAiB;;;;;;;;;;;;;;;;;;kEAKrC,6LAAC,oIAAA,CAAA,YAAS;kEAAE,iBAAiB,GAAG,QAAQ;;;;;;kEACxC,6LAAC,oIAAA,CAAA,YAAS;kEACP,IAAI,KAAK,GAAG,gBAAgB,EAAE,kBAAkB,CAAC;;;;;;kEAEpD,6LAAC,oIAAA,CAAA,YAAS;kEAAE,GAAG,wBAAwB,IAAI;;;;;;kEAC3C,6LAAC,oIAAA,CAAA,YAAS;kEAAE,cAAc,GAAG,KAAK;;;;;;kEAClC,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,KAAO;oEAChB,OAAM;8EAEN,cAAA,6LAAC,mMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;;;;;;8EAEjB,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,wBAAwB;oEACvC,OAAM;8EAEN,cAAA,6LAAC,8MAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;8EAElB,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,0BAA0B,GAAG,iBAAiB;oEAC7D,OAAM;8EAEN,cAAA,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+CAtDX,GAAG,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAmEhD,uBACC,6LAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;;kCACf,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC,oIAAA,CAAA,mBAAgB;wBAAC,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;AAKtD;GA/UwB;KAAA", "debugId": null}}, {"offset": {"line": 7030, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/certificazioni/CertificazioniManager.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Input } from '@/components/ui/input'\nimport { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { certificazioniApi, strumentiApi, rapportiGeneraliApi, nonConformitaApi } from '@/lib/api'\nimport { CertificazioneCavo, StrumentoCertificato, RapportoGeneraleCollaudo, NonConformita } from '@/types/certificazioni'\nimport { \n  FileText, \n  Search, \n  Plus, \n  Edit, \n  Trash2, \n  CheckCircle, \n  Clock, \n  AlertCircle,\n  Eye,\n  Download,\n  Upload,\n  Loader2,\n  <PERSON>,\n  Settings,\n  BarChart3,\n  Alert<PERSON>riangle\n} from 'lucide-react'\nimport CertificazioneForm from './CertificazioneForm'\nimport StrumentiManager from './StrumentiManager'\nimport RapportiGeneraliManager from './RapportiGeneraliManager'\nimport CertificazioniStatistics from './CertificazioniStatistics'\nimport CertificazioneForm from './CertificazioneForm'\nimport StrumentiManager from './StrumentiManager'\nimport RapportiGeneraliManager from './RapportiGeneraliManager'\nimport NonConformitaManager from './NonConformitaManager'\n\ninterface CertificazioniManagerProps {\n  cantiereId: number\n}\n\nexport default function CertificazioniManager({ cantiereId }: CertificazioniManagerProps) {\n  const [activeTab, setActiveTab] = useState('certificazioni')\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedStatus, setSelectedStatus] = useState('all')\n  const [certificazioni, setCertificazioni] = useState<CertificazioneCavo[]>([])\n  const [strumenti, setStrumenti] = useState<StrumentoCertificato[]>([])\n  const [rapporti, setRapporti] = useState<RapportoGeneraleCollaudo[]>([])\n  const [nonConformita, setNonConformita] = useState<NonConformita[]>([])\n  const [isLoading, setIsLoading] = useState(true)\n  const [error, setError] = useState('')\n  const [showForm, setShowForm] = useState(false)\n  const [selectedCertificazione, setSelectedCertificazione] = useState<CertificazioneCavo | null>(null)\n  const [selectedCertificazione, setSelectedCertificazione] = useState<CertificazioneCavo | null>(null)\n\n  const { user, cantiere } = useAuth()\n\n  useEffect(() => {\n    if (cantiereId) {\n      loadData()\n    }\n  }, [cantiereId])\n\n  const loadData = async () => {\n    try {\n      setIsLoading(true)\n      setError('')\n\n      const [certData, strumData, rappData, ncData] = await Promise.all([\n        certificazioniApi.getCertificazioni(cantiereId),\n        strumentiApi.getStrumenti(cantiereId),\n        rapportiGeneraliApi.getRapporti(cantiereId),\n        nonConformitaApi.getNonConformita(cantiereId)\n      ])\n\n      setCertificazioni(certData)\n      setStrumenti(strumData)\n      setRapporti(rappData)\n      setNonConformita(ncData)\n    } catch (error: any) {\n      setError(error.response?.data?.detail || 'Errore durante il caricamento dei dati')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleCreateCertificazione = () => {\n    setSelectedCertificazione(null)\n    setShowForm(true)\n  }\n\n  const handleEditCertificazione = (cert: CertificazioneCavo) => {\n    setSelectedCertificazione(cert)\n    setShowForm(true)\n  }\n\n  const handleDeleteCertificazione = async (id: number) => {\n    if (!confirm('Sei sicuro di voler eliminare questa certificazione?')) return\n    \n    try {\n      await certificazioniApi.deleteCertificazione(cantiereId, id)\n      await loadData()\n    } catch (error: any) {\n      setError(error.response?.data?.detail || 'Errore durante l\\'eliminazione')\n    }\n  }\n\n  const handleGeneratePDF = async (id: number) => {\n    try {\n      const blob = await certificazioniApi.generatePDF(cantiereId, id)\n      const url = window.URL.createObjectURL(blob)\n      const link = document.createElement('a')\n      link.href = url\n      link.download = `certificato_${id}.pdf`\n      document.body.appendChild(link)\n      link.click()\n      document.body.removeChild(link)\n      window.URL.revokeObjectURL(url)\n    } catch (error: any) {\n      setError(error.response?.data?.detail || 'Errore durante la generazione del PDF')\n    }\n  }\n\n  const getStatusBadge = (stato: string) => {\n    switch (stato?.toLowerCase()) {\n      case 'conforme':\n        return <Badge className=\"bg-green-100 text-green-800 border-green-200\">Conforme</Badge>\n      case 'non_conforme':\n        return <Badge className=\"bg-red-100 text-red-800 border-red-200\">Non Conforme</Badge>\n      case 'conforme_con_riserva':\n        return <Badge className=\"bg-yellow-100 text-yellow-800 border-yellow-200\">Con Riserva</Badge>\n      case 'bozza':\n        return <Badge className=\"bg-gray-100 text-gray-800 border-gray-200\">Bozza</Badge>\n      default:\n        return <Badge className=\"bg-gray-100 text-gray-800 border-gray-200\">Da Verificare</Badge>\n    }\n  }\n\n  const filteredCertificazioni = certificazioni.filter(cert => {\n    const matchesSearch = cert.id_cavo?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         cert.numero_certificato?.toLowerCase().includes(searchTerm.toLowerCase())\n    \n    let matchesStatus = true\n    if (selectedStatus !== 'all') {\n      matchesStatus = cert.stato_certificato?.toLowerCase() === selectedStatus\n    }\n    \n    return matchesSearch && matchesStatus\n  })\n\n  const stats = {\n    totali: certificazioni.length,\n    conformi: certificazioni.filter(c => c.stato_certificato === 'CONFORME').length,\n    non_conformi: certificazioni.filter(c => c.stato_certificato === 'NON_CONFORME').length,\n    bozze: certificazioni.filter(c => c.stato_certificato === 'BOZZA').length,\n    con_riserva: certificazioni.filter(c => c.stato_certificato === 'CONFORME_CON_RISERVA').length\n  }\n\n  if (showForm) {\n    return (\n      <CertificazioneForm\n        cantiereId={cantiereId}\n        certificazione={selectedCertificazione}\n        strumenti={strumenti}\n        onSuccess={() => {\n          setShowForm(false)\n          loadData()\n        }}\n        onCancel={() => setShowForm(false)}\n      />\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-slate-900 flex items-center gap-3\">\n            <FileText className=\"h-8 w-8 text-blue-600\" />\n            Sistema Certificazioni\n          </h1>\n          <p className=\"text-slate-600 mt-1\">Gestione completa certificazioni CEI 64-8</p>\n        </div>\n        \n        <div className=\"flex gap-2\">\n          <Button variant=\"outline\" size=\"sm\" onClick={() => {}}>\n            <Download className=\"h-4 w-4 mr-2\" />\n            Esporta\n          </Button>\n          <Button size=\"sm\" onClick={handleCreateCertificazione}>\n            <Plus className=\"h-4 w-4 mr-2\" />\n            Nuova Certificazione\n          </Button>\n        </div>\n      </div>\n\n      {/* Statistics */}\n      <CertificazioniStatistics stats={stats} />\n\n      {/* Tabs */}\n      <Tabs value={activeTab} onValueChange={setActiveTab}>\n        <TabsList className=\"grid w-full grid-cols-5\">\n          <TabsTrigger value=\"certificazioni\">\n            <FileText className=\"h-4 w-4 mr-2\" />\n            Certificazioni\n          </TabsTrigger>\n          <TabsTrigger value=\"strumenti\">\n            <Settings className=\"h-4 w-4 mr-2\" />\n            Strumenti\n          </TabsTrigger>\n          <TabsTrigger value=\"rapporti\">\n            <BarChart3 className=\"h-4 w-4 mr-2\" />\n            Rapporti Generali\n          </TabsTrigger>\n          <TabsTrigger value=\"non_conformita\">\n            <AlertTriangle className=\"h-4 w-4 mr-2\" />\n            Non Conformità\n          </TabsTrigger>\n          <TabsTrigger value=\"statistiche\">\n            <Award className=\"h-4 w-4 mr-2\" />\n            Statistiche\n          </TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"certificazioni\" className=\"space-y-4\">\n          {/* Filters */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                <Search className=\"h-5 w-5\" />\n                Ricerca e Filtri\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"flex gap-4\">\n                <div className=\"flex-1\">\n                  <Input\n                    placeholder=\"Cerca per ID cavo o numero certificato...\"\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                  />\n                </div>\n                <div className=\"flex gap-2\">\n                  {['all', 'conforme', 'non_conforme', 'bozza', 'conforme_con_riserva'].map((status) => (\n                    <Button\n                      key={status}\n                      variant={selectedStatus === status ? 'default' : 'outline'}\n                      size=\"sm\"\n                      onClick={() => setSelectedStatus(status)}\n                    >\n                      {status === 'all' ? 'Tutte' : \n                       status === 'conforme' ? 'Conformi' :\n                       status === 'non_conforme' ? 'Non Conformi' :\n                       status === 'bozza' ? 'Bozze' : 'Con Riserva'}\n                    </Button>\n                  ))}\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Certificazioni Table */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Elenco Certificazioni ({filteredCertificazioni.length})</CardTitle>\n              <CardDescription>\n                Gestione completa delle certificazioni CEI 64-8\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"rounded-md border\">\n                <Table>\n                  <TableHeader>\n                    <TableRow>\n                      <TableHead>Numero Certificato</TableHead>\n                      <TableHead>ID Cavo</TableHead>\n                      <TableHead>Data</TableHead>\n                      <TableHead>Stato</TableHead>\n                      <TableHead>Isolamento (MΩ)</TableHead>\n                      <TableHead>Operatore</TableHead>\n                      <TableHead>Azioni</TableHead>\n                    </TableRow>\n                  </TableHeader>\n                  <TableBody>\n                    {isLoading ? (\n                      <TableRow>\n                        <TableCell colSpan={7} className=\"text-center py-8\">\n                          <div className=\"flex items-center justify-center gap-2\">\n                            <Loader2 className=\"h-4 w-4 animate-spin\" />\n                            Caricamento...\n                          </div>\n                        </TableCell>\n                      </TableRow>\n                    ) : filteredCertificazioni.length === 0 ? (\n                      <TableRow>\n                        <TableCell colSpan={7} className=\"text-center py-8 text-slate-500\">\n                          Nessuna certificazione trovata\n                        </TableCell>\n                      </TableRow>\n                    ) : (\n                      filteredCertificazioni.map((cert) => (\n                        <TableRow key={cert.id_certificazione}>\n                          <TableCell className=\"font-medium\">{cert.numero_certificato}</TableCell>\n                          <TableCell className=\"font-mono\">{cert.id_cavo}</TableCell>\n                          <TableCell>\n                            {new Date(cert.data_certificazione).toLocaleDateString('it-IT')}\n                          </TableCell>\n                          <TableCell>{getStatusBadge(cert.stato_certificato || '')}</TableCell>\n                          <TableCell>\n                            <span className={`font-medium ${\n                              parseFloat(cert.valore_isolamento || '0') >= 500 \n                                ? 'text-green-600' \n                                : 'text-red-600'\n                            }`}>\n                              {cert.valore_isolamento || '-'}\n                            </span>\n                          </TableCell>\n                          <TableCell>{cert.id_operatore || '-'}</TableCell>\n                          <TableCell>\n                            <div className=\"flex gap-1\">\n                              <Button \n                                variant=\"ghost\" \n                                size=\"sm\"\n                                onClick={() => handleEditCertificazione(cert)}\n                              >\n                                <Eye className=\"h-4 w-4\" />\n                              </Button>\n                              <Button \n                                variant=\"ghost\" \n                                size=\"sm\"\n                                onClick={() => handleEditCertificazione(cert)}\n                              >\n                                <Edit className=\"h-4 w-4\" />\n                              </Button>\n                              <Button \n                                variant=\"ghost\" \n                                size=\"sm\"\n                                onClick={() => handleGeneratePDF(cert.id_certificazione)}\n                              >\n                                <Download className=\"h-4 w-4\" />\n                              </Button>\n                              <Button \n                                variant=\"ghost\" \n                                size=\"sm\"\n                                onClick={() => handleDeleteCertificazione(cert.id_certificazione)}\n                              >\n                                <Trash2 className=\"h-4 w-4\" />\n                              </Button>\n                            </div>\n                          </TableCell>\n                        </TableRow>\n                      ))\n                    )}\n                  </TableBody>\n                </Table>\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"strumenti\">\n          <StrumentiManager cantiereId={cantiereId} strumenti={strumenti} onUpdate={loadData} />\n        </TabsContent>\n\n        <TabsContent value=\"rapporti\">\n          <RapportiGeneraliManager cantiereId={cantiereId} rapporti={rapporti} onUpdate={loadData} />\n        </TabsContent>\n\n        <TabsContent value=\"strumenti\">\n          <StrumentiManager\n            cantiereId={cantiereId}\n            strumenti={strumenti}\n            onUpdate={loadData}\n          />\n        </TabsContent>\n\n        <TabsContent value=\"rapporti\">\n          <RapportiGeneraliManager\n            cantiereId={cantiereId}\n            rapporti={rapporti}\n            onUpdate={loadData}\n          />\n        </TabsContent>\n\n        <TabsContent value=\"non_conformita\">\n          <NonConformitaManager\n            cantiereId={cantiereId}\n            nonConformita={nonConformita}\n            onUpdate={loadData}\n          />\n        </TabsContent>\n\n        <TabsContent value=\"statistiche\">\n          <CertificazioniStatistics stats={stats} detailed />\n        </TabsContent>\n      </Tabs>\n\n      {error && (\n        <Alert className=\"border-red-200 bg-red-50\">\n          <AlertCircle className=\"h-4 w-4 text-red-600\" />\n          <AlertDescription className=\"text-red-800\">{error}</AlertDescription>\n        </Alert>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBA;AACA;AACA;AACA;AAIA;;;AArCA;;;;;;;;;;;;;;;;;;;AA2Ce,SAAS,sBAAsB,EAAE,UAAU,EAA8B;;IACtF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB,EAAE;IAC7E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,EAAE;IACrE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B,EAAE;IACvE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IAChG,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IAEhG,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,IAAI,YAAY;gBACd;YACF;QACF;0CAAG;QAAC;KAAW;IAEf,MAAM,WAAW;QACf,IAAI;YACF,aAAa;YACb,SAAS;YAET,MAAM,CAAC,UAAU,WAAW,UAAU,OAAO,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAChE,oHAAA,CAAA,oBAAiB,CAAC,iBAAiB,CAAC;gBACpC,oHAAA,CAAA,eAAY,CAAC,YAAY,CAAC;gBAC1B,oHAAA,CAAA,sBAAmB,CAAC,WAAW,CAAC;gBAChC,oHAAA,CAAA,mBAAgB,CAAC,gBAAgB,CAAC;aACnC;YAED,kBAAkB;YAClB,aAAa;YACb,YAAY;YACZ,iBAAiB;QACnB,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,QAAQ,EAAE,MAAM,UAAU;QAC3C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,6BAA6B;QACjC,0BAA0B;QAC1B,YAAY;IACd;IAEA,MAAM,2BAA2B,CAAC;QAChC,0BAA0B;QAC1B,YAAY;IACd;IAEA,MAAM,6BAA6B,OAAO;QACxC,IAAI,CAAC,QAAQ,yDAAyD;QAEtE,IAAI;YACF,MAAM,oHAAA,CAAA,oBAAiB,CAAC,oBAAoB,CAAC,YAAY;YACzD,MAAM;QACR,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,QAAQ,EAAE,MAAM,UAAU;QAC3C;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,MAAM,OAAO,MAAM,oHAAA,CAAA,oBAAiB,CAAC,WAAW,CAAC,YAAY;YAC7D,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;YACvC,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,QAAQ,GAAG,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC;YACvC,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO,GAAG,CAAC,eAAe,CAAC;QAC7B,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,QAAQ,EAAE,MAAM,UAAU;QAC3C;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ,OAAO;YACb,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA+C;;;;;;YACzE,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAyC;;;;;;YACnE,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAkD;;;;;;YAC5E,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA4C;;;;;;YACtE;gBACE,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA4C;;;;;;QACxE;IACF;IAEA,MAAM,yBAAyB,eAAe,MAAM,CAAC,CAAA;QACnD,MAAM,gBAAgB,KAAK,OAAO,EAAE,cAAc,SAAS,WAAW,WAAW,OAC5D,KAAK,kBAAkB,EAAE,cAAc,SAAS,WAAW,WAAW;QAE3F,IAAI,gBAAgB;QACpB,IAAI,mBAAmB,OAAO;YAC5B,gBAAgB,KAAK,iBAAiB,EAAE,kBAAkB;QAC5D;QAEA,OAAO,iBAAiB;IAC1B;IAEA,MAAM,QAAQ;QACZ,QAAQ,eAAe,MAAM;QAC7B,UAAU,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,iBAAiB,KAAK,YAAY,MAAM;QAC/E,cAAc,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,iBAAiB,KAAK,gBAAgB,MAAM;QACvF,OAAO,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,iBAAiB,KAAK,SAAS,MAAM;QACzE,aAAa,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,iBAAiB,KAAK,wBAAwB,MAAM;IAChG;IAEA,IAAI,UAAU;QACZ,qBACE,6LAAC,6JAAA,CAAA,UAAkB;YACjB,YAAY;YACZ,gBAAgB;YAChB,WAAW;YACX,WAAW;gBACT,YAAY;gBACZ;YACF;YACA,UAAU,IAAM,YAAY;;;;;;IAGlC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAA0B;;;;;;;0CAGhD,6LAAC;gCAAE,WAAU;0CAAsB;;;;;;;;;;;;kCAGrC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,SAAS,KAAO;;kDAClD,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,6LAAC,qIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAK,SAAS;;kDACzB,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAOvC,6LAAC,mKAAA,CAAA,UAAwB;gBAAC,OAAO;;;;;;0BAGjC,6LAAC,mIAAA,CAAA,OAAI;gBAAC,OAAO;gBAAW,eAAe;;kCACrC,6LAAC,mIAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;;kDACjB,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;;kDACjB,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;;kDACjB,6LAAC,qNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGxC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;;kDACjB,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAG5C,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;;kDACjB,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAKtC,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAiB,WAAU;;0CAE5C,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;kDAIlC,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;wDACJ,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;8DAGjD,6LAAC;oDAAI,WAAU;8DACZ;wDAAC;wDAAO;wDAAY;wDAAgB;wDAAS;qDAAuB,CAAC,GAAG,CAAC,CAAC,uBACzE,6LAAC,qIAAA,CAAA,SAAM;4DAEL,SAAS,mBAAmB,SAAS,YAAY;4DACjD,MAAK;4DACL,SAAS,IAAM,kBAAkB;sEAEhC,WAAW,QAAQ,UACnB,WAAW,aAAa,aACxB,WAAW,iBAAiB,iBAC5B,WAAW,UAAU,UAAU;2DAR3B;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAiBjB,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC,mIAAA,CAAA,YAAS;;oDAAC;oDAAwB,uBAAuB,MAAM;oDAAC;;;;;;;0DACjE,6LAAC,mIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;;kEACJ,6LAAC,oIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;8EACP,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;;;;;;;;;;;;kEAGf,6LAAC,oIAAA,CAAA,YAAS;kEACP,0BACC,6LAAC,oIAAA,CAAA,WAAQ;sEACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;gEAAC,SAAS;gEAAG,WAAU;0EAC/B,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,oNAAA,CAAA,UAAO;4EAAC,WAAU;;;;;;wEAAyB;;;;;;;;;;;;;;;;mEAKhD,uBAAuB,MAAM,KAAK,kBACpC,6LAAC,oIAAA,CAAA,WAAQ;sEACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;gEAAC,SAAS;gEAAG,WAAU;0EAAkC;;;;;;;;;;mEAKrE,uBAAuB,GAAG,CAAC,CAAC,qBAC1B,6LAAC,oIAAA,CAAA,WAAQ;;kFACP,6LAAC,oIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAe,KAAK,kBAAkB;;;;;;kFAC3D,6LAAC,oIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAa,KAAK,OAAO;;;;;;kFAC9C,6LAAC,oIAAA,CAAA,YAAS;kFACP,IAAI,KAAK,KAAK,mBAAmB,EAAE,kBAAkB,CAAC;;;;;;kFAEzD,6LAAC,oIAAA,CAAA,YAAS;kFAAE,eAAe,KAAK,iBAAiB,IAAI;;;;;;kFACrD,6LAAC,oIAAA,CAAA,YAAS;kFACR,cAAA,6LAAC;4EAAK,WAAW,CAAC,YAAY,EAC5B,WAAW,KAAK,iBAAiB,IAAI,QAAQ,MACzC,mBACA,gBACJ;sFACC,KAAK,iBAAiB,IAAI;;;;;;;;;;;kFAG/B,6LAAC,oIAAA,CAAA,YAAS;kFAAE,KAAK,YAAY,IAAI;;;;;;kFACjC,6LAAC,oIAAA,CAAA,YAAS;kFACR,cAAA,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,qIAAA,CAAA,SAAM;oFACL,SAAQ;oFACR,MAAK;oFACL,SAAS,IAAM,yBAAyB;8FAExC,cAAA,6LAAC,mMAAA,CAAA,MAAG;wFAAC,WAAU;;;;;;;;;;;8FAEjB,6LAAC,qIAAA,CAAA,SAAM;oFACL,SAAQ;oFACR,MAAK;oFACL,SAAS,IAAM,yBAAyB;8FAExC,cAAA,6LAAC,8MAAA,CAAA,OAAI;wFAAC,WAAU;;;;;;;;;;;8FAElB,6LAAC,qIAAA,CAAA,SAAM;oFACL,SAAQ;oFACR,MAAK;oFACL,SAAS,IAAM,kBAAkB,KAAK,iBAAiB;8FAEvD,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wFAAC,WAAU;;;;;;;;;;;8FAEtB,6LAAC,qIAAA,CAAA,SAAM;oFACL,SAAQ;oFACR,MAAK;oFACL,SAAS,IAAM,2BAA2B,KAAK,iBAAiB;8FAEhE,cAAA,6LAAC,6MAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+DA7CX,KAAK,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCA2DrD,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;kCACjB,cAAA,6LAAC,2JAAA,CAAA,UAAgB;4BAAC,YAAY;4BAAY,WAAW;4BAAW,UAAU;;;;;;;;;;;kCAG5E,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;kCACjB,cAAA,6LAAC,kKAAA,CAAA,UAAuB;4BAAC,YAAY;4BAAY,UAAU;4BAAU,UAAU;;;;;;;;;;;kCAGjF,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;kCACjB,cAAA,6LAAC,2JAAA,CAAA,UAAgB;4BACf,YAAY;4BACZ,WAAW;4BACX,UAAU;;;;;;;;;;;kCAId,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;kCACjB,cAAA,6LAAC,kKAAA,CAAA,UAAuB;4BACtB,YAAY;4BACZ,UAAU;4BACV,UAAU;;;;;;;;;;;kCAId,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;kCACjB,cAAA,6LAAC,+JAAA,CAAA,UAAoB;4BACnB,YAAY;4BACZ,eAAe;4BACf,UAAU;;;;;;;;;;;kCAId,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;kCACjB,cAAA,6LAAC,mKAAA,CAAA,UAAwB;4BAAC,OAAO;4BAAO,QAAQ;;;;;;;;;;;;;;;;;YAInD,uBACC,6LAAC;gBAAM,WAAU;;kCACf,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC;wBAAiB,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;AAKtD;GA7WwB;;QAcK,kIAAA,CAAA,UAAO;;;KAdZ", "debugId": null}}, {"offset": {"line": 7984, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/certificazioni/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useAuth } from '@/contexts/AuthContext'\nimport CertificazioniManager from '@/components/certificazioni/CertificazioniManager'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { AlertCircle } from 'lucide-react'\n\nexport default function CertificazioniPage() {\n  const { user, cantiere } = useAuth()\n\n  const cantiereId = cantiere?.id_cantiere || user?.id_utente\n\n  if (!cantiereId) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6\">\n        <div className=\"max-w-[90%] mx-auto\">\n          <Alert className=\"border-red-200 bg-red-50\">\n            <AlertCircle className=\"h-4 w-4 text-red-600\" />\n            <AlertDescription className=\"text-red-800\">\n              Cantiere non selezionato. Seleziona un cantiere per accedere alle certificazioni.\n            </AlertDescription>\n          </Alert>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6\">\n      <div className=\"max-w-[95%] mx-auto\">\n        <CertificazioniManager cantiereId={cantiereId} />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEjC,MAAM,aAAa,UAAU,eAAe,MAAM;IAElD,IAAI,CAAC,YAAY;QACf,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;;sCACf,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,6LAAC,oIAAA,CAAA,mBAAgB;4BAAC,WAAU;sCAAe;;;;;;;;;;;;;;;;;;;;;;IAOrD;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,gKAAA,CAAA,UAAqB;gBAAC,YAAY;;;;;;;;;;;;;;;;AAI3C;GA3BwB;;QACK,kIAAA,CAAA,UAAO;;;KADZ", "debugId": null}}]}