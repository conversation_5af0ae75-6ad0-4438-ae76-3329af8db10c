"use strict";exports.id=940,exports.ids=[940],exports.modules={9593:(e,a,s)=>{s.d(a,{A:()=>t});let t=(0,s(62688).A)("cloud",[["path",{d:"M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z",key:"p7xjir"}]])},15079:(e,a,s)=>{s.d(a,{bq:()=>m,eb:()=>x,gC:()=>u,l6:()=>d,yv:()=>c});var t=s(60687);s(43210);var r=s(97822),i=s(78272),n=s(13964),l=s(3589),o=s(4780);function d({...e}){return(0,t.jsx)(r.bL,{"data-slot":"select",...e})}function c({...e}){return(0,t.jsx)(r.WT,{"data-slot":"select-value",...e})}function m({className:e,size:a="default",children:s,...n}){return(0,t.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":a,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n,children:[s,(0,t.jsx)(r.In,{asChild:!0,children:(0,t.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function u({className:e,children:a,position:s="popper",...i}){return(0,t.jsx)(r.ZL,{children:(0,t.jsxs)(r.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...i,children:[(0,t.jsx)(p,{}),(0,t.jsx)(r.LM,{className:(0,o.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,t.jsx)(v,{})]})})}function x({className:e,children:a,...s}){return(0,t.jsxs)(r.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,children:[(0,t.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,t.jsx)(r.VF,{children:(0,t.jsx)(n.A,{className:"size-4"})})}),(0,t.jsx)(r.p4,{children:a})]})}function p({className:e,...a}){return(0,t.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,t.jsx)(l.A,{className:"size-4"})})}function v({className:e,...a}){return(0,t.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,t.jsx)(i.A,{className:"size-4"})})}},34729:(e,a,s)=>{s.d(a,{T:()=>n});var t=s(60687),r=s(43210),i=s(4780);let n=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...a}));n.displayName="Textarea"},44493:(e,a,s)=>{s.d(a,{BT:()=>o,Wu:()=>d,ZB:()=>l,Zp:()=>i,aR:()=>n});var t=s(60687);s(43210);var r=s(4780);function i({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...a})}function n({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...a})}function l({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",e),...a})}function o({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...a})}function d({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...a})}},70440:(e,a,s)=>{s.r(a),s.d(a,{default:()=>r});var t=s(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},80013:(e,a,s)=>{s.d(a,{J:()=>n});var t=s(60687);s(43210);var r=s(78148),i=s(4780);function n({className:e,...a}){return(0,t.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...a})}},89667:(e,a,s)=>{s.d(a,{p:()=>n});var t=s(60687),r=s(43210),i=s(4780);let n=r.forwardRef(({className:e,type:a,...s},r)=>(0,t.jsx)("input",{type:a,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),ref:r,...s}));n.displayName="Input"},91136:(e,a,s)=>{s.d(a,{A:()=>w});var t=s(60687),r=s(29523),i=s(91821),n=s(41862),l=s(10022),o=s(8819),d=s(93613),c=s(43210),m=s(62185),u=s(44493),x=s(80013),p=s(15079);function v({formData:e,cavi:a,responsabili:s,strumenti:r,validationErrors:i,isCavoLocked:n,onInputChange:l}){return(0,t.jsxs)(u.Zp,{children:[(0,t.jsx)(u.aR,{className:"pb-1 px-3 pt-2",children:(0,t.jsx)(u.ZB,{className:"text-sm font-semibold",children:"Informazioni Base"})}),(0,t.jsx)(u.Wu,{className:"space-y-2 pt-0 px-3 pb-2",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(x.J,{htmlFor:"id_cavo",className:"text-sm",children:"Cavo *"}),(0,t.jsxs)(p.l6,{value:e.id_cavo,onValueChange:e=>l("id_cavo",e),disabled:n,children:[(0,t.jsx)(p.bq,{children:(0,t.jsx)(p.yv,{placeholder:"Seleziona cavo..."})}),(0,t.jsx)(p.gC,{children:a.map(e=>(0,t.jsxs)(p.eb,{value:e.id_cavo,children:[e.id_cavo," - ",e.descrizione]},e.id_cavo))})]}),i.id_cavo&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:i.id_cavo})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(x.J,{htmlFor:"id_operatore",className:"text-sm",children:"Operatore"}),(0,t.jsxs)(p.l6,{value:e.id_operatore?.toString()||"",onValueChange:e=>l("id_operatore",parseInt(e)),children:[(0,t.jsx)(p.bq,{children:(0,t.jsx)(p.yv,{placeholder:"Seleziona operatore..."})}),(0,t.jsx)(p.gC,{children:s.map(e=>(0,t.jsxs)(p.eb,{value:e.id_responsabile.toString(),children:[e.nome," ",e.cognome]},e.id_responsabile))})]})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(x.J,{htmlFor:"id_strumento",className:"text-sm",children:"Strumento"}),(0,t.jsxs)(p.l6,{value:e.id_strumento?.toString()||"",onValueChange:e=>{let a=r.find(a=>a.id_strumento===parseInt(e));l("id_strumento",parseInt(e)),a&&l("strumento_utilizzato",`${a.marca} ${a.modello}`)},children:[(0,t.jsx)(p.bq,{children:(0,t.jsx)(p.yv,{placeholder:"Seleziona strumento..."})}),(0,t.jsx)(p.gC,{children:r.map(e=>(0,t.jsxs)(p.eb,{value:e.id_strumento.toString(),children:[e.marca," ",e.modello," - ",e.numero_serie]},e.id_strumento))})]})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(x.J,{htmlFor:"tipo_certificato",className:"text-sm",children:"Tipo Certificato"}),(0,t.jsxs)(p.l6,{value:e.tipo_certificato||"SINGOLO",onValueChange:e=>l("tipo_certificato",e),children:[(0,t.jsx)(p.bq,{children:(0,t.jsx)(p.yv,{})}),(0,t.jsxs)(p.gC,{children:[(0,t.jsx)(p.eb,{value:"SINGOLO",children:"Singolo"}),(0,t.jsx)(p.eb,{value:"GRUPPO",children:"Gruppo"})]})]})]})]})})]})}var h=s(89667),g=s(9593),f=s(11860),j=s(84027);function b({formData:e,weatherData:a,isLoadingWeather:s,isWeatherOverride:i,onInputChange:l,onToggleWeatherOverride:o}){return a?(0,t.jsxs)(u.Zp,{children:[(0,t.jsx)(u.aR,{className:"pb-1 px-3 pt-2",children:(0,t.jsxs)(u.ZB,{className:"flex items-center gap-2 text-sm font-semibold",children:[(0,t.jsx)(g.A,{className:"h-3 w-3"}),"Condizioni Ambientali"]})}),(0,t.jsxs)(u.Wu,{className:"pt-0 px-3 pb-2",children:[(0,t.jsx)("div",{className:`p-3 rounded-lg border ${a.isDemo?"bg-yellow-50 border-yellow-200":"bg-green-50 border-green-200"}`,children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[s?(0,t.jsx)(n.A,{className:"h-4 w-4 animate-spin"}):(0,t.jsx)(g.A,{className:"h-4 w-4 text-blue-600"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"text-sm font-medium",children:[a.city&&`${a.city} - `,a.temperature,"\xb0C, ",a.humidity,"% UR"]}),(0,t.jsx)(r.$,{type:"button",variant:"ghost",size:"sm",onClick:o,className:"h-6 px-2 text-xs",children:i?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(f.A,{className:"h-3 w-3 mr-1"}),"Auto"]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(j.A,{className:"h-3 w-3 mr-1"}),"Modifica"]})})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-600 mt-1",children:[a.source,a.isDemo&&" (Dati dimostrativi)"]})]})]})}),i&&(0,t.jsxs)("div",{className:"mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-blue-800 mb-2",children:"Inserimento Manuale"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(x.J,{htmlFor:"temperatura_prova",className:"text-sm",children:"Temperatura (\xb0C)"}),(0,t.jsx)(h.p,{id:"temperatura_prova",type:"number",value:e.temperatura_prova||"",onChange:e=>l("temperatura_prova",parseFloat(e.target.value)),placeholder:"20",className:"text-sm"})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(x.J,{htmlFor:"umidita_prova",className:"text-sm",children:"Umidit\xe0 (%)"}),(0,t.jsx)(h.p,{id:"umidita_prova",type:"number",value:e.umidita_prova||"",onChange:e=>l("umidita_prova",parseFloat(e.target.value)),placeholder:"50",className:"text-sm"})]})]})]})]})]}):null}function N({formData:e,validationErrors:a,onInputChange:s}){return(0,t.jsxs)(u.Zp,{children:[(0,t.jsx)(u.aR,{className:"pb-1 px-3 pt-2",children:(0,t.jsxs)(u.ZB,{className:"flex items-center gap-2 text-sm font-semibold",children:[(0,t.jsx)(j.A,{className:"h-3 w-3"}),"Misurazioni e Test"]})}),(0,t.jsxs)(u.Wu,{className:"space-y-2 pt-0 px-3 pb-2",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(x.J,{htmlFor:"valore_isolamento",className:"text-sm",children:"Isolamento (MΩ) *"}),(0,t.jsx)(h.p,{id:"valore_isolamento",type:"number",step:"0.01",value:e.valore_isolamento||"",onChange:e=>s("valore_isolamento",parseFloat(e.target.value)),placeholder:"1000",className:"text-sm"}),a.valore_isolamento&&(0,t.jsx)("p",{className:"text-xs text-red-600",children:a.valore_isolamento})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(x.J,{htmlFor:"valore_continuita",className:"text-sm",children:"Continuit\xe0 *"}),(0,t.jsxs)(p.l6,{value:e.valore_continuita,onValueChange:e=>s("valore_continuita",e),children:[(0,t.jsx)(p.bq,{className:"text-sm",children:(0,t.jsx)(p.yv,{placeholder:"Seleziona..."})}),(0,t.jsxs)(p.gC,{children:[(0,t.jsx)(p.eb,{value:"CONFORME",children:"✅ CONFORME"}),(0,t.jsx)(p.eb,{value:"NON_CONFORME",children:"❌ NON CONFORME"})]})]}),a.valore_continuita&&(0,t.jsx)("p",{className:"text-xs text-red-600",children:a.valore_continuita})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(x.J,{htmlFor:"valore_resistenza",className:"text-sm",children:"Resistenza (Ω) *"}),(0,t.jsx)(h.p,{id:"valore_resistenza",type:"number",step:"0.01",value:e.valore_resistenza||"",onChange:e=>s("valore_resistenza",parseFloat(e.target.value)),placeholder:"0.5",className:"text-sm"}),a.valore_resistenza&&(0,t.jsx)("p",{className:"text-xs text-red-600",children:a.valore_resistenza})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(x.J,{htmlFor:"tensione_prova_isolamento",className:"text-sm",children:"Tensione Prova (V)"}),(0,t.jsx)(h.p,{id:"tensione_prova_isolamento",type:"number",value:e.tensione_prova_isolamento||500,onChange:e=>s("tensione_prova_isolamento",parseInt(e.target.value)),className:"text-sm"})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(x.J,{htmlFor:"durata_prova",className:"text-sm",children:"Durata Prova (min)"}),(0,t.jsx)(h.p,{id:"durata_prova",type:"number",value:e.durata_prova||"",onChange:e=>s("durata_prova",parseInt(e.target.value)),placeholder:"1",className:"text-sm"})]})]})]})]})}var _=s(34729);function y({formData:e,onInputChange:a}){return(0,t.jsxs)(u.Zp,{children:[(0,t.jsx)(u.aR,{className:"pb-1 px-3 pt-2",children:(0,t.jsx)(u.ZB,{className:"text-sm font-semibold",children:"Note e Stato"})}),(0,t.jsxs)(u.Wu,{className:"space-y-2 px-3 pb-2",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(x.J,{htmlFor:"note",className:"text-sm",children:"Note"}),(0,t.jsx)(_.T,{id:"note",value:e.note,onChange:e=>a("note",e.target.value),placeholder:"Note aggiuntive...",rows:2,className:"text-sm"})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(x.J,{htmlFor:"stato_certificato",className:"text-sm",children:"Stato Certificato"}),(0,t.jsxs)(p.l6,{value:e.stato_certificato||"BOZZA",onValueChange:e=>a("stato_certificato",e),children:[(0,t.jsx)(p.bq,{className:"text-sm",children:(0,t.jsx)(p.yv,{})}),(0,t.jsxs)(p.gC,{children:[(0,t.jsx)(p.eb,{value:"BOZZA",children:"Bozza"}),(0,t.jsx)(p.eb,{value:"CONFORME",children:"Conforme"}),(0,t.jsx)(p.eb,{value:"NON_CONFORME",children:"Non Conforme"}),(0,t.jsx)(p.eb,{value:"CONFORME_CON_RISERVA",children:"Conforme con Riserva"})]})]})]})]})]})}function w({cantiereId:e,certificazione:a,strumenti:s,preselectedCavoId:u,onSuccess:x,onCancel:p}){let{formData:h,cavi:g,responsabili:f,weatherData:j,isLoading:_,isSaving:w,isLoadingWeather:C,error:z,validationErrors:O,isWeatherOverride:S,isEdit:F,isCavoLocked:A,handleInputChange:I,handleSubmit:k,setIsWeatherOverride:R,onCancel:Z}=function({cantiereId:e,certificazione:a,strumenti:s,preselectedCavoId:t,onSuccess:r,onCancel:i}){let[n,l]=(0,c.useState)({id_cantiere:e,tipo_certificato:"SINGOLO",stato_certificato:"BOZZA",valore_continuita:"CONFORME",tensione_prova_isolamento:500,temperatura_prova:20,umidita_prova:50}),[o,d]=(0,c.useState)(!0),[u,x]=(0,c.useState)(!1),[p,v]=(0,c.useState)(!1),[h,g]=(0,c.useState)(""),[f,j]=(0,c.useState)({}),[b,N]=(0,c.useState)(!1),[_,y]=(0,c.useState)([]),[w,C]=(0,c.useState)([]),[z,O]=(0,c.useState)(null),S=!!a;async function F(){if(!function(){let e={};return n.id_cavo||(e.id_cavo="Seleziona un cavo"),(!n.valore_isolamento||n.valore_isolamento<=0)&&(e.valore_isolamento="Inserisci un valore di isolamento valido"),n.valore_continuita||(e.valore_continuita="Seleziona il risultato della continuit\xe0"),n.valore_resistenza||(e.valore_resistenza="Inserisci un valore di resistenza"),j(e),0===Object.keys(e).length}())return void g("Correggi gli errori nel form prima di continuare");try{let s;x(!0),g("");let t=function(){let a={...n};return z&&!b&&(a.temperatura_prova=z.temperature,a.umidita_prova=z.humidity),a.id_cantiere=e,a.data_certificazione=a.data_certificazione||new Date().toISOString().split("T")[0],a}();(s=S&&a?await m.km.updateCertificazione(a.id_certificazione,t):await m.km.createCertificazione(t)).data&&r(s.data)}catch(e){console.error("Errore salvataggio:",e),g(e.response?.data?.detail||"Errore durante il salvataggio")}finally{x(!1)}}return{formData:n,cavi:_,responsabili:w,strumenti:s,weatherData:z,isLoading:o,isSaving:u,isLoadingWeather:p,error:h,validationErrors:f,isWeatherOverride:b,isEdit:S,isCavoLocked:!!t,handleInputChange:function(e,a){l(s=>({...s,[e]:a})),f[e]&&j(a=>{let s={...a};return delete s[e],s})},handleSubmit:F,setIsWeatherOverride:N,onCancel:i}}({cantiereId:e,certificazione:a,strumenti:s,preselectedCavoId:u,onSuccess:x,onCancel:p});return _?(0,t.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,t.jsx)(n.A,{className:"h-8 w-8 animate-spin"}),(0,t.jsx)("span",{className:"ml-2",children:"Caricamento dati..."})]}):(0,t.jsxs)("div",{className:"h-full w-full",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 border-b bg-white",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(l.A,{className:"h-4 w-4 text-blue-600"}),(0,t.jsx)("h1",{className:"text-base font-semibold text-slate-900",children:F?"Modifica Certificazione":"Nuova Certificazione"})]}),(0,t.jsxs)(r.$,{onClick:k,disabled:w,size:"sm",children:[w?(0,t.jsx)(n.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(o.A,{className:"h-4 w-4 mr-2"}),F?"Aggiorna":"Salva"]})]}),(0,t.jsxs)("div",{className:"p-3 h-[calc(100%-60px)] overflow-y-auto",children:[z&&(0,t.jsxs)(i.Fc,{variant:"destructive",className:"mb-2",children:[(0,t.jsx)(d.A,{className:"h-4 w-4"}),(0,t.jsx)(i.TN,{children:z})]}),(0,t.jsx)("form",{onSubmit:e=>{e.preventDefault(),k()},children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-3 h-full",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(v,{formData:h,cavi:g,responsabili:f,strumenti:s,validationErrors:O,isCavoLocked:A,onInputChange:I}),(0,t.jsx)(b,{formData:h,weatherData:j,isLoadingWeather:C,isWeatherOverride:S,onInputChange:I,onToggleWeatherOverride:()=>R(!S)})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(N,{formData:h,validationErrors:O,onInputChange:I}),(0,t.jsx)(y,{formData:h,onInputChange:I})]})]})})]})]})}},96834:(e,a,s)=>{s.d(a,{E:()=>o});var t=s(60687);s(43210);var r=s(8730),i=s(24224),n=s(4780);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:a,asChild:s=!1,...i}){let o=s?r.DX:"span";return(0,t.jsx)(o,{"data-slot":"badge",className:(0,n.cn)(l({variant:a}),e),...i})}}};