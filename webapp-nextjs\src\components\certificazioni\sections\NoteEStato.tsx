'use client'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { CertificazioneCavoCreate } from '@/types/certificazioni'

interface NoteEStatoProps {
  formData: Partial<CertificazioneCavoCreate>
  onInputChange: (field: string, value: any) => void
}

export function NoteEStato({
  formData,
  onInputChange
}: NoteEStatoProps) {
  return (
    <Card>
      <CardHeader className="pb-1 px-3 pt-2">
        <CardTitle className="text-sm font-semibold">Note e Stato</CardTitle>
      </CardHeader>
      <CardContent className="space-y-2 px-3 pb-2">
        <div className="space-y-1">
          <Label htmlFor="note" className="text-sm">Note</Label>
          <Textarea
            id="note"
            value={formData.note}
            onChange={(e) => onInputChange('note', e.target.value)}
            placeholder="Note aggiuntive..."
            rows={2}
            className="text-sm"
          />
        </div>

        <div className="space-y-1">
          <Label htmlFor="stato_certificato" className="text-sm">Stato Certificato</Label>
          <Select
            value={formData.stato_certificato || 'BOZZA'}
            onValueChange={(value) => onInputChange('stato_certificato', value)}
          >
            <SelectTrigger className="text-sm">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="BOZZA">Bozza</SelectItem>
              <SelectItem value="CONFORME">Conforme</SelectItem>
              <SelectItem value="NON_CONFORME">Non Conforme</SelectItem>
              <SelectItem value="CONFORME_CON_RISERVA">Conforme con Riserva</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardContent>
    </Card>
  )
}
