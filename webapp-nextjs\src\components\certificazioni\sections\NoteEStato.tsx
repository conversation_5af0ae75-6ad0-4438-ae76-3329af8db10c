'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { CertificazioneCavoCreate } from '@/types/certificazioni'

interface NoteEStatoProps {
  formData: Partial<CertificazioneCavoCreate>
  onInputChange: (field: string, value: any) => void
}

export function NoteEStato({
  formData,
  onInputChange
}: NoteEStatoProps) {
  return (
    <Card className="h-fit shadow-sm border-gray-200 bg-white">
      <CardHeader className="pb-4 px-6 pt-6 border-b border-gray-100">
        <CardTitle className="text-lg font-semibold text-gray-900">
          📝 Note e Stato Certificazione
        </CardTitle>
      </CardHeader>
      <CardContent className="px-6 py-6 space-y-5">
        {/* Note */}
        <div className="space-y-2">
          <Label htmlFor="note" className="text-sm font-medium text-gray-700">
            Note Aggiuntive
          </Label>
          <Textarea
            id="note"
            value={formData.note || ''}
            onChange={(e) => onInputChange('note', e.target.value)}
            placeholder="Inserisci eventuali note, osservazioni o anomalie riscontrate durante la certificazione..."
            rows={5}
            className="resize-none text-sm border-gray-300 focus:border-blue-500 focus:ring-blue-500"
          />
          <p className="text-xs text-gray-500">
            Descrivi eventuali condizioni particolari o osservazioni rilevanti
          </p>
        </div>

        {/* Stato Certificazione */}
        <div className="space-y-3">
          <Label htmlFor="stato_certificato" className="text-base font-medium">
            Stato Certificazione
          </Label>
          <Select
            value={formData.stato_certificato || 'BOZZA'}
            onValueChange={(value) => onInputChange('stato_certificato', value)}
          >
            <SelectTrigger className="h-12 text-base">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="BOZZA">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-gray-400"></div>
                  <span className="text-base">Bozza</span>
                </div>
              </SelectItem>
              <SelectItem value="CONFORME">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-green-500"></div>
                  <span className="text-base">✅ Conforme</span>
                </div>
              </SelectItem>
              <SelectItem value="NON_CONFORME">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-red-500"></div>
                  <span className="text-base">❌ Non Conforme</span>
                </div>
              </SelectItem>
              <SelectItem value="CONFORME_CON_RISERVA">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                  <span className="text-base">⚠️ Conforme con Riserva</span>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
          <p className="text-sm text-slate-500">
            Lo stato verrà determinato automaticamente in base ai valori misurati
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
