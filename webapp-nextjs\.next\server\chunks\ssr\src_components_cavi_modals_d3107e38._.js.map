{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/modals/CableActionModals.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport {\n  AlertTriangle,\n  FileText,\n  X,\n  Download,\n  AlertCircle,\n  CheckCircle,\n  Loader2,\n  Zap,\n  Lock,\n  HelpCircle,\n  Settings,\n  Award\n} from 'lucide-react'\nimport { Cavo } from '@/types'\nimport CertificazioneForm from '@/components/certificazioni/CertificazioneForm'\n\n// Types for modal props\ninterface BaseModalProps {\n  open: boolean\n  onClose: () => void\n  cavo: Cavo | null\n}\n\ninterface DisconnectModalProps extends BaseModalProps {\n  onConfirm: (cavoId: string) => Promise<void>\n}\n\ninterface GeneratePdfModalProps extends BaseModalProps {\n  onGenerate: (cavoId: string, options: PdfGenerationOptions) => Promise<void>\n}\n\ninterface CertificationErrorModalProps extends BaseModalProps {\n  errorMessage?: string\n  missingRequirements?: string[]\n}\n\ninterface PdfGenerationOptions {\n  fileName: string\n  includeTestData: boolean\n  format: 'standard' | 'detailed'\n  emailRecipient?: string\n}\n\n// Enhanced Modal Header Component with prominent cable ID\ninterface EnhancedModalHeaderProps {\n  icon: React.ReactNode\n  title: string\n  cableId: string\n  description?: string\n}\n\nconst EnhancedModalHeader: React.FC<EnhancedModalHeaderProps> = ({\n  icon,\n  title,\n  cableId,\n  description\n}) => (\n  <DialogHeader>\n    <DialogTitle className=\"flex items-center gap-2\">\n      {icon}\n      <span className=\"flex items-center gap-2\">\n        {title}\n        <span className=\"px-2 py-1 bg-blue-100 text-blue-800 rounded-md text-sm font-mono font-semibold\">\n          {cableId}\n        </span>\n      </span>\n    </DialogTitle>\n    {description && (\n      <DialogDescription className=\"text-sm text-muted-foreground\">\n        {description}\n      </DialogDescription>\n    )}\n  </DialogHeader>\n)\n\n// Enhanced Dialog Content with improved overlay behavior\ninterface EnhancedDialogContentProps {\n  children: React.ReactNode\n  className?: string\n  onKeyDown?: (e: React.KeyboardEvent) => void\n  ariaLabelledBy?: string\n  ariaDescribedBy?: string\n}\n\nconst EnhancedDialogContent: React.FC<EnhancedDialogContentProps> = ({\n  children,\n  className = \"sm:max-w-md\",\n  onKeyDown,\n  ariaLabelledBy,\n  ariaDescribedBy\n}) => (\n  <DialogContent\n    className={className}\n    onKeyDown={onKeyDown}\n    aria-labelledby={ariaLabelledBy}\n    aria-describedby={ariaDescribedBy}\n    onPointerDownOutside={(e) => e.preventDefault()} // Prevent closing on outside click\n    onEscapeKeyDown={(e) => {\n      // Allow ESC key to close\n      if (onKeyDown) {\n        onKeyDown(e as any)\n      }\n    }}\n  >\n    {children}\n  </DialogContent>\n)\n\n// Disconnect Confirmation Modal with enhanced UI\nexport const DisconnectCableModal: React.FC<DisconnectModalProps> = ({\n  open,\n  onClose,\n  cavo,\n  onConfirm\n}) => {\n  const [isLoading, setIsLoading] = useState(false)\n  const [showFinalConfirmation, setShowFinalConfirmation] = useState(false)\n\n  const handleInitialConfirm = () => {\n    setShowFinalConfirmation(true)\n  }\n\n  const handleFinalConfirm = async () => {\n    if (!cavo) return\n\n    setIsLoading(true)\n    try {\n      await onConfirm(cavo.id_cavo)\n      onClose()\n      setShowFinalConfirmation(false)\n    } catch (error) {\n      console.error('Error disconnecting cable:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleClose = () => {\n    setShowFinalConfirmation(false)\n    onClose()\n  }\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Escape') {\n      handleClose()\n    }\n  }\n\n  if (!cavo) return null\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <EnhancedDialogContent\n        className=\"sm:max-w-md\"\n        onKeyDown={handleKeyDown}\n        ariaLabelledBy=\"disconnect-modal-title\"\n        ariaDescribedBy=\"disconnect-modal-description\"\n      >\n        <EnhancedModalHeader\n          icon={<Zap className=\"h-5 w-5 text-orange-500\" />}\n          title=\"Gestione Collegamenti\"\n          cableId={cavo.id_cavo}\n          description=\"Gestisci le connessioni del cavo selezionato\"\n        />\n\n        {!showFinalConfirmation ? (\n          <>\n            <div className=\"py-4\">\n              <div className=\"flex items-center gap-2 mb-4\">\n                <div className=\"flex items-center gap-1\">\n                  <div className=\"w-3 h-3 rounded-full bg-green-500\"></div>\n                  <div className=\"w-3 h-3 rounded-full bg-green-500\"></div>\n                </div>\n                <span className=\"text-sm font-medium text-green-700\">\n                  Completamente collegato\n                  <HelpCircle\n                    className=\"inline h-4 w-4 ml-1 cursor-help\"\n                    title=\"Cavo collegato sia all'origine che alla destinazione\"\n                  />\n                </span>\n              </div>\n\n              <div className=\"space-y-3\">\n                <div>\n                  <Label htmlFor=\"responsabile-collegamento\" className=\"text-sm font-medium\">\n                    Responsabile Collegamento\n                  </Label>\n                  <select\n                    id=\"responsabile-collegamento\"\n                    className=\"w-full mt-1 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                    defaultValue=\"\"\n                  >\n                    <option value=\"\" disabled>Seleziona responsabile...</option>\n                    <option value=\"cantiere\">Cantiere</option>\n                    <option value=\"tecnico1\">Tecnico 1</option>\n                    <option value=\"tecnico2\">Tecnico 2</option>\n                  </select>\n                </div>\n              </div>\n            </div>\n\n            <Alert className=\"my-4 bg-orange-50 border-orange-200\">\n              <AlertTriangle className=\"h-4 w-4 text-orange-600\" />\n              <AlertDescription className=\"text-orange-800\">\n                <strong>Attenzione:</strong> Lo scollegamento rimuoverà tutte le connessioni attive del cavo.\n                Questa azione potrebbe influenzare altri componenti collegati.\n              </AlertDescription>\n            </Alert>\n\n            <DialogFooter className=\"gap-2\">\n              <Button\n                variant=\"outline\"\n                onClick={handleClose}\n                disabled={isLoading}\n                className=\"flex-1 hover:bg-gray-50\"\n              >\n                Annulla\n              </Button>\n              <Button\n                variant=\"destructive\"\n                onClick={handleInitialConfirm}\n                disabled={isLoading}\n                className=\"flex-1 hover:bg-red-600\"\n              >\n                <AlertTriangle className=\"mr-2 h-4 w-4\" />\n                Scollega Completamente\n              </Button>\n            </DialogFooter>\n          </>\n        ) : (\n          <>\n            <div className=\"py-4 text-center\">\n              <div className=\"mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4\">\n                <AlertTriangle className=\"h-6 w-6 text-red-600\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                Conferma Scollegamento\n              </h3>\n              <p className=\"text-sm text-gray-600 mb-4\">\n                Sei veramente sicuro di voler scollegare completamente il cavo <strong>{cavo.id_cavo}</strong>?\n              </p>\n              <div className=\"bg-red-50 border border-red-200 rounded-md p-3\">\n                <p className=\"text-sm text-red-800 font-medium\">\n                  ⚠️ Questa azione è irreversibile\n                </p>\n              </div>\n            </div>\n\n            <DialogFooter className=\"gap-2\">\n              <Button\n                variant=\"outline\"\n                onClick={() => setShowFinalConfirmation(false)}\n                disabled={isLoading}\n                className=\"flex-1\"\n              >\n                No, Annulla\n              </Button>\n              <Button\n                variant=\"destructive\"\n                onClick={handleFinalConfirm}\n                disabled={isLoading}\n                className=\"flex-1\"\n              >\n                {isLoading ? (\n                  <>\n                    <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                    Scollegando...\n                  </>\n                ) : (\n                  'Sì, Scollega'\n                )}\n              </Button>\n            </DialogFooter>\n          </>\n        )}\n      </EnhancedDialogContent>\n    </Dialog>\n  )\n}\n\n// Enhanced PDF Generation Modal\nexport const GeneratePdfModal: React.FC<GeneratePdfModalProps> = ({\n  open,\n  onClose,\n  cavo,\n  onGenerate\n}) => {\n  const [isLoading, setIsLoading] = useState(false)\n  const [options, setOptions] = useState<PdfGenerationOptions>({\n    fileName: '',\n    includeTestData: true,\n    format: 'standard',\n    emailRecipient: ''\n  })\n  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})\n\n  // Set default filename when modal opens\n  useEffect(() => {\n    if (cavo && open) {\n      setOptions(prev => ({\n        ...prev,\n        fileName: `Certificato_${cavo.id_cavo}_${new Date().toISOString().split('T')[0]}.pdf`\n      }))\n      setValidationErrors({})\n    }\n  }, [cavo, open])\n\n  // Validate form fields\n  const validateForm = () => {\n    const errors: Record<string, string> = {}\n\n    if (!options.fileName.trim()) {\n      errors.fileName = 'Il nome del file è obbligatorio'\n    } else if (!/^[a-zA-Z0-9_\\-\\s]+\\.pdf$/i.test(options.fileName)) {\n      errors.fileName = 'Il nome del file deve terminare con .pdf e contenere solo caratteri validi'\n    }\n\n    if (options.emailRecipient && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(options.emailRecipient)) {\n      errors.emailRecipient = 'Inserisci un indirizzo email valido'\n    }\n\n    setValidationErrors(errors)\n    return Object.keys(errors).length === 0\n  }\n\n  const handleGenerate = async () => {\n    if (!cavo || !validateForm()) return\n\n    setIsLoading(true)\n    try {\n      await onGenerate(cavo.id_cavo, options)\n      onClose()\n    } catch (error) {\n      console.error('Error generating PDF:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Escape') {\n      onClose()\n    }\n  }\n\n  const isFormValid = options.fileName.trim() && Object.keys(validationErrors).length === 0\n\n  if (!cavo) return null\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <EnhancedDialogContent\n        className=\"sm:max-w-lg\"\n        onKeyDown={handleKeyDown}\n        ariaLabelledBy=\"pdf-modal-title\"\n      >\n        <EnhancedModalHeader\n          icon={<FileText className=\"h-5 w-5 text-blue-500\" />}\n          title=\"Genera Certificato\"\n          cableId={cavo.id_cavo}\n          description=\"Configura le opzioni per la generazione del certificato PDF\"\n        />\n\n        <div className=\"space-y-4 py-4\">\n          {/* File Name */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"fileName\" className=\"text-sm font-medium\">\n              Nome File *\n            </Label>\n            <Input\n              id=\"fileName\"\n              value={options.fileName}\n              onChange={(e) => {\n                setOptions(prev => ({ ...prev, fileName: e.target.value }))\n                if (validationErrors.fileName) {\n                  setValidationErrors(prev => ({ ...prev, fileName: '' }))\n                }\n              }}\n              onBlur={validateForm}\n              placeholder=\"Certificato_C001_2025-06-29.pdf\"\n              className={validationErrors.fileName ? 'border-red-500 focus:ring-red-500' : ''}\n            />\n            {validationErrors.fileName && (\n              <p className=\"text-sm text-red-600 flex items-center gap-1\">\n                <AlertCircle className=\"h-3 w-3\" />\n                {validationErrors.fileName}\n              </p>\n            )}\n          </div>\n\n          {/* Format Selection */}\n          <div className=\"space-y-3\">\n            <Label className=\"text-sm font-medium\">Formato Certificato</Label>\n            <div className=\"space-y-2\">\n              <label className=\"flex items-center space-x-3 cursor-pointer p-2 border rounded-md hover:bg-gray-50 transition-colors\">\n                <input\n                  type=\"radio\"\n                  name=\"format\"\n                  value=\"standard\"\n                  checked={options.format === 'standard'}\n                  onChange={(e) => setOptions(prev => ({ ...prev, format: e.target.value as 'standard' | 'detailed' }))}\n                  className=\"text-blue-600 focus:ring-blue-500\"\n                />\n                <div>\n                  <span className=\"text-sm font-medium\">Standard</span>\n                  <p className=\"text-xs text-gray-500\">Certificato con informazioni essenziali</p>\n                </div>\n              </label>\n              <label className=\"flex items-center space-x-3 cursor-pointer p-2 border rounded-md hover:bg-gray-50 transition-colors\">\n                <input\n                  type=\"radio\"\n                  name=\"format\"\n                  value=\"detailed\"\n                  checked={options.format === 'detailed'}\n                  onChange={(e) => setOptions(prev => ({ ...prev, format: e.target.value as 'standard' | 'detailed' }))}\n                  className=\"text-blue-600 focus:ring-blue-500\"\n                />\n                <div>\n                  <span className=\"text-sm font-medium\">Dettagliato</span>\n                  <p className=\"text-xs text-gray-500\">Certificato con tutti i dati tecnici</p>\n                </div>\n              </label>\n            </div>\n          </div>\n\n          {/* Include Test Data */}\n          <div className=\"flex items-center space-x-3 p-2 border rounded-md\">\n            <Checkbox\n              id=\"includeTestData\"\n              checked={options.includeTestData}\n              onCheckedChange={(checked) => setOptions(prev => ({ ...prev, includeTestData: checked as boolean }))}\n            />\n            <div>\n              <Label htmlFor=\"includeTestData\" className=\"text-sm font-medium cursor-pointer\">\n                Includi Dati di Collaudo\n              </Label>\n              <p className=\"text-xs text-gray-500\">Aggiunge i risultati dei test al certificato</p>\n            </div>\n          </div>\n\n          {/* Email Recipient (Optional) */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"emailRecipient\" className=\"text-sm font-medium\">\n              Email Destinatario (Opzionale)\n            </Label>\n            <Input\n              id=\"emailRecipient\"\n              type=\"email\"\n              value={options.emailRecipient}\n              onChange={(e) => {\n                setOptions(prev => ({ ...prev, emailRecipient: e.target.value }))\n                if (validationErrors.emailRecipient) {\n                  setValidationErrors(prev => ({ ...prev, emailRecipient: '' }))\n                }\n              }}\n              onBlur={validateForm}\n              placeholder=\"<EMAIL>\"\n              className={validationErrors.emailRecipient ? 'border-red-500 focus:ring-red-500' : ''}\n            />\n            {validationErrors.emailRecipient && (\n              <p className=\"text-sm text-red-600 flex items-center gap-1\">\n                <AlertCircle className=\"h-3 w-3\" />\n                {validationErrors.emailRecipient}\n              </p>\n            )}\n          </div>\n        </div>\n\n        <DialogFooter className=\"gap-2\">\n          <Button\n            variant=\"outline\"\n            onClick={onClose}\n            disabled={isLoading}\n            className=\"flex-1 hover:bg-gray-50\"\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={handleGenerate}\n            disabled={isLoading || !isFormValid}\n            className={`flex-1 ${!isFormValid ? 'opacity-50 cursor-not-allowed' : 'hover:bg-blue-600'}`}\n          >\n            {isLoading ? (\n              <>\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                Generando...\n              </>\n            ) : (\n              <>\n                <Download className=\"mr-2 h-4 w-4\" />\n                Genera PDF\n              </>\n            )}\n          </Button>\n        </DialogFooter>\n      </EnhancedDialogContent>\n    </Dialog>\n  )\n}\n\n// Enhanced Certification Error Modal\nexport const CertificationErrorModal: React.FC<CertificationErrorModalProps> = ({\n  open,\n  onClose,\n  cavo,\n  errorMessage,\n  missingRequirements = []\n}) => {\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Escape') {\n      onClose()\n    }\n  }\n\n  const defaultRequirements = [\n    'Il cavo deve essere nello stato \"Installato\"',\n    'Il cavo deve essere completamente collegato',\n    'Tutti i dati di collaudo devono essere presenti'\n  ]\n\n  const requirements = missingRequirements.length > 0 ? missingRequirements : defaultRequirements\n\n  if (!cavo) return null\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <EnhancedDialogContent\n        className=\"sm:max-w-md\"\n        onKeyDown={handleKeyDown}\n        ariaLabelledBy=\"certification-error-title\"\n      >\n        <EnhancedModalHeader\n          icon={<AlertCircle className=\"h-5 w-5 text-red-500\" />}\n          title=\"Impossibile Certificare Cavo\"\n          cableId={cavo.id_cavo}\n          description=\"Il cavo non può essere certificato nel suo stato attuale\"\n        />\n\n        <div className=\"py-4\">\n          {errorMessage && (\n            <Alert className=\"mb-4 bg-red-50 border-red-200\">\n              <AlertCircle className=\"h-4 w-4 text-red-600\" />\n              <AlertDescription className=\"text-red-800\">{errorMessage}</AlertDescription>\n            </Alert>\n          )}\n\n          <div className=\"space-y-4\">\n            <div>\n              <h4 className=\"text-sm font-semibold text-gray-900 mb-3 flex items-center gap-2\">\n                <AlertTriangle className=\"h-4 w-4 text-amber-500\" />\n                Requisiti mancanti:\n              </h4>\n              <ul className=\"space-y-3\">\n                {requirements.map((requirement, index) => (\n                  <li key={index} className=\"flex items-start gap-3 p-2 bg-red-50 border border-red-200 rounded-md\">\n                    <div className=\"flex-shrink-0 w-5 h-5 bg-red-100 rounded-full flex items-center justify-center mt-0.5\">\n                      <X className=\"h-3 w-3 text-red-600\" />\n                    </div>\n                    <span className=\"text-sm text-red-800\">{requirement}</span>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            <Alert className=\"bg-blue-50 border-blue-200\">\n              <CheckCircle className=\"h-4 w-4 text-blue-600\" />\n              <AlertDescription className=\"text-blue-800\">\n                <strong>Prossimi passi:</strong> Completa tutti i requisiti sopra elencati per abilitare la certificazione del cavo.\n              </AlertDescription>\n            </Alert>\n          </div>\n        </div>\n\n        <DialogFooter>\n          <Button\n            onClick={onClose}\n            className=\"w-full hover:bg-blue-600 focus:ring-2 focus:ring-blue-500\"\n          >\n            <CheckCircle className=\"mr-2 h-4 w-4\" />\n            Ho Capito\n          </Button>\n        </DialogFooter>\n      </EnhancedDialogContent>\n    </Dialog>\n  )\n}\n\n// Cable Certification Modal\ninterface CertificationModalProps extends BaseModalProps {\n  cantiereId?: number\n  onCertify: (cavoId: string, certificationData: CertificationData) => Promise<void>\n}\n\ninterface CertificationData {\n  responsabile: string\n  dataCertificazione: string\n  esitoCertificazione: 'CONFORME' | 'NON_CONFORME' | 'PARZIALMENTE_CONFORME'\n  note?: string\n}\n\nexport const CertificationModal: React.FC<CertificationModalProps> = ({\n  open,\n  onClose,\n  cavo,\n  cantiereId = 1,\n  onCertify\n}) => {\n  console.log('🚀 CertificationModal render:', { open, cavo: cavo?.id_cavo })\n\n  if (!cavo) return null\n\n  const handleFormSuccess = async (certificazioneData?: any) => {\n    // Se abbiamo dati di certificazione dal form completo, usali\n    if (certificazioneData && cavo) {\n      try {\n        await onCertify(cavo.id_cavo, certificazioneData)\n      } catch (error) {\n        console.error('Errore certificazione:', error)\n        return // Non chiudere il modal se c'è un errore\n      }\n    }\n    onClose()\n  }\n\n  const handleFormCancel = () => {\n    onClose()\n  }\n\n  // Modal a schermo intero per certificazione\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <DialogContent className=\"max-w-[98vw] w-[98vw] h-[98vh] p-0 overflow-hidden\">\n        <DialogTitle className=\"sr-only\">Certificazione Cavo {cavo.id_cavo}</DialogTitle>\n        <CertificazioneForm\n          cantiereId={cantiereId}\n          certificazione={null}\n          strumenti={[]}\n          preselectedCavoId={cavo.id_cavo}\n          onSuccess={handleFormSuccess}\n          onCancel={handleFormCancel}\n        />\n      </DialogContent>\n    </Dialog>\n  )\n}\n\n// Form semplificato originale con pulsante per form avanzato\ninterface CertificationModalSimpleProps extends CertificationModalProps {\n  onUseAdvanced: () => void\n}\n\nconst CertificationModalSimple: React.FC<CertificationModalSimpleProps> = ({\n  open,\n  onClose,\n  cavo,\n  onCertify,\n  onUseAdvanced\n}) => {\n  const [isLoading, setIsLoading] = useState(false)\n  const [formData, setFormData] = useState<CertificationData>({\n    responsabile: '',\n    dataCertificazione: new Date().toISOString().split('T')[0],\n    esitoCertificazione: 'CONFORME',\n    note: ''\n  })\n  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})\n\n  // Reset form when modal opens\n  useEffect(() => {\n    if (open && cavo) {\n      console.log('🚀 CertificationModalSimple opened for cavo:', cavo.id_cavo)\n      setFormData({\n        responsabile: '',\n        dataCertificazione: new Date().toISOString().split('T')[0],\n        esitoCertificazione: 'CONFORME',\n        note: ''\n      })\n      setValidationErrors({})\n    }\n  }, [open, cavo])\n\n  const validateForm = () => {\n    const errors: Record<string, string> = {}\n\n    if (!formData.responsabile.trim()) {\n      errors.responsabile = 'Il responsabile è obbligatorio'\n    }\n\n    if (!formData.dataCertificazione) {\n      errors.dataCertificazione = 'La data di certificazione è obbligatoria'\n    } else {\n      const selectedDate = new Date(formData.dataCertificazione)\n      const today = new Date()\n      today.setHours(0, 0, 0, 0)\n\n      if (selectedDate > today) {\n        errors.dataCertificazione = 'La data non può essere futura'\n      }\n    }\n\n    setValidationErrors(errors)\n    return Object.keys(errors).length === 0\n  }\n\n  const handleCertify = async () => {\n    if (!cavo || !validateForm()) return\n\n    setIsLoading(true)\n    try {\n      await onCertify(cavo.id_cavo, formData)\n      onClose()\n    } catch (error) {\n      console.error('Error certifying cable:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Escape') {\n      onClose()\n    }\n  }\n\n  const isFormValid = formData.responsabile.trim() && formData.dataCertificazione && Object.keys(validationErrors).length === 0\n\n  // Mock cable status for demonstration\n  const cableStatus = {\n    installato: true,\n    collegato: true,\n    certificato: false\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <EnhancedDialogContent\n        className=\"sm:max-w-lg\"\n        onKeyDown={handleKeyDown}\n        ariaLabelledBy=\"certification-modal-title\"\n      >\n        <EnhancedModalHeader\n          icon={<Lock className=\"h-5 w-5 text-blue-500\" />}\n          title=\"Gestione Certificazione\"\n          cableId={cavo.id_cavo}\n          description=\"Certifica il cavo dopo aver completato tutti i controlli\"\n        />\n\n        <div className=\"space-y-4 py-4\">\n          {/* Cable Status Indicators */}\n          <div className=\"space-y-3\">\n            <h4 className=\"text-sm font-semibold text-gray-900\">Stato Cavo:</h4>\n            <div className=\"grid grid-cols-3 gap-3\">\n              <div className=\"flex items-center gap-2 p-2 bg-gray-50 rounded-md\">\n                <div className={`w-3 h-3 rounded-full ${cableStatus.installato ? 'bg-green-500' : 'bg-red-500'}`}></div>\n                <span className=\"text-sm font-medium\">\n                  {cableStatus.installato ? '✓' : '✗'} Installato\n                </span>\n              </div>\n              <div className=\"flex items-center gap-2 p-2 bg-gray-50 rounded-md\">\n                <div className={`w-3 h-3 rounded-full ${cableStatus.collegato ? 'bg-green-500' : 'bg-red-500'}`}></div>\n                <span className=\"text-sm font-medium\">\n                  {cableStatus.collegato ? '✓' : '✗'} Collegato\n                </span>\n              </div>\n              <div className=\"flex items-center gap-2 p-2 bg-gray-50 rounded-md\">\n                <div className={`w-3 h-3 rounded-full ${cableStatus.certificato ? 'bg-green-500' : 'bg-orange-500'}`}></div>\n                <span className=\"text-sm font-medium\">\n                  {cableStatus.certificato ? '✓' : '⚠'} Non certificato\n                </span>\n              </div>\n            </div>\n          </div>\n\n          {/* Responsabile Certificazione */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"responsabile\" className=\"text-sm font-medium\">\n              Responsabile Certificazione *\n            </Label>\n            <select\n              id=\"responsabile\"\n              value={formData.responsabile}\n              onChange={(e) => {\n                setFormData(prev => ({ ...prev, responsabile: e.target.value }))\n                if (validationErrors.responsabile) {\n                  setValidationErrors(prev => ({ ...prev, responsabile: '' }))\n                }\n              }}\n              onBlur={validateForm}\n              className={`w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${\n                validationErrors.responsabile ? 'border-red-500' : 'border-gray-300'\n              }`}\n            >\n              <option value=\"\" disabled>Seleziona responsabile...</option>\n              <option value=\"cantiere\">Cantiere</option>\n              <option value=\"tecnico_1\">Tecnico 1</option>\n              <option value=\"tecnico_2\">Tecnico 2</option>\n              <option value=\"supervisore\">Supervisore</option>\n            </select>\n            {validationErrors.responsabile && (\n              <p className=\"text-sm text-red-600 flex items-center gap-1\">\n                <AlertCircle className=\"h-3 w-3\" />\n                {validationErrors.responsabile}\n              </p>\n            )}\n          </div>\n\n          {/* Data Certificazione */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"dataCertificazione\" className=\"text-sm font-medium\">\n              Data Certificazione *\n            </Label>\n            <Input\n              id=\"dataCertificazione\"\n              type=\"date\"\n              value={formData.dataCertificazione}\n              onChange={(e) => {\n                setFormData(prev => ({ ...prev, dataCertificazione: e.target.value }))\n                if (validationErrors.dataCertificazione) {\n                  setValidationErrors(prev => ({ ...prev, dataCertificazione: '' }))\n                }\n              }}\n              onBlur={validateForm}\n              max={new Date().toISOString().split('T')[0]}\n              className={validationErrors.dataCertificazione ? 'border-red-500 focus:ring-red-500' : ''}\n            />\n            {validationErrors.dataCertificazione && (\n              <p className=\"text-sm text-red-600 flex items-center gap-1\">\n                <AlertCircle className=\"h-3 w-3\" />\n                {validationErrors.dataCertificazione}\n              </p>\n            )}\n          </div>\n\n          {/* Esito Certificazione */}\n          <div className=\"space-y-2\">\n            <Label className=\"text-sm font-medium\">Esito Certificazione</Label>\n            <select\n              value={formData.esitoCertificazione}\n              onChange={(e) => setFormData(prev => ({ ...prev, esitoCertificazione: e.target.value as any }))}\n              className=\"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n            >\n              <option value=\"CONFORME\">CONFORME</option>\n              <option value=\"NON_CONFORME\">NON CONFORME</option>\n              <option value=\"PARZIALMENTE_CONFORME\">PARZIALMENTE CONFORME</option>\n            </select>\n          </div>\n\n          {/* Note (opzionale) */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"note\" className=\"text-sm font-medium\">\n              Note (opzionale)\n            </Label>\n            <textarea\n              id=\"note\"\n              value={formData.note}\n              onChange={(e) => setFormData(prev => ({ ...prev, note: e.target.value }))}\n              placeholder=\"Inserisci eventuali note sulla certificazione...\"\n              rows={3}\n              className=\"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none\"\n            />\n          </div>\n        </div>\n\n        <DialogFooter className=\"gap-2\">\n          <Button\n            variant=\"outline\"\n            onClick={onClose}\n            disabled={isLoading}\n            className=\"hover:bg-gray-50\"\n          >\n            Chiudi\n          </Button>\n\n          <Button\n            variant=\"outline\"\n            onClick={() => {\n              console.log('🚀 Switching to advanced form')\n              onUseAdvanced()\n            }}\n            disabled={isLoading}\n            className=\"hover:bg-blue-50 border-blue-200 text-blue-700\"\n          >\n            <Settings className=\"mr-2 h-4 w-4\" />\n            Form Completo CEI 64-8\n          </Button>\n\n          <Button\n            onClick={handleCertify}\n            disabled={isLoading || !isFormValid}\n            className={`${!isFormValid ? 'opacity-50 cursor-not-allowed bg-gray-400' : 'bg-blue-600 hover:bg-blue-700'}`}\n          >\n            {isLoading ? (\n              <>\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                Certificando...\n              </>\n            ) : (\n              <>\n                <Lock className=\"mr-2 h-4 w-4\" />\n                Certifica Rapido\n              </>\n            )}\n          </Button>\n        </DialogFooter>\n      </EnhancedDialogContent>\n    </Dialog>\n  )\n}\n\n// Bulk Certification Modal\ninterface BulkCertificationModalProps {\n  open: boolean\n  onClose: () => void\n  cavi: Cavo[]\n  onCertify: (caviIds: string[], formData: CertificationData) => Promise<void>\n}\n\nexport const BulkCertificationModal: React.FC<BulkCertificationModalProps> = ({\n  open,\n  onClose,\n  cavi,\n  onCertify\n}) => {\n  const [useAdvancedForm, setUseAdvancedForm] = useState(false)\n  const [isLoading, setIsLoading] = useState(false)\n  const [formData, setFormData] = useState<CertificationData>({\n    responsabile: '',\n    dataCertificazione: new Date().toISOString().split('T')[0],\n    esitoCertificazione: 'CONFORME',\n    note: ''\n  })\n  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})\n\n  // Reset form when modal opens\n  useEffect(() => {\n    if (open) {\n      setFormData({\n        responsabile: '',\n        dataCertificazione: new Date().toISOString().split('T')[0],\n        esitoCertificazione: 'CONFORME',\n        note: ''\n      })\n      setValidationErrors({})\n      setUseAdvancedForm(false)\n    }\n  }, [open])\n\n  const validateForm = () => {\n    const errors: Record<string, string> = {}\n\n    if (!formData.responsabile.trim()) {\n      errors.responsabile = 'Il responsabile è obbligatorio'\n    }\n\n    if (!formData.dataCertificazione) {\n      errors.dataCertificazione = 'La data di certificazione è obbligatoria'\n    } else {\n      const selectedDate = new Date(formData.dataCertificazione)\n      const today = new Date()\n      today.setHours(0, 0, 0, 0)\n\n      if (selectedDate > today) {\n        errors.dataCertificazione = 'La data non può essere futura'\n      }\n    }\n\n    setValidationErrors(errors)\n    return Object.keys(errors).length === 0\n  }\n\n  const handleBulkCertify = async () => {\n    if (!validateForm()) return\n\n    setIsLoading(true)\n    try {\n      const caviIds = cavi.map(c => c.id_cavo)\n      await onCertify(caviIds, formData)\n      onClose()\n    } catch (error) {\n      console.error('Error bulk certifying cables:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Escape') {\n      onClose()\n    }\n  }\n\n  const isFormValid = formData.responsabile.trim() && formData.dataCertificazione && Object.keys(validationErrors).length === 0\n\n  if (cavi.length === 0) return null\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <EnhancedDialogContent\n        className=\"sm:max-w-2xl\"\n        onKeyDown={handleKeyDown}\n        ariaLabelledBy=\"bulk-certification-modal-title\"\n      >\n        <EnhancedModalHeader\n          icon={<Award className=\"h-5 w-5 text-blue-500\" />}\n          title=\"Certificazione Multipla\"\n          cableId={`${cavi.length} cavi selezionati`}\n          description=\"Certifica tutti i cavi selezionati con gli stessi parametri\"\n        />\n\n        <div className=\"space-y-4 py-4\">\n          {/* Lista cavi da certificare */}\n          <div className=\"space-y-3\">\n            <h4 className=\"text-sm font-semibold text-gray-900\">Cavi da certificare:</h4>\n            <div className=\"max-h-32 overflow-y-auto border border-gray-200 rounded-md p-3 bg-gray-50\">\n              <div className=\"grid grid-cols-4 gap-2 text-xs\">\n                {cavi.map((cavo) => (\n                  <div key={cavo.id_cavo} className=\"flex items-center gap-1 p-1 bg-white rounded border\">\n                    <CheckCircle className=\"w-3 h-3 text-green-500\" />\n                    <span className=\"font-medium\">{cavo.id_cavo}</span>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Responsabile Certificazione */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"responsabile\" className=\"text-sm font-medium\">\n              Responsabile Certificazione *\n            </Label>\n            <select\n              id=\"responsabile\"\n              value={formData.responsabile}\n              onChange={(e) => {\n                setFormData(prev => ({ ...prev, responsabile: e.target.value }))\n                if (validationErrors.responsabile) {\n                  setValidationErrors(prev => ({ ...prev, responsabile: '' }))\n                }\n              }}\n              className={`w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${\n                validationErrors.responsabile ? 'border-red-500' : 'border-gray-300'\n              }`}\n            >\n              <option value=\"\">Seleziona responsabile...</option>\n              <option value=\"Tecnico 1\">Tecnico 1</option>\n              <option value=\"Tecnico 2\">Tecnico 2</option>\n              <option value=\"Supervisore\">Supervisore</option>\n              <option value=\"Capo Cantiere\">Capo Cantiere</option>\n            </select>\n            {validationErrors.responsabile && (\n              <p className=\"text-sm text-red-600\">{validationErrors.responsabile}</p>\n            )}\n          </div>\n\n          {/* Data Certificazione */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"dataCertificazione\" className=\"text-sm font-medium\">\n              Data Certificazione *\n            </Label>\n            <Input\n              id=\"dataCertificazione\"\n              type=\"date\"\n              value={formData.dataCertificazione}\n              onChange={(e) => {\n                setFormData(prev => ({ ...prev, dataCertificazione: e.target.value }))\n                if (validationErrors.dataCertificazione) {\n                  setValidationErrors(prev => ({ ...prev, dataCertificazione: '' }))\n                }\n              }}\n              className={validationErrors.dataCertificazione ? 'border-red-500' : ''}\n            />\n            {validationErrors.dataCertificazione && (\n              <p className=\"text-sm text-red-600\">{validationErrors.dataCertificazione}</p>\n            )}\n          </div>\n\n          {/* Esito Certificazione */}\n          <div className=\"space-y-2\">\n            <Label className=\"text-sm font-medium\">Esito Certificazione</Label>\n            <select\n              value={formData.esitoCertificazione}\n              onChange={(e) => setFormData(prev => ({ ...prev, esitoCertificazione: e.target.value as any }))}\n              className=\"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n            >\n              <option value=\"CONFORME\">CONFORME</option>\n              <option value=\"NON_CONFORME\">NON CONFORME</option>\n              <option value=\"PARZIALMENTE_CONFORME\">PARZIALMENTE CONFORME</option>\n            </select>\n          </div>\n\n          {/* Note (opzionale) */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"note\" className=\"text-sm font-medium\">\n              Note (opzionale)\n            </Label>\n            <textarea\n              id=\"note\"\n              value={formData.note}\n              onChange={(e) => setFormData(prev => ({ ...prev, note: e.target.value }))}\n              placeholder=\"Inserisci eventuali note sulla certificazione multipla...\"\n              rows={3}\n              className=\"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none\"\n            />\n          </div>\n        </div>\n\n        <DialogFooter className=\"gap-2\">\n          <Button\n            variant=\"outline\"\n            onClick={onClose}\n            disabled={isLoading}\n            className=\"hover:bg-gray-50\"\n          >\n            Annulla\n          </Button>\n\n          <Button\n            onClick={handleBulkCertify}\n            disabled={isLoading || !isFormValid}\n            className={`${!isFormValid ? 'opacity-50 cursor-not-allowed bg-gray-400' : 'bg-blue-600 hover:bg-blue-700'}`}\n          >\n            {isLoading ? (\n              <>\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                Certificando {cavi.length} cavi...\n              </>\n            ) : (\n              <>\n                <Award className=\"mr-2 h-4 w-4\" />\n                Certifica {cavi.length} Cavi\n              </>\n            )}\n          </Button>\n        </DialogFooter>\n      </EnhancedDialogContent>\n    </Dialog>\n  )\n}\n\n// Success Toast Component (for feedback after actions)\ninterface SuccessToastProps {\n  message: string\n  visible: boolean\n  onClose: () => void\n}\n\nexport const SuccessToast: React.FC<SuccessToastProps> = ({\n  message,\n  visible,\n  onClose\n}) => {\n  useEffect(() => {\n    if (visible) {\n      const timer = setTimeout(() => {\n        onClose()\n      }, 3000)\n      return () => clearTimeout(timer)\n    }\n  }, [visible, onClose])\n\n  if (!visible) return null\n\n  return (\n    <div className=\"fixed top-4 right-4 z-50 animate-in slide-in-from-top-2\">\n      <Alert className=\"bg-green-50 border-green-200 text-green-800 shadow-lg\">\n        <CheckCircle className=\"h-4 w-4\" />\n        <AlertDescription className=\"font-medium\">\n          {message}\n        </AlertDescription>\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          className=\"absolute top-2 right-2 h-6 w-6 p-0 hover:bg-green-100\"\n          onClick={onClose}\n        >\n          <X className=\"h-3 w-3\" />\n        </Button>\n      </Alert>\n    </div>\n  )\n}\n\n// Export all modal types for easy importing\nexport type {\n  DisconnectModalProps,\n  GeneratePdfModalProps,\n  CertificationErrorModalProps,\n  CertificationModalProps,\n  PdfGenerationOptions,\n  CertificationData\n}\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AA/BA;;;;;;;;;;;AAoEA,MAAM,sBAA0D,CAAC,EAC/D,IAAI,EACJ,KAAK,EACL,OAAO,EACP,WAAW,EACZ,iBACC,8OAAC,kIAAA,CAAA,eAAY;;0BACX,8OAAC,kIAAA,CAAA,cAAW;gBAAC,WAAU;;oBACpB;kCACD,8OAAC;wBAAK,WAAU;;4BACb;0CACD,8OAAC;gCAAK,WAAU;0CACb;;;;;;;;;;;;;;;;;;YAIN,6BACC,8OAAC,kIAAA,CAAA,oBAAiB;gBAAC,WAAU;0BAC1B;;;;;;;;;;;;AAeT,MAAM,wBAA8D,CAAC,EACnE,QAAQ,EACR,YAAY,aAAa,EACzB,SAAS,EACT,cAAc,EACd,eAAe,EAChB,iBACC,8OAAC,kIAAA,CAAA,gBAAa;QACZ,WAAW;QACX,WAAW;QACX,mBAAiB;QACjB,oBAAkB;QAClB,sBAAsB,CAAC,IAAM,EAAE,cAAc;QAC7C,iBAAiB,CAAC;YAChB,yBAAyB;YACzB,IAAI,WAAW;gBACb,UAAU;YACZ;QACF;kBAEC;;;;;;AAKE,MAAM,uBAAuD,CAAC,EACnE,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,SAAS,EACV;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnE,MAAM,uBAAuB;QAC3B,yBAAyB;IAC3B;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,MAAM;QAEX,aAAa;QACb,IAAI;YACF,MAAM,UAAU,KAAK,OAAO;YAC5B;YACA,yBAAyB;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,cAAc;QAClB,yBAAyB;QACzB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB;QACF;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC;YACC,WAAU;YACV,WAAW;YACX,gBAAe;YACf,iBAAgB;;8BAEhB,8OAAC;oBACC,oBAAM,8OAAC,gMAAA,CAAA,MAAG;wBAAC,WAAU;;;;;;oBACrB,OAAM;oBACN,SAAS,KAAK,OAAO;oBACrB,aAAY;;;;;;gBAGb,CAAC,sCACA;;sCACE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;;;;;;;sDAEjB,8OAAC;4CAAK,WAAU;;gDAAqC;8DAEnD,8OAAC,kNAAA,CAAA,aAAU;oDACT,WAAU;oDACV,OAAM;;;;;;;;;;;;;;;;;;8CAKZ,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAA4B,WAAU;0DAAsB;;;;;;0DAG3E,8OAAC;gDACC,IAAG;gDACH,WAAU;gDACV,cAAa;;kEAEb,8OAAC;wDAAO,OAAM;wDAAG,QAAQ;kEAAC;;;;;;kEAC1B,8OAAC;wDAAO,OAAM;kEAAW;;;;;;kEACzB,8OAAC;wDAAO,OAAM;kEAAW;;;;;;kEACzB,8OAAC;wDAAO,OAAM;kEAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMjC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,WAAU;;8CACf,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC,iIAAA,CAAA,mBAAgB;oCAAC,WAAU;;sDAC1B,8OAAC;sDAAO;;;;;;wCAAoB;;;;;;;;;;;;;sCAKhC,8OAAC,kIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;oCACV,WAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;oCACV,WAAU;;sDAEV,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;iDAMhD;;sCACE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;8CAE3B,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAGzD,8OAAC;oCAAE,WAAU;;wCAA6B;sDACuB,8OAAC;sDAAQ,KAAK,OAAO;;;;;;wCAAU;;;;;;;8CAEhG,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;;;;;;;sCAMpD,8OAAC,kIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,yBAAyB;oCACxC,UAAU;oCACV,WAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,0BACC;;0DACE,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAA8B;;uDAInD;;;;;;;;;;;;;;;;;;;;;;;;;AASlB;AAGO,MAAM,mBAAoD,CAAC,EAChE,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,UAAU,EACX;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;QAC3D,UAAU;QACV,iBAAiB;QACjB,QAAQ;QACR,gBAAgB;IAClB;IACA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAElF,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,MAAM;YAChB,WAAW,CAAA,OAAQ,CAAC;oBAClB,GAAG,IAAI;oBACP,UAAU,CAAC,YAAY,EAAE,KAAK,OAAO,CAAC,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;gBACvF,CAAC;YACD,oBAAoB,CAAC;QACvB;IACF,GAAG;QAAC;QAAM;KAAK;IAEf,uBAAuB;IACvB,MAAM,eAAe;QACnB,MAAM,SAAiC,CAAC;QAExC,IAAI,CAAC,QAAQ,QAAQ,CAAC,IAAI,IAAI;YAC5B,OAAO,QAAQ,GAAG;QACpB,OAAO,IAAI,CAAC,4BAA4B,IAAI,CAAC,QAAQ,QAAQ,GAAG;YAC9D,OAAO,QAAQ,GAAG;QACpB;QAEA,IAAI,QAAQ,cAAc,IAAI,CAAC,6BAA6B,IAAI,CAAC,QAAQ,cAAc,GAAG;YACxF,OAAO,cAAc,GAAG;QAC1B;QAEA,oBAAoB;QACpB,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;IACxC;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,QAAQ,CAAC,gBAAgB;QAE9B,aAAa;QACb,IAAI;YACF,MAAM,WAAW,KAAK,OAAO,EAAE;YAC/B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB;QACF;IACF;IAEA,MAAM,cAAc,QAAQ,QAAQ,CAAC,IAAI,MAAM,OAAO,IAAI,CAAC,kBAAkB,MAAM,KAAK;IAExF,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC;YACC,WAAU;YACV,WAAW;YACX,gBAAe;;8BAEf,8OAAC;oBACC,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBAC1B,OAAM;oBACN,SAAS,KAAK,OAAO;oBACrB,aAAY;;;;;;8BAGd,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAW,WAAU;8CAAsB;;;;;;8CAG1D,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO,QAAQ,QAAQ;oCACvB,UAAU,CAAC;wCACT,WAAW,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;wCACzD,IAAI,iBAAiB,QAAQ,EAAE;4CAC7B,oBAAoB,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,UAAU;gDAAG,CAAC;wCACxD;oCACF;oCACA,QAAQ;oCACR,aAAY;oCACZ,WAAW,iBAAiB,QAAQ,GAAG,sCAAsC;;;;;;gCAE9E,iBAAiB,QAAQ,kBACxB,8OAAC;oCAAE,WAAU;;sDACX,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCACtB,iBAAiB,QAAQ;;;;;;;;;;;;;sCAMhC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAM;oDACN,SAAS,QAAQ,MAAM,KAAK;oDAC5B,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;4DAA4B,CAAC;oDACnG,WAAU;;;;;;8DAEZ,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAAsB;;;;;;sEACtC,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAGzC,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAM;oDACN,SAAS,QAAQ,MAAM,KAAK;oDAC5B,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;4DAA4B,CAAC;oDACnG,WAAU;;;;;;8DAEZ,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAAsB;;;;;;sEACtC,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO7C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,SAAS,QAAQ,eAAe;oCAChC,iBAAiB,CAAC,UAAY,WAAW,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,iBAAiB;4CAAmB,CAAC;;;;;;8CAEpG,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAkB,WAAU;sDAAqC;;;;;;sDAGhF,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAKzC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAiB,WAAU;8CAAsB;;;;;;8CAGhE,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,OAAO,QAAQ,cAAc;oCAC7B,UAAU,CAAC;wCACT,WAAW,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;wCAC/D,IAAI,iBAAiB,cAAc,EAAE;4CACnC,oBAAoB,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,gBAAgB;gDAAG,CAAC;wCAC9D;oCACF;oCACA,QAAQ;oCACR,aAAY;oCACZ,WAAW,iBAAiB,cAAc,GAAG,sCAAsC;;;;;;gCAEpF,iBAAiB,cAAc,kBAC9B,8OAAC;oCAAE,WAAU;;sDACX,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCACtB,iBAAiB,cAAc;;;;;;;;;;;;;;;;;;;8BAMxC,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU;4BACV,WAAU;sCACX;;;;;;sCAGD,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,aAAa,CAAC;4BACxB,WAAW,CAAC,OAAO,EAAE,CAAC,cAAc,kCAAkC,qBAAqB;sCAE1F,0BACC;;kDACE,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA8B;;6DAInD;;kDACE,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AASrD;AAGO,MAAM,0BAAkE,CAAC,EAC9E,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,YAAY,EACZ,sBAAsB,EAAE,EACzB;IACC,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB;QACF;IACF;IAEA,MAAM,sBAAsB;QAC1B;QACA;QACA;KACD;IAED,MAAM,eAAe,oBAAoB,MAAM,GAAG,IAAI,sBAAsB;IAE5E,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC;YACC,WAAU;YACV,WAAW;YACX,gBAAe;;8BAEf,8OAAC;oBACC,oBAAM,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;oBAC7B,OAAM;oBACN,SAAS,KAAK,OAAO;oBACrB,aAAY;;;;;;8BAGd,8OAAC;oBAAI,WAAU;;wBACZ,8BACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,WAAU;;8CACf,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;oCAAC,WAAU;8CAAgB;;;;;;;;;;;;sCAIhD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;gDAA2B;;;;;;;sDAGtD,8OAAC;4CAAG,WAAU;sDACX,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC;oDAAe,WAAU;;sEACxB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,4LAAA,CAAA,IAAC;gEAAC,WAAU;;;;;;;;;;;sEAEf,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;;mDAJjC;;;;;;;;;;;;;;;;8CAUf,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;;sDACf,8OAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC,iIAAA,CAAA,mBAAgB;4CAAC,WAAU;;8DAC1B,8OAAC;8DAAO;;;;;;gDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;8BAMxC,8OAAC,kIAAA,CAAA,eAAY;8BACX,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,WAAU;;0CAEV,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;;;;;;AAOpD;AAeO,MAAM,qBAAwD,CAAC,EACpE,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,aAAa,CAAC,EACd,SAAS,EACV;IACC,QAAQ,GAAG,CAAC,iCAAiC;QAAE;QAAM,MAAM,MAAM;IAAQ;IAEzE,IAAI,CAAC,MAAM,OAAO;IAElB,MAAM,oBAAoB,OAAO;QAC/B,6DAA6D;QAC7D,IAAI,sBAAsB,MAAM;YAC9B,IAAI;gBACF,MAAM,UAAU,KAAK,OAAO,EAAE;YAChC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,QAAO,yCAAyC;YAClD;QACF;QACA;IACF;IAEA,MAAM,mBAAmB;QACvB;IACF;IAEA,4CAA4C;IAC5C,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,cAAW;oBAAC,WAAU;;wBAAU;wBAAqB,KAAK,OAAO;;;;;;;8BAClE,8OAAC,0JAAA,CAAA,UAAkB;oBACjB,YAAY;oBACZ,gBAAgB;oBAChB,WAAW,EAAE;oBACb,mBAAmB,KAAK,OAAO;oBAC/B,WAAW;oBACX,UAAU;;;;;;;;;;;;;;;;;AAKpB;AAOA,MAAM,2BAAoE,CAAC,EACzE,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,SAAS,EACT,aAAa,EACd;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;QAC1D,cAAc;QACd,oBAAoB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC1D,qBAAqB;QACrB,MAAM;IACR;IACA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAElF,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,MAAM;YAChB,QAAQ,GAAG,CAAC,gDAAgD,KAAK,OAAO;YACxE,YAAY;gBACV,cAAc;gBACd,oBAAoB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC1D,qBAAqB;gBACrB,MAAM;YACR;YACA,oBAAoB,CAAC;QACvB;IACF,GAAG;QAAC;QAAM;KAAK;IAEf,MAAM,eAAe;QACnB,MAAM,SAAiC,CAAC;QAExC,IAAI,CAAC,SAAS,YAAY,CAAC,IAAI,IAAI;YACjC,OAAO,YAAY,GAAG;QACxB;QAEA,IAAI,CAAC,SAAS,kBAAkB,EAAE;YAChC,OAAO,kBAAkB,GAAG;QAC9B,OAAO;YACL,MAAM,eAAe,IAAI,KAAK,SAAS,kBAAkB;YACzD,MAAM,QAAQ,IAAI;YAClB,MAAM,QAAQ,CAAC,GAAG,GAAG,GAAG;YAExB,IAAI,eAAe,OAAO;gBACxB,OAAO,kBAAkB,GAAG;YAC9B;QACF;QAEA,oBAAoB;QACpB,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;IACxC;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,QAAQ,CAAC,gBAAgB;QAE9B,aAAa;QACb,IAAI;YACF,MAAM,UAAU,KAAK,OAAO,EAAE;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB;QACF;IACF;IAEA,MAAM,cAAc,SAAS,YAAY,CAAC,IAAI,MAAM,SAAS,kBAAkB,IAAI,OAAO,IAAI,CAAC,kBAAkB,MAAM,KAAK;IAE5H,sCAAsC;IACtC,MAAM,cAAc;QAClB,YAAY;QACZ,WAAW;QACX,aAAa;IACf;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC;YACC,WAAU;YACV,WAAW;YACX,gBAAe;;8BAEf,8OAAC;oBACC,oBAAM,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBACtB,OAAM;oBACN,SAAS,KAAK,OAAO;oBACrB,aAAY;;;;;;8BAGd,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAW,CAAC,qBAAqB,EAAE,YAAY,UAAU,GAAG,iBAAiB,cAAc;;;;;;8DAChG,8OAAC;oDAAK,WAAU;;wDACb,YAAY,UAAU,GAAG,MAAM;wDAAI;;;;;;;;;;;;;sDAGxC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAW,CAAC,qBAAqB,EAAE,YAAY,SAAS,GAAG,iBAAiB,cAAc;;;;;;8DAC/F,8OAAC;oDAAK,WAAU;;wDACb,YAAY,SAAS,GAAG,MAAM;wDAAI;;;;;;;;;;;;;sDAGvC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAW,CAAC,qBAAqB,EAAE,YAAY,WAAW,GAAG,iBAAiB,iBAAiB;;;;;;8DACpG,8OAAC;oDAAK,WAAU;;wDACb,YAAY,WAAW,GAAG,MAAM;wDAAI;;;;;;;;;;;;;;;;;;;;;;;;;sCAO7C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAe,WAAU;8CAAsB;;;;;;8CAG9D,8OAAC;oCACC,IAAG;oCACH,OAAO,SAAS,YAAY;oCAC5B,UAAU,CAAC;wCACT,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,cAAc,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;wCAC9D,IAAI,iBAAiB,YAAY,EAAE;4CACjC,oBAAoB,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,cAAc;gDAAG,CAAC;wCAC5D;oCACF;oCACA,QAAQ;oCACR,WAAW,CAAC,oFAAoF,EAC9F,iBAAiB,YAAY,GAAG,mBAAmB,mBACnD;;sDAEF,8OAAC;4CAAO,OAAM;4CAAG,QAAQ;sDAAC;;;;;;sDAC1B,8OAAC;4CAAO,OAAM;sDAAW;;;;;;sDACzB,8OAAC;4CAAO,OAAM;sDAAY;;;;;;sDAC1B,8OAAC;4CAAO,OAAM;sDAAY;;;;;;sDAC1B,8OAAC;4CAAO,OAAM;sDAAc;;;;;;;;;;;;gCAE7B,iBAAiB,YAAY,kBAC5B,8OAAC;oCAAE,WAAU;;sDACX,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCACtB,iBAAiB,YAAY;;;;;;;;;;;;;sCAMpC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAqB,WAAU;8CAAsB;;;;;;8CAGpE,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,kBAAkB;oCAClC,UAAU,CAAC;wCACT,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,oBAAoB,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;wCACpE,IAAI,iBAAiB,kBAAkB,EAAE;4CACvC,oBAAoB,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,oBAAoB;gDAAG,CAAC;wCAClE;oCACF;oCACA,QAAQ;oCACR,KAAK,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oCAC3C,WAAW,iBAAiB,kBAAkB,GAAG,sCAAsC;;;;;;gCAExF,iBAAiB,kBAAkB,kBAClC,8OAAC;oCAAE,WAAU;;sDACX,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCACtB,iBAAiB,kBAAkB;;;;;;;;;;;;;sCAM1C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,8OAAC;oCACC,OAAO,SAAS,mBAAmB;oCACnC,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,qBAAqB,EAAE,MAAM,CAAC,KAAK;4CAAQ,CAAC;oCAC7F,WAAU;;sDAEV,8OAAC;4CAAO,OAAM;sDAAW;;;;;;sDACzB,8OAAC;4CAAO,OAAM;sDAAe;;;;;;sDAC7B,8OAAC;4CAAO,OAAM;sDAAwB;;;;;;;;;;;;;;;;;;sCAK1C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAO,WAAU;8CAAsB;;;;;;8CAGtD,8OAAC;oCACC,IAAG;oCACH,OAAO,SAAS,IAAI;oCACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACvE,aAAY;oCACZ,MAAM;oCACN,WAAU;;;;;;;;;;;;;;;;;;8BAKhB,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU;4BACV,WAAU;sCACX;;;;;;sCAID,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;gCACP,QAAQ,GAAG,CAAC;gCACZ;4BACF;4BACA,UAAU;4BACV,WAAU;;8CAEV,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAIvC,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,aAAa,CAAC;4BACxB,WAAW,GAAG,CAAC,cAAc,8CAA8C,iCAAiC;sCAE3G,0BACC;;kDACE,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA8B;;6DAInD;;kDACE,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AASjD;AAUO,MAAM,yBAAgE,CAAC,EAC5E,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,SAAS,EACV;IACC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;QAC1D,cAAc;QACd,oBAAoB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC1D,qBAAqB;QACrB,MAAM;IACR;IACA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAElF,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,YAAY;gBACV,cAAc;gBACd,oBAAoB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC1D,qBAAqB;gBACrB,MAAM;YACR;YACA,oBAAoB,CAAC;YACrB,mBAAmB;QACrB;IACF,GAAG;QAAC;KAAK;IAET,MAAM,eAAe;QACnB,MAAM,SAAiC,CAAC;QAExC,IAAI,CAAC,SAAS,YAAY,CAAC,IAAI,IAAI;YACjC,OAAO,YAAY,GAAG;QACxB;QAEA,IAAI,CAAC,SAAS,kBAAkB,EAAE;YAChC,OAAO,kBAAkB,GAAG;QAC9B,OAAO;YACL,MAAM,eAAe,IAAI,KAAK,SAAS,kBAAkB;YACzD,MAAM,QAAQ,IAAI;YAClB,MAAM,QAAQ,CAAC,GAAG,GAAG,GAAG;YAExB,IAAI,eAAe,OAAO;gBACxB,OAAO,kBAAkB,GAAG;YAC9B;QACF;QAEA,oBAAoB;QACpB,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;IACxC;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,gBAAgB;QAErB,aAAa;QACb,IAAI;YACF,MAAM,UAAU,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO;YACvC,MAAM,UAAU,SAAS;YACzB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB;QACF;IACF;IAEA,MAAM,cAAc,SAAS,YAAY,CAAC,IAAI,MAAM,SAAS,kBAAkB,IAAI,OAAO,IAAI,CAAC,kBAAkB,MAAM,KAAK;IAE5H,IAAI,KAAK,MAAM,KAAK,GAAG,OAAO;IAE9B,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC;YACC,WAAU;YACV,WAAW;YACX,gBAAe;;8BAEf,8OAAC;oBACC,oBAAM,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;oBACvB,OAAM;oBACN,SAAS,GAAG,KAAK,MAAM,CAAC,iBAAiB,CAAC;oBAC1C,aAAY;;;;;;8BAGd,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACZ,KAAK,GAAG,CAAC,CAAC,qBACT,8OAAC;gDAAuB,WAAU;;kEAChC,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,8OAAC;wDAAK,WAAU;kEAAe,KAAK,OAAO;;;;;;;+CAFnC,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;sCAU9B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAe,WAAU;8CAAsB;;;;;;8CAG9D,8OAAC;oCACC,IAAG;oCACH,OAAO,SAAS,YAAY;oCAC5B,UAAU,CAAC;wCACT,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,cAAc,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;wCAC9D,IAAI,iBAAiB,YAAY,EAAE;4CACjC,oBAAoB,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,cAAc;gDAAG,CAAC;wCAC5D;oCACF;oCACA,WAAW,CAAC,oFAAoF,EAC9F,iBAAiB,YAAY,GAAG,mBAAmB,mBACnD;;sDAEF,8OAAC;4CAAO,OAAM;sDAAG;;;;;;sDACjB,8OAAC;4CAAO,OAAM;sDAAY;;;;;;sDAC1B,8OAAC;4CAAO,OAAM;sDAAY;;;;;;sDAC1B,8OAAC;4CAAO,OAAM;sDAAc;;;;;;sDAC5B,8OAAC;4CAAO,OAAM;sDAAgB;;;;;;;;;;;;gCAE/B,iBAAiB,YAAY,kBAC5B,8OAAC;oCAAE,WAAU;8CAAwB,iBAAiB,YAAY;;;;;;;;;;;;sCAKtE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAqB,WAAU;8CAAsB;;;;;;8CAGpE,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,kBAAkB;oCAClC,UAAU,CAAC;wCACT,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,oBAAoB,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;wCACpE,IAAI,iBAAiB,kBAAkB,EAAE;4CACvC,oBAAoB,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,oBAAoB;gDAAG,CAAC;wCAClE;oCACF;oCACA,WAAW,iBAAiB,kBAAkB,GAAG,mBAAmB;;;;;;gCAErE,iBAAiB,kBAAkB,kBAClC,8OAAC;oCAAE,WAAU;8CAAwB,iBAAiB,kBAAkB;;;;;;;;;;;;sCAK5E,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,8OAAC;oCACC,OAAO,SAAS,mBAAmB;oCACnC,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,qBAAqB,EAAE,MAAM,CAAC,KAAK;4CAAQ,CAAC;oCAC7F,WAAU;;sDAEV,8OAAC;4CAAO,OAAM;sDAAW;;;;;;sDACzB,8OAAC;4CAAO,OAAM;sDAAe;;;;;;sDAC7B,8OAAC;4CAAO,OAAM;sDAAwB;;;;;;;;;;;;;;;;;;sCAK1C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAO,WAAU;8CAAsB;;;;;;8CAGtD,8OAAC;oCACC,IAAG;oCACH,OAAO,SAAS,IAAI;oCACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACvE,aAAY;oCACZ,MAAM;oCACN,WAAU;;;;;;;;;;;;;;;;;;8BAKhB,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU;4BACV,WAAU;sCACX;;;;;;sCAID,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,aAAa,CAAC;4BACxB,WAAW,GAAG,CAAC,cAAc,8CAA8C,iCAAiC;sCAE3G,0BACC;;kDACE,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA8B;oCACnC,KAAK,MAAM;oCAAC;;6DAG5B;;kDACE,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAiB;oCACvB,KAAK,MAAM;oCAAC;;;;;;;;;;;;;;;;;;;;;;;;;AAQvC;AASO,MAAM,eAA4C,CAAC,EACxD,OAAO,EACP,OAAO,EACP,OAAO,EACR;IACC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS;YACX,MAAM,QAAQ,WAAW;gBACvB;YACF,GAAG;YACH,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;QAAS;KAAQ;IAErB,IAAI,CAAC,SAAS,OAAO;IAErB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;YAAC,WAAU;;8BACf,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,8OAAC,iIAAA,CAAA,mBAAgB;oBAAC,WAAU;8BACzB;;;;;;8BAEH,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,SAAS;8BAET,cAAA,8OAAC,4LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKvB", "debugId": null}}, {"offset": {"line": 2252, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/modals/BobinaManagementModals.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect, useMemo } from 'react'\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'\nimport {\n  Search,\n  CheckCircle,\n  AlertTriangle,\n  Ruler,\n  Package,\n  X,\n  Loader2,\n  HelpCircle,\n  Settings\n} from 'lucide-react'\nimport { Cavo } from '@/types'\nimport { parcoCaviApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useCantiere } from '@/hooks/useCantiere'\n\n// Enhanced Modal Components (imported from CableActionModals)\ninterface EnhancedModalHeaderProps {\n  icon: React.ReactNode\n  title: string\n  cableId: string\n  description?: string\n}\n\nconst EnhancedModalHeader: React.FC<EnhancedModalHeaderProps> = ({\n  icon,\n  title,\n  cableId,\n  description\n}) => (\n  <DialogHeader>\n    <DialogTitle className=\"flex items-center gap-2\">\n      {icon}\n      <span>{title}</span>\n    </DialogTitle>\n    {description && (\n      <DialogDescription className=\"text-sm text-muted-foreground\">\n        {description}\n      </DialogDescription>\n    )}\n  </DialogHeader>\n)\n\ninterface EnhancedDialogContentProps {\n  children: React.ReactNode\n  className?: string\n  onKeyDown?: (e: React.KeyboardEvent) => void\n  ariaLabelledBy?: string\n  ariaDescribedBy?: string\n}\n\nconst EnhancedDialogContent: React.FC<EnhancedDialogContentProps> = ({\n  children,\n  className = \"sm:max-w-md\",\n  onKeyDown,\n  ariaLabelledBy,\n  ariaDescribedBy\n}) => (\n  <DialogContent \n    className={className}\n    onKeyDown={onKeyDown}\n    aria-labelledby={ariaLabelledBy}\n    aria-describedby={ariaDescribedBy}\n    onPointerDownOutside={(e) => e.preventDefault()}\n    onEscapeKeyDown={(e) => {\n      if (onKeyDown) {\n        onKeyDown(e as any)\n      }\n    }}\n  >\n    {children}\n  </DialogContent>\n)\n\n// Types\ninterface Bobina {\n  id_bobina: string\n  numero_bobina: string\n  tipologia: string\n  n_conduttori: string\n  sezione: string\n  metri_totali: number\n  metri_residui: number\n  stato_bobina: string\n  fornitore?: string\n  compatible?: boolean\n}\n\ninterface ModificaBobinaModalProps {\n  open: boolean\n  onClose: () => void\n  cavo: Cavo | null\n  cantiere?: { id_cantiere: string | number; commessa: string } | null\n  onSave: (cavoId: string, bobinaId: string, option: string) => Promise<void>\n}\n\ninterface InserisciMetriModalProps {\n  open: boolean\n  onClose: () => void\n  cavo: Cavo | null\n  onSave: (cavoId: string, metriPosati: number) => Promise<void>\n}\n\n// Unified Modal Props\ninterface UnifiedCableBobbinModalProps {\n  mode: 'aggiungi_metri' | 'modifica_bobina'\n  open: boolean\n  onClose: () => void\n  cavo: Cavo | null\n  cantiere?: { id_cantiere: string | number; commessa: string } | null\n  onSave: (data: any) => Promise<void>\n}\n\n// Edit options for modifica_bobina mode\ntype EditOption = 'cambia_bobina' | 'bobina_vuota' | 'annulla_posa'\n\n// Cable Info Card Component\ninterface CableInfoCardProps {\n  cavo: Cavo\n}\n\nconst CableInfoCard: React.FC<CableInfoCardProps> = ({ cavo }) => (\n  <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-5 mb-5\">\n    {/* Titolo della sezione migliorato - rimosso codice cavo duplicato */}\n    <div className=\"flex items-center gap-3 mb-4\">\n      <Package className=\"h-5 w-5 text-blue-600\" />\n      <h3 className=\"font-semibold text-blue-800 text-base\">Informazioni Cavo</h3>\n    </div>\n\n    {/* Griglia 2x2 con gerarchia visiva migliorata - Opzione A */}\n    <div className=\"grid grid-cols-2 gap-x-8 gap-y-4\">\n      <div className=\"info-item\">\n        <span className=\"block text-xs text-gray-500 font-medium uppercase mb-1\">Tipologia</span>\n        <span className=\"text-gray-900 font-bold text-sm\">{cavo.tipologia || 'N/A'}</span>\n      </div>\n\n      <div className=\"info-item\">\n        <span className=\"block text-xs text-gray-500 font-medium uppercase mb-1\">Formazione</span>\n        <span className=\"text-gray-900 font-bold text-sm\">{cavo.sezione || 'N/A'}</span>\n      </div>\n\n      <div className=\"info-item\">\n        <span className=\"block text-xs text-gray-500 font-medium uppercase mb-1\">Da</span>\n        <span className=\"text-gray-900 font-bold text-sm\">{cavo.ubicazione_partenza || 'N/A'}</span>\n      </div>\n\n      <div className=\"info-item\">\n        <span className=\"block text-xs text-gray-500 font-medium uppercase mb-1\">A</span>\n        <span className=\"text-gray-900 font-bold text-sm\">{cavo.ubicazione_arrivo || 'N/A'}</span>\n      </div>\n    </div>\n  </div>\n)\n\n// ==================== UNIFIED CABLE BOBBIN MODAL ====================\n\nexport const UnifiedCableBobbinModal: React.FC<UnifiedCableBobbinModalProps> = ({\n  mode,\n  open,\n  onClose,\n  cavo,\n  cantiere,\n  onSave\n}) => {\n  // Stati comuni\n  const [isLoading, setIsLoading] = useState(false)\n  const [error, setError] = useState('')\n  const [bobine, setBobine] = useState<Bobina[]>([])\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedBobina, setSelectedBobina] = useState<string>('')\n\n  // Stati specifici per modalità\n  const [metersInput, setMetersInput] = useState<string>('')\n  const [selectedEditOption, setSelectedEditOption] = useState<EditOption | null>(null)\n  const [activeTab, setActiveTab] = useState<'compatibili' | 'incompatibili'>('compatibili')\n\n  const { user } = useAuth()\n  const { cantiereId: cantiereIdFromHook, cantiere: cantiereFromHook, isValidCantiere: isValidCantiereFromHook } = useCantiere()\n\n  // Usa il cantiere passato come prop o quello dal sistema universale come fallback (come InserisciMetriDialog)\n  const cantiereToUse = cantiere || cantiereFromHook\n  const cantiereId = cantiereToUse?.id_cantiere || cantiereIdFromHook\n  const isValidCantiere = cantiereToUse ? true : isValidCantiereFromHook\n\n  // Logica per determinare titolo e descrizione dinamici\n  const modalConfig = useMemo(() => {\n    if (mode === 'aggiungi_metri') {\n      // Titolo corretto: \"Inserisci Metri Posati: C001\"\n      const title = `Inserisci Metri Posati: ${cavo?.id_cavo || 'N/A'}`\n\n      return {\n        title: title,\n        description: 'Inserisci i metri effettivamente posati per il cavo e seleziona una bobina o usa BOBINA VUOTA',\n        icon: <Ruler className=\"h-5 w-5 text-green-500\" />,\n        primaryButtonText: 'Salva'\n      }\n    } else {\n      const bobinaNumber = cavo?.id_bobina ?\n        cavo.id_bobina.split('_B')[1] || cavo.id_bobina.split('_b')[1] || cavo.id_bobina : ''\n\n      // Titolo corretto: \"Modifica Bobina Cavo: C001 / Bobina: 2\"\n      let title = `Modifica Bobina Cavo: ${cavo?.id_cavo || 'N/A'}`\n\n      if (bobinaNumber) {\n        title += ` / Bobina: ${bobinaNumber}`\n      }\n\n      return {\n        title: title,\n        description: 'Seleziona una nuova bobina per il cavo o modifica i parametri',\n        icon: <Settings className=\"h-5 w-5 text-blue-500\" />,\n        primaryButtonText: 'Salva Modifiche'\n      }\n    }\n  }, [mode, cavo?.id_cavo, cavo?.id_bobina]) // Aggiunte dipendenze specifiche\n\n  // Determina se la sezione selezione bobina è abilitata\n  const isBobbinSelectionEnabled = useMemo(() => {\n    if (mode === 'aggiungi_metri') return true\n    if (mode === 'modifica_bobina') return true // SEMPRE visibile in modifica bobina\n    return false\n  }, [mode])\n\n  // Reset stati quando si apre/chiude la modale\n  useEffect(() => {\n    if (open) {\n      setError('')\n      setSearchTerm('')\n      setSelectedBobina('')\n      setSelectedEditOption(null)\n\n      if (mode === 'aggiungi_metri') {\n        setMetersInput('')\n      } else if (mode === 'modifica_bobina' && cavo) {\n        setMetersInput(String(cavo.metratura_reale || cavo.metri_posati || 0))\n      }\n    }\n  }, [open, mode, cavo])\n\n  // Carica bobine dall'API usando il sistema universale\n  const loadBobine = async () => {\n    if (!cavo || !cantiereId || !isValidCantiere) {\n      console.log('🔍 UnifiedModal: Caricamento bobine saltato - mancano dati:', {\n        cavo: !!cavo,\n        cantiereId,\n        isValidCantiere,\n        cantiereToUse: !!cantiereToUse\n      })\n      return\n    }\n\n    console.log('🔄 UnifiedModal: Caricamento bobine per cantiere:', cantiereId)\n    console.log('🔐 UnifiedModal: Debug autenticazione:', {\n      user: !!user,\n      token: !!localStorage.getItem('token'),\n      tokenLength: localStorage.getItem('token')?.length || 0\n    })\n    setIsLoading(true)\n\n    try {\n      const response = await parcoCaviApi.getBobine(cantiereId)\n\n      // Gestisce diversi formati di risposta (come InserisciMetriDialog)\n      let bobineData = []\n      if (Array.isArray(response)) {\n        bobineData = response\n      } else if (response && Array.isArray(response.data)) {\n        bobineData = response.data\n      } else if (response && response.bobine && Array.isArray(response.bobine)) {\n        bobineData = response.bobine\n      } else {\n        throw new Error('Formato risposta API non valido')\n      }\n\n      console.log('📦 UnifiedModal: Bobine ricevute:', bobineData.length)\n      console.log('📋 UnifiedModal: Dettaglio bobine:', bobineData.map(b => ({\n        id: b.id_bobina,\n        tipologia: b.tipologia,\n        sezione: b.sezione,\n        metri_residui: b.metri_residui,\n        stato: b.stato_bobina\n      })))\n\n      // Filtra bobine utilizzabili (stessa logica delle modali esistenti)\n      const bobineUtilizzabili = bobineData.filter((bobina: any) =>\n        bobina.stato_bobina !== 'Terminata' &&\n        bobina.stato_bobina !== 'Over' &&\n        bobina.metri_residui > 0\n      )\n\n      console.log('✅ UnifiedModal: Bobine utilizzabili:', bobineUtilizzabili.length)\n\n      // Determina compatibilità\n      const bobineWithCompatibility = bobineUtilizzabili.map((bobina: any) => ({\n        ...bobina,\n        compatible: bobina.tipologia === cavo.tipologia && bobina.sezione === cavo.sezione\n      }))\n\n      const compatibili = bobineWithCompatibility.filter(b => b.compatible).length\n      const incompatibili = bobineWithCompatibility.filter(b => !b.compatible).length\n\n      console.log('🎯 UnifiedModal: Compatibilità bobine:', { compatibili, incompatibili })\n\n      setBobine(bobineWithCompatibility)\n      setError('') // Pulisci eventuali errori precedenti\n    } catch (error) {\n      console.error('❌ UnifiedModal: Errore caricamento bobine:', error)\n      setError('Errore durante il caricamento delle bobine. Riprova.')\n      setBobine([])\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  // Carica bobine quando si apre la modale o cambia il cantiere\n  useEffect(() => {\n    if (open && cavo && cantiereId && isValidCantiere) {\n      loadBobine()\n    }\n  }, [open, cavo, cantiereId, isValidCantiere])\n\n  // Filtra bobine in base alla ricerca\n  const filteredBobine = useMemo(() => {\n    if (!searchTerm) return bobine\n\n    const searchLower = searchTerm.toLowerCase()\n    return bobine.filter(bobina => {\n      const idBobina = bobina.id_bobina?.toLowerCase() || ''\n      const tipologia = bobina.tipologia?.toLowerCase() || ''\n      const numeroBobina = bobina.numero_bobina?.toLowerCase() || ''\n\n      return idBobina.includes(searchLower) ||\n             tipologia.includes(searchLower) ||\n             numeroBobina.includes(searchLower)\n    })\n  }, [bobine, searchTerm])\n\n  // Separa bobine compatibili e incompatibili\n  const compatibleBobine = filteredBobine.filter(b => b.compatible)\n  const incompatibleBobine = filteredBobine.filter(b => !b.compatible)\n\n  // Validazione input metri con messaggi migliorati\n  const validateMeters = (value: string): string => {\n    if (!value.trim()) return 'Il campo metri è obbligatorio'\n    const meters = parseFloat(value)\n    if (isNaN(meters)) return 'Inserire un valore numerico valido'\n    if (meters < 0) return 'I metri non possono essere negativi'\n    if (meters === 0) return 'I metri devono essere maggiori di zero'\n    if (meters > 10000) return 'Valore troppo elevato (massimo 10.000m)'\n    return ''\n  }\n\n  // Gestione errori con toast notifications\n  const showError = (message: string) => {\n    setError(message)\n    // Auto-clear error after 5 seconds\n    setTimeout(() => setError(''), 5000)\n  }\n\n  // Gestione salvataggio\n  const handleSave = async () => {\n    if (!cavo) return\n\n    setError('')\n    setIsLoading(true)\n\n    try {\n      let payload: any = {}\n\n      if (mode === 'aggiungi_metri') {\n        // Validazione per aggiungi metri\n        const metersError = validateMeters(metersInput)\n        if (metersError) {\n          showError(metersError)\n          setIsLoading(false)\n          return\n        }\n\n        payload = {\n          mode: 'aggiungi_metri',\n          cableId: cavo.id_cavo,\n          metersToInstall: parseFloat(metersInput),\n          bobbinId: selectedBobina || 'BOBINA_VUOTA'\n        }\n      } else if (mode === 'modifica_bobina') {\n        // Validazione per modifica bobina\n        if (!selectedEditOption) {\n          showError('Selezionare un\\'opzione di modifica')\n          setIsLoading(false)\n          return\n        }\n\n        if (selectedEditOption === 'cambia_bobina' && !selectedBobina) {\n          showError('Selezionare una bobina per continuare')\n          setIsLoading(false)\n          return\n        }\n\n        payload = {\n          mode: 'modifica_bobina',\n          cableId: cavo.id_cavo,\n          editOption: selectedEditOption,\n          newBobbinId: selectedBobina || null\n        }\n      }\n\n      await onSave(payload)\n      onClose()\n    } catch (error: any) {\n      console.error('Errore salvataggio unified modal:', error)\n      const errorMessage = error.response?.data?.message ||\n                          error.message ||\n                          'Errore durante il salvataggio. Riprovare.'\n      showError(errorMessage)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  // Gestione chiusura\n  const handleClose = () => {\n    if (!isLoading) {\n      onClose()\n    }\n  }\n\n  // Gestione tasti\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Escape') {\n      handleClose()\n    }\n  }\n\n  // Determina se il form è valido\n  const isFormValid = useMemo(() => {\n    if (mode === 'aggiungi_metri') {\n      return metersInput.trim() && !validateMeters(metersInput)\n    } else if (mode === 'modifica_bobina') {\n      if (!selectedEditOption) return false\n\n      // Validazione metri per tutte le opzioni tranne annulla_posa\n      if (selectedEditOption !== 'annulla_posa') {\n        if (!metersInput.trim() || validateMeters(metersInput)) return false\n      }\n\n      // Validazione specifica per cambio bobina\n      if (selectedEditOption === 'cambia_bobina' && !selectedBobina) return false\n\n      return true\n    }\n    return false\n  }, [mode, metersInput, selectedEditOption, selectedBobina])\n\n  // Controllo di sicurezza: non renderizzare se non c'è un cavo valido\n  if (!cavo || !cavo.id_cavo) {\n    console.warn('⚠️ UnifiedCableBobbinModal: Tentativo di apertura senza cavo valido:', cavo)\n    return null\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <EnhancedDialogContent\n        className=\"sm:max-w-3xl max-h-[85vh] overflow-hidden\"\n        onKeyDown={handleKeyDown}\n        ariaLabelledBy=\"unified-modal-title\"\n      >\n        <EnhancedModalHeader\n          icon={modalConfig.icon}\n          title={modalConfig.title}\n          cableId={cavo.id_cavo}\n          description={modalConfig.description}\n        />\n\n        <div className=\"space-y-4 py-4 overflow-y-auto max-h-[calc(85vh-200px)]\">\n          {/* Sezione Informazioni Cavo */}\n          <CableInfoCard cavo={cavo} />\n\n          {/* Messaggio di errore */}\n          {error && (\n            <Alert className=\"bg-red-50 border-red-200\" role=\"alert\" aria-live=\"polite\">\n              <AlertTriangle className=\"h-4 w-4 text-red-600\" aria-hidden=\"true\" />\n              <AlertDescription className=\"text-red-800\">\n                {error}\n              </AlertDescription>\n            </Alert>\n          )}\n\n          {/* Sezione Metri da Installare (solo per aggiungi_metri) */}\n          {mode === 'aggiungi_metri' && (\n            <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n\n              {/* Campo metri e pulsante BOBINA VUOTA allineati */}\n              <div className=\"flex gap-6 items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"flex items-center gap-2\">\n                    <Label htmlFor=\"meters-input\" className=\"text-sm font-medium text-gray-700\">\n                      Metri Installati:\n                    </Label>\n                    <div className=\"relative\">\n                      <Input\n                        id=\"meters-input\"\n                        type=\"number\"\n                        value={metersInput}\n                        onChange={(e) => setMetersInput(e.target.value)}\n                        placeholder=\"0\"\n                        min=\"0\"\n                        max=\"10000\"\n                        step=\"0.1\"\n                        className=\"pr-6 w-24\"\n                        disabled={isLoading}\n                        aria-describedby={metersInput && validateMeters(metersInput) ? \"meters-error\" : undefined}\n                        aria-invalid={metersInput && validateMeters(metersInput) ? \"true\" : \"false\"}\n                      />\n                      <span className=\"absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm\">\n                        m\n                      </span>\n                    </div>\n                  </div>\n                  {metersInput && validateMeters(metersInput) && (\n                    <p id=\"meters-error\" className=\"text-red-600 text-sm mt-1\" role=\"alert\">\n                      {validateMeters(metersInput)}\n                    </p>\n                  )}\n                </div>\n\n                {/* Pulsante BOBINA VUOTA allineato al design modifica_bobina */}\n                <div className=\"flex gap-4 flex-1 justify-end\">\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => setSelectedBobina('BOBINA_VUOTA')}\n                    className={`text-xs font-medium border-2 px-3 py-1.5 ${\n                      selectedBobina === 'BOBINA_VUOTA'\n                        ? 'bg-blue-100 border-blue-500 text-blue-800 shadow-lg'\n                        : 'border-blue-300 text-blue-600 hover:bg-blue-50 hover:border-blue-400'\n                    }`}\n                    disabled={isLoading}\n                  >\n                    🔄 BOBINA VUOTA\n                  </Button>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Sezione Modifica Bobina */}\n          {mode === 'modifica_bobina' && (\n            <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n\n              {/* Campo metri e pulsanti affiancati */}\n              <div className=\"flex gap-6 items-center\">\n                <div className=\"flex-shrink-0\">\n                  <Label className=\"text-sm font-medium text-gray-700\">\n                    Metri Posati: <span className=\"font-bold text-lg\">{cavo.metratura_reale || cavo.metri_posati || 0}m</span>\n                  </Label>\n                </div>\n\n                {/* Pulsanti BOBINA VUOTA e ANNULLA INSTALLAZIONE - dimensioni ottimizzate */}\n                <div className=\"flex gap-4 flex-1 justify-end\">\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => {\n                      setSelectedEditOption('bobina_vuota')\n                      setSelectedBobina(null) // Deseleziona bobina quando clicco BOBINA VUOTA\n                    }}\n                    className={`text-xs font-medium border-2 px-3 py-1.5 ${\n                      selectedEditOption === 'bobina_vuota'\n                        ? 'bg-blue-100 border-blue-500 text-blue-800 shadow-lg'\n                        : 'border-blue-300 text-blue-600 hover:bg-blue-50 hover:border-blue-400'\n                    }`}\n                    disabled={isLoading}\n                  >\n                    🔄 BOBINA VUOTA\n                  </Button>\n\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => {\n                      setSelectedEditOption('annulla_posa')\n                      setSelectedBobina(null) // Deseleziona bobina quando clicco ANNULLA INSTALLAZIONE\n                    }}\n                    className={`text-xs font-medium border-2 px-3 py-1.5 ${\n                      selectedEditOption === 'annulla_posa'\n                        ? 'bg-blue-100 border-blue-500 text-blue-800 shadow-lg'\n                        : 'border-blue-300 text-blue-600 hover:bg-blue-50 hover:border-blue-400'\n                    }`}\n                    disabled={isLoading}\n                  >\n                    ❌ ANNULLA INSTALLAZIONE\n                  </Button>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Sezione Selezione Bobina (condizionale) */}\n          {isBobbinSelectionEnabled && (\n            <div className=\"bg-gray-50 border border-gray-200 rounded-lg p-4\">\n              <h3 className=\"font-semibold text-gray-800 mb-3 flex items-center gap-2\">\n                <Package className=\"h-5 w-5\" />\n                Selezione Bobina\n              </h3>\n\n              {/* Campo di ricerca */}\n              <div className=\"mb-4\">\n                <div className=\"relative\">\n                  <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                  <Input\n                    id=\"search-bobina\"\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                    placeholder=\"Cerca bobina per ID, tipologia o numero...\"\n                    className=\"pl-10\"\n                    disabled={isLoading}\n                  />\n                </div>\n\n\n              </div>\n\n              {/* Tab per bobine compatibili/incompatibili */}\n              <div className=\"flex border-b border-gray-200 mb-4\">\n                <button\n                  onClick={() => setActiveTab('compatibili')}\n                  className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${\n                    activeTab === 'compatibili'\n                      ? 'border-green-500 text-green-600 bg-green-50'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  Compatibili ({compatibleBobine.length})\n                </button>\n                <button\n                  onClick={() => setActiveTab('incompatibili')}\n                  className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${\n                    activeTab === 'incompatibili'\n                      ? 'border-orange-500 text-orange-600 bg-orange-50'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  Incompatibili ({incompatibleBobine.length})\n                </button>\n              </div>\n\n              {/* Lista bobine con tab */}\n              <div className=\"border rounded-lg h-64 overflow-y-auto\">\n                {isLoading ? (\n                  <div className=\"flex items-center justify-center h-full\">\n                    <div className=\"flex items-center space-x-2\">\n                      <Loader2 className=\"h-4 w-4 animate-spin\" />\n                      <span className=\"text-sm text-gray-600\">Caricamento bobine...</span>\n                    </div>\n                  </div>\n                ) : (\n                  <div className=\"p-2\">\n                    {activeTab === 'compatibili' ? (\n                      compatibleBobine.length === 0 ? (\n                        <div className=\"text-center py-8\">\n                          <div className=\"text-gray-500 text-sm mb-2\">\n                            Nessuna bobina compatibile trovata\n                          </div>\n                          <div className=\"text-xs text-gray-400\">\n                            Cercando bobine con tipologia <strong>{cavo?.tipologia}</strong> e formazione <strong>{cavo?.sezione}</strong>\n                          </div>\n                        </div>\n                      ) : (\n                        <div className=\"space-y-1\">\n                          {compatibleBobine.map((bobina) => {\n                            // Estrai solo il numero finale dall'ID (es: da \"C1_B2\" -> \"2\")\n                            const displayId = bobina.id_bobina.split('_B')[1] || bobina.id_bobina.split('_b')[1] || bobina.id_bobina\n\n                            return (\n                              <div\n                                key={bobina.id_bobina}\n                                onClick={() => {\n                                  setSelectedBobina(bobina.id_bobina)\n                                  // In modifica bobina, imposta automaticamente \"cambia_bobina\"\n                                  if (mode === 'modifica_bobina') {\n                                    setSelectedEditOption('cambia_bobina')\n                                  }\n                                }}\n                                className={`p-3 rounded cursor-pointer transition-colors border-2 ${\n                                  selectedBobina === bobina.id_bobina\n                                    ? 'bg-blue-100 border-blue-500 shadow-md'\n                                    : 'hover:bg-gray-50 border-gray-200 hover:border-gray-300'\n                                }`}\n                              >\n                                {/* Layout orizzontale migliorato */}\n                                <div className=\"flex flex-wrap gap-6 text-sm\">\n                                  <div className=\"flex items-center gap-2\">\n                                    <span className=\"text-gray-500 font-medium\">ID:</span>\n                                    <span className=\"text-gray-900 font-bold text-base\">{displayId}</span>\n                                  </div>\n                                  <div className=\"flex items-center gap-2\">\n                                    <span className=\"text-gray-500 font-medium\">TIPOLOGIA:</span>\n                                    <span className=\"text-gray-900 font-semibold\">{bobina.tipologia}</span>\n                                  </div>\n                                  <div className=\"flex items-center gap-2\">\n                                    <span className=\"text-gray-500 font-medium\">FORMAZIONE:</span>\n                                    <span className=\"text-gray-900 font-semibold\">{bobina.sezione}</span>\n                                  </div>\n                                  <div className=\"flex items-center gap-2\">\n                                    <span className=\"text-gray-500 font-medium\">RESIDUI:</span>\n                                    <span className=\"text-gray-900 font-bold\">{bobina.metri_residui}m</span>\n                                  </div>\n                                </div>\n                              </div>\n                            )\n                          })}\n                        </div>\n                      )\n                    ) : (\n                      incompatibleBobine.length === 0 ? (\n                        <div className=\"text-center py-8\">\n                          <div className=\"text-gray-500 text-sm\">\n                            Nessuna bobina incompatibile trovata\n                          </div>\n                        </div>\n                      ) : (\n                        <div className=\"space-y-1\">\n                          {incompatibleBobine.map((bobina) => {\n                            // Estrai solo il numero finale dall'ID (es: da \"C1_B2\" -> \"2\")\n                            const displayId = bobina.id_bobina.split('_B')[1] || bobina.id_bobina.split('_b')[1] || bobina.id_bobina\n\n                            return (\n                              <div\n                                key={bobina.id_bobina}\n                                onClick={() => {\n                                  setSelectedBobina(bobina.id_bobina)\n                                  // In modifica bobina, imposta automaticamente \"cambia_bobina\"\n                                  if (mode === 'modifica_bobina') {\n                                    setSelectedEditOption('cambia_bobina')\n                                  }\n                                }}\n                                className={`p-3 rounded cursor-pointer transition-colors border-2 ${\n                                  selectedBobina === bobina.id_bobina\n                                    ? 'bg-blue-100 border-blue-500 shadow-md'\n                                    : 'hover:bg-gray-50 border-gray-200 hover:border-gray-300'\n                                }`}\n                              >\n                                {/* Layout orizzontale migliorato */}\n                                <div className=\"flex flex-wrap gap-6 text-sm\">\n                                  <div className=\"flex items-center gap-2\">\n                                    <span className=\"text-gray-500 font-medium\">ID:</span>\n                                    <span className=\"text-gray-900 font-bold text-base\">{displayId}</span>\n                                  </div>\n                                  <div className=\"flex items-center gap-2\">\n                                    <span className=\"text-gray-500 font-medium\">TIPOLOGIA:</span>\n                                    <span className=\"text-gray-900 font-semibold\">{bobina.tipologia}</span>\n                                  </div>\n                                  <div className=\"flex items-center gap-2\">\n                                    <span className=\"text-gray-500 font-medium\">FORMAZIONE:</span>\n                                    <span className=\"text-gray-900 font-semibold\">{bobina.sezione}</span>\n                                  </div>\n                                  <div className=\"flex items-center gap-2\">\n                                    <span className=\"text-gray-500 font-medium\">RESIDUI:</span>\n                                    <span className=\"text-gray-900 font-bold\">{bobina.metri_residui}m</span>\n                                  </div>\n                                </div>\n                              </div>\n                            )\n                          })}\n                        </div>\n                      )\n                    )}\n\n                    {/* Messaggio quando non ci sono bobine */}\n                    {!isLoading && bobine.length === 0 && (\n                      <div className=\"text-center py-8 text-gray-500\">\n                        <Package className=\"h-12 w-12 mx-auto mb-2 text-gray-300\" />\n                        <p>Nessuna bobina disponibile</p>\n                        <p className=\"text-sm\">Verifica che ci siano bobine nel parco cavi</p>\n                      </div>\n                    )}\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Footer */}\n        <DialogFooter className=\"gap-2\">\n          <Button\n            variant=\"outline\"\n            onClick={handleClose}\n            disabled={isLoading}\n            className=\"flex-1 hover:bg-gray-50\"\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={handleSave}\n            disabled={isLoading || !isFormValid}\n            className={`flex-1 ${\n              !isFormValid\n                ? 'opacity-50 cursor-not-allowed'\n                : mode === 'aggiungi_metri'\n                  ? 'bg-green-600 hover:bg-green-700'\n                  : 'bg-blue-600 hover:bg-blue-700'\n            }`}\n          >\n            {isLoading ? (\n              <>\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                Salvando...\n              </>\n            ) : (\n              <>\n                <CheckCircle className=\"mr-2 h-4 w-4\" />\n                {modalConfig.primaryButtonText}\n              </>\n            )}\n          </Button>\n        </DialogFooter>\n      </EnhancedDialogContent>\n    </Dialog>\n  )\n}\n\n\n// Export types\nexport type {\n  ModificaBobinaModalProps,\n  InserisciMetriModalProps,\n  UnifiedCableBobbinModalProps,\n  EditOption,\n  Bobina\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;AA9BA;;;;;;;;;;;;AAwCA,MAAM,sBAA0D,CAAC,EAC/D,IAAI,EACJ,KAAK,EACL,OAAO,EACP,WAAW,EACZ,iBACC,8OAAC,kIAAA,CAAA,eAAY;;0BACX,8OAAC,kIAAA,CAAA,cAAW;gBAAC,WAAU;;oBACpB;kCACD,8OAAC;kCAAM;;;;;;;;;;;;YAER,6BACC,8OAAC,kIAAA,CAAA,oBAAiB;gBAAC,WAAU;0BAC1B;;;;;;;;;;;;AAcT,MAAM,wBAA8D,CAAC,EACnE,QAAQ,EACR,YAAY,aAAa,EACzB,SAAS,EACT,cAAc,EACd,eAAe,EAChB,iBACC,8OAAC,kIAAA,CAAA,gBAAa;QACZ,WAAW;QACX,WAAW;QACX,mBAAiB;QACjB,oBAAkB;QAClB,sBAAsB,CAAC,IAAM,EAAE,cAAc;QAC7C,iBAAiB,CAAC;YAChB,IAAI,WAAW;gBACb,UAAU;YACZ;QACF;kBAEC;;;;;;AAmDL,MAAM,gBAA8C,CAAC,EAAE,IAAI,EAAE,iBAC3D,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,wMAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;;;;;;;0BAIxD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAyD;;;;;;0CACzE,8OAAC;gCAAK,WAAU;0CAAmC,KAAK,SAAS,IAAI;;;;;;;;;;;;kCAGvE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAyD;;;;;;0CACzE,8OAAC;gCAAK,WAAU;0CAAmC,KAAK,OAAO,IAAI;;;;;;;;;;;;kCAGrE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAyD;;;;;;0CACzE,8OAAC;gCAAK,WAAU;0CAAmC,KAAK,mBAAmB,IAAI;;;;;;;;;;;;kCAGjF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAyD;;;;;;0CACzE,8OAAC;gCAAK,WAAU;0CAAmC,KAAK,iBAAiB,IAAI;;;;;;;;;;;;;;;;;;;;;;;;AAQ9E,MAAM,0BAAkE,CAAC,EAC9E,IAAI,EACJ,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,QAAQ,EACR,MAAM,EACP;IACC,eAAe;IACf,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE7D,+BAA+B;IAC/B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IAChF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmC;IAE5E,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,YAAY,kBAAkB,EAAE,UAAU,gBAAgB,EAAE,iBAAiB,uBAAuB,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAE3H,8GAA8G;IAC9G,MAAM,gBAAgB,YAAY;IAClC,MAAM,aAAa,eAAe,eAAe;IACjD,MAAM,kBAAkB,gBAAgB,OAAO;IAE/C,uDAAuD;IACvD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC1B,IAAI,SAAS,kBAAkB;YAC7B,kDAAkD;YAClD,MAAM,QAAQ,CAAC,wBAAwB,EAAE,MAAM,WAAW,OAAO;YAEjE,OAAO;gBACL,OAAO;gBACP,aAAa;gBACb,oBAAM,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;gBACvB,mBAAmB;YACrB;QACF,OAAO;YACL,MAAM,eAAe,MAAM,YACzB,KAAK,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,IAAI,KAAK,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,IAAI,KAAK,SAAS,GAAG;YAErF,4DAA4D;YAC5D,IAAI,QAAQ,CAAC,sBAAsB,EAAE,MAAM,WAAW,OAAO;YAE7D,IAAI,cAAc;gBAChB,SAAS,CAAC,WAAW,EAAE,cAAc;YACvC;YAEA,OAAO;gBACL,OAAO;gBACP,aAAa;gBACb,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;gBAC1B,mBAAmB;YACrB;QACF;IACF,GAAG;QAAC;QAAM,MAAM;QAAS,MAAM;KAAU,EAAE,iCAAiC;;IAE5E,uDAAuD;IACvD,MAAM,2BAA2B,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACvC,IAAI,SAAS,kBAAkB,OAAO;QACtC,IAAI,SAAS,mBAAmB,OAAO,KAAK,qCAAqC;;QACjF,OAAO;IACT,GAAG;QAAC;KAAK;IAET,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,SAAS;YACT,cAAc;YACd,kBAAkB;YAClB,sBAAsB;YAEtB,IAAI,SAAS,kBAAkB;gBAC7B,eAAe;YACjB,OAAO,IAAI,SAAS,qBAAqB,MAAM;gBAC7C,eAAe,OAAO,KAAK,eAAe,IAAI,KAAK,YAAY,IAAI;YACrE;QACF;IACF,GAAG;QAAC;QAAM;QAAM;KAAK;IAErB,sDAAsD;IACtD,MAAM,aAAa;QACjB,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,iBAAiB;YAC5C,QAAQ,GAAG,CAAC,+DAA+D;gBACzE,MAAM,CAAC,CAAC;gBACR;gBACA;gBACA,eAAe,CAAC,CAAC;YACnB;YACA;QACF;QAEA,QAAQ,GAAG,CAAC,qDAAqD;QACjE,QAAQ,GAAG,CAAC,0CAA0C;YACpD,MAAM,CAAC,CAAC;YACR,OAAO,CAAC,CAAC,aAAa,OAAO,CAAC;YAC9B,aAAa,aAAa,OAAO,CAAC,UAAU,UAAU;QACxD;QACA,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,iHAAA,CAAA,eAAY,CAAC,SAAS,CAAC;YAE9C,mEAAmE;YACnE,IAAI,aAAa,EAAE;YACnB,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,aAAa;YACf,OAAO,IAAI,YAAY,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBACnD,aAAa,SAAS,IAAI;YAC5B,OAAO,IAAI,YAAY,SAAS,MAAM,IAAI,MAAM,OAAO,CAAC,SAAS,MAAM,GAAG;gBACxE,aAAa,SAAS,MAAM;YAC9B,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;YAEA,QAAQ,GAAG,CAAC,qCAAqC,WAAW,MAAM;YAClE,QAAQ,GAAG,CAAC,sCAAsC,WAAW,GAAG,CAAC,CAAA,IAAK,CAAC;oBACrE,IAAI,EAAE,SAAS;oBACf,WAAW,EAAE,SAAS;oBACtB,SAAS,EAAE,OAAO;oBAClB,eAAe,EAAE,aAAa;oBAC9B,OAAO,EAAE,YAAY;gBACvB,CAAC;YAED,oEAAoE;YACpE,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAC,SAC5C,OAAO,YAAY,KAAK,eACxB,OAAO,YAAY,KAAK,UACxB,OAAO,aAAa,GAAG;YAGzB,QAAQ,GAAG,CAAC,wCAAwC,mBAAmB,MAAM;YAE7E,0BAA0B;YAC1B,MAAM,0BAA0B,mBAAmB,GAAG,CAAC,CAAC,SAAgB,CAAC;oBACvE,GAAG,MAAM;oBACT,YAAY,OAAO,SAAS,KAAK,KAAK,SAAS,IAAI,OAAO,OAAO,KAAK,KAAK,OAAO;gBACpF,CAAC;YAED,MAAM,cAAc,wBAAwB,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,EAAE,MAAM;YAC5E,MAAM,gBAAgB,wBAAwB,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,UAAU,EAAE,MAAM;YAE/E,QAAQ,GAAG,CAAC,0CAA0C;gBAAE;gBAAa;YAAc;YAEnF,UAAU;YACV,SAAS,IAAI,sCAAsC;;QACrD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,SAAS;YACT,UAAU,EAAE;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,8DAA8D;IAC9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,QAAQ,cAAc,iBAAiB;YACjD;QACF;IACF,GAAG;QAAC;QAAM;QAAM;QAAY;KAAgB;IAE5C,qCAAqC;IACrC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC7B,IAAI,CAAC,YAAY,OAAO;QAExB,MAAM,cAAc,WAAW,WAAW;QAC1C,OAAO,OAAO,MAAM,CAAC,CAAA;YACnB,MAAM,WAAW,OAAO,SAAS,EAAE,iBAAiB;YACpD,MAAM,YAAY,OAAO,SAAS,EAAE,iBAAiB;YACrD,MAAM,eAAe,OAAO,aAAa,EAAE,iBAAiB;YAE5D,OAAO,SAAS,QAAQ,CAAC,gBAClB,UAAU,QAAQ,CAAC,gBACnB,aAAa,QAAQ,CAAC;QAC/B;IACF,GAAG;QAAC;QAAQ;KAAW;IAEvB,4CAA4C;IAC5C,MAAM,mBAAmB,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU;IAChE,MAAM,qBAAqB,eAAe,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,UAAU;IAEnE,kDAAkD;IAClD,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,MAAM,IAAI,IAAI,OAAO;QAC1B,MAAM,SAAS,WAAW;QAC1B,IAAI,MAAM,SAAS,OAAO;QAC1B,IAAI,SAAS,GAAG,OAAO;QACvB,IAAI,WAAW,GAAG,OAAO;QACzB,IAAI,SAAS,OAAO,OAAO;QAC3B,OAAO;IACT;IAEA,0CAA0C;IAC1C,MAAM,YAAY,CAAC;QACjB,SAAS;QACT,mCAAmC;QACnC,WAAW,IAAM,SAAS,KAAK;IACjC;IAEA,uBAAuB;IACvB,MAAM,aAAa;QACjB,IAAI,CAAC,MAAM;QAEX,SAAS;QACT,aAAa;QAEb,IAAI;YACF,IAAI,UAAe,CAAC;YAEpB,IAAI,SAAS,kBAAkB;gBAC7B,iCAAiC;gBACjC,MAAM,cAAc,eAAe;gBACnC,IAAI,aAAa;oBACf,UAAU;oBACV,aAAa;oBACb;gBACF;gBAEA,UAAU;oBACR,MAAM;oBACN,SAAS,KAAK,OAAO;oBACrB,iBAAiB,WAAW;oBAC5B,UAAU,kBAAkB;gBAC9B;YACF,OAAO,IAAI,SAAS,mBAAmB;gBACrC,kCAAkC;gBAClC,IAAI,CAAC,oBAAoB;oBACvB,UAAU;oBACV,aAAa;oBACb;gBACF;gBAEA,IAAI,uBAAuB,mBAAmB,CAAC,gBAAgB;oBAC7D,UAAU;oBACV,aAAa;oBACb;gBACF;gBAEA,UAAU;oBACR,MAAM;oBACN,SAAS,KAAK,OAAO;oBACrB,YAAY;oBACZ,aAAa,kBAAkB;gBACjC;YACF;YAEA,MAAM,OAAO;YACb;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,qCAAqC;YACnD,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WACvB,MAAM,OAAO,IACb;YACpB,UAAU;QACZ,SAAU;YACR,aAAa;QACf;IACF;IAEA,oBAAoB;IACpB,MAAM,cAAc;QAClB,IAAI,CAAC,WAAW;YACd;QACF;IACF;IAEA,iBAAiB;IACjB,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB;QACF;IACF;IAEA,gCAAgC;IAChC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC1B,IAAI,SAAS,kBAAkB;YAC7B,OAAO,YAAY,IAAI,MAAM,CAAC,eAAe;QAC/C,OAAO,IAAI,SAAS,mBAAmB;YACrC,IAAI,CAAC,oBAAoB,OAAO;YAEhC,6DAA6D;YAC7D,IAAI,uBAAuB,gBAAgB;gBACzC,IAAI,CAAC,YAAY,IAAI,MAAM,eAAe,cAAc,OAAO;YACjE;YAEA,0CAA0C;YAC1C,IAAI,uBAAuB,mBAAmB,CAAC,gBAAgB,OAAO;YAEtE,OAAO;QACT;QACA,OAAO;IACT,GAAG;QAAC;QAAM;QAAa;QAAoB;KAAe;IAE1D,qEAAqE;IACrE,IAAI,CAAC,QAAQ,CAAC,KAAK,OAAO,EAAE;QAC1B,QAAQ,IAAI,CAAC,wEAAwE;QACrF,OAAO;IACT;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC;YACC,WAAU;YACV,WAAW;YACX,gBAAe;;8BAEf,8OAAC;oBACC,MAAM,YAAY,IAAI;oBACtB,OAAO,YAAY,KAAK;oBACxB,SAAS,KAAK,OAAO;oBACrB,aAAa,YAAY,WAAW;;;;;;8BAGtC,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAc,MAAM;;;;;;wBAGpB,uBACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,WAAU;4BAA2B,MAAK;4BAAQ,aAAU;;8CACjE,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;oCAAuB,eAAY;;;;;;8CAC5D,8OAAC,iIAAA,CAAA,mBAAgB;oCAAC,WAAU;8CACzB;;;;;;;;;;;;wBAMN,SAAS,kCACR,8OAAC;4BAAI,WAAU;sCAGb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAe,WAAU;kEAAoC;;;;;;kEAG5E,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,OAAO;gEACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gEAC9C,aAAY;gEACZ,KAAI;gEACJ,KAAI;gEACJ,MAAK;gEACL,WAAU;gEACV,UAAU;gEACV,oBAAkB,eAAe,eAAe,eAAe,iBAAiB;gEAChF,gBAAc,eAAe,eAAe,eAAe,SAAS;;;;;;0EAEtE,8OAAC;gEAAK,WAAU;0EAA4E;;;;;;;;;;;;;;;;;;4CAK/F,eAAe,eAAe,8BAC7B,8OAAC;gDAAE,IAAG;gDAAe,WAAU;gDAA4B,MAAK;0DAC7D,eAAe;;;;;;;;;;;;kDAMtB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,kBAAkB;4CACjC,WAAW,CAAC,yCAAyC,EACnD,mBAAmB,iBACf,wDACA,wEACJ;4CACF,UAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;wBASR,SAAS,mCACR,8OAAC;4BAAI,WAAU;sCAGb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;;gDAAoC;8DACrC,8OAAC;oDAAK,WAAU;;wDAAqB,KAAK,eAAe,IAAI,KAAK,YAAY,IAAI;wDAAE;;;;;;;;;;;;;;;;;;kDAKtG,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;oDACP,sBAAsB;oDACtB,kBAAkB,MAAM,gDAAgD;;gDAC1E;gDACA,WAAW,CAAC,yCAAyC,EACnD,uBAAuB,iBACnB,wDACA,wEACJ;gDACF,UAAU;0DACX;;;;;;0DAID,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;oDACP,sBAAsB;oDACtB,kBAAkB,MAAM,yDAAyD;;gDACnF;gDACA,WAAW,CAAC,yCAAyC,EACnD,uBAAuB,iBACnB,wDACA,wEACJ;gDACF,UAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;wBASR,0CACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC,wMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAKjC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,aAAY;gDACZ,WAAU;gDACV,UAAU;;;;;;;;;;;;;;;;;8CAQhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAW,CAAC,2DAA2D,EACrE,cAAc,gBACV,gDACA,8EACJ;;gDACH;gDACe,iBAAiB,MAAM;gDAAC;;;;;;;sDAExC,8OAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAW,CAAC,2DAA2D,EACrE,cAAc,kBACV,mDACA,8EACJ;;gDACH;gDACiB,mBAAmB,MAAM;gDAAC;;;;;;;;;;;;;8CAK9C,8OAAC;oCAAI,WAAU;8CACZ,0BACC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;;;;;;6DAI5C,8OAAC;wCAAI,WAAU;;4CACZ,cAAc,gBACb,iBAAiB,MAAM,KAAK,kBAC1B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAA6B;;;;;;kEAG5C,8OAAC;wDAAI,WAAU;;4DAAwB;0EACP,8OAAC;0EAAQ,MAAM;;;;;;4DAAmB;0EAAc,8OAAC;0EAAQ,MAAM;;;;;;;;;;;;;;;;;qEAIjG,8OAAC;gDAAI,WAAU;0DACZ,iBAAiB,GAAG,CAAC,CAAC;oDACrB,+DAA+D;oDAC/D,MAAM,YAAY,OAAO,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,IAAI,OAAO,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,IAAI,OAAO,SAAS;oDAExG,qBACE,8OAAC;wDAEC,SAAS;4DACP,kBAAkB,OAAO,SAAS;4DAClC,8DAA8D;4DAC9D,IAAI,SAAS,mBAAmB;gEAC9B,sBAAsB;4DACxB;wDACF;wDACA,WAAW,CAAC,sDAAsD,EAChE,mBAAmB,OAAO,SAAS,GAC/B,0CACA,0DACJ;kEAGF,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAA4B;;;;;;sFAC5C,8OAAC;4EAAK,WAAU;sFAAqC;;;;;;;;;;;;8EAEvD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAA4B;;;;;;sFAC5C,8OAAC;4EAAK,WAAU;sFAA+B,OAAO,SAAS;;;;;;;;;;;;8EAEjE,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAA4B;;;;;;sFAC5C,8OAAC;4EAAK,WAAU;sFAA+B,OAAO,OAAO;;;;;;;;;;;;8EAE/D,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAA4B;;;;;;sFAC5C,8OAAC;4EAAK,WAAU;;gFAA2B,OAAO,aAAa;gFAAC;;;;;;;;;;;;;;;;;;;uDA9B/D,OAAO,SAAS;;;;;gDAmC3B;;;;;uDAIJ,mBAAmB,MAAM,KAAK,kBAC5B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;qEAKzC,8OAAC;gDAAI,WAAU;0DACZ,mBAAmB,GAAG,CAAC,CAAC;oDACvB,+DAA+D;oDAC/D,MAAM,YAAY,OAAO,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,IAAI,OAAO,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,IAAI,OAAO,SAAS;oDAExG,qBACE,8OAAC;wDAEC,SAAS;4DACP,kBAAkB,OAAO,SAAS;4DAClC,8DAA8D;4DAC9D,IAAI,SAAS,mBAAmB;gEAC9B,sBAAsB;4DACxB;wDACF;wDACA,WAAW,CAAC,sDAAsD,EAChE,mBAAmB,OAAO,SAAS,GAC/B,0CACA,0DACJ;kEAGF,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAA4B;;;;;;sFAC5C,8OAAC;4EAAK,WAAU;sFAAqC;;;;;;;;;;;;8EAEvD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAA4B;;;;;;sFAC5C,8OAAC;4EAAK,WAAU;sFAA+B,OAAO,SAAS;;;;;;;;;;;;8EAEjE,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAA4B;;;;;;sFAC5C,8OAAC;4EAAK,WAAU;sFAA+B,OAAO,OAAO;;;;;;;;;;;;8EAE/D,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAA4B;;;;;;sFAC5C,8OAAC;4EAAK,WAAU;;gFAA2B,OAAO,aAAa;gFAAC;;;;;;;;;;;;;;;;;;;uDA9B/D,OAAO,SAAS;;;;;gDAmC3B;;;;;;4CAML,CAAC,aAAa,OAAO,MAAM,KAAK,mBAC/B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,wMAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,8OAAC;kEAAE;;;;;;kEACH,8OAAC;wDAAE,WAAU;kEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAWvC,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU;4BACV,WAAU;sCACX;;;;;;sCAGD,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,aAAa,CAAC;4BACxB,WAAW,CAAC,OAAO,EACjB,CAAC,cACG,kCACA,SAAS,mBACP,oCACA,iCACN;sCAED,0BACC;;kDACE,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA8B;;6DAInD;;kDACE,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB,YAAY,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9C", "debugId": null}}]}