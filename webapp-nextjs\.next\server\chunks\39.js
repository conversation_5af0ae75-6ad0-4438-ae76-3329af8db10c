"use strict";exports.id=39,exports.ids=[39],exports.modules={49039:(e,a,i)=>{i.d(a,{B:()=>_});var s=i(60687),t=i(43210),r=i(63503),o=i(29523),l=i(89667),n=i(80013),d=i(91821),c=i(19080);let m=(0,i(62688).A)("ruler",[["path",{d:"M21.3 15.3a2.4 2.4 0 0 1 0 3.4l-2.6 2.6a2.4 2.4 0 0 1-3.4 0L2.7 8.7a2.41 2.41 0 0 1 0-3.4l2.6-2.6a2.41 2.41 0 0 1 3.4 0Z",key:"icamh8"}],["path",{d:"m14.5 12.5 2-2",key:"inckbg"}],["path",{d:"m11.5 9.5 2-2",key:"fmmyf7"}],["path",{d:"m8.5 6.5 2-2",key:"vc6u1g"}],["path",{d:"m17.5 15.5 2-2",key:"wo5hmg"}]]);var b=i(84027),x=i(43649),g=i(99270),u=i(41862),p=i(5336),h=i(62185),f=i(63213),v=i(76628);let N=({icon:e,title:a,cableId:i,description:t})=>(0,s.jsxs)(r.c7,{children:[(0,s.jsxs)(r.L3,{className:"flex items-center gap-2",children:[e,(0,s.jsx)("span",{children:a})]}),t&&(0,s.jsx)(r.rr,{className:"text-sm text-muted-foreground",children:t})]}),j=({children:e,className:a="sm:max-w-md",onKeyDown:i,ariaLabelledBy:t,ariaDescribedBy:o})=>(0,s.jsx)(r.Cf,{className:a,onKeyDown:i,"aria-labelledby":t,"aria-describedby":o,onPointerDownOutside:e=>e.preventDefault(),onEscapeKeyDown:e=>{i&&i(e)},children:e}),y=({cavo:e})=>(0,s.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-5 mb-5",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,s.jsx)(c.A,{className:"h-5 w-5 text-blue-600"}),(0,s.jsx)("h3",{className:"font-semibold text-blue-800 text-base",children:"Informazioni Cavo"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-x-8 gap-y-4",children:[(0,s.jsxs)("div",{className:"info-item",children:[(0,s.jsx)("span",{className:"block text-xs text-gray-500 font-medium uppercase mb-1",children:"Tipologia"}),(0,s.jsx)("span",{className:"text-gray-900 font-bold text-sm",children:e.tipologia||"N/A"})]}),(0,s.jsxs)("div",{className:"info-item",children:[(0,s.jsx)("span",{className:"block text-xs text-gray-500 font-medium uppercase mb-1",children:"Formazione"}),(0,s.jsx)("span",{className:"text-gray-900 font-bold text-sm",children:e.sezione||"N/A"})]}),(0,s.jsxs)("div",{className:"info-item",children:[(0,s.jsx)("span",{className:"block text-xs text-gray-500 font-medium uppercase mb-1",children:"Da"}),(0,s.jsx)("span",{className:"text-gray-900 font-bold text-sm",children:e.ubicazione_partenza||"N/A"})]}),(0,s.jsxs)("div",{className:"info-item",children:[(0,s.jsx)("span",{className:"block text-xs text-gray-500 font-medium uppercase mb-1",children:"A"}),(0,s.jsx)("span",{className:"text-gray-900 font-bold text-sm",children:e.ubicazione_arrivo||"N/A"})]})]})]}),_=({mode:e,open:a,onClose:i,cavo:_,cantiere:A,onSave:w})=>{let[I,z]=(0,t.useState)(!1),[C,k]=(0,t.useState)(""),[B,O]=(0,t.useState)([]),[M,D]=(0,t.useState)(""),[S,U]=(0,t.useState)(""),[T,E]=(0,t.useState)(""),[L,$]=(0,t.useState)(null),[F,V]=(0,t.useState)("compatibili"),{user:P}=(0,f.A)(),{cantiereId:R,cantiere:Z,isValidCantiere:G}=(0,v.jV)(),J=A||Z,K=J?.id_cantiere||R,Y=!!J||G,q=(0,t.useMemo)(()=>{if("aggiungi_metri"===e)return{title:`Inserisci Metri Posati: ${_?.id_cavo||"N/A"}`,description:"Inserisci i metri effettivamente posati per il cavo e seleziona una bobina o usa BOBINA VUOTA",icon:(0,s.jsx)(m,{className:"h-5 w-5 text-green-500"}),primaryButtonText:"Salva"};{let e=_?.id_bobina?_.id_bobina.split("_B")[1]||_.id_bobina.split("_b")[1]||_.id_bobina:"",a=`Modifica Bobina Cavo: ${_?.id_cavo||"N/A"}`;return e&&(a+=` / Bobina: ${e}`),{title:a,description:"Seleziona una nuova bobina per il cavo o modifica i parametri",icon:(0,s.jsx)(b.A,{className:"h-5 w-5 text-blue-500"}),primaryButtonText:"Salva Modifiche"}}},[e,_?.id_cavo,_?.id_bobina]),H=(0,t.useMemo)(()=>"aggiungi_metri"===e||"modifica_bobina"===e,[e]);(0,t.useEffect)(()=>{a&&(k(""),D(""),U(""),$(null),"aggiungi_metri"===e?E(""):"modifica_bobina"===e&&_&&E(String(_.metratura_reale||_.metri_posati||0)))},[a,e,_]);let Q=async()=>{if(!_||!K||!Y)return void console.log("\uD83D\uDD0D UnifiedModal: Caricamento bobine saltato - mancano dati:",{cavo:!!_,cantiereId:K,isValidCantiere:Y,cantiereToUse:!!J});console.log("\uD83D\uDD04 UnifiedModal: Caricamento bobine per cantiere:",K),console.log("\uD83D\uDD10 UnifiedModal: Debug autenticazione:",{user:!!P,token:!!localStorage.getItem("token"),tokenLength:localStorage.getItem("token")?.length||0}),z(!0);try{let e=await h.Fw.getBobine(K),a=[];if(Array.isArray(e))a=e;else if(e&&Array.isArray(e.data))a=e.data;else if(e&&e.bobine&&Array.isArray(e.bobine))a=e.bobine;else throw Error("Formato risposta API non valido");console.log("\uD83D\uDCE6 UnifiedModal: Bobine ricevute:",a.length),console.log("\uD83D\uDCCB UnifiedModal: Dettaglio bobine:",a.map(e=>({id:e.id_bobina,tipologia:e.tipologia,sezione:e.sezione,metri_residui:e.metri_residui,stato:e.stato_bobina})));let i=a.filter(e=>"Terminata"!==e.stato_bobina&&"Over"!==e.stato_bobina&&e.metri_residui>0);console.log("✅ UnifiedModal: Bobine utilizzabili:",i.length);let s=i.map(e=>({...e,compatible:e.tipologia===_.tipologia&&e.sezione===_.sezione})),t=s.filter(e=>e.compatible).length,r=s.filter(e=>!e.compatible).length;console.log("\uD83C\uDFAF UnifiedModal: Compatibilit\xe0 bobine:",{compatibili:t,incompatibili:r}),O(s),k("")}catch(e){console.error("❌ UnifiedModal: Errore caricamento bobine:",e),k("Errore durante il caricamento delle bobine. Riprova."),O([])}finally{z(!1)}};(0,t.useEffect)(()=>{a&&_&&K&&Y&&Q()},[a,_,K,Y]);let W=(0,t.useMemo)(()=>{if(!M)return B;let e=M.toLowerCase();return B.filter(a=>{let i=a.id_bobina?.toLowerCase()||"",s=a.tipologia?.toLowerCase()||"",t=a.numero_bobina?.toLowerCase()||"";return i.includes(e)||s.includes(e)||t.includes(e)})},[B,M]),X=W.filter(e=>e.compatible),ee=W.filter(e=>!e.compatible),ea=e=>{if(!e.trim())return"Il campo metri \xe8 obbligatorio";let a=parseFloat(e);return isNaN(a)?"Inserire un valore numerico valido":a<0?"I metri non possono essere negativi":0===a?"I metri devono essere maggiori di zero":a>1e4?"Valore troppo elevato (massimo 10.000m)":""},ei=e=>{k(e),setTimeout(()=>k(""),5e3)},es=async()=>{if(_){k(""),z(!0);try{let a={};if("aggiungi_metri"===e){let e=ea(T);if(e){ei(e),z(!1);return}a={mode:"aggiungi_metri",cableId:_.id_cavo,metersToInstall:parseFloat(T),bobbinId:S||"BOBINA_VUOTA"}}else if("modifica_bobina"===e){if(!L){ei("Selezionare un'opzione di modifica"),z(!1);return}if("cambia_bobina"===L&&!S){ei("Selezionare una bobina per continuare"),z(!1);return}a={mode:"modifica_bobina",cableId:_.id_cavo,editOption:L,newBobbinId:S||null}}await w(a),i()}catch(e){console.error("Errore salvataggio unified modal:",e),ei(e.response?.data?.message||e.message||"Errore durante il salvataggio. Riprovare.")}finally{z(!1)}}},et=()=>{I||i()},er=(0,t.useMemo)(()=>"aggiungi_metri"===e?T.trim()&&!ea(T):"modifica_bobina"===e&&!(!L||"annulla_posa"!==L&&(!T.trim()||ea(T)))&&("cambia_bobina"!==L||!!S),[e,T,L,S]);return _&&_.id_cavo?(0,s.jsx)(r.lG,{open:a,onOpenChange:et,children:(0,s.jsxs)(j,{className:"sm:max-w-3xl max-h-[85vh] overflow-hidden",onKeyDown:e=>{"Escape"===e.key&&et()},ariaLabelledBy:"unified-modal-title",children:[(0,s.jsx)(N,{icon:q.icon,title:q.title,cableId:_.id_cavo,description:q.description}),(0,s.jsxs)("div",{className:"space-y-4 py-4 overflow-y-auto max-h-[calc(85vh-200px)]",children:[(0,s.jsx)(y,{cavo:_}),C&&(0,s.jsxs)(d.Fc,{className:"bg-red-50 border-red-200",role:"alert","aria-live":"polite",children:[(0,s.jsx)(x.A,{className:"h-4 w-4 text-red-600","aria-hidden":"true"}),(0,s.jsx)(d.TN,{className:"text-red-800",children:C})]}),"aggiungi_metri"===e&&(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex gap-6 items-center",children:[(0,s.jsxs)("div",{className:"flex-shrink-0",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(n.J,{htmlFor:"meters-input",className:"text-sm font-medium text-gray-700",children:"Metri Installati:"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(l.p,{id:"meters-input",type:"number",value:T,onChange:e=>E(e.target.value),placeholder:"0",min:"0",max:"10000",step:"0.1",className:"pr-6 w-24",disabled:I,"aria-describedby":T&&ea(T)?"meters-error":void 0,"aria-invalid":T&&ea(T)?"true":"false"}),(0,s.jsx)("span",{className:"absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm",children:"m"})]})]}),T&&ea(T)&&(0,s.jsx)("p",{id:"meters-error",className:"text-red-600 text-sm mt-1",role:"alert",children:ea(T)})]}),(0,s.jsx)("div",{className:"flex gap-4 flex-1 justify-end",children:(0,s.jsx)(o.$,{variant:"outline",size:"sm",onClick:()=>U("BOBINA_VUOTA"),className:`text-xs font-medium border-2 px-3 py-1.5 ${"BOBINA_VUOTA"===S?"bg-blue-100 border-blue-500 text-blue-800 shadow-lg":"border-blue-300 text-blue-600 hover:bg-blue-50 hover:border-blue-400"}`,disabled:I,children:"\uD83D\uDD04 BOBINA VUOTA"})})]})}),"modifica_bobina"===e&&(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex gap-6 items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsxs)(n.J,{className:"text-sm font-medium text-gray-700",children:["Metri Posati: ",(0,s.jsxs)("span",{className:"font-bold text-lg",children:[_.metratura_reale||_.metri_posati||0,"m"]})]})}),(0,s.jsxs)("div",{className:"flex gap-4 flex-1 justify-end",children:[(0,s.jsx)(o.$,{variant:"outline",size:"sm",onClick:()=>{$("bobina_vuota"),U(null)},className:`text-xs font-medium border-2 px-3 py-1.5 ${"bobina_vuota"===L?"bg-blue-100 border-blue-500 text-blue-800 shadow-lg":"border-blue-300 text-blue-600 hover:bg-blue-50 hover:border-blue-400"}`,disabled:I,children:"\uD83D\uDD04 BOBINA VUOTA"}),(0,s.jsx)(o.$,{variant:"outline",size:"sm",onClick:()=>{$("annulla_posa"),U(null)},className:`text-xs font-medium border-2 px-3 py-1.5 ${"annulla_posa"===L?"bg-blue-100 border-blue-500 text-blue-800 shadow-lg":"border-blue-300 text-blue-600 hover:bg-blue-50 hover:border-blue-400"}`,disabled:I,children:"❌ ANNULLA INSTALLAZIONE"})]})]})}),H&&(0,s.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[(0,s.jsxs)("h3",{className:"font-semibold text-gray-800 mb-3 flex items-center gap-2",children:[(0,s.jsx)(c.A,{className:"h-5 w-5"}),"Selezione Bobina"]}),(0,s.jsx)("div",{className:"mb-4",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,s.jsx)(l.p,{id:"search-bobina",value:M,onChange:e=>D(e.target.value),placeholder:"Cerca bobina per ID, tipologia o numero...",className:"pl-10",disabled:I})]})}),(0,s.jsxs)("div",{className:"flex border-b border-gray-200 mb-4",children:[(0,s.jsxs)("button",{onClick:()=>V("compatibili"),className:`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${"compatibili"===F?"border-green-500 text-green-600 bg-green-50":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:["Compatibili (",X.length,")"]}),(0,s.jsxs)("button",{onClick:()=>V("incompatibili"),className:`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${"incompatibili"===F?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:["Incompatibili (",ee.length,")"]})]}),(0,s.jsx)("div",{className:"border rounded-lg h-64 overflow-y-auto",children:I?(0,s.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(u.A,{className:"h-4 w-4 animate-spin"}),(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Caricamento bobine..."})]})}):(0,s.jsxs)("div",{className:"p-2",children:["compatibili"===F?0===X.length?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("div",{className:"text-gray-500 text-sm mb-2",children:"Nessuna bobina compatibile trovata"}),(0,s.jsxs)("div",{className:"text-xs text-gray-400",children:["Cercando bobine con tipologia ",(0,s.jsx)("strong",{children:_?.tipologia})," e formazione ",(0,s.jsx)("strong",{children:_?.sezione})]})]}):(0,s.jsx)("div",{className:"space-y-1",children:X.map(a=>{let i=a.id_bobina.split("_B")[1]||a.id_bobina.split("_b")[1]||a.id_bobina;return(0,s.jsx)("div",{onClick:()=>{U(a.id_bobina),"modifica_bobina"===e&&$("cambia_bobina")},className:`p-3 rounded cursor-pointer transition-colors border-2 ${S===a.id_bobina?"bg-blue-100 border-blue-500 shadow-md":"hover:bg-gray-50 border-gray-200 hover:border-gray-300"}`,children:(0,s.jsxs)("div",{className:"flex flex-wrap gap-6 text-sm",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-gray-500 font-medium",children:"ID:"}),(0,s.jsx)("span",{className:"text-gray-900 font-bold text-base",children:i})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-gray-500 font-medium",children:"TIPOLOGIA:"}),(0,s.jsx)("span",{className:"text-gray-900 font-semibold",children:a.tipologia})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-gray-500 font-medium",children:"FORMAZIONE:"}),(0,s.jsx)("span",{className:"text-gray-900 font-semibold",children:a.sezione})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-gray-500 font-medium",children:"RESIDUI:"}),(0,s.jsxs)("span",{className:"text-gray-900 font-bold",children:[a.metri_residui,"m"]})]})]})},a.id_bobina)})}):0===ee.length?(0,s.jsx)("div",{className:"text-center py-8",children:(0,s.jsx)("div",{className:"text-gray-500 text-sm",children:"Nessuna bobina incompatibile trovata"})}):(0,s.jsx)("div",{className:"space-y-1",children:ee.map(a=>{let i=a.id_bobina.split("_B")[1]||a.id_bobina.split("_b")[1]||a.id_bobina;return(0,s.jsx)("div",{onClick:()=>{U(a.id_bobina),"modifica_bobina"===e&&$("cambia_bobina")},className:`p-3 rounded cursor-pointer transition-colors border-2 ${S===a.id_bobina?"bg-blue-100 border-blue-500 shadow-md":"hover:bg-gray-50 border-gray-200 hover:border-gray-300"}`,children:(0,s.jsxs)("div",{className:"flex flex-wrap gap-6 text-sm",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-gray-500 font-medium",children:"ID:"}),(0,s.jsx)("span",{className:"text-gray-900 font-bold text-base",children:i})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-gray-500 font-medium",children:"TIPOLOGIA:"}),(0,s.jsx)("span",{className:"text-gray-900 font-semibold",children:a.tipologia})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-gray-500 font-medium",children:"FORMAZIONE:"}),(0,s.jsx)("span",{className:"text-gray-900 font-semibold",children:a.sezione})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-gray-500 font-medium",children:"RESIDUI:"}),(0,s.jsxs)("span",{className:"text-gray-900 font-bold",children:[a.metri_residui,"m"]})]})]})},a.id_bobina)})}),!I&&0===B.length&&(0,s.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,s.jsx)(c.A,{className:"h-12 w-12 mx-auto mb-2 text-gray-300"}),(0,s.jsx)("p",{children:"Nessuna bobina disponibile"}),(0,s.jsx)("p",{className:"text-sm",children:"Verifica che ci siano bobine nel parco cavi"})]})]})})]})]}),(0,s.jsxs)(r.Es,{className:"gap-2",children:[(0,s.jsx)(o.$,{variant:"outline",onClick:et,disabled:I,className:"flex-1 hover:bg-gray-50",children:"Annulla"}),(0,s.jsx)(o.$,{onClick:es,disabled:I||!er,className:`flex-1 ${!er?"opacity-50 cursor-not-allowed":"aggiungi_metri"===e?"bg-green-600 hover:bg-green-700":"bg-blue-600 hover:bg-blue-700"}`,children:I?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(u.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Salvando..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(p.A,{className:"mr-2 h-4 w-4"}),q.primaryButtonText]})})]})]})}):(console.warn("⚠️ UnifiedCableBobbinModal: Tentativo di apertura senza cavo valido:",_),null)}},63503:(e,a,i)=>{i.d(a,{Cf:()=>m,Es:()=>x,L3:()=>g,c7:()=>b,lG:()=>l,rr:()=>u,zM:()=>n});var s=i(60687);i(43210);var t=i(26134),r=i(11860),o=i(4780);function l({...e}){return(0,s.jsx)(t.bL,{"data-slot":"dialog",...e})}function n({...e}){return(0,s.jsx)(t.l9,{"data-slot":"dialog-trigger",...e})}function d({...e}){return(0,s.jsx)(t.ZL,{"data-slot":"dialog-portal",...e})}function c({className:e,...a}){return(0,s.jsx)(t.hJ,{"data-slot":"dialog-overlay",className:(0,o.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...a})}function m({className:e,children:a,showCloseButton:i=!0,...l}){return(0,s.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,s.jsx)(c,{}),(0,s.jsxs)(t.UC,{"data-slot":"dialog-content",className:(0,o.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...l,children:[a,i&&(0,s.jsxs)(t.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(r.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function b({className:e,...a}){return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,o.cn)("flex flex-col gap-2 text-center sm:text-left",e),...a})}function x({className:e,...a}){return(0,s.jsx)("div",{"data-slot":"dialog-footer",className:(0,o.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...a})}function g({className:e,...a}){return(0,s.jsx)(t.hE,{"data-slot":"dialog-title",className:(0,o.cn)("text-lg leading-none font-semibold",e),...a})}function u({className:e,...a}){return(0,s.jsx)(t.VY,{"data-slot":"dialog-description",className:(0,o.cn)("text-muted-foreground text-sm",e),...a})}}};