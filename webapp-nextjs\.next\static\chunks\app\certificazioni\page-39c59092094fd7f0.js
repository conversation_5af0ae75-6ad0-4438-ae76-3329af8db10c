(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9992],{3783:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>U});var i=a(95155),t=a(40283),l=a(12115),r=a(66695),n=a(30285),c=a(26126),o=a(62523),d=a(17313),x=a(85127),m=a(25731),h=a(51154),j=a(69037),u=a(57434),p=a(381),v=a(72713),N=a(1243),f=a(47924),g=a(84616),b=a(40646),_=a(85339),z=a(14186),w=a(13717),y=a(62525),A=a(6803),C=a(55365),S=a(85057),I=a(88539),E=a(59409),R=a(54416),T=a(4229);function Z(e){let{cantiereId:s,strumento:a,onSuccess:t,onCancel:c}=e,[d,x]=(0,l.useState)({nome:"",marca:"",modello:"",numero_serie:"",data_calibrazione:"",data_scadenza_calibrazione:"",note:"",tipo_strumento:"MEGGER",ente_certificatore:"",numero_certificato_calibrazione:"",range_misura:"",precisione:"",stato_strumento:"ATTIVO"}),[j,u]=(0,l.useState)(!1),[v,N]=(0,l.useState)(""),[f,g]=(0,l.useState)({}),b=!!a;(0,l.useEffect)(()=>{a&&x({nome:a.nome,marca:a.marca,modello:a.modello,numero_serie:a.numero_serie,data_calibrazione:a.data_calibrazione.split("T")[0],data_scadenza_calibrazione:a.data_scadenza_calibrazione.split("T")[0],note:a.note||"",tipo_strumento:a.tipo_strumento||"MEGGER",ente_certificatore:a.ente_certificatore||"",numero_certificato_calibrazione:a.numero_certificato_calibrazione||"",range_misura:a.range_misura||"",precisione:a.precisione||"",stato_strumento:a.stato_strumento||"ATTIVO"})},[a]);let z=()=>{let e={};if(d.nome.trim()||(e.nome="Il nome \xe8 obbligatorio"),d.marca.trim()||(e.marca="La marca \xe8 obbligatoria"),d.modello.trim()||(e.modello="Il modello \xe8 obbligatorio"),d.numero_serie.trim()||(e.numero_serie="Il numero di serie \xe8 obbligatorio"),d.data_calibrazione||(e.data_calibrazione="La data di calibrazione \xe8 obbligatoria"),d.data_scadenza_calibrazione||(e.data_scadenza_calibrazione="La data di scadenza \xe8 obbligatoria"),d.data_calibrazione&&d.data_scadenza_calibrazione){let s=new Date(d.data_calibrazione);new Date(d.data_scadenza_calibrazione)<=s&&(e.data_scadenza_calibrazione="La data di scadenza deve essere successiva alla calibrazione")}return g(e),0===Object.keys(e).length},w=async e=>{if(e.preventDefault(),z())try{u(!0),N(""),b&&a?await m.kw.updateStrumento(s,a.id_strumento,d):await m.kw.createStrumento(s,d),t()}catch(e){var i,l;N((null==(l=e.response)||null==(i=l.data)?void 0:i.detail)||"Errore durante il salvataggio")}finally{u(!1)}},y=(e,s)=>{x(a=>({...a,[e]:s})),f[e]&&g(s=>{let a={...s};return delete a[e],a})},A=e=>{if(y("data_calibrazione",e),e&&!d.data_scadenza_calibrazione){let s=new Date(new Date(e));s.setFullYear(s.getFullYear()+1),y("data_scadenza_calibrazione",s.toISOString().split("T")[0])}};return(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("h2",{className:"text-2xl font-bold text-slate-900 flex items-center gap-3",children:[(0,i.jsx)(p.A,{className:"h-6 w-6 text-blue-600"}),b?"Modifica Strumento":"Nuovo Strumento"]}),(0,i.jsx)("p",{className:"text-slate-600 mt-1",children:b?"Modifica i dati dello strumento esistente":"Aggiungi un nuovo strumento di misura"})]}),(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsxs)(n.$,{variant:"outline",onClick:c,children:[(0,i.jsx)(R.A,{className:"h-4 w-4 mr-2"}),"Annulla"]}),(0,i.jsxs)(n.$,{onClick:w,disabled:j,children:[j?(0,i.jsx)(h.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,i.jsx)(T.A,{className:"h-4 w-4 mr-2"}),b?"Aggiorna":"Salva"]})]})]}),(0,i.jsxs)("form",{onSubmit:w,className:"space-y-6",children:[(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsx)(r.ZB,{children:"Informazioni Base"}),(0,i.jsx)(r.BT,{children:"Dati identificativi dello strumento"})]}),(0,i.jsx)(r.Wu,{className:"space-y-4",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(S.J,{htmlFor:"nome",children:"Nome Strumento *"}),(0,i.jsx)(o.p,{id:"nome",value:d.nome,onChange:e=>y("nome",e.target.value),className:f.nome?"border-red-500":"",placeholder:"es. Megger MFT1741"}),f.nome&&(0,i.jsx)("p",{className:"text-sm text-red-600",children:f.nome})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(S.J,{htmlFor:"tipo_strumento",children:"Tipo Strumento"}),(0,i.jsxs)(E.l6,{value:d.tipo_strumento,onValueChange:e=>y("tipo_strumento",e),children:[(0,i.jsx)(E.bq,{children:(0,i.jsx)(E.yv,{})}),(0,i.jsxs)(E.gC,{children:[(0,i.jsx)(E.eb,{value:"MEGGER",children:"Megger"}),(0,i.jsx)(E.eb,{value:"MULTIMETRO",children:"Multimetro"}),(0,i.jsx)(E.eb,{value:"OSCILLOSCOPIO",children:"Oscilloscopio"}),(0,i.jsx)(E.eb,{value:"ALTRO",children:"Altro"})]})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(S.J,{htmlFor:"marca",children:"Marca *"}),(0,i.jsx)(o.p,{id:"marca",value:d.marca,onChange:e=>y("marca",e.target.value),className:f.marca?"border-red-500":"",placeholder:"es. Fluke, Megger, Keysight"}),f.marca&&(0,i.jsx)("p",{className:"text-sm text-red-600",children:f.marca})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(S.J,{htmlFor:"modello",children:"Modello *"}),(0,i.jsx)(o.p,{id:"modello",value:d.modello,onChange:e=>y("modello",e.target.value),className:f.modello?"border-red-500":"",placeholder:"es. MFT1741, 87V"}),f.modello&&(0,i.jsx)("p",{className:"text-sm text-red-600",children:f.modello})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(S.J,{htmlFor:"numero_serie",children:"Numero Serie *"}),(0,i.jsx)(o.p,{id:"numero_serie",value:d.numero_serie,onChange:e=>y("numero_serie",e.target.value),className:f.numero_serie?"border-red-500":"",placeholder:"Numero di serie univoco"}),f.numero_serie&&(0,i.jsx)("p",{className:"text-sm text-red-600",children:f.numero_serie})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(S.J,{htmlFor:"stato_strumento",children:"Stato"}),(0,i.jsxs)(E.l6,{value:d.stato_strumento,onValueChange:e=>y("stato_strumento",e),children:[(0,i.jsx)(E.bq,{children:(0,i.jsx)(E.yv,{})}),(0,i.jsxs)(E.gC,{children:[(0,i.jsx)(E.eb,{value:"ATTIVO",children:"Attivo"}),(0,i.jsx)(E.eb,{value:"SCADUTO",children:"Scaduto"}),(0,i.jsx)(E.eb,{value:"FUORI_SERVIZIO",children:"Fuori Servizio"})]})]})]})]})})]}),(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsx)(r.ZB,{children:"Calibrazione"}),(0,i.jsx)(r.BT,{children:"Informazioni sulla calibrazione dello strumento"})]}),(0,i.jsx)(r.Wu,{className:"space-y-4",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(S.J,{htmlFor:"data_calibrazione",children:"Data Calibrazione *"}),(0,i.jsx)(o.p,{id:"data_calibrazione",type:"date",value:d.data_calibrazione,onChange:e=>A(e.target.value),className:f.data_calibrazione?"border-red-500":""}),f.data_calibrazione&&(0,i.jsx)("p",{className:"text-sm text-red-600",children:f.data_calibrazione})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(S.J,{htmlFor:"data_scadenza_calibrazione",children:"Data Scadenza *"}),(0,i.jsx)(o.p,{id:"data_scadenza_calibrazione",type:"date",value:d.data_scadenza_calibrazione,onChange:e=>y("data_scadenza_calibrazione",e.target.value),className:f.data_scadenza_calibrazione?"border-red-500":""}),f.data_scadenza_calibrazione&&(0,i.jsx)("p",{className:"text-sm text-red-600",children:f.data_scadenza_calibrazione})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(S.J,{htmlFor:"ente_certificatore",children:"Ente Certificatore"}),(0,i.jsx)(o.p,{id:"ente_certificatore",value:d.ente_certificatore,onChange:e=>y("ente_certificatore",e.target.value),placeholder:"es. LAT 123, ACCREDIA"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(S.J,{htmlFor:"numero_certificato_calibrazione",children:"Numero Certificato"}),(0,i.jsx)(o.p,{id:"numero_certificato_calibrazione",value:d.numero_certificato_calibrazione,onChange:e=>y("numero_certificato_calibrazione",e.target.value),placeholder:"Numero del certificato di calibrazione"})]})]})})]}),(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsx)(r.ZB,{children:"Specifiche Tecniche"}),(0,i.jsx)(r.BT,{children:"Caratteristiche tecniche dello strumento"})]}),(0,i.jsxs)(r.Wu,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(S.J,{htmlFor:"range_misura",children:"Range di Misura"}),(0,i.jsx)(o.p,{id:"range_misura",value:d.range_misura,onChange:e=>y("range_misura",e.target.value),placeholder:"es. 0-1000V, 0-20A"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(S.J,{htmlFor:"precisione",children:"Precisione"}),(0,i.jsx)(o.p,{id:"precisione",value:d.precisione,onChange:e=>y("precisione",e.target.value),placeholder:"es. \xb10.1%, \xb12 digit"})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(S.J,{htmlFor:"note",children:"Note"}),(0,i.jsx)(I.T,{id:"note",value:d.note,onChange:e=>y("note",e.target.value),placeholder:"Note aggiuntive sullo strumento...",rows:3})]})]})]}),v&&(0,i.jsxs)(C.Fc,{className:"border-red-200 bg-red-50",children:[(0,i.jsx)(_.A,{className:"h-4 w-4 text-red-600"}),(0,i.jsx)(C.TN,{className:"text-red-800",children:v})]})]})]})}function F(e){let{cantiereId:s,strumenti:a,onUpdate:t}=e,[d,j]=(0,l.useState)(""),[u,v]=(0,l.useState)(!1),[N,A]=(0,l.useState)(null),[S,I]=(0,l.useState)(!1),[E,R]=(0,l.useState)(""),T=e=>{A(e),v(!0)},F=async e=>{if(confirm("Sei sicuro di voler eliminare questo strumento?"))try{I(!0),await m.kw.deleteStrumento(s,e),t()}catch(e){var a,i;R((null==(i=e.response)||null==(a=i.data)?void 0:a.detail)||"Errore durante l'eliminazione")}finally{I(!1)}},O=e=>{let s=new Date,a=Math.ceil((new Date(e.data_scadenza_calibrazione).getTime()-s.getTime())/864e5);return"FUORI_SERVIZIO"===e.stato_strumento?(0,i.jsx)(c.E,{className:"bg-gray-100 text-gray-800 border-gray-200",children:"Fuori Servizio"}):a<0?(0,i.jsx)(c.E,{className:"bg-red-100 text-red-800 border-red-200",children:"Scaduto"}):a<=30?(0,i.jsx)(c.E,{className:"bg-yellow-100 text-yellow-800 border-yellow-200",children:"In Scadenza"}):(0,i.jsx)(c.E,{className:"bg-green-100 text-green-800 border-green-200",children:"Attivo"})},D=e=>{let s=new Date,a=Math.ceil((new Date(e.data_scadenza_calibrazione).getTime()-s.getTime())/864e5);return"FUORI_SERVIZIO"===e.stato_strumento||a<0?(0,i.jsx)(_.A,{className:"h-4 w-4 text-red-500"}):a<=30?(0,i.jsx)(z.A,{className:"h-4 w-4 text-yellow-500"}):(0,i.jsx)(b.A,{className:"h-4 w-4 text-green-500"})},B=a.filter(e=>{var s,a,i,t;let l=d.toLowerCase();return(null==(s=e.nome)?void 0:s.toLowerCase().includes(l))||(null==(a=e.marca)?void 0:a.toLowerCase().includes(l))||(null==(i=e.modello)?void 0:i.toLowerCase().includes(l))||(null==(t=e.numero_serie)?void 0:t.toLowerCase().includes(l))}),L={totali:a.length,attivi:a.filter(e=>{let s=new Date,a=new Date(e.data_scadenza_calibrazione);return"ATTIVO"===e.stato_strumento&&a>s}).length,scaduti:a.filter(e=>{let s=new Date;return new Date(e.data_scadenza_calibrazione)<=s}).length,in_scadenza:a.filter(e=>{let s=new Date,a=Math.ceil((new Date(e.data_scadenza_calibrazione).getTime()-s.getTime())/864e5);return a>0&&a<=30}).length};return u?(0,i.jsx)(Z,{cantiereId:s,strumento:N,onSuccess:()=>{v(!1),t()},onCancel:()=>v(!1)}):(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("h2",{className:"text-2xl font-bold text-slate-900 flex items-center gap-3",children:[(0,i.jsx)(p.A,{className:"h-6 w-6 text-blue-600"}),"Gestione Strumenti"]}),(0,i.jsx)("p",{className:"text-slate-600 mt-1",children:"Strumenti di misura e calibrazione"})]}),(0,i.jsxs)(n.$,{onClick:()=>{A(null),v(!0)},children:[(0,i.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Nuovo Strumento"]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,i.jsx)(r.Zp,{children:(0,i.jsx)(r.Wu,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Totali"}),(0,i.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:L.totali})]}),(0,i.jsx)(p.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,i.jsx)(r.Zp,{children:(0,i.jsx)(r.Wu,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Attivi"}),(0,i.jsx)("p",{className:"text-2xl font-bold text-green-600",children:L.attivi})]}),(0,i.jsx)(b.A,{className:"h-8 w-8 text-green-500"})]})})}),(0,i.jsx)(r.Zp,{children:(0,i.jsx)(r.Wu,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"In Scadenza"}),(0,i.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:L.in_scadenza})]}),(0,i.jsx)(z.A,{className:"h-8 w-8 text-yellow-500"})]})})}),(0,i.jsx)(r.Zp,{children:(0,i.jsx)(r.Wu,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Scaduti"}),(0,i.jsx)("p",{className:"text-2xl font-bold text-red-600",children:L.scaduti})]}),(0,i.jsx)(_.A,{className:"h-8 w-8 text-red-500"})]})})})]}),(0,i.jsxs)(r.Zp,{children:[(0,i.jsx)(r.aR,{children:(0,i.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(f.A,{className:"h-5 w-5"}),"Ricerca Strumenti"]})}),(0,i.jsx)(r.Wu,{children:(0,i.jsx)(o.p,{placeholder:"Cerca per nome, marca, modello o numero serie...",value:d,onChange:e=>j(e.target.value)})})]}),(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsxs)(r.ZB,{children:["Elenco Strumenti (",B.length,")"]}),(0,i.jsx)(r.BT,{children:"Gestione strumenti di misura e certificazione"})]}),(0,i.jsx)(r.Wu,{children:(0,i.jsx)("div",{className:"rounded-md border",children:(0,i.jsxs)(x.XI,{children:[(0,i.jsx)(x.A0,{children:(0,i.jsxs)(x.Hj,{children:[(0,i.jsx)(x.nd,{children:"Nome"}),(0,i.jsx)(x.nd,{children:"Marca/Modello"}),(0,i.jsx)(x.nd,{children:"Numero Serie"}),(0,i.jsx)(x.nd,{children:"Tipo"}),(0,i.jsx)(x.nd,{children:"Calibrazione"}),(0,i.jsx)(x.nd,{children:"Scadenza"}),(0,i.jsx)(x.nd,{children:"Stato"}),(0,i.jsx)(x.nd,{children:"Azioni"})]})}),(0,i.jsx)(x.BF,{children:S?(0,i.jsx)(x.Hj,{children:(0,i.jsx)(x.nA,{colSpan:8,className:"text-center py-8",children:(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,i.jsx)(h.A,{className:"h-4 w-4 animate-spin"}),"Caricamento..."]})})}):0===B.length?(0,i.jsx)(x.Hj,{children:(0,i.jsx)(x.nA,{colSpan:8,className:"text-center py-8 text-slate-500",children:"Nessuno strumento trovato"})}):B.map(e=>{let s=new Date,a=Math.ceil((new Date(e.data_scadenza_calibrazione).getTime()-s.getTime())/864e5);return(0,i.jsxs)(x.Hj,{children:[(0,i.jsx)(x.nA,{className:"font-medium",children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[D(e),e.nome]})}),(0,i.jsx)(x.nA,{children:(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-medium",children:e.marca}),(0,i.jsx)("div",{className:"text-sm text-slate-500",children:e.modello})]})}),(0,i.jsx)(x.nA,{className:"font-mono text-sm",children:e.numero_serie}),(0,i.jsx)(x.nA,{children:(0,i.jsx)(c.E,{variant:"outline",children:e.tipo_strumento||"N/A"})}),(0,i.jsx)(x.nA,{children:new Date(e.data_calibrazione).toLocaleDateString("it-IT")}),(0,i.jsx)(x.nA,{children:(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{children:new Date(e.data_scadenza_calibrazione).toLocaleDateString("it-IT")}),a>0&&a<=30&&(0,i.jsxs)("div",{className:"text-xs text-yellow-600",children:[a," giorni rimanenti"]}),a<0&&(0,i.jsxs)("div",{className:"text-xs text-red-600",children:["Scaduto da ",Math.abs(a)," giorni"]})]})}),(0,i.jsx)(x.nA,{children:O(e)}),(0,i.jsx)(x.nA,{children:(0,i.jsxs)("div",{className:"flex gap-1",children:[(0,i.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>T(e),children:(0,i.jsx)(w.A,{className:"h-4 w-4"})}),(0,i.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>F(e.id_strumento),children:(0,i.jsx)(y.A,{className:"h-4 w-4"})})]})})]},e.id_strumento)})})]})})})]}),E&&(0,i.jsxs)(C.Fc,{className:"border-red-200 bg-red-50",children:[(0,i.jsx)(_.A,{className:"h-4 w-4 text-red-600"}),(0,i.jsx)(C.TN,{className:"text-red-800",children:E})]})]})}var O=a(53904),D=a(91788);function B(e){let{cantiereId:s,rapporti:a,onUpdate:t}=e,[d,j]=(0,l.useState)(""),[p,N]=(0,l.useState)(!1),[z,A]=(0,l.useState)(""),S=e=>{console.log("Modifica rapporto",e)},I=async e=>{if(confirm("Sei sicuro di voler eliminare questo rapporto?"))try{N(!0),await m.l9.deleteRapporto(s,e),t()}catch(e){var a,i;A((null==(i=e.response)||null==(a=i.data)?void 0:a.detail)||"Errore durante l'eliminazione")}finally{N(!1)}},E=async e=>{try{N(!0),await m.l9.aggiornaStatistiche(s,e),t()}catch(e){var a,i;A((null==(i=e.response)||null==(a=i.data)?void 0:a.detail)||"Errore durante l'aggiornamento")}finally{N(!1)}},R=e=>{switch(null==e?void 0:e.toLowerCase()){case"completato":return(0,i.jsx)(c.E,{className:"bg-green-100 text-green-800 border-green-200",children:"Completato"});case"approvato":return(0,i.jsx)(c.E,{className:"bg-blue-100 text-blue-800 border-blue-200",children:"Approvato"});case"bozza":return(0,i.jsx)(c.E,{className:"bg-gray-100 text-gray-800 border-gray-200",children:"Bozza"});default:return(0,i.jsx)(c.E,{className:"bg-gray-100 text-gray-800 border-gray-200",children:"Da Verificare"})}},T=e=>{if(0===e.numero_cavi_totali)return(0,i.jsx)(c.E,{variant:"outline",children:"Nessun Cavo"});let s=e.numero_cavi_conformi/e.numero_cavi_totali*100;return 100===s?(0,i.jsx)(c.E,{className:"bg-green-100 text-green-800 border-green-200",children:"100% Conforme"}):s>=90?(0,i.jsxs)(c.E,{className:"bg-yellow-100 text-yellow-800 border-yellow-200",children:[s.toFixed(1),"% Conforme"]}):(0,i.jsxs)(c.E,{className:"bg-red-100 text-red-800 border-red-200",children:[s.toFixed(1),"% Conforme"]})},Z=a.filter(e=>{var s,a,i;let t=d.toLowerCase();return(null==(s=e.numero_rapporto)?void 0:s.toLowerCase().includes(t))||(null==(a=e.nome_progetto)?void 0:a.toLowerCase().includes(t))||(null==(i=e.cliente_finale)?void 0:i.toLowerCase().includes(t))}),F={totali:a.length,completati:a.filter(e=>"COMPLETATO"===e.stato_rapporto).length,approvati:a.filter(e=>"APPROVATO"===e.stato_rapporto).length,bozze:a.filter(e=>"BOZZA"===e.stato_rapporto).length,cavi_totali:a.reduce((e,s)=>e+s.numero_cavi_totali,0),cavi_conformi:a.reduce((e,s)=>e+s.numero_cavi_conformi,0)};return(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("h2",{className:"text-2xl font-bold text-slate-900 flex items-center gap-3",children:[(0,i.jsx)(v.A,{className:"h-6 w-6 text-blue-600"}),"Rapporti Generali di Collaudo"]}),(0,i.jsx)("p",{className:"text-slate-600 mt-1",children:"Gestione rapporti generali CEI 64-8"})]}),(0,i.jsxs)(n.$,{onClick:()=>{console.log("Crea nuovo rapporto")},children:[(0,i.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Nuovo Rapporto"]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,i.jsx)(r.Zp,{children:(0,i.jsx)(r.Wu,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Totali"}),(0,i.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:F.totali})]}),(0,i.jsx)(u.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,i.jsx)(r.Zp,{children:(0,i.jsx)(r.Wu,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Completati"}),(0,i.jsx)("p",{className:"text-2xl font-bold text-green-600",children:F.completati})]}),(0,i.jsx)(b.A,{className:"h-8 w-8 text-green-500"})]})})}),(0,i.jsx)(r.Zp,{children:(0,i.jsx)(r.Wu,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Approvati"}),(0,i.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:F.approvati})]}),(0,i.jsx)(v.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,i.jsx)(r.Zp,{children:(0,i.jsx)(r.Wu,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Cavi Totali"}),(0,i.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:F.cavi_totali})]}),(0,i.jsx)(u.A,{className:"h-8 w-8 text-slate-500"})]})})}),(0,i.jsx)(r.Zp,{children:(0,i.jsx)(r.Wu,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Conformit\xe0"}),(0,i.jsxs)("p",{className:"text-2xl font-bold text-green-600",children:[F.cavi_totali>0?(F.cavi_conformi/F.cavi_totali*100).toFixed(1):0,"%"]})]}),(0,i.jsx)(b.A,{className:"h-8 w-8 text-green-500"})]})})})]}),(0,i.jsxs)(r.Zp,{children:[(0,i.jsx)(r.aR,{children:(0,i.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(f.A,{className:"h-5 w-5"}),"Ricerca Rapporti"]})}),(0,i.jsx)(r.Wu,{children:(0,i.jsx)(o.p,{placeholder:"Cerca per numero rapporto, progetto o cliente...",value:d,onChange:e=>j(e.target.value)})})]}),(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsxs)(r.ZB,{children:["Elenco Rapporti (",Z.length,")"]}),(0,i.jsx)(r.BT,{children:"Gestione rapporti generali di collaudo"})]}),(0,i.jsx)(r.Wu,{children:(0,i.jsx)("div",{className:"rounded-md border",children:(0,i.jsxs)(x.XI,{children:[(0,i.jsx)(x.A0,{children:(0,i.jsxs)(x.Hj,{children:[(0,i.jsx)(x.nd,{children:"Numero Rapporto"}),(0,i.jsx)(x.nd,{children:"Progetto"}),(0,i.jsx)(x.nd,{children:"Cliente"}),(0,i.jsx)(x.nd,{children:"Data"}),(0,i.jsx)(x.nd,{children:"Cavi"}),(0,i.jsx)(x.nd,{children:"Conformit\xe0"}),(0,i.jsx)(x.nd,{children:"Stato"}),(0,i.jsx)(x.nd,{children:"Azioni"})]})}),(0,i.jsx)(x.BF,{children:p?(0,i.jsx)(x.Hj,{children:(0,i.jsx)(x.nA,{colSpan:8,className:"text-center py-8",children:(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,i.jsx)(h.A,{className:"h-4 w-4 animate-spin"}),"Caricamento..."]})})}):0===Z.length?(0,i.jsx)(x.Hj,{children:(0,i.jsx)(x.nA,{colSpan:8,className:"text-center py-8 text-slate-500",children:"Nessun rapporto trovato"})}):Z.map(e=>(0,i.jsxs)(x.Hj,{children:[(0,i.jsx)(x.nA,{className:"font-medium",children:e.numero_rapporto}),(0,i.jsx)(x.nA,{children:(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-medium",children:e.nome_progetto||"-"}),(0,i.jsx)("div",{className:"text-sm text-slate-500",children:e.societa_installatrice||"-"})]})}),(0,i.jsx)(x.nA,{children:e.cliente_finale||"-"}),(0,i.jsx)(x.nA,{children:new Date(e.data_rapporto).toLocaleDateString("it-IT")}),(0,i.jsx)(x.nA,{children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"font-medium",children:e.numero_cavi_totali}),(0,i.jsxs)("div",{className:"text-xs text-slate-500",children:[e.numero_cavi_conformi,"C / ",e.numero_cavi_non_conformi,"NC"]})]})}),(0,i.jsx)(x.nA,{children:T(e)}),(0,i.jsx)(x.nA,{children:R(e.stato_rapporto)}),(0,i.jsx)(x.nA,{children:(0,i.jsxs)("div",{className:"flex gap-1",children:[(0,i.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>S(e),title:"Visualizza/Modifica",children:(0,i.jsx)(w.A,{className:"h-4 w-4"})}),(0,i.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>E(e.id_rapporto),title:"Aggiorna Statistiche",children:(0,i.jsx)(O.A,{className:"h-4 w-4"})}),(0,i.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>{},title:"Genera PDF",children:(0,i.jsx)(D.A,{className:"h-4 w-4"})}),(0,i.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>I(e.id_rapporto),title:"Elimina",children:(0,i.jsx)(y.A,{className:"h-4 w-4"})})]})})]},e.id_rapporto))})]})})})]}),z&&(0,i.jsxs)(C.Fc,{className:"border-red-200 bg-red-50",children:[(0,i.jsx)(_.A,{className:"h-4 w-4 text-red-600"}),(0,i.jsx)(C.TN,{className:"text-red-800",children:z})]})]})}var L=a(24944),k=a(16785),M=a(33109);function W(e){let{stats:s,detailed:a=!1}=e,t=s.totali>0?s.conformi/s.totali*100:0,l=s.totali>0?(s.conformi+s.non_conformi+s.con_riserva)/s.totali*100:0;return(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,i.jsx)(r.Zp,{children:(0,i.jsx)(r.Wu,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Totali"}),(0,i.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:s.totali})]}),(0,i.jsx)(u.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,i.jsx)(r.Zp,{children:(0,i.jsx)(r.Wu,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Conformi"}),(0,i.jsx)("p",{className:"text-2xl font-bold text-green-600",children:s.conformi}),(0,i.jsx)("p",{className:"text-xs text-slate-500",children:s.totali>0?"".concat((s.conformi/s.totali*100).toFixed(1),"%"):"0%"})]}),(0,i.jsx)(b.A,{className:"h-8 w-8 text-green-500"})]})})}),(0,i.jsx)(r.Zp,{children:(0,i.jsx)(r.Wu,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Non Conformi"}),(0,i.jsx)("p",{className:"text-2xl font-bold text-red-600",children:s.non_conformi}),(0,i.jsx)("p",{className:"text-xs text-slate-500",children:s.totali>0?"".concat((s.non_conformi/s.totali*100).toFixed(1),"%"):"0%"})]}),(0,i.jsx)(_.A,{className:"h-8 w-8 text-red-500"})]})})}),(0,i.jsx)(r.Zp,{children:(0,i.jsx)(r.Wu,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Bozze"}),(0,i.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:s.bozze}),(0,i.jsx)("p",{className:"text-xs text-slate-500",children:s.totali>0?"".concat((s.bozze/s.totali*100).toFixed(1),"%"):"0%"})]}),(0,i.jsx)(z.A,{className:"h-8 w-8 text-yellow-500"})]})})})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(k.A,{className:"h-5 w-5 text-blue-600"}),"Tasso di Conformit\xe0"]}),(0,i.jsx)(r.BT,{children:"Percentuale di certificazioni conformi sul totale"})]}),(0,i.jsx)(r.Wu,{children:(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("span",{className:"text-sm font-medium",children:"Conformit\xe0"}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[t>=95?(0,i.jsx)(b.A,{className:"h-5 w-5 text-green-600"}):t>=85?(0,i.jsx)(N.A,{className:"h-5 w-5 text-yellow-600"}):(0,i.jsx)(_.A,{className:"h-5 w-5 text-red-600"}),(0,i.jsxs)("span",{className:"text-lg font-bold ".concat(t>=95?"text-green-600":t>=85?"text-yellow-600":"text-red-600"),children:[t.toFixed(1),"%"]})]})]}),(0,i.jsx)(L.k,{value:t,className:"h-2"}),(0,i.jsxs)("div",{className:"flex justify-between text-xs text-slate-500",children:[(0,i.jsx)("span",{children:"Target: 95%"}),(0,i.jsxs)("span",{children:[s.conformi," / ",s.totali]})]})]})})]}),(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(M.A,{className:"h-5 w-5 text-green-600"}),"Completamento Certificazioni"]}),(0,i.jsx)(r.BT,{children:"Percentuale di certificazioni completate (non bozze)"})]}),(0,i.jsx)(r.Wu,{children:(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("span",{className:"text-sm font-medium",children:"Completamento"}),(0,i.jsxs)("span",{className:"text-lg font-bold text-blue-600",children:[l.toFixed(1),"%"]})]}),(0,i.jsx)(L.k,{value:l,className:"h-2"}),(0,i.jsxs)("div",{className:"flex justify-between text-xs text-slate-500",children:[(0,i.jsxs)("span",{children:["Completate: ",s.conformi+s.non_conformi+s.con_riserva]}),(0,i.jsxs)("span",{children:["Bozze: ",s.bozze]})]})]})})]})]}),a&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,i.jsxs)(r.Zp,{children:[(0,i.jsx)(r.aR,{children:(0,i.jsx)(r.ZB,{className:"text-lg",children:"Distribuzione Stati"})}),(0,i.jsx)(r.Wu,{children:(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),(0,i.jsx)("span",{className:"text-sm",children:"Conformi"})]}),(0,i.jsx)(c.E,{variant:"outline",className:"text-green-600 border-green-200",children:s.conformi})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)("div",{className:"w-3 h-3 bg-red-500 rounded-full"}),(0,i.jsx)("span",{className:"text-sm",children:"Non Conformi"})]}),(0,i.jsx)(c.E,{variant:"outline",className:"text-red-600 border-red-200",children:s.non_conformi})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)("div",{className:"w-3 h-3 bg-yellow-500 rounded-full"}),(0,i.jsx)("span",{className:"text-sm",children:"Con Riserva"})]}),(0,i.jsx)(c.E,{variant:"outline",className:"text-yellow-600 border-yellow-200",children:s.con_riserva})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)("div",{className:"w-3 h-3 bg-gray-500 rounded-full"}),(0,i.jsx)("span",{className:"text-sm",children:"Bozze"})]}),(0,i.jsx)(c.E,{variant:"outline",className:"text-gray-600 border-gray-200",children:s.bozze})]})]})})]}),(0,i.jsxs)(r.Zp,{children:[(0,i.jsx)(r.aR,{children:(0,i.jsx)(r.ZB,{className:"text-lg",children:"Indicatori Qualit\xe0"})}),(0,i.jsx)(r.Wu,{children:(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,i.jsx)("span",{children:"Tasso Successo"}),(0,i.jsxs)("span",{className:"font-medium",children:[s.totali>0?((s.conformi+s.con_riserva)/s.totali*100).toFixed(1):0,"%"]})]}),(0,i.jsx)(L.k,{value:s.totali>0?(s.conformi+s.con_riserva)/s.totali*100:0,className:"h-2"})]}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,i.jsx)("span",{children:"Tasso Fallimento"}),(0,i.jsxs)("span",{className:"font-medium text-red-600",children:[s.totali>0?(s.non_conformi/s.totali*100).toFixed(1):0,"%"]})]}),(0,i.jsx)(L.k,{value:s.totali>0?s.non_conformi/s.totali*100:0,className:"h-2"})]})]})})]}),(0,i.jsxs)(r.Zp,{children:[(0,i.jsx)(r.aR,{children:(0,i.jsxs)(r.ZB,{className:"text-lg flex items-center gap-2",children:[(0,i.jsx)(j.A,{className:"h-5 w-5 text-yellow-500"}),"Valutazione Complessiva"]})}),(0,i.jsx)(r.Wu,{children:(0,i.jsxs)("div",{className:"text-center space-y-2",children:[(0,i.jsx)("div",{className:"text-3xl font-bold",children:t>=95?"\uD83C\uDFC6":t>=85?"⭐":t>=70?"\uD83D\uDC4D":"⚠️"}),(0,i.jsx)("div",{className:"text-lg font-semibold",children:t>=95?"Eccellente":t>=85?"Buono":t>=70?"Sufficiente":"Da Migliorare"}),(0,i.jsx)("div",{className:"text-sm text-slate-600",children:t>=95?"Qualit\xe0 certificazioni ottimale":t>=85?"Qualit\xe0 certificazioni buona":t>=70?"Qualit\xe0 certificazioni accettabile":"Necessario miglioramento qualit\xe0"})]})})]})]}),(t<95||s.bozze>0)&&(0,i.jsxs)(r.Zp,{children:[(0,i.jsx)(r.aR,{children:(0,i.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(N.A,{className:"h-5 w-5 text-yellow-600"}),"Raccomandazioni"]})}),(0,i.jsx)(r.Wu,{children:(0,i.jsxs)("div",{className:"space-y-2",children:[t<95&&(0,i.jsxs)("div",{className:"flex items-start gap-2 text-sm",children:[(0,i.jsx)("div",{className:"w-2 h-2 bg-yellow-500 rounded-full mt-2"}),(0,i.jsxs)("span",{children:["Il tasso di conformit\xe0 \xe8 del ",t.toFixed(1),"%. Obiettivo raccomandato: 95%. Verificare procedure di test e qualit\xe0 installazioni."]})]}),s.bozze>0&&(0,i.jsxs)("div",{className:"flex items-start gap-2 text-sm",children:[(0,i.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full mt-2"}),(0,i.jsxs)("span",{children:["Ci sono ",s.bozze," certificazioni in bozza. Completare le certificazioni per avere dati accurati."]})]}),s.non_conformi>s.conformi&&(0,i.jsxs)("div",{className:"flex items-start gap-2 text-sm",children:[(0,i.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full mt-2"}),(0,i.jsx)("span",{children:"Il numero di certificazioni non conformi supera quelle conformi. Rivedere urgentemente le procedure di installazione e test."})]})]})})]})]})]})}var V=a(92657);function J(e){let{cantiereId:s,nonConformita:a,onSuccess:t,onCancel:c}=e,[d,x]=(0,l.useState)({id_cavo:"",tipo_non_conformita:"",descrizione:"",severita:"MEDIA",stato:"APERTA",responsabile_rilevazione:"",data_rilevazione:new Date().toISOString().split("T")[0],azione_correttiva:"",responsabile_risoluzione:"",data_risoluzione:null,note:""}),[j,u]=(0,l.useState)([]),[p,v]=(0,l.useState)([]),[f,g]=(0,l.useState)(!1),[b,z]=(0,l.useState)(!1),[w,y]=(0,l.useState)(""),[A,Z]=(0,l.useState)({}),F=!!a;(0,l.useEffect)(()=>{O(),a&&x({id_cavo:a.id_cavo||"",tipo_non_conformita:a.tipo_non_conformita||"",descrizione:a.descrizione||"",severita:a.severita||"MEDIA",stato:a.stato||"APERTA",responsabile_rilevazione:a.responsabile_rilevazione||"",data_rilevazione:a.data_rilevazione?new Date(a.data_rilevazione).toISOString().split("T")[0]:new Date().toISOString().split("T")[0],azione_correttiva:a.azione_correttiva||"",responsabile_risoluzione:a.responsabile_risoluzione||"",data_risoluzione:a.data_risoluzione?new Date(a.data_risoluzione).toISOString().split("T")[0]:null,note:a.note||""})},[a]);let O=async()=>{try{g(!0);let[e,a]=await Promise.all([m.At.getCavi(s),m.AR.getResponsabili(s)]);u(e),v(a)}catch(s){var e,a;y((null==(a=s.response)||null==(e=a.data)?void 0:e.detail)||"Errore durante il caricamento dei dati")}finally{g(!1)}},D=()=>{let e={};return d.id_cavo||(e.id_cavo="ID Cavo \xe8 obbligatorio"),d.tipo_non_conformita||(e.tipo_non_conformita="Tipo non conformit\xe0 \xe8 obbligatorio"),d.descrizione||(e.descrizione="Descrizione \xe8 obbligatoria"),d.responsabile_rilevazione||(e.responsabile_rilevazione="Responsabile rilevazione \xe8 obbligatorio"),Z(e),0===Object.keys(e).length},B=async e=>{if(e.preventDefault(),D())try{z(!0),y("");let e={...d,data_risoluzione:d.data_risoluzione||null};F&&a?await m.om.updateNonConformita(s,a.id_non_conformita,e):await m.om.createNonConformita(s,e),t()}catch(e){var i,l;y((null==(l=e.response)||null==(i=l.data)?void 0:i.detail)||"Errore durante il salvataggio")}finally{z(!1)}},L=(e,s)=>{x(a=>({...a,[e]:s})),A[e]&&Z(s=>{let a={...s};return delete a[e],a})};return f?(0,i.jsx)("div",{className:"flex items-center justify-center p-8",children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(h.A,{className:"h-4 w-4 animate-spin"}),"Caricamento dati..."]})}):(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsx)("div",{className:"flex items-center justify-between",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("h2",{className:"text-2xl font-bold text-slate-900 flex items-center gap-3",children:[(0,i.jsx)(N.A,{className:"h-6 w-6 text-red-600"}),F?"Modifica Non Conformit\xe0":"Nuova Non Conformit\xe0"]}),(0,i.jsx)("p",{className:"text-slate-600 mt-1",children:F?"Aggiorna i dettagli della non conformit\xe0":"Registra una nuova non conformit\xe0"})]})}),(0,i.jsxs)("form",{onSubmit:B,className:"space-y-6",children:[(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsx)(r.ZB,{children:"Informazioni Base"}),(0,i.jsx)(r.BT,{children:"Dettagli principali della non conformit\xe0"})]}),(0,i.jsxs)(r.Wu,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(S.J,{htmlFor:"id_cavo",children:"ID Cavo *"}),(0,i.jsxs)(E.l6,{value:d.id_cavo,onValueChange:e=>L("id_cavo",e),children:[(0,i.jsx)(E.bq,{className:A.id_cavo?"border-red-500":"",children:(0,i.jsx)(E.yv,{placeholder:"Seleziona cavo"})}),(0,i.jsx)(E.gC,{children:j.map(e=>(0,i.jsxs)(E.eb,{value:e.id_cavo,children:[e.id_cavo," ",e.tipologia&&"- ".concat(e.tipologia)]},e.id_cavo))})]}),A.id_cavo&&(0,i.jsx)("p",{className:"text-sm text-red-600",children:A.id_cavo})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(S.J,{htmlFor:"tipo_non_conformita",children:"Tipo Non Conformit\xe0 *"}),(0,i.jsxs)(E.l6,{value:d.tipo_non_conformita,onValueChange:e=>L("tipo_non_conformita",e),children:[(0,i.jsx)(E.bq,{className:A.tipo_non_conformita?"border-red-500":"",children:(0,i.jsx)(E.yv,{placeholder:"Seleziona tipo"})}),(0,i.jsxs)(E.gC,{children:[(0,i.jsx)(E.eb,{value:"ISOLAMENTO",children:"Isolamento"}),(0,i.jsx)(E.eb,{value:"CONTINUITA",children:"Continuit\xe0"}),(0,i.jsx)(E.eb,{value:"RESISTENZA",children:"Resistenza"}),(0,i.jsx)(E.eb,{value:"INSTALLAZIONE",children:"Installazione"}),(0,i.jsx)(E.eb,{value:"COLLEGAMENTO",children:"Collegamento"}),(0,i.jsx)(E.eb,{value:"DOCUMENTAZIONE",children:"Documentazione"}),(0,i.jsx)(E.eb,{value:"ALTRO",children:"Altro"})]})]}),A.tipo_non_conformita&&(0,i.jsx)("p",{className:"text-sm text-red-600",children:A.tipo_non_conformita})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(S.J,{htmlFor:"severita",children:"Severit\xe0"}),(0,i.jsxs)(E.l6,{value:d.severita,onValueChange:e=>L("severita",e),children:[(0,i.jsx)(E.bq,{children:(0,i.jsx)(E.yv,{})}),(0,i.jsxs)(E.gC,{children:[(0,i.jsx)(E.eb,{value:"BASSA",children:"Bassa"}),(0,i.jsx)(E.eb,{value:"MEDIA",children:"Media"}),(0,i.jsx)(E.eb,{value:"ALTA",children:"Alta"}),(0,i.jsx)(E.eb,{value:"CRITICA",children:"Critica"})]})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(S.J,{htmlFor:"stato",children:"Stato"}),(0,i.jsxs)(E.l6,{value:d.stato,onValueChange:e=>L("stato",e),children:[(0,i.jsx)(E.bq,{children:(0,i.jsx)(E.yv,{})}),(0,i.jsxs)(E.gC,{children:[(0,i.jsx)(E.eb,{value:"APERTA",children:"Aperta"}),(0,i.jsx)(E.eb,{value:"IN_RISOLUZIONE",children:"In Risoluzione"}),(0,i.jsx)(E.eb,{value:"RISOLTA",children:"Risolta"}),(0,i.jsx)(E.eb,{value:"CHIUSA",children:"Chiusa"})]})]})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(S.J,{htmlFor:"descrizione",children:"Descrizione *"}),(0,i.jsx)(I.T,{id:"descrizione",value:d.descrizione,onChange:e=>L("descrizione",e.target.value),className:A.descrizione?"border-red-500":"",placeholder:"Descrivi dettagliatamente la non conformit\xe0 rilevata...",rows:3}),A.descrizione&&(0,i.jsx)("p",{className:"text-sm text-red-600",children:A.descrizione})]})]})]}),(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsx)(r.ZB,{children:"Responsabili e Date"}),(0,i.jsx)(r.BT,{children:"Informazioni su rilevazione e risoluzione"})]}),(0,i.jsxs)(r.Wu,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(S.J,{htmlFor:"responsabile_rilevazione",children:"Responsabile Rilevazione *"}),(0,i.jsx)(o.p,{id:"responsabile_rilevazione",value:d.responsabile_rilevazione,onChange:e=>L("responsabile_rilevazione",e.target.value),className:A.responsabile_rilevazione?"border-red-500":"",placeholder:"Nome responsabile"}),A.responsabile_rilevazione&&(0,i.jsx)("p",{className:"text-sm text-red-600",children:A.responsabile_rilevazione})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(S.J,{htmlFor:"data_rilevazione",children:"Data Rilevazione"}),(0,i.jsx)(o.p,{id:"data_rilevazione",type:"date",value:d.data_rilevazione,onChange:e=>L("data_rilevazione",e.target.value)})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(S.J,{htmlFor:"responsabile_risoluzione",children:"Responsabile Risoluzione"}),(0,i.jsx)(o.p,{id:"responsabile_risoluzione",value:d.responsabile_risoluzione,onChange:e=>L("responsabile_risoluzione",e.target.value),placeholder:"Nome responsabile risoluzione"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(S.J,{htmlFor:"data_risoluzione",children:"Data Risoluzione"}),(0,i.jsx)(o.p,{id:"data_risoluzione",type:"date",value:d.data_risoluzione||"",onChange:e=>L("data_risoluzione",e.target.value||null)})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(S.J,{htmlFor:"azione_correttiva",children:"Azione Correttiva"}),(0,i.jsx)(I.T,{id:"azione_correttiva",value:d.azione_correttiva,onChange:e=>L("azione_correttiva",e.target.value),placeholder:"Descrivi l'azione correttiva intrapresa...",rows:2})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(S.J,{htmlFor:"note",children:"Note"}),(0,i.jsx)(I.T,{id:"note",value:d.note,onChange:e=>L("note",e.target.value),placeholder:"Note aggiuntive...",rows:2})]})]})]}),(0,i.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,i.jsxs)(n.$,{type:"button",variant:"outline",onClick:c,children:[(0,i.jsx)(R.A,{className:"h-4 w-4 mr-2"}),"Annulla"]}),(0,i.jsx)(n.$,{type:"submit",disabled:b,children:b?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(h.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Salvataggio..."]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(T.A,{className:"h-4 w-4 mr-2"}),F?"Aggiorna":"Crea"," Non Conformit\xe0"]})})]}),w&&(0,i.jsxs)(C.Fc,{className:"border-red-200 bg-red-50",children:[(0,i.jsx)(_.A,{className:"h-4 w-4 text-red-600"}),(0,i.jsx)(C.TN,{className:"text-red-800",children:w})]})]})]})}function $(e){let{cantiereId:s,nonConformita:a,onUpdate:t}=e,[d,j]=(0,l.useState)(""),[p,v]=(0,l.useState)("all"),[A,S]=(0,l.useState)(!1),[I,E]=(0,l.useState)(""),[R,T]=(0,l.useState)(!1),[Z,F]=(0,l.useState)(null),O=e=>{F(e),T(!0)},D=async e=>{if(confirm("Sei sicuro di voler eliminare questa non conformit\xe0?"))try{S(!0),await m.om.deleteNonConformita(s,e),t()}catch(e){var a,i;E((null==(i=e.response)||null==(a=i.data)?void 0:a.detail)||"Errore durante l'eliminazione")}finally{S(!1)}},B=e=>{switch(null==e?void 0:e.toLowerCase()){case"aperta":return(0,i.jsx)(c.E,{className:"bg-red-100 text-red-800 border-red-200",children:"Aperta"});case"in_risoluzione":return(0,i.jsx)(c.E,{className:"bg-yellow-100 text-yellow-800 border-yellow-200",children:"In Risoluzione"});case"risolta":return(0,i.jsx)(c.E,{className:"bg-green-100 text-green-800 border-green-200",children:"Risolta"});case"chiusa":return(0,i.jsx)(c.E,{className:"bg-gray-100 text-gray-800 border-gray-200",children:"Chiusa"});default:return(0,i.jsx)(c.E,{className:"bg-gray-100 text-gray-800 border-gray-200",children:"Da Verificare"})}},L=e=>{switch(null==e?void 0:e.toLowerCase()){case"critica":return(0,i.jsx)(c.E,{className:"bg-red-100 text-red-800 border-red-200",children:"Critica"});case"alta":return(0,i.jsx)(c.E,{className:"bg-orange-100 text-orange-800 border-orange-200",children:"Alta"});case"media":return(0,i.jsx)(c.E,{className:"bg-yellow-100 text-yellow-800 border-yellow-200",children:"Media"});case"bassa":return(0,i.jsx)(c.E,{className:"bg-blue-100 text-blue-800 border-blue-200",children:"Bassa"});default:return(0,i.jsx)(c.E,{variant:"outline",children:"Non Specificata"})}},k=e=>{switch(null==e?void 0:e.toLowerCase()){case"aperta":return(0,i.jsx)(_.A,{className:"h-4 w-4 text-red-500"});case"in_risoluzione":return(0,i.jsx)(z.A,{className:"h-4 w-4 text-yellow-500"});case"risolta":case"chiusa":return(0,i.jsx)(b.A,{className:"h-4 w-4 text-green-500"});default:return(0,i.jsx)(N.A,{className:"h-4 w-4 text-gray-500"})}},M=a.filter(e=>{var s,a,i,t,l;let r=d.toLowerCase(),n=(null==(s=e.id_cavo)?void 0:s.toLowerCase().includes(r))||(null==(a=e.descrizione)?void 0:a.toLowerCase().includes(r))||(null==(i=e.tipo_non_conformita)?void 0:i.toLowerCase().includes(r))||(null==(t=e.responsabile_rilevazione)?void 0:t.toLowerCase().includes(r)),c=!0;return"all"!==p&&(c=(null==(l=e.stato)?void 0:l.toLowerCase())===p),n&&c}),W={totali:a.length,aperte:a.filter(e=>"APERTA"===e.stato).length,in_risoluzione:a.filter(e=>"IN_RISOLUZIONE"===e.stato).length,risolte:a.filter(e=>"RISOLTA"===e.stato||"CHIUSA"===e.stato).length,critiche:a.filter(e=>"CRITICA"===e.severita).length};return(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("h2",{className:"text-2xl font-bold text-slate-900 flex items-center gap-3",children:[(0,i.jsx)(N.A,{className:"h-6 w-6 text-red-600"}),"Gestione Non Conformit\xe0"]}),(0,i.jsx)("p",{className:"text-slate-600 mt-1",children:"Tracciamento e risoluzione delle non conformit\xe0"})]}),(0,i.jsxs)(n.$,{onClick:()=>{F(null),T(!0)},children:[(0,i.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Nuova Non Conformit\xe0"]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,i.jsx)(r.Zp,{children:(0,i.jsx)(r.Wu,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Totali"}),(0,i.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:W.totali})]}),(0,i.jsx)(u.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,i.jsx)(r.Zp,{children:(0,i.jsx)(r.Wu,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Aperte"}),(0,i.jsx)("p",{className:"text-2xl font-bold text-red-600",children:W.aperte})]}),(0,i.jsx)(_.A,{className:"h-8 w-8 text-red-500"})]})})}),(0,i.jsx)(r.Zp,{children:(0,i.jsx)(r.Wu,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"In Risoluzione"}),(0,i.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:W.in_risoluzione})]}),(0,i.jsx)(z.A,{className:"h-8 w-8 text-yellow-500"})]})})}),(0,i.jsx)(r.Zp,{children:(0,i.jsx)(r.Wu,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Risolte"}),(0,i.jsx)("p",{className:"text-2xl font-bold text-green-600",children:W.risolte})]}),(0,i.jsx)(b.A,{className:"h-8 w-8 text-green-500"})]})})}),(0,i.jsx)(r.Zp,{children:(0,i.jsx)(r.Wu,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Critiche"}),(0,i.jsx)("p",{className:"text-2xl font-bold text-red-600",children:W.critiche})]}),(0,i.jsx)(N.A,{className:"h-8 w-8 text-red-500"})]})})})]}),(0,i.jsxs)(r.Zp,{children:[(0,i.jsx)(r.aR,{children:(0,i.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(f.A,{className:"h-5 w-5"}),"Ricerca e Filtri"]})}),(0,i.jsx)(r.Wu,{children:(0,i.jsxs)("div",{className:"flex gap-4",children:[(0,i.jsx)("div",{className:"flex-1",children:(0,i.jsx)(o.p,{placeholder:"Cerca per ID cavo, descrizione, tipo o responsabile...",value:d,onChange:e=>j(e.target.value)})}),(0,i.jsx)("div",{className:"flex gap-2",children:[{value:"all",label:"Tutte"},{value:"aperta",label:"Aperte"},{value:"in_risoluzione",label:"In Risoluzione"},{value:"risolta",label:"Risolte"},{value:"chiusa",label:"Chiuse"}].map(e=>(0,i.jsx)(n.$,{variant:p===e.value?"default":"outline",size:"sm",onClick:()=>v(e.value),children:e.label},e.value))})]})})]}),(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsxs)(r.ZB,{children:["Elenco Non Conformit\xe0 (",M.length,")"]}),(0,i.jsx)(r.BT,{children:"Gestione delle non conformit\xe0 rilevate durante le certificazioni"})]}),(0,i.jsx)(r.Wu,{children:(0,i.jsx)("div",{className:"rounded-md border",children:(0,i.jsxs)(x.XI,{children:[(0,i.jsx)(x.A0,{children:(0,i.jsxs)(x.Hj,{children:[(0,i.jsx)(x.nd,{children:"ID Cavo"}),(0,i.jsx)(x.nd,{children:"Tipo"}),(0,i.jsx)(x.nd,{children:"Descrizione"}),(0,i.jsx)(x.nd,{children:"Severit\xe0"}),(0,i.jsx)(x.nd,{children:"Data Rilevazione"}),(0,i.jsx)(x.nd,{children:"Responsabile"}),(0,i.jsx)(x.nd,{children:"Stato"}),(0,i.jsx)(x.nd,{children:"Azioni"})]})}),(0,i.jsx)(x.BF,{children:A?(0,i.jsx)(x.Hj,{children:(0,i.jsx)(x.nA,{colSpan:8,className:"text-center py-8",children:(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,i.jsx)(h.A,{className:"h-4 w-4 animate-spin"}),"Caricamento..."]})})}):0===M.length?(0,i.jsx)(x.Hj,{children:(0,i.jsx)(x.nA,{colSpan:8,className:"text-center py-8 text-slate-500",children:"Nessuna non conformit\xe0 trovata"})}):M.map(e=>(0,i.jsxs)(x.Hj,{children:[(0,i.jsx)(x.nA,{className:"font-medium",children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[k(e.stato),e.id_cavo]})}),(0,i.jsx)(x.nA,{children:(0,i.jsx)(c.E,{variant:"outline",children:e.tipo_non_conformita||"Non Specificato"})}),(0,i.jsx)(x.nA,{children:(0,i.jsxs)("div",{className:"max-w-xs",children:[(0,i.jsx)("div",{className:"font-medium truncate",title:e.descrizione,children:e.descrizione}),e.azione_correttiva&&(0,i.jsxs)("div",{className:"text-xs text-slate-500 truncate",title:e.azione_correttiva,children:["Azione: ",e.azione_correttiva]})]})}),(0,i.jsx)(x.nA,{children:L(e.severita)}),(0,i.jsx)(x.nA,{children:new Date(e.data_rilevazione).toLocaleDateString("it-IT")}),(0,i.jsx)(x.nA,{children:e.responsabile_rilevazione||"-"}),(0,i.jsx)(x.nA,{children:B(e.stato)}),(0,i.jsx)(x.nA,{children:(0,i.jsxs)("div",{className:"flex gap-1",children:[(0,i.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>{},title:"Visualizza Dettagli",children:(0,i.jsx)(V.A,{className:"h-4 w-4"})}),(0,i.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>O(e),title:"Modifica",children:(0,i.jsx)(w.A,{className:"h-4 w-4"})}),(0,i.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>D(e.id_non_conformita),title:"Elimina",children:(0,i.jsx)(y.A,{className:"h-4 w-4"})})]})})]},e.id_non_conformita))})]})})})]}),I&&(0,i.jsxs)(C.Fc,{className:"border-red-200 bg-red-50",children:[(0,i.jsx)(_.A,{className:"h-4 w-4 text-red-600"}),(0,i.jsx)(C.TN,{className:"text-red-800",children:I})]}),R&&(0,i.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,i.jsx)("div",{className:"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:(0,i.jsx)("div",{className:"p-6",children:(0,i.jsx)(J,{cantiereId:s,nonConformita:Z,onSuccess:()=>{T(!1),F(null),t()},onCancel:()=>{T(!1),F(null)}})})})})]})}function H(e){let{cantiereId:s}=e,[a,C]=(0,l.useState)("certificazioni"),[S,I]=(0,l.useState)(""),[E,R]=(0,l.useState)("all"),[T,Z]=(0,l.useState)([]),[O,D]=(0,l.useState)([]),[L,k]=(0,l.useState)([]),[M,V]=(0,l.useState)([]),[J,H]=(0,l.useState)(!0),[U,G]=(0,l.useState)(""),[P,q]=(0,l.useState)(!1),[X,Q]=(0,l.useState)(null),{user:Y,cantiere:K}=(0,t.A)();(0,l.useEffect)(()=>{s&&ee()},[s]);let ee=async()=>{try{H(!0),G("");let[e,a,i,t]=await Promise.all([m.km.getCertificazioni(s),m.kw.getStrumenti(s),m.l9.getRapporti(s),m.om.getNonConformita(s)]);Z(e),D(a),k(i),V(t)}catch(s){var e,a;G((null==(a=s.response)||null==(e=a.data)?void 0:e.detail)||"Errore durante il caricamento dei dati")}finally{H(!1)}},es=e=>{Q(e),q(!0)},ea=async e=>{if(confirm("Sei sicuro di voler eliminare questa certificazione?"))try{await m.km.deleteCertificazione(s,e),ee()}catch(e){var a,i;G((null==(i=e.response)||null==(a=i.data)?void 0:a.detail)||"Errore durante l'eliminazione")}},ei=e=>{switch(e){case"CONFORME":return(0,i.jsx)(c.E,{className:"bg-green-100 text-green-800 border-green-200",children:"Conforme"});case"NON_CONFORME":return(0,i.jsx)(c.E,{className:"bg-red-100 text-red-800 border-red-200",children:"Non Conforme"});case"BOZZA":return(0,i.jsx)(c.E,{className:"bg-gray-100 text-gray-800 border-gray-200",children:"Bozza"});case"IN_REVISIONE":return(0,i.jsx)(c.E,{className:"bg-yellow-100 text-yellow-800 border-yellow-200",children:"In Revisione"});default:return(0,i.jsx)(c.E,{variant:"outline",children:e})}},et=T.filter(e=>{var s,a;let i=!S||(null==(s=e.id_cavo)?void 0:s.toLowerCase().includes(S.toLowerCase()))||(null==(a=e.responsabile_certificazione)?void 0:a.toLowerCase().includes(S.toLowerCase())),t="all"===E||e.stato_certificato===E;return i&&t});return J?(0,i.jsx)("div",{className:"flex items-center justify-center p-8",children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(h.A,{className:"h-4 w-4 animate-spin"}),"Caricamento certificazioni..."]})}):P?(0,i.jsx)(A.A,{cantiereId:s,certificazione:X,strumenti:O,onSuccess:()=>{q(!1),ee()},onCancel:()=>q(!1)}):(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsx)("div",{className:"flex items-center justify-between",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("h1",{className:"text-3xl font-bold text-slate-900 flex items-center gap-3",children:[(0,i.jsx)(j.A,{className:"h-8 w-8 text-blue-600"}),"Sistema Certificazioni CEI 64-8"]}),(0,i.jsx)("p",{className:"text-slate-600 mt-1",children:"Gestione completa certificazioni, strumenti e rapporti di collaudo"})]})}),(0,i.jsxs)(d.tU,{value:a,onValueChange:C,className:"space-y-6",children:[(0,i.jsxs)(d.j7,{className:"grid w-full grid-cols-5",children:[(0,i.jsxs)(d.Xi,{value:"certificazioni",className:"flex items-center gap-2",children:[(0,i.jsx)(u.A,{className:"h-4 w-4"}),"Certificazioni"]}),(0,i.jsxs)(d.Xi,{value:"strumenti",className:"flex items-center gap-2",children:[(0,i.jsx)(p.A,{className:"h-4 w-4"}),"Strumenti"]}),(0,i.jsxs)(d.Xi,{value:"rapporti",className:"flex items-center gap-2",children:[(0,i.jsx)(v.A,{className:"h-4 w-4"}),"Rapporti Generali"]}),(0,i.jsxs)(d.Xi,{value:"non-conformita",className:"flex items-center gap-2",children:[(0,i.jsx)(N.A,{className:"h-4 w-4"}),"Non Conformit\xe0"]}),(0,i.jsxs)(d.Xi,{value:"statistiche",className:"flex items-center gap-2",children:[(0,i.jsx)(j.A,{className:"h-4 w-4"}),"Statistiche"]})]}),(0,i.jsxs)(d.av,{value:"certificazioni",className:"space-y-6",children:[(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 flex-1",children:[(0,i.jsxs)("div",{className:"relative flex-1 max-w-md",children:[(0,i.jsx)(f.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,i.jsx)(o.p,{placeholder:"Cerca per ID cavo o responsabile...",value:S,onChange:e=>I(e.target.value),className:"pl-10"})]}),(0,i.jsxs)("select",{value:E,onChange:e=>R(e.target.value),className:"px-3 py-2 border border-slate-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,i.jsx)("option",{value:"all",children:"Tutti gli stati"}),(0,i.jsx)("option",{value:"CONFORME",children:"Conforme"}),(0,i.jsx)("option",{value:"NON_CONFORME",children:"Non Conforme"}),(0,i.jsx)("option",{value:"BOZZA",children:"Bozza"}),(0,i.jsx)("option",{value:"IN_REVISIONE",children:"In Revisione"})]})]}),(0,i.jsxs)(n.$,{onClick:()=>{Q(null),q(!0)},children:[(0,i.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Nuova Certificazione"]})]}),(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsx)(r.ZB,{children:"Certificazioni Cavi"}),(0,i.jsxs)(r.BT,{children:[et.length," certificazioni trovate"]})]}),(0,i.jsx)(r.Wu,{children:(0,i.jsx)("div",{className:"overflow-x-auto",children:(0,i.jsxs)(x.XI,{children:[(0,i.jsx)(x.A0,{children:(0,i.jsxs)(x.Hj,{children:[(0,i.jsx)(x.nd,{children:"ID Cavo"}),(0,i.jsx)(x.nd,{children:"Data Certificazione"}),(0,i.jsx)(x.nd,{children:"Responsabile"}),(0,i.jsx)(x.nd,{children:"Stato"}),(0,i.jsx)(x.nd,{children:"Isolamento (MΩ)"}),(0,i.jsx)(x.nd,{children:"Esito"}),(0,i.jsx)(x.nd,{children:"Azioni"})]})}),(0,i.jsx)(x.BF,{children:0===et.length?(0,i.jsx)(x.Hj,{children:(0,i.jsx)(x.nA,{colSpan:7,className:"text-center py-8 text-slate-500",children:"Nessuna certificazione trovata"})}):et.map(e=>(0,i.jsxs)(x.Hj,{children:[(0,i.jsx)(x.nA,{className:"font-medium",children:e.id_cavo}),(0,i.jsx)(x.nA,{children:e.data_certificazione?new Date(e.data_certificazione).toLocaleDateString("it-IT"):"-"}),(0,i.jsx)(x.nA,{children:e.responsabile_certificazione||"-"}),(0,i.jsx)(x.nA,{children:ei(e.stato_certificato||"BOZZA")}),(0,i.jsx)(x.nA,{children:e.valore_isolamento||"-"}),(0,i.jsx)(x.nA,{children:"CONFORME"===e.esito_complessivo?(0,i.jsx)(b.A,{className:"h-4 w-4 text-green-600"}):"NON_CONFORME"===e.esito_complessivo?(0,i.jsx)(_.A,{className:"h-4 w-4 text-red-600"}):(0,i.jsx)(z.A,{className:"h-4 w-4 text-yellow-600"})}),(0,i.jsx)(x.nA,{children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>es(e),children:(0,i.jsx)(w.A,{className:"h-4 w-4"})}),(0,i.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>ea(e.id_certificazione),children:(0,i.jsx)(y.A,{className:"h-4 w-4"})})]})})]},e.id_certificazione))})]})})})]})]}),(0,i.jsx)(d.av,{value:"strumenti",children:(0,i.jsx)(F,{cantiereId:s,strumenti:O,onUpdate:ee})}),(0,i.jsx)(d.av,{value:"rapporti",children:(0,i.jsx)(B,{cantiereId:s,rapporti:L,onUpdate:ee})}),(0,i.jsx)(d.av,{value:"non-conformita",children:(0,i.jsx)($,{cantiereId:s,nonConformita:M,onUpdate:ee})}),(0,i.jsx)(d.av,{value:"statistiche",children:(0,i.jsx)(W,{cantiereId:s,certificazioni:T,strumenti:O,nonConformita:M})})]}),U&&(0,i.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(_.A,{className:"h-4 w-4 text-red-600"}),(0,i.jsx)("span",{className:"text-red-800",children:U})]})})]})}function U(){let{user:e,cantiere:s}=(0,t.A)(),a=(null==s?void 0:s.id_cantiere)||(null==e?void 0:e.id_utente);return a?(0,i.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,i.jsx)("div",{className:"max-w-[95%] mx-auto",children:(0,i.jsx)(H,{cantiereId:a})})}):(0,i.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,i.jsx)("div",{className:"max-w-[90%] mx-auto",children:(0,i.jsxs)(C.Fc,{className:"border-red-200 bg-red-50",children:[(0,i.jsx)(_.A,{className:"h-4 w-4 text-red-600"}),(0,i.jsx)(C.TN,{className:"text-red-800",children:"Cantiere non selezionato. Seleziona un cantiere per accedere alle certificazioni."})]})})})}},17313:(e,s,a)=>{"use strict";a.d(s,{Xi:()=>o,av:()=>d,j7:()=>c,tU:()=>n});var i=a(95155),t=a(12115),l=a(60704),r=a(59434);let n=l.bL,c=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,i.jsx)(l.B8,{ref:s,className:(0,r.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",a),...t})});c.displayName=l.B8.displayName;let o=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,i.jsx)(l.l9,{ref:s,className:(0,r.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...t})});o.displayName=l.l9.displayName;let d=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,i.jsx)(l.UC,{ref:s,className:(0,r.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...t})});d.displayName=l.UC.displayName},24944:(e,s,a)=>{"use strict";a.d(s,{k:()=>r});var i=a(95155);a(12115);var t=a(55863),l=a(59434);function r(e){let{className:s,value:a,...r}=e;return(0,i.jsx)(t.bL,{"data-slot":"progress",className:(0,l.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",s),...r,children:(0,i.jsx)(t.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:"translateX(-".concat(100-(a||0),"%)")}})})}},62811:(e,s,a)=>{Promise.resolve().then(a.bind(a,3783))},85127:(e,s,a)=>{"use strict";a.d(s,{A0:()=>r,BF:()=>n,Hj:()=>c,XI:()=>l,nA:()=>d,nd:()=>o});var i=a(95155);a(12115);var t=a(59434);function l(e){let{className:s,...a}=e;return(0,i.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,i.jsx)("table",{"data-slot":"table",className:(0,t.cn)("w-full caption-bottom text-sm border-collapse",s),...a})})}function r(e){let{className:s,...a}=e;return(0,i.jsx)("thead",{"data-slot":"table-header",className:(0,t.cn)("[&_tr]:border-b",s),...a})}function n(e){let{className:s,...a}=e;return(0,i.jsx)("tbody",{"data-slot":"table-body",className:(0,t.cn)("[&_tr:last-child]:border-0",s),...a})}function c(e){let{className:s,...a}=e;return(0,i.jsx)("tr",{"data-slot":"table-row",className:(0,t.cn)("data-[state=selected]:bg-muted border-b",s),...a})}function o(e){let{className:s,...a}=e;return(0,i.jsx)("th",{"data-slot":"table-head",className:(0,t.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...a})}function d(e){let{className:s,...a}=e;return(0,i.jsx)("td",{"data-slot":"table-cell",className:(0,t.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...a})}}},e=>{var s=s=>e(e.s=s);e.O(0,[3455,3464,9816,9384,5171,9066,283,9972,8441,1684,7358],()=>s(62811)),_N_E=e.O()}]);