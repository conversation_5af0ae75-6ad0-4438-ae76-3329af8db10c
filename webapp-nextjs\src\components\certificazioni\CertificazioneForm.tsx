'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, AlertCircle, Save, X, FileText, Settings, Cloud } from 'lucide-react'
import { CertificazioneCavo, CertificazioneCavoCreate, StrumentoCertificato } from '@/types/certificazioni'
import { certificazioniApi, caviApi, responsabiliApi, cantieriApi } from '@/lib/api'

interface CertificazioneFormProps {
  cantiereId: number
  certificazione?: CertificazioneCavo | null
  strumenti: StrumentoCertificato[]
  preselectedCavoId?: string // Cavo preselezionato per certificazione singola
  onSuccess: (certificazioneData?: any) => void
  onCancel: () => void
}

interface Cavo {
  id_cavo: string
  tipologia?: string
  sezione?: string
  lunghezza?: number
  stato_installazione?: string
}

interface Responsabile {
  id_responsabile: number
  nome_responsabile: string
}

interface WeatherData {
  temperature: number
  humidity: number
  pressure?: number
  description?: string
  city?: string
  country?: string
  timestamp?: string
  isDemo?: boolean
  source?: string
}

export default function CertificazioneForm({
  cantiereId,
  certificazione,
  strumenti,
  preselectedCavoId,
  onSuccess,
  onCancel
}: CertificazioneFormProps) {
  const [formData, setFormData] = useState<CertificazioneCavoCreate>({
    id_cavo: preselectedCavoId || '',
    id_operatore: undefined,
    strumento_utilizzato: '',
    id_strumento: undefined,
    lunghezza_misurata: undefined,
    valore_continuita: '',
    valore_isolamento: '',
    valore_resistenza: '',
    note: '',
    tipo_certificato: 'SINGOLO',
    stato_certificato: 'BOZZA',
    designazione_funzionale: '',
    tensione_nominale: '230V',
    tensione_prova_isolamento: 500,
    durata_prova_isolamento: 60,
    valore_minimo_isolamento: 500,
    temperatura_prova: 20,
    umidita_prova: 50,
    esito_complessivo: ''
  })

  const [cavi, setCavi] = useState<Cavo[]>([])
  const [responsabili, setResponsabili] = useState<Responsabile[]>([])
  const [weatherData, setWeatherData] = useState<WeatherData | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [isLoadingWeather, setIsLoadingWeather] = useState(false)
  const [error, setError] = useState('')
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})

  const isEdit = !!certificazione
  const isCavoLocked = isEdit || !!preselectedCavoId

  // Carica dati iniziali solo una volta
  useEffect(() => {
    loadInitialData()
    loadWeatherData()
  }, [cantiereId]) // Solo quando cambia il cantiere

  // Aggiorna form quando cambia la certificazione
  useEffect(() => {
    if (certificazione) {
      setFormData({
        id_cavo: certificazione.id_cavo,
        id_operatore: certificazione.id_operatore,
        strumento_utilizzato: certificazione.strumento_utilizzato || '',
        id_strumento: certificazione.id_strumento,
        lunghezza_misurata: certificazione.lunghezza_misurata,
        valore_continuita: certificazione.valore_continuita || '',
        valore_isolamento: certificazione.valore_isolamento || '',
        valore_resistenza: certificazione.valore_resistenza || '',
        note: certificazione.note || '',
        tipo_certificato: certificazione.tipo_certificato || 'SINGOLO',
        stato_certificato: certificazione.stato_certificato || 'BOZZA',
        designazione_funzionale: certificazione.designazione_funzionale || '',
        tensione_nominale: certificazione.tensione_nominale || '230V',
        tensione_prova_isolamento: certificazione.tensione_prova_isolamento || 500,
        durata_prova_isolamento: certificazione.durata_prova_isolamento || 60,
        valore_minimo_isolamento: certificazione.valore_minimo_isolamento || 500,
        temperatura_prova: certificazione.temperatura_prova || weatherData?.temperature || 20,
        umidita_prova: certificazione.umidita_prova || weatherData?.humidity || 50,
        esito_complessivo: certificazione.esito_complessivo || ''
      })
    }
  }, [certificazione])

  // Aggiorna temperatura e umidità quando arrivano i dati meteo
  useEffect(() => {
    if (weatherData && !certificazione) {
      setFormData(prev => ({
        ...prev,
        temperatura_prova: weatherData.temperature || 20,
        umidita_prova: weatherData.humidity || 50
      }))
    }
  }, [weatherData, certificazione])

  const loadInitialData = async () => {
    try {
      setIsLoading(true)
      const [caviData, responsabiliData] = await Promise.all([
        caviApi.getCavi(cantiereId),
        responsabiliApi.getResponsabili(cantiereId)
      ])
      setCavi(caviData)
      setResponsabili(responsabiliData)
    } catch (error: any) {
      setError('Errore durante il caricamento dei dati')
    } finally {
      setIsLoading(false)
    }
  }

  const loadWeatherData = async () => {
    try {
      setIsLoadingWeather(true)
      const response = await cantieriApi.getWeatherData(cantiereId)
      if (response.success && response.data) {
        setWeatherData(response.data)
        // I valori del form vengono aggiornati tramite useEffect separato
      }
    } catch (error: any) {
      console.warn('Impossibile caricare dati meteorologici:', error)
      // Non mostrare errore all'utente, usa valori predefiniti
    } finally {
      setIsLoadingWeather(false)
    }
  }

  // Funzione per collegare automaticamente un cavo (CEI 64-8 compliance)
  const collegaCavoAutomatico = async (cavoId: string, responsabile: string = 'cantiere') => {
    try {
      let partenzaCollegata = false
      let arrivoCollegato = false

      // Prova a collegare il lato partenza
      try {
        await caviApi.collegaCavo(cantiereId, cavoId, 'partenza', responsabile)
        partenzaCollegata = true
      } catch (error: any) {
        if (error.response?.data?.detail?.includes('già collegato')) {
          partenzaCollegata = true
        } else {
          throw error
        }
      }

      // Prova a collegare il lato arrivo
      try {
        await caviApi.collegaCavo(cantiereId, cavoId, 'arrivo', responsabile)
        arrivoCollegato = true
      } catch (error: any) {
        if (error.response?.data?.detail?.includes('già collegato')) {
          arrivoCollegato = true
        } else {
          throw error
        }
      }

      return partenzaCollegata && arrivoCollegato
    } catch (error) {
      console.error('Errore nel collegamento automatico:', error)
      throw error
    }
  }

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {}

    if (!formData.id_cavo) {
      errors.id_cavo = 'Seleziona un cavo'
    }

    if (!formData.valore_isolamento) {
      errors.valore_isolamento = 'Inserisci il valore di isolamento'
    } else if (parseFloat(formData.valore_isolamento) < 0) {
      errors.valore_isolamento = 'Il valore deve essere positivo'
    }

    if (formData.tensione_prova_isolamento && formData.tensione_prova_isolamento < 0) {
      errors.tensione_prova_isolamento = 'La tensione deve essere positiva'
    }

    if (formData.temperatura_prova && (formData.temperatura_prova < -40 || formData.temperatura_prova > 80)) {
      errors.temperatura_prova = 'Temperatura deve essere tra -40°C e 80°C'
    }

    if (formData.umidita_prova && (formData.umidita_prova < 0 || formData.umidita_prova > 100)) {
      errors.umidita_prova = 'Umidità deve essere tra 0% e 100%'
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    try {
      setIsSaving(true)
      setError('')

      // Verifica se il cavo è completamente collegato (CEI 64-8 compliance)
      if (!isEdit && formData.id_cavo) {
        const cavo = cavi.find(c => c.id_cavo === formData.id_cavo)
        const isCompletelyConnected = cavo && (cavo.collegamenti || 0) === 3

        if (!isCompletelyConnected) {
          const statoCollegamenti = (cavo?.collegamenti || 0) === 0 ? 'Non collegato' :
                                   (cavo?.collegamenti || 0) === 1 ? 'Solo partenza collegata' :
                                   (cavo?.collegamenti || 0) === 2 ? 'Solo arrivo collegato' :
                                   'Stato sconosciuto'

          const conferma = window.confirm(
            `ATTENZIONE: Il cavo ${formData.id_cavo} non risulta completamente collegato.\n\n` +
            `Stato collegamenti: ${statoCollegamenti}\n\n` +
            `Vuoi collegare automaticamente entrambi i lati del cavo e procedere con la certificazione CEI 64-8?\n\n` +
            `(Il sistema collegherà automaticamente il cavo a "cantiere" su entrambi i lati)`
          )

          if (!conferma) {
            setIsSaving(false)
            return
          }

          // Collega automaticamente il cavo
          try {
            await collegaCavoAutomatico(formData.id_cavo, 'cantiere')
            // Ricarica i cavi per aggiornare lo stato
            const updatedCavi = await caviApi.getCavi(cantiereId)
            setCavi(updatedCavi)
          } catch (error: any) {
            setError(`Errore nel collegamento automatico: ${error.response?.data?.detail || error.message}`)
            setIsSaving(false)
            return
          }
        }
      }

      // Determina automaticamente lo stato in base ai valori
      const isolamento = parseFloat(formData.valore_isolamento || '0')
      const statoAutomatico = isolamento >= (formData.valore_minimo_isolamento || 500) ? 'CONFORME' : 'NON_CONFORME'
      
      const dataToSubmit = {
        ...formData,
        stato_certificato: formData.stato_certificato === 'BOZZA' ? statoAutomatico : formData.stato_certificato,
        esito_complessivo: isolamento >= (formData.valore_minimo_isolamento || 500) ? 'CONFORME' : 'NON CONFORME',
        // Includi i dati meteorologici automatici
        temperatura_prova: formData.temperatura_prova || weatherData?.temperature || 20,
        umidita_prova: formData.umidita_prova || weatherData?.humidity || 50
      }

      if (isEdit && certificazione) {
        await certificazioniApi.updateCertificazione(cantiereId, certificazione.id_certificazione, dataToSubmit)
      } else {
        await certificazioniApi.createCertificazione(cantiereId, dataToSubmit)
      }

      onSuccess(dataToSubmit)
    } catch (error: any) {
      setError(error.response?.data?.detail || 'Errore durante il salvataggio')
    } finally {
      setIsSaving(false)
    }
  }

  const handleInputChange = (field: keyof CertificazioneCavoCreate, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Rimuovi errore di validazione se presente
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="flex items-center gap-2">
          <Loader2 className="h-4 w-4 animate-spin" />
          Caricamento dati...
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-slate-900 flex items-center gap-3">
            <FileText className="h-6 w-6 text-blue-600" />
            {isEdit ? 'Modifica Certificazione' : 'Nuova Certificazione'}
          </h1>
          <p className="text-slate-600 mt-1">
            {isEdit ? 'Modifica i dati della certificazione esistente' : 'Crea una nuova certificazione CEI 64-8'}
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline" onClick={onCancel}>
            <X className="h-4 w-4 mr-2" />
            Annulla
          </Button>
          <Button onClick={handleSubmit} disabled={isSaving}>
            {isSaving ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Save className="h-4 w-4 mr-2" />}
            {isEdit ? 'Aggiorna' : 'Salva'}
          </Button>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Informazioni Base */}
        <Card>
          <CardHeader>
            <CardTitle>Informazioni Base</CardTitle>
            <CardDescription>Dati principali della certificazione</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="id_cavo">Cavo *</Label>
                <Select
                  value={formData.id_cavo}
                  onValueChange={(value) => handleInputChange('id_cavo', value)}
                  disabled={isCavoLocked}
                >
                  <SelectTrigger className={validationErrors.id_cavo ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Seleziona cavo..." />
                  </SelectTrigger>
                  <SelectContent>
                    {cavi.map((cavo) => (
                      <SelectItem key={cavo.id_cavo} value={cavo.id_cavo}>
                        {cavo.id_cavo} - {cavo.tipologia} {cavo.sezione}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {validationErrors.id_cavo && (
                  <p className="text-sm text-red-600">{validationErrors.id_cavo}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="id_operatore">Operatore</Label>
                <Select
                  value={formData.id_operatore?.toString() || ''}
                  onValueChange={(value) => handleInputChange('id_operatore', parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Seleziona operatore..." />
                  </SelectTrigger>
                  <SelectContent>
                    {responsabili.map((resp) => (
                      <SelectItem key={resp.id_responsabile} value={resp.id_responsabile.toString()}>
                        {resp.nome_responsabile}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="id_strumento">Strumento</Label>
                <Select
                  value={formData.id_strumento?.toString() || ''}
                  onValueChange={(value) => {
                    const strumento = strumenti.find(s => s.id_strumento === parseInt(value))
                    handleInputChange('id_strumento', parseInt(value))
                    if (strumento) {
                      handleInputChange('strumento_utilizzato', `${strumento.marca} ${strumento.modello}`)
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Seleziona strumento..." />
                  </SelectTrigger>
                  <SelectContent>
                    {strumenti.map((strumento) => (
                      <SelectItem key={strumento.id_strumento} value={strumento.id_strumento.toString()}>
                        {strumento.nome} - {strumento.marca} {strumento.modello}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="tipo_certificato">Tipo Certificato</Label>
                <Select
                  value={formData.tipo_certificato || 'SINGOLO'}
                  onValueChange={(value) => handleInputChange('tipo_certificato', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="SINGOLO">Singolo</SelectItem>
                    <SelectItem value="GRUPPO">Gruppo</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Condizioni Ambientali Automatiche */}
        {weatherData && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Cloud className="h-5 w-5" />
                Condizioni Ambientali (Rilevate Automaticamente)
              </CardTitle>
              <CardDescription>Dati meteorologici per il cantiere</CardDescription>
            </CardHeader>
            <CardContent>
              <div className={`p-4 rounded-lg border ${weatherData.isDemo ? 'bg-yellow-50 border-yellow-200' : 'bg-green-50 border-green-200'}`}>
                <div className="flex items-center gap-4">
                  {isLoadingWeather ? (
                    <Loader2 className="h-5 w-5 animate-spin" />
                  ) : (
                    <span className="text-2xl">🌤️</span>
                  )}
                  <div className="flex-1">
                    <div className="flex items-center gap-6">
                      <div>
                        <span className="text-sm text-slate-600">Temperatura:</span>
                        <span className="ml-2 font-semibold">{weatherData.temperature}°C</span>
                      </div>
                      <div>
                        <span className="text-sm text-slate-600">Umidità:</span>
                        <span className="ml-2 font-semibold">{weatherData.humidity}%</span>
                      </div>
                      {weatherData.city && (
                        <div>
                          <span className="text-sm text-slate-600">Località:</span>
                          <span className="ml-2 font-semibold">{weatherData.city}</span>
                        </div>
                      )}
                    </div>
                    <div className="text-xs text-slate-500 mt-1">
                      {weatherData.isDemo ? 'Dati demo' : 'Dati live'} -
                      {weatherData.source === 'cantiere_database' ? ' Database cantiere' : ' API meteo'}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Misurazioni */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Misurazioni e Test
            </CardTitle>
            <CardDescription>Risultati delle prove elettriche</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="valore_isolamento">Isolamento (MΩ) *</Label>
                <Input
                  id="valore_isolamento"
                  type="number"
                  step="0.1"
                  value={formData.valore_isolamento}
                  onChange={(e) => handleInputChange('valore_isolamento', e.target.value)}
                  className={validationErrors.valore_isolamento ? 'border-red-500' : ''}
                  placeholder="500"
                />
                {validationErrors.valore_isolamento && (
                  <p className="text-sm text-red-600">{validationErrors.valore_isolamento}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="valore_continuita">Continuità</Label>
                <Input
                  id="valore_continuita"
                  value={formData.valore_continuita}
                  onChange={(e) => handleInputChange('valore_continuita', e.target.value)}
                  placeholder="OK"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="valore_resistenza">Resistenza</Label>
                <Input
                  id="valore_resistenza"
                  value={formData.valore_resistenza}
                  onChange={(e) => handleInputChange('valore_resistenza', e.target.value)}
                  placeholder="OK"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="tensione_prova_isolamento">Tensione Prova (V)</Label>
                <Input
                  id="tensione_prova_isolamento"
                  type="number"
                  value={formData.tensione_prova_isolamento}
                  onChange={(e) => handleInputChange('tensione_prova_isolamento', parseInt(e.target.value))}
                  placeholder="500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="temperatura_prova">Temperatura (°C)</Label>
                <Input
                  id="temperatura_prova"
                  type="number"
                  value={formData.temperatura_prova}
                  onChange={(e) => handleInputChange('temperatura_prova', parseInt(e.target.value))}
                  placeholder="20"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="umidita_prova">Umidità (%)</Label>
                <Input
                  id="umidita_prova"
                  type="number"
                  min="0"
                  max="100"
                  value={formData.umidita_prova}
                  onChange={(e) => handleInputChange('umidita_prova', parseInt(e.target.value))}
                  placeholder="50"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Note e Stato */}
        <Card>
          <CardHeader>
            <CardTitle>Note e Stato</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="note">Note</Label>
              <Textarea
                id="note"
                value={formData.note}
                onChange={(e) => handleInputChange('note', e.target.value)}
                placeholder="Note aggiuntive sulla certificazione..."
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="stato_certificato">Stato Certificato</Label>
              <Select
                value={formData.stato_certificato || 'BOZZA'}
                onValueChange={(value) => handleInputChange('stato_certificato', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="BOZZA">Bozza</SelectItem>
                  <SelectItem value="CONFORME">Conforme</SelectItem>
                  <SelectItem value="NON_CONFORME">Non Conforme</SelectItem>
                  <SelectItem value="CONFORME_CON_RISERVA">Conforme con Riserva</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {error && (
          <Alert className="border-red-200 bg-red-50">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">{error}</AlertDescription>
          </Alert>
        )}
      </form>
    </div>
  )
}
