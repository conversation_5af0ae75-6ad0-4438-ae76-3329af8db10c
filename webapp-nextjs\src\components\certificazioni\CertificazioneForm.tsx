'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { FileText, Save, Loader2, AlertCircle, X } from 'lucide-react'
import { CertificazioneCavo, StrumentoCertificato } from '@/types/certificazioni'
import { useCertificazioneForm } from '@/hooks/useCertificazioneForm'

// Componenti sezioni
import { InformazioniBase } from './sections/InformazioniBase'
import { CondizioniAmbientali } from './sections/CondizioniAmbientali'
import { MisurazioniTest } from './sections/MisurazioniTest'
import { NoteEStato } from './sections/NoteEStato'

interface CertificazioneFormProps {
  cantiereId: number
  certificazione?: CertificazioneCavo | null
  strumenti: StrumentoCertificato[]
  preselectedCavoId?: string
  onSuccess: (certificazione: CertificazioneCavo) => void
  onCancel: () => void
}

/**
 * Form di certificazione CEI 64-8 completamente ridisegnato
 * Layout moderno a singola colonna con sezioni organizzate
 */
export default function CertificazioneForm({
  cantiereId,
  certificazione,
  strumenti,
  preselectedCavoId,
  onSuccess,
  onCancel
}: CertificazioneFormProps) {
  const {
    // Dati
    formData,
    cavi,
    responsabili,
    weatherData,

    // Stati
    isLoading,
    isSaving,
    isLoadingWeather,
    error,
    validationErrors,
    isWeatherOverride,
    isEdit,
    isCavoLocked,

    // Funzioni
    handleInputChange,
    handleSubmit,
    setIsWeatherOverride,
    onCancel: handleCancel
  } = useCertificazioneForm({
    cantiereId,
    certificazione,
    strumenti,
    preselectedCavoId,
    onSuccess,
    onCancel
  })

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Caricamento dati certificazione...</p>
        </div>
      </div>
    )
  }

  // Render del componente principale
  return (
    <div className="h-full w-full bg-gray-50">
      {/* Header Moderno */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-4xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FileText className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  {isEdit ? 'Modifica Certificazione' : 'Nuova Certificazione CEI 64-8'}
                </h1>
                {preselectedCavoId && (
                  <p className="text-sm text-gray-500">Cavo: {preselectedCavoId}</p>
                )}
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onCancel}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>

      {/* Alert Errori */}
      {error && (
        <div className="bg-red-50 border-b border-red-200">
          <div className="max-w-4xl mx-auto px-6 py-4">
            <Alert variant="destructive" className="border-red-200 bg-red-50">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          </div>
        </div>
      )}

      {/* Contenuto Principale */}
      <div className="max-w-4xl mx-auto px-6 py-8">
        <form onSubmit={(e) => { e.preventDefault(); handleSubmit(); }} className="space-y-8">

          {/* Sezione 1: Informazioni Base */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-100 bg-gray-50">
              <h2 className="text-lg font-semibold text-gray-900">📋 Informazioni Base</h2>
              <p className="text-sm text-gray-600 mt-1">Dati principali del cavo e operatore</p>
            </div>
            <div className="p-6">
              <InformazioniBase
                formData={formData}
                cavi={cavi}
                responsabili={responsabili}
                strumenti={strumenti}
                validationErrors={validationErrors}
                isCavoLocked={isCavoLocked}
                onInputChange={handleInputChange}
              />
            </div>
          </div>

          {/* Sezione 2: Condizioni Ambientali */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-100 bg-gray-50">
              <h2 className="text-lg font-semibold text-gray-900">🌤️ Condizioni Ambientali</h2>
              <p className="text-sm text-gray-600 mt-1">Temperatura e umidità durante la certificazione</p>
            </div>
            <div className="p-6">
              <CondizioniAmbientali
                formData={formData}
                weatherData={weatherData}
                isLoadingWeather={isLoadingWeather}
                isWeatherOverride={isWeatherOverride}
                onInputChange={handleInputChange}
                onToggleWeatherOverride={() => setIsWeatherOverride(!isWeatherOverride)}
              />
            </div>
          </div>

          {/* Sezione 3: Misurazioni e Test */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-100 bg-gray-50">
              <h2 className="text-lg font-semibold text-gray-900">⚡ Misurazioni e Test CEI 64-8</h2>
              <p className="text-sm text-gray-600 mt-1">Valori di isolamento, continuità e parametri di prova</p>
            </div>
            <div className="p-6">
              <MisurazioniTest
                formData={formData}
                validationErrors={validationErrors}
                onInputChange={handleInputChange}
              />
            </div>
          </div>

          {/* Sezione 4: Note e Stato */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-100 bg-gray-50">
              <h2 className="text-lg font-semibold text-gray-900">📝 Note e Stato Certificazione</h2>
              <p className="text-sm text-gray-600 mt-1">Osservazioni aggiuntive e stato finale</p>
            </div>
            <div className="p-6">
              <NoteEStato
                formData={formData}
                onInputChange={handleInputChange}
              />
            </div>
          </div>

          {/* Footer Azioni */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex justify-end gap-4">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSaving}
                className="px-8 py-2"
              >
                Annulla
              </Button>
              <Button
                type="submit"
                disabled={isSaving}
                className="px-8 py-2 bg-blue-600 hover:bg-blue-700"
              >
                {isSaving ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Salvando...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    {isEdit ? 'Aggiorna Certificazione' : 'Salva Certificazione'}
                  </>
                )}
              </Button>
            </div>
          </div>
        </form>
      </div>
    </div>
  )
}
