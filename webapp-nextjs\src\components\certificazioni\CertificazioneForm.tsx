'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { FileText, Save, Loader2, AlertCircle } from 'lucide-react'
import { CertificazioneCavo, StrumentoCertificato } from '@/types/certificazioni'
import { useCertificazioneForm } from '@/hooks/useCertificazioneForm'

// Componenti sezioni
import { InformazioniBase } from './sections/InformazioniBase'
import { CondizioniAmbientali } from './sections/CondizioniAmbientali'
import { MisurazioniTest } from './sections/MisurazioniTest'
import { NoteEStato } from './sections/NoteEStato'

interface CertificazioneFormProps {
  cantiereId: number
  certificazione?: CertificazioneCavo | null
  strumenti: StrumentoCertificato[]
  preselectedCavoId?: string
  onSuccess: (certificazione: CertificazioneCavo) => void
  onCancel: () => void
}

/**
 * Componente principale per la gestione delle certificazioni CEI 64-8
 * Utilizza il pattern di separazione delle responsabilità:
 * - useCertificazioneForm: gestisce tutta la logica di business
 * - Sotto-componenti: gestiscono la visualizzazione delle singole sezioni
 */
export default function CertificazioneForm({
  cantiereId,
  certificazione,
  strumenti,
  preselectedCavoId,
  onSuccess,
  onCancel
}: CertificazioneFormProps) {
  // Utilizzo del custom hook per la gestione della logica
  const {
    // Dati
    formData,
    cavi,
    responsabili,
    weatherData,

    // Stati
    isLoading,
    isSaving,
    isLoadingWeather,
    error,
    validationErrors,
    isWeatherOverride,
    isEdit,
    isCavoLocked,

    // Funzioni
    handleInputChange,
    handleSubmit,
    setIsWeatherOverride,
    onCancel: handleCancel
  } = useCertificazioneForm({
    cantiereId,
    certificazione,
    strumenti,
    preselectedCavoId,
    onSuccess,
    onCancel
  })
  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Caricamento dati...</span>
      </div>
    )
  }

  // Render del componente principale
  return (
    <div className="h-full w-full flex flex-col">
      {/* Header Ottimizzato */}
      <div className="flex items-center justify-between px-6 py-4 border-b bg-white shrink-0">
        <div className="flex items-center gap-3">
          <FileText className="h-5 w-5 text-blue-600" />
          <div>
            <h1 className="text-lg font-semibold text-slate-900">
              {isEdit ? 'Modifica Certificazione' : 'Nuova Certificazione CEI 64-8'}
            </h1>
            {preselectedCavoId && (
              <p className="text-sm text-slate-600">Cavo: {preselectedCavoId}</p>
            )}
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Button variant="outline" onClick={onCancel} disabled={isSaving}>
            Annulla
          </Button>
          <Button onClick={handleSubmit} disabled={isSaving}>
            {isSaving ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Save className="h-4 w-4 mr-2" />}
            {isEdit ? 'Aggiorna' : 'Salva Certificazione'}
          </Button>
        </div>
      </div>

      {/* Contenuto Principale */}
      <div className="flex-1 overflow-hidden">
        {error && (
          <div className="px-6 py-3 border-b bg-red-50">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          </div>
        )}

        <form onSubmit={(e) => { e.preventDefault(); handleSubmit(); }} className="h-full">
          {/* Layout a 2 colonne ottimizzato per schermi grandi */}
          <div className="h-full p-6 overflow-y-auto">
            <div className="grid grid-cols-1 xl:grid-cols-2 gap-6 min-h-full">

              {/* COLONNA SINISTRA */}
              <div className="space-y-4">
                <InformazioniBase
                  formData={formData}
                  cavi={cavi}
                  responsabili={responsabili}
                  strumenti={strumenti}
                  validationErrors={validationErrors}
                  isCavoLocked={isCavoLocked}
                  onInputChange={handleInputChange}
                />

                <CondizioniAmbientali
                  formData={formData}
                  weatherData={weatherData}
                  isLoadingWeather={isLoadingWeather}
                  isWeatherOverride={isWeatherOverride}
                  onInputChange={handleInputChange}
                  onToggleWeatherOverride={() => setIsWeatherOverride(!isWeatherOverride)}
                />
              </div>

              {/* COLONNA DESTRA */}
              <div className="space-y-4">
                <MisurazioniTest
                  formData={formData}
                  validationErrors={validationErrors}
                  onInputChange={handleInputChange}
                />

                <NoteEStato
                  formData={formData}
                  onInputChange={handleInputChange}
                />
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  )
}
