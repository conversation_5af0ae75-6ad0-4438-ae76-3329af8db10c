'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { FileText, Save, Loader2, AlertCircle } from 'lucide-react'
import { CertificazioneCavo, StrumentoCertificato } from '@/types/certificazioni'
import { useCertificazioneForm } from '@/hooks/useCertificazioneForm'

// Componenti sezioni
import { InformazioniBase } from './sections/InformazioniBase'
import { CondizioniAmbientali } from './sections/CondizioniAmbientali'
import { MisurazioniTest } from './sections/MisurazioniTest'
import { NoteEStato } from './sections/NoteEStato'

interface CertificazioneFormProps {
  cantiereId: number
  certificazione?: CertificazioneCavo | null
  strumenti: StrumentoCertificato[]
  preselectedCavoId?: string
  onSuccess: (certificazione: CertificazioneCavo) => void
  onCancel: () => void
}

/**
 * Componente principale per la gestione delle certificazioni CEI 64-8
 * Utilizza il pattern di separazione delle responsabilità:
 * - useCertificazioneForm: gestisce tutta la logica di business
 * - Sotto-componenti: gestiscono la visualizzazione delle singole sezioni
 */
export default function CertificazioneForm({
  cantiereId,
  certificazione,
  strumenti,
  preselectedCavoId,
  onSuccess,
  onCancel
}: CertificazioneFormProps) {
  // Utilizzo del custom hook per la gestione della logica
  const {
    // Dati
    formData,
    cavi,
    responsabili,
    weatherData,

    // Stati
    isLoading,
    isSaving,
    isLoadingWeather,
    error,
    validationErrors,
    isWeatherOverride,
    isEdit,
    isCavoLocked,

    // Funzioni
    handleInputChange,
    handleSubmit,
    setIsWeatherOverride,
    onCancel: handleCancel
  } = useCertificazioneForm({
    cantiereId,
    certificazione,
    strumenti,
    preselectedCavoId,
    onSuccess,
    onCancel
  })
  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Caricamento dati...</span>
      </div>
    )
  }

  // Render del componente principale
  return (
    <div className="h-full w-full">
      {/* Header Compatto */}
      <div className="flex items-center justify-between p-3 border-b bg-white">
        <div className="flex items-center gap-2">
          <FileText className="h-4 w-4 text-blue-600" />
          <h1 className="text-base font-semibold text-slate-900">
            {isEdit ? 'Modifica Certificazione' : 'Nuova Certificazione'}
          </h1>
        </div>

        <Button onClick={handleSubmit} disabled={isSaving} size="sm">
          {isSaving ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Save className="h-4 w-4 mr-2" />}
          {isEdit ? 'Aggiorna' : 'Salva'}
        </Button>
      </div>

      {/* Contenuto in Grid Layout */}
      <div className="p-3 h-[calc(100%-60px)] overflow-y-auto">
        {error && (
          <Alert variant="destructive" className="mb-2">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <form onSubmit={(e) => { e.preventDefault(); handleSubmit(); }}>
          {/* Layout a 2 colonne per ottimizzare spazio */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 h-full">

            {/* COLONNA SINISTRA */}
            <div className="space-y-2">
              <InformazioniBase
                formData={formData}
                cavi={cavi}
                responsabili={responsabili}
                strumenti={strumenti}
                validationErrors={validationErrors}
                isCavoLocked={isCavoLocked}
                onInputChange={handleInputChange}
              />

              <CondizioniAmbientali
                formData={formData}
                weatherData={weatherData}
                isLoadingWeather={isLoadingWeather}
                isWeatherOverride={isWeatherOverride}
                onInputChange={handleInputChange}
                onToggleWeatherOverride={() => setIsWeatherOverride(!isWeatherOverride)}
              />
            </div>

            {/* COLONNA DESTRA */}
            <div className="space-y-2">
              <MisurazioniTest
                formData={formData}
                validationErrors={validationErrors}
                onInputChange={handleInputChange}
              />

              <NoteEStato
                formData={formData}
                onInputChange={handleInputChange}
              />
            </div>
          </div>
        </form>
      </div>
    </div>
  )
}
