'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { FileText, Save, Loader2, AlertCircle } from 'lucide-react'
import { CertificazioneCavo, StrumentoCertificato } from '@/types/certificazioni'
import { useCertificazioneForm } from '@/hooks/useCertificazioneForm'

// Componenti sezioni
import { InformazioniBase } from './sections/InformazioniBase'
import { CondizioniAmbientali } from './sections/CondizioniAmbientali'
import { MisurazioniTest } from './sections/MisurazioniTest'
import { NoteEStato } from './sections/NoteEStato'

interface CertificazioneFormProps {
  cantiereId: number
  certificazione?: CertificazioneCavo | null
  strumenti: StrumentoCertificato[]
  preselectedCavoId?: string
  onSuccess: (certificazione: CertificazioneCavo) => void
  onCancel: () => void
}

/**
 * Componente principale per la gestione delle certificazioni CEI 64-8
 * Utilizza il pattern di separazione delle responsabilità:
 * - useCertificazioneForm: gestisce tutta la logica di business
 * - Sotto-componenti: gestiscono la visualizzazione delle singole sezioni
 */
export default function CertificazioneForm({
  cantiereId,
  certificazione,
  strumenti,
  preselectedCavoId,
  onSuccess,
  onCancel
}: CertificazioneFormProps) {
  // Utilizzo del custom hook per la gestione della logica
  const {
    // Dati
    formData,
    cavi,
    responsabili,
    weatherData,

    // Stati
    isLoading,
    isSaving,
    isLoadingWeather,
    error,
    validationErrors,
    isWeatherOverride,
    isEdit,
    isCavoLocked,

    // Funzioni
    handleInputChange,
    handleSubmit,
    setIsWeatherOverride,
    onCancel: handleCancel
  } = useCertificazioneForm({
    cantiereId,
    certificazione,
    strumenti,
    preselectedCavoId,
    onSuccess,
    onCancel
  })
  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Caricamento dati...</span>
      </div>
    )
  }

  // Render del componente principale
  return (
    <div className="h-full w-full flex flex-col">
      {/* Header Professionale */}
      <div className="flex items-center px-6 py-5 border-b border-gray-200 bg-white shrink-0">
        <div className="flex items-center gap-3 max-w-7xl mx-auto w-full">
          <FileText className="h-6 w-6 text-blue-600" />
          <div>
            <h1 className="text-xl font-semibold text-gray-900">
              {isEdit ? 'Modifica Certificazione' : 'Nuova Certificazione CEI 64-8'}
            </h1>
            {preselectedCavoId && (
              <p className="text-sm text-gray-600">Cavo: {preselectedCavoId}</p>
            )}
          </div>
        </div>
      </div>

      {/* Contenuto Principale */}
      <div className="flex-1 overflow-hidden flex flex-col">
        {error && (
          <div className="px-6 py-4 border-b border-red-200 bg-red-50">
            <div className="max-w-7xl mx-auto">
              <Alert variant="destructive" className="border-red-200">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="text-sm">{error}</AlertDescription>
              </Alert>
            </div>
          </div>
        )}

        <form onSubmit={(e) => { e.preventDefault(); handleSubmit(); }} className="flex-1 flex flex-col min-w-[800px] max-w-full">
          {/* Layout professionale con spaziature ottimizzate */}
          <div className="flex-1 p-6 overflow-y-auto bg-gray-50">
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 min-h-full max-w-7xl mx-auto">

              {/* COLONNA SINISTRA */}
              <div className="space-y-6">
                <InformazioniBase
                  formData={formData}
                  cavi={cavi}
                  responsabili={responsabili}
                  strumenti={strumenti}
                  validationErrors={validationErrors}
                  isCavoLocked={isCavoLocked}
                  onInputChange={handleInputChange}
                />
              </div>

              {/* COLONNA CENTRALE */}
              <div className="space-y-6">
                <CondizioniAmbientali
                  formData={formData}
                  weatherData={weatherData}
                  isLoadingWeather={isLoadingWeather}
                  isWeatherOverride={isWeatherOverride}
                  onInputChange={handleInputChange}
                  onToggleWeatherOverride={() => setIsWeatherOverride(!isWeatherOverride)}
                />
              </div>

              {/* COLONNA DESTRA */}
              <div className="space-y-6">
                <MisurazioniTest
                  formData={formData}
                  validationErrors={validationErrors}
                  onInputChange={handleInputChange}
                />

                <NoteEStato
                  formData={formData}
                  onInputChange={handleInputChange}
                />
              </div>
            </div>
          </div>

          {/* Footer con pulsanti */}
          <div className="border-t border-gray-200 bg-white px-6 py-4 shrink-0">
            <div className="flex justify-end gap-3 max-w-7xl mx-auto">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSaving}
                className="px-6 py-2 text-sm font-medium border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                Annulla
              </Button>
              <Button
                type="button"
                onClick={handleSubmit}
                disabled={isSaving}
                className="px-6 py-2 text-sm font-medium bg-blue-600 hover:bg-blue-700 text-white"
              >
                {isSaving ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Save className="h-4 w-4 mr-2" />}
                {isEdit ? 'Aggiorna Certificazione' : 'Salva Certificazione'}
              </Button>
            </div>
          </div>
        </form>
      </div>
    </div>
  )
}
